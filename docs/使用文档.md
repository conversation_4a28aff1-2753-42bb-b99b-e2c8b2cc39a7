# Excel通用导入导出功能使用文档

## 概述

本项目基于EasyExcel框架实现了通用的Excel导入导出功能，支持通过表名或模板标识进行Excel文件的导入导出操作，具有详细的错误定位、灵活的配置选项和完善的数据校验功能。

## 主要特性

- ✅ **支持表名和模板标识两种方式**：可以通过传入表名或枚举标识来导入导出
- ✅ **详细的错误定位**：错误信息精确到行号和列号
- ✅ **灵活的配置选项**：支持严格模式、宽松模式、预览模式等
- ✅ **完善的数据校验**：支持必填、长度、范围、格式等多种校验规则
- ✅ **异步处理支持**：支持异步导入导出，适合大数据量处理
- ✅ **Java 8兼容**：使用Java 8语法，兼容JDK 8

## 快速开始

### 1. 添加依赖

项目已包含所需依赖，主要包括：

```xml
<!-- EasyExcel 依赖 -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>easyexcel</artifactId>
    <version>3.3.2</version>
</dependency>

<!-- Spring Boot Web -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- 工具类依赖 -->
<dependency>
    <groupId>cn.hutool</groupId>
    <artifactId>hutool-all</artifactId>
    <version>5.8.38</version>
</dependency>
```

### 2. 创建数据模型

使用EasyExcel注解定义数据模型：

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo {
    
    @ExcelProperty(value = "用户ID", index = 0)
    private Long userId;
    
    @ExcelProperty(value = "用户名", index = 1)
    private String username;
    
    @ExcelProperty(value = "姓名", index = 2)
    private String realName;
    
    @ExcelProperty(value = "邮箱", index = 3)
    private String email;
    
    // ... 其他字段
}
```

### 3. 基本使用

#### 3.1 导出Excel

```java
@Autowired
private ExcelService excelService;

// 准备数据
List<UserInfo> userList = getUserList();

// 方式1：根据表名导出
try (FileOutputStream outputStream = new FileOutputStream("用户数据.xlsx")) {
    ExcelExportResult result = excelService.exportByTableName(
        outputStream,
        "用户数据.xlsx",
        "user_info",
        userList,
        UserInfo.class
    );
    
    if (result.isSuccess()) {
        System.out.println("导出成功：" + result.getSummary());
    }
}

// 方式2：根据模板导出
try (FileOutputStream outputStream = new FileOutputStream("用户数据.xlsx")) {
    ExcelExportResult result = excelService.exportByTemplate(
        outputStream,
        "用户数据.xlsx",
        ExcelTemplateEnum.USER_INFO,
        userList,
        UserInfo.class
    );
}
```

#### 3.2 导入Excel

```java
// 方式1：根据表名导入
try (FileInputStream inputStream = new FileInputStream("用户数据.xlsx")) {
    ExcelImportResult<UserInfo> result = excelService.importByTableName(
        inputStream,
        "用户数据.xlsx",
        "user_info",
        UserInfo.class
    );
    
    if (result.isSuccess()) {
        System.out.println("导入成功：" + result.getSummary());
        List<UserInfo> successData = result.getSuccessData();
        // 处理导入的数据
    } else {
        // 处理错误信息
        result.getErrors().forEach(error -> 
            System.err.println(error.getFullErrorDescription())
        );
    }
}

// 方式2：根据模板导入
try (FileInputStream inputStream = new FileInputStream("用户数据.xlsx")) {
    ExcelImportResult<UserInfo> result = excelService.importByTemplate(
        inputStream,
        "用户数据.xlsx",
        ExcelTemplateEnum.USER_INFO,
        UserInfo.class
    );
}
```

#### 3.3 预览Excel

```java
try (FileInputStream inputStream = new FileInputStream("用户数据.xlsx")) {
    ExcelImportResult<UserInfo> result = excelService.previewExcel(
        inputStream,
        "用户数据.xlsx",
        UserInfo.class,
        10 // 预览前10行
    );
    
    // 处理预览数据
    result.getSuccessData().forEach(user -> 
        System.out.println(user.getUsername())
    );
}
```

## 高级功能

### 1. 自定义配置

```java
// 创建自定义配置
ExcelConfig config = ExcelConfig.defaultConfig();
config.setSkipEmptyRows(true);
config.setAutoTrim(true);
config.setContinueOnError(true);
config.setMaxErrorCount(100);

// 使用自定义配置的导入请求
ExcelImportRequest request = ExcelImportRequest.byTableName(
    inputStream, fileName, tableName, targetClass
).withConfig(config);

ExcelImportResult<UserInfo> result = excelService.importExcel(request);
```

### 2. 严格模式和宽松模式

```java
// 严格模式：遇到错误立即停止
ExcelImportResult<UserInfo> result = excelService.importStrict(
    inputStream, fileName, tableName, UserInfo.class
);

// 宽松模式：跳过错误继续处理
ExcelImportResult<UserInfo> result = excelService.importLenient(
    inputStream, fileName, tableName, UserInfo.class
);
```

### 3. 异步处理

```java
// 异步导入
ExcelImportRequest request = ExcelImportRequest.byTableName(
    inputStream, fileName, tableName, UserInfo.class
);

CompletableFuture<ExcelImportResult<UserInfo>> future = 
    excelService.importExcelAsync(request);

future.thenAccept(result -> {
    if (result.isSuccess()) {
        System.out.println("异步导入完成：" + result.getSummary());
    }
});

// 异步导出
ExcelExportRequest<UserInfo> exportRequest = ExcelExportRequest.byTableName(
    outputStream, fileName, tableName, userList, UserInfo.class
);

CompletableFuture<ExcelExportResult> exportFuture = 
    excelService.exportExcelAsync(exportRequest);
```

### 4. 导出模板文件

```java
// 导出模板文件（只有表头，没有数据）
try (FileOutputStream outputStream = new FileOutputStream("用户信息模板.xlsx")) {
    ExcelExportResult result = excelService.exportTemplate(
        outputStream,
        "用户信息模板.xlsx",
        ExcelTemplateEnum.USER_INFO
    );
}
```

### 5. 文件验证和信息获取

```java
// 验证Excel文件格式
boolean isValid = excelService.validateExcelFormat(
    inputStream, fileName, tableName
);

// 获取Excel文件信息
ExcelFileInfo fileInfo = excelService.getExcelFileInfo(
    inputStream, fileName
);

System.out.println("文件类型：" + fileInfo.getFileType());
System.out.println("工作表数量：" + fileInfo.getSheetCount());
System.out.println("总行数：" + fileInfo.getTotalRows());

// 获取支持的模板列表
List<ExcelTemplateEnum> templates = excelService.getSupportedTemplates();

// 根据表名获取对应模板
ExcelTemplateEnum template = excelService.getTemplateByTableName("user_info");
```

### 6. 服务健康检查

```java
// 获取服务健康状态
ServiceHealthInfo healthInfo = excelService.getServiceHealth();

System.out.println("服务状态：" + healthInfo.getStatus());
System.out.println("运行时间：" + healthInfo.getUptime() + "ms");
System.out.println("活跃导入任务：" + healthInfo.getActiveImportTasks());
System.out.println("活跃导出任务：" + healthInfo.getActiveExportTasks());
System.out.println("内存使用：" + healthInfo.getMetrics().get("memoryUsage"));
```

### 7. 工具服务使用

如果需要直接使用工具服务，可以注入`ExcelUtilService`：

```java
@Autowired
private ExcelUtilService excelUtilService;

// 检查文件是否为Excel格式
boolean isExcel = excelUtilService.isExcelFile("data.xlsx");

// 获取文件扩展名
String extension = excelUtilService.getFileExtension("data.xlsx");

// 格式化文件大小
String sizeStr = excelUtilService.formatFileSize(1024000); // "1000.00 KB"
```

## REST API使用

### 1. 导入接口

```bash
# 根据表名导入
POST /api/excel/import/table/{tableName}
Content-Type: multipart/form-data

# 根据模板导入
POST /api/excel/import/template/{template}
Content-Type: multipart/form-data

# 预览Excel
POST /api/excel/preview
Content-Type: multipart/form-data
```

### 2. 导出接口

```bash
# 根据表名导出
POST /api/excel/export/table/{tableName}
Content-Type: application/json

# 根据模板导出
POST /api/excel/export/template/{template}
Content-Type: application/json

# 下载模板
GET /api/excel/template/{template}
```

### 3. 工具接口

```bash
# 验证文件格式
POST /api/excel/validate

# 获取文件信息
POST /api/excel/info

# 获取支持的模板
GET /api/excel/templates

# 健康检查
GET /api/excel/health
```

## 错误处理

### 1. 错误类型

- **REQUIRED_ERROR**：必填字段为空
- **FORMAT_ERROR**：数据格式错误
- **VALIDATION_ERROR**：数据校验失败
- **TYPE_ERROR**：数据类型错误
- **BUSINESS_ERROR**：业务逻辑错误
- **OTHER_ERROR**：其他错误

### 2. 错误信息结构

```java
ExcelErrorInfo error = ...;
System.out.println("错误行号：" + error.getRowIndex());
System.out.println("错误列号：" + error.getColumnIndex());
System.out.println("列名：" + error.getColumnName());
System.out.println("错误值：" + error.getErrorValue());
System.out.println("错误信息：" + error.getErrorMessage());
System.out.println("完整描述：" + error.getFullErrorDescription());
```

### 3. 批量错误处理

```java
ExcelImportResult<UserInfo> result = excelService.importByTableName(...);

if (result.hasErrors()) {
    System.out.println("导入过程中发现 " + result.getErrorCount() + " 个错误：");
    
    result.getErrors().forEach(error -> {
        System.err.println(error.getFullErrorDescription());
    });
    
    // 获取失败的行数据
    result.getFailureData().forEach(rowData -> {
        System.err.println("第" + rowData.getRowIndex() + "行失败：" + 
                          rowData.getErrorMessages());
    });
}
```

## 性能优化建议

### 1. 大数据量处理

- 使用分页导出模式处理大数据量
- 启用异步处理避免阻塞
- 适当调整批处理大小

### 2. 内存优化

- 及时关闭输入输出流
- 使用流式处理模式
- 避免一次性加载过多数据到内存

### 3. 错误处理优化

- 设置合理的最大错误数量限制
- 在宽松模式下跳过错误继续处理
- 使用预览模式验证数据格式

## 常见问题

### Q1: 如何自定义数据校验规则？

A: 可以通过实现`CustomValidator`接口来自定义校验规则：

```java
CustomValidator emailValidator = value -> {
    if (value instanceof String) {
        String email = (String) value;
        if (!email.contains("@")) {
            return ValidationResult.failure("邮箱格式不正确");
        }
    }
    return ValidationResult.success();
};
```

### Q2: 如何处理日期格式？

A: 系统支持多种日期格式自动识别，也可以在数据模型中使用`@DateTimeFormat`注解指定格式。

### Q3: 如何支持多工作表？

A: 使用`exportMultiSheet`方法可以导出多工作表Excel文件。

### Q4: 如何监控导入导出任务的进度？

A: 可以通过健康检查接口获取当前活跃任务数量，或者使用异步方式处理并监听完成事件。

## 更多示例

详细的使用示例请参考：
- `ExcelExample.java` - 完整的功能演示
- `ExcelServiceTest.java` - 单元测试示例
- `ExcelController.java` - REST API使用示例

## 总结

本Excel通用导入导出功能提供了完整的解决方案，支持多种使用方式和配置选项，能够满足大部分业务场景的需求。通过合理的配置和使用，可以实现高效、稳定的Excel数据处理功能。
