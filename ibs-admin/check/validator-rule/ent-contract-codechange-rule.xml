<?xml version="1.0" encoding="UTF-8"?>
<validators>
	<validator id="30079182.0.0"
		classType="org.pbccrc.archive.collect.messagetools.entcontract.message.ContractCodeChangeRecord">

		<field name="InfRecType" fbTag="InfRecType" classType="java.lang.String">
			<rule id="InfRecType_NotNull" type="NotNull" errCode="ABD000" />
			<rule id="InfRecType_NotEmpty" type="NotEmpty" errCode="ABE000"
				depExp="InfRecType_NotNull" />
			<rule id="InfRecType_EqCtxt" type="Equals" errCode="ABR000"
				fbTag="0000" fbValue="" depExp="InfRecType_NotEmpty">
				<property name="data" value="${$context.REC_TYPE}" />
			</rule>
		</field>
		<field name="OdBnesCode" fbTag="OdBnesCode" classType="java.lang.String">
			<rule id="OdBnesCode_NotNull" type="NotNull" errCode="ABD000" />
			<rule id="OdBnesCode_NotEmpty" type="NotEmpty" errCode="ABE000"
				depExp="OdBnesCode_NotNull" />
			<rule id="OdBnesCode_DataType" type="DataType" errCode="ABE001"
				depExp="OdBnesCode_NotEmpty">
				<property name="type" value="AN..60" />
			</rule>
			<!-- 校验前14位与数据机构提供区段码相等 错误码ABE002 -->
			<rule id="OdBnesCode_finanOrg" type="FinanOrgan" errCode="ABE002"
				depExp="OdBnesCode_DataType" />
		</field>
		<field name="NwBnesCode" fbTag="NwBnesCode" classType="java.lang.String">
			<rule id="NwBnesCode_NotNull" type="NotNull" errCode="ABD000" />
			<rule id="NwBnesCode_NotEmpty" type="NotEmpty" errCode="ABE000"
				depExp="NwBnesCode_NotNull" />
			<rule id="NwBnesCode_DataType" type="DataType" errCode="ABE001"
				depExp="NwBnesCode_NotEmpty">
				<property name="type" value="AN..60" />
			</rule>
			<!-- 校验前14位与数据机构提供区段码相等 错误码ABE002 -->
			<rule id="NwBnesCode_FinanOrgan" type="FinanOrgan" errCode="ABE002" depExp="NwBnesCode_DataType"/>
			<rule id="NwBnesCode_OdBnesCode" type="NotEquals" errCode="CIR100"
				depExp="NwBnesCode_DataType &amp; OdBnesCode_DataType">
				<property name="data" value="${OdBnesCode}" />
			</rule>
		</field>

	</validator>
</validators>
