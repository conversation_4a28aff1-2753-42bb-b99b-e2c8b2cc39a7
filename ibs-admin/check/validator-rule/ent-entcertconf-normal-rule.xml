<?xml version="1.0" encoding="UTF-8"?>
<validators>
	<validator id="30079102.0.0" classType="org.pbccrc.archive.collect.messagetools.entrelation.message.EntCertConfNormalRecord" > 
			<field  name="InfRecType" fbTag="InfRecType"  classType="java.lang.String" >
                <rule  id="InfRecType_NotNull" type="NotNull" errCode="ABD000" />
                <rule  id="InfRecType_NotEmpty" type="NotEmpty" errCode="ABE000" />

                <rule id="InfRecType_Equal" type="Equals" errCode="ABR000" fbTag="0000" fbValue="" >
                    <property name="data" value="${$context.REC_TYPE}" />
                </rule>
                <rule id="InfRecType_Dict" type="DictManage" errCode="ABE001" depExp="InfRecType_Equal">
                    <property  name="dictKey" value="EntInfRecType" />
                </rule> 
			</field> 
            <field  name="EntName" fbTag="EntName" classType="java.lang.String" >
                <rule  id="EntName_NotNull" type="NotNull" errCode="ABD000" />
                <rule  id="EntName_NotEmpty" type="NotEmpty" errCode="ABE000" />
                <rule  id="EntName_DataType" type="DataType" errCode="ABE001" >
                    <property  name="type" value="ANC..80" />
                </rule>
            </field> 
			<field  name="EntCertType" fbTag="EntCertType" classType="java.lang.String" >
                <rule  id="EntCertType_NotNull" type="NotNull" errCode="ABD000" />
                <rule  id="EntCertType_NotEmpty" type="NotEmpty" errCode="ABE000" />
                <rule  id="EntCertType_Dict" type="Dict" errCode="ABE001" depExp="EntCertType_NotEmpty" scope="local">
                    <property name="dictKey" value="EntCertType" />
                </rule> 
			</field> 
			<field  name="EntCertNum" fbTag="EntCertNum" classType="java.lang.String" >
                <rule  id="EntCertNum_NotNull" type="NotNull" errCode="ABD000" />
                <rule  id="EntCertNum_NotEmpty" type="NotEmpty" errCode="ABE000" />
                <rule  id="EntCertNum_DataType" type="DataType" errCode="ABE001" >
                    <property  name="type" value="ANC..40" />
                </rule>
				<!-- 根据证件类型，校验不同的证件号xxxx -->
				<rule id="EntCertNum_Vali" type="LegCert" errCode="ABE004" > 
				   <property name="certType" value="${EntCertType}"></property>
				</rule>
			</field> 
			<field  name="OthEntCertType" fbTag="OthEntCertType" classType="java.lang.String" >
			    <rule  id="OthEntCertType_NotNull" type="NotNull" errCode="ABD000" />
                <rule  id="OthEntCertType_NotEmpty" type="NotEmpty" errCode="ABE000" />
                <rule  id="OthEntCertType_Dict" type="Dict" errCode="ABE001" depExp="OthEntCertType_NotEmpty" scope="local">
                    <property name="dictKey" value="EntCertType" />
                </rule> 
			</field> 
			<field  name="OthEntCertNum" fbTag="OthEntCertNum" classType="java.lang.String" >
			    <rule  id="OthEntCertNum_NotNull" type="NotNull" errCode="ABD000" />
                <rule  id="OthEntCertNum_NotEmpty" type="NotEmpty" errCode="ABE000" />
                <rule  id="OthEntCertNum_DataType" type="DataType" errCode="ABE001" >
                    <property  name="type" value="ANC..40" />
                </rule>
				<!-- 根据证件类型，校验不同的证件号xxxx -->
				<rule id="OthEntCertNum_Vali" type="LegCert" errCode="ABE004" > 
				   <property name="certType" value="${OthEntCertType}"></property>
				</rule>
				<rule id="I3400B01_NotRepeat" fbTag="0000" type="Method" fbValue="" errCode="CGE000">
					<package>
				        org.pbccrc.archive.collect.messagetools.entrelation.message.EntCertConfNormalRecord
					</package>
					<method>
					{
						String entCertNum = record.getEntCertNum();
						String entCertType = record.getEntCertType();
						if(ValueUtil.hasNull(entCertNum,entCertType)){
							return true;
						}
						String othEntCertNum = record.getOthEntCertNum();
						String othEntCertType = record.getOthEntCertType();
						if(ValueUtil.hasNull(othEntCertNum,othEntCertType)){
							return true;
						}
						if (entCertNum.equals(othEntCertNum) &amp;&amp; entCertType.equals(othEntCertType)){
							return false;
						}
						return true;
					}
					</method>
				</rule>
			</field> 
			<field  name="CertAssFlg" fbTag="CertAssFlg" classType="java.lang.String" >
				<rule  id="CertAssFlg_NotNull" type="NotNull" errCode="ABD000" />
				<rule  id="CertAssFlg_NotEmpty" type="NotEmpty" errCode="ABE000"  />
				<rule  id="CertAssFlg_DataType" type="Dict" errCode="ABE001" depExp="CertAssFlg_NotEmpty" >
					<property name="dictKey" value="EntCertAssFlgId" />
				</rule>
			</field> 
			<field  name="RptDate" fbTag="RptDate" classType="java.lang.String" >
				<rule  id="RptDate_NotNull" type="NotNull" errCode="ABD000" />
                <rule  id="RptDate_NotEmpty" type="NotEmpty" errCode="ABE000" />
                <rule  id="RptDate_DataType" type="DataType" errCode="ABE001" depExp="RptDate_NotEmpty" >
                    <property  name="type" value="Date" />
                </rule>
                <rule  id="RptDate_Range" type="DateRange" errCode="ABE008" depExp="RptDate_DataType" />
                <rule id="RptDate_LTfileCreate" type="DateCompare" errCode="ABR001" depExp="RptDate_Range">
                        <property name="type" value="date" />
                        <property name="endDate" value="${$context.FILE_CREATE_DATE}" />
                </rule>
			</field> 
	</validator>
</validators>
