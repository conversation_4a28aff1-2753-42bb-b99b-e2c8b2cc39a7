<?xml version="1.0" encoding="UTF-8"?>
<validators>
	<validator id="30079062.0.0" classType="org.pbccrc.archive.collect.messagetools.collateral.msg.CollateralCodeChangeRecord" >
		<field  name="IdCagsType" fbTag="InfRecType" classType="java.lang.String" >
			<rule  id="IdCagsType_NotNull" type="NotNull" errCode="ABD000"/>
			<rule  id="IdCagsType_NotEmpty" type="NotEmpty" errCode="ABE000" depExp="IdCagsType_NotNull" />
			<rule  id="IdCagsType_Dict" type="DictManage" errCode="ABE001" depExp="IdCagsType_NotEmpty">
				<property name="dictKey" value="InfRecType" />
			</rule>
			<rule id="IdCagsType_EqCtxt" type="Equals" errCode="ABR000" fbTag="0000" fbValue="" depExp="IdCagsType_Dict" >
				<property  name="data" value="${$context.REC_TYPE}" />
			</rule>
		</field> 
		<field  name="OdBnesCode" fbTag="OdBnesCode" classType="java.lang.String" >
			<rule  id="OdBnesCode_NotNull" type="NotNull" errCode="ABD000"/>
			<rule  id="OdBnesCode_NotEmpty" type="NotEmpty" errCode="ABE000" depExp="OdBnesCode_NotNull" />
			<rule  id="OdBnesCode_DataType" type="DataType" errCode="ABE001" depExp="OdBnesCode_NotEmpty" >
				<property  name="type" value="AN..60" />
			</rule>
			<rule id="OdBnesCode_FinanOrgan" type="FinanOrgan" errCode="ABE002" depExp="OdBnesCode_DataType"/>
		</field> 
		<field  name="NwBnesCode" fbTag="NwBnesCode" classType="java.lang.String" >
			<rule  id="NwBnesCode_NotNull" type="NotNull" errCode="ABD000"/>
			<rule  id="NwBnesCode_NotEmpty" type="NotEmpty" errCode="ABE000" depExp="NwBnesCode_NotNull" />
			<rule  id="NwBnesCode_DataType" type="DataType" errCode="ABE001" depExp="NwBnesCode_NotEmpty" >
				<property  name="type" value="AN..60" />
			</rule>
			<rule id="NwBnesCode_FinanOrgan" type="FinanOrgan" errCode="ABE002" depExp="NwBnesCode_DataType"/>
			<rule id="NwBnesCode_OdBnesCode" type="NotEquals" fbTag="0000" fbValue="" errCode="BIE300" depExp="NwBnesCode_DataType &amp; OdBnesCode_DataType">
				<property name="data" value="${OdBnesCode}" />
			</rule>
		</field> 
	</validator>
</validators>
