-- '%Y%m%d %H%M%S'
-- 404  个人存款分户账明细记录 GRCKFHZMX; 活期当日明细帐
select (replace(cdm.dm_inn_acc, " ", "") ||
        replace(cdm.tran_date, "-", "") ||
        abs(cdm.host_seqno))             jyxlh,
       cdm.open_inst                     nbjgh,
       cdm.agt_inst                      ywbljgh,
       trim(mgmt.name)                   yhjgmc,
       cdm.ditm_no                       mxkmbh,
       trim(dict.ditm_name)              mxkmmc,
       ledger.cstm_no                    khtybh,
       trim(ledger.cstm_name)            zhmc,
       prsn.paper_no[1]                  zjlb,
       trim(prsn.paper_no[2, 19])        zjhm,
       trim(cdm.dm_out_acc)              grckzh,
       trim(cdm.dm_out_acc)              wbzh,
       cdm.summ_no                       jylx,
       cdm.dr_cr_flag                    jyjdbz,
       to_char(cdm.tran_date, '%Y%m%d')  hxjyrq,
       to_char(cdm.tran_stamp, '%H%M%S') hxjysj,
       cdm.amt                           jyje,
       cdm.bal                           zhye,
       trim(cdm.peer_acc)                dfzh,
       trim(cdm.summ)                    zy,
       cdm.flag[1]                       cbmbz,
       cdm.csh_tsf_flag                  xzbz,
       cdm.tlr                           jygyh,
       'CNY'                             bz
from t_cdm_dtl cdm,
     t_cdm_ledger ledger,
     t_cstm_prsn_base prsn,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict
where cdm.tran_date >= '2022-03-01'
  and cdm.tran_date <= '2022-12-30'
  and cdm.flag[1] = '0'
  and cdm.dm_out_acc = ledger.acc
  and ledger.cstm_no = prsn.cstm_no
  and cdm.ditm_no = dict.ditm_no
  and cdm.agt_inst = mgmt.inst_no;


-- 404  个人存款分户账明细记录 GRCKFHZMX; 定期当日明细帐

select (replace(fix.fix_inn_acc, " ", "") ||
        replace(fix.tran_date, "-", "") ||
        fix.host_seqno)                 jyxlh,
       fix.op_inst                      nbjgh,
       fix.agt_inst                     ywbljgh,
       trim(mgmt.name)                  yhjgmc,
       fix.ditm_no                      mxkmbh,
       trim(dict.ditm_name)             mxkmmc,
       ledger.cstm_no                   khtybh,
       trim(ledger.acc_name)            zhmc,
       prsn.paper_no[1]                 zjlb,
       trim(prsn.paper_no[2, 19])       zjhm,
       trim(fix.fix_inn_acc)            grckzh,
       trim(fix.fix_inn_acc)            wbzh,
       fix.summ_no                      jylx,
       fix.dr_cr_flag                   jyjdbz,
       to_char(fix.tran_date, '%Y%m%d') hxjyrq,
       to_char(fix.tran_time, '%H%M%S') hxjysj,
       fix.amt                          jyje,
       fix.bal                          zhye,
       trim(fix.peer_acc)               dfzh,
       trim(fix.summ)                   zy,
       fix.csh_tsf_flag                 xzbz,
       fix.flag[1]                      cbmbz,
       fix.tlr                          jygyh,
       'CNY'                            bz
from t_fix_dtl fix,
     t_fix_ledger ledger,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict,
     t_cstm_prsn_base prsn
where fix.flag[1] = '0'
  and fix.tran_date >= '2022-03-01'
  and fix.tran_date <= '2022-03-31'
  and fix.fix_inn_acc = ledger.acc
  and fix.ditm_no = dict.ditm_no
  and prsn.cstm_no = ledger.cstm_no
  and fix.agt_inst = mgmt.inst_no;


-- 405  对公存款分户账 DGCKFHZ; 结算活期户主帐
-- 利率 t_srm_rate rate
select ledger.inst_no                           nbjgh,
       trim(mgmt.name)                          yhjgmc,
       svt.ditm_no                              mxkmbh,
       trim(dict.ditm_name)                     mxkmmc,
       ledger.cstm_no                           khtybh,
       trim(ledger.cstm_name)                   zhmc,
       trim(ledger.acc)                         dgckzh,
       0                                        ll,
       ledger.bal                               ckye,
       to_char(ledger.open_date, '%Y%m%d')      khrq,
       ledger.open_tlr                          khgyh,
       to_char(ledger.cls_date, '%Y%m%d')       xhrq,
       to_char(ledger.last_tran_date, '%Y%m%d') scdhrq,
       ledger.flag[3]                           chlb,
       ledger.flag[1]                           zhzt,
       'CNY'                                    bz
from t_sdm_ledger ledger,
     t_srm_inst_mgmt mgmt,
     t_pub_svt svt,
     t_srm_ditm_dict dict
where ledger.inst_no = mgmt.inst_no
  and ledger.prd_no = svt.prd_no
  and svt.ditm_no = dict.ditm_no
  and ledger.open_date >= '2014-01-07'
  and ledger.open_date <= '2014-12-07';


-- 405  对公存款分户账 DGCKFHZ; 定期户主帐
-- 利率 t_srm_rate rate
select ledger.inst_no                           nbjgh,
       trim(mgmt.name)                          yhjgmc,
       svt.ditm_no                              mxkmbh,
       trim(dict.ditm_name)                     mxkmmc,
       ledger.cstm_no                           khtybh,
       trim(ledger.acc_name)                    zhmc,
       trim(ledger.acc)                         dgckzh,
       ledger.bal                               ckye,
       to_char(ledger.op_date, '%Y%m%d')        khrq,
       ledger.op_tlr                            khgyh,
       to_char(ledger.cls_date, '%Y%m%d')       xhrq,
       to_char(ledger.last_tran_date, '%Y%m%d') scdhrq,
       ledger.flag[6]                           chlb,
       ledger.flag[1]                           zhzt,
       'CNY'                                    bz
from t_fix_ledger ledger,
     t_srm_inst_mgmt mgmt,
     t_pub_svt svt,
     t_srm_ditm_dict dict
where ledger.inst_no = mgmt.inst_no
  and ledger.prd_no = svt.prd_no
  and svt.ditm_no = dict.ditm_no
  and ledger.op_date >= '2014-01-07'
  and ledger.op_date <= '2014-12-07';

-- 406  对公存款分户账明细记录 DGCKFHZMX 结算活期当日明细帐

select (replace(dtl.acc, " ", "") ||
        replace(dtl.tran_date, "-", "") ||
        abs(dtl.host_seqno))             jyxlh,
       dtl.open_inst                     nbjgh,
       dtl.agt_inst                      ywbljgh,
       trim(mgmt.name)                   yhjgmc,
       dtl.ditm_no                       mxkmbh,
       trim(dict.ditm_name)              mxkmmc,
       ledger.cstm_no                    khtybh,
       trim(ledger.cstm_name)            zhmc,
       trim(dtl.acc)                     dgckzh,
       trim(dtl.acc)                     wbzh,
       dtl.dr_cr_flag                    jyjdbz,
       to_char(dtl.tran_date, '%Y%m%d')  hxjyrq,
       to_char(dtl.tran_stamp, '%H%M%S') hxjysj,
       dtl.amt                           jyje,
       dtl.bal                           zhye,
       trim(dtl.peer_acc)                dfzh,
       trim(dtl.peer_inst)               dfxh,
       trim(dtl.summ)                    zy,
       dtl.flag[1]                       cbmbz,
       dtl.csh_tsf_flag                  xzbz,
       dtl.tlr                           jygyh,
       'CNY'                             bz
from t_sdm_dtl dtl,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict,
     t_sdm_ledger ledger
where dtl.acc = ledger.acc
  and dtl.ditm_no = dict.ditm_no
  and ledger.inst_no = mgmt.inst_no
  and dtl.tran_date >= '2015-01-01'
  and dtl.tran_date <= '2016-12-31';


-- 406  对公存款分户账明细记录 DGCKFHZMX 定期当日明细帐
select (replace(dtl.fix_inn_acc, " ", "") ||
        replace(dtl.tran_date, "-", "") ||
        abs(dtl.host_seqno))            jyxlh,
       dtl.op_inst                      nbjgh,
       dtl.agt_inst                     ywbljgh,
       trim(mgmt.name)                  yhjgmc,
       dtl.ditm_no                      mxkmbh,
       trim(dict.ditm_name)             mxkmmc,
       ledger.cstm_no                   khtybh,
       trim(ledger.acc_name)            zhmc,
       trim(dtl.fix_inn_acc)            dgckzh,
       trim(dtl.fix_inn_acc)            wbzh,
       dtl.dr_cr_flag                   jyjdbz,
       to_char(dtl.tran_date, '%Y%m%d') hxjyrq,
       to_char(dtl.tran_time, '%H%M%S') hxjysj,
       dtl.amt                          jyje,
       dtl.bal                          zhye,
       trim(dtl.peer_acc)               dfzh,
       trim(dtl.peer_inst)              dfxh,
       trim(sdm.summ)                   zy,
       dtl.flag[1]                      cbmbz,
       dtl.csh_tsf_flag                 xzbz,
       dtl.tlr                          jygyh,
       'CNY'                            bz
from t_fix_dtl dtl,
     t_sdm_dtl sdm,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict,
     t_fix_ledger ledger
where dtl.fix_out_acc = ledger.acc
  and sdm.acc = ledger.acc
  and sdm.ditm_no = dict.ditm_no
  and dtl.ditm_no = dict.ditm_no
  and ledger.inst_no = mgmt.inst_no
  and dtl.tran_date >= '2014-01-07'
  and dtl.tran_date <= '2014-12-07';


-- 407  内部分户账 NBFHZ 分户帐
select ledger.inst_no                     nbjgh,
       trim(mgmt.name)                    yhjgmc,
       ledger.ditm_no                     mxkmbh,
       trim(dict.ditm_name)               mxkmmc,
       trim(ledger.acc_name)              zhmc,
       trim(ledger.acc)                   nbfhzzh,
       ledger.bal_dir                     jdbz,
       ledger.crt_bal                     jfye,
       ledger.crt_bal                     dfye,
       ledger.int_ctrlwd[1]               jxbz,
       ledger.int_ctrlwd[4]               jxfs,
       0                                  ll,
       to_char(ledger.op_date, '%Y%m%d')  khrq,
       to_char(ledger.cls_date, '%Y%m%d') xhrq,
       ledger.acc_stat_fld                zhzt,
       'CNY'                              bz
from t_inn_ledger ledger,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict
where ledger.ditm_no = dict.ditm_no
  and ledger.inst_no = mgmt.inst_no
  AND ledger.tran_date >= '2014-01-07'
  and ledger.tran_date <= '2014-12-07';


-- 408  内部分户账明细记录 NBFHZMX 内部帐当日明细帐
-- dtl.summ_no或dtl.tran_no判断 jylx,
SELECT (replace(dtl.acc, " ", "") ||
        replace(dtl.date, "-", "") ||
        abs(dtl.host_seqno))       jyxlh,
       dtl.inst_no                 nbjgh,
       trim(mgmt.name)             yhjgmc,
       dtl.ditm_no                 mxkmbh,
       trim(dict.ditm_name)        mxkmmc,
       trim(ledger.acc_name)       zhmc,
       trim(dtl.acc)               nbfhzzh,
       to_char(dtl.date, '%Y%m%d') hxjyrq,
       to_char(dtl.time, '%H%M%S') hxjysj,
       dtl.dr_cr_flag              jyjdbz,
       dtl.amt                     jyje,
       dtl.crt_bal                 jfye,
       dtl.crt_bal                 dfye,
       trim(dtl.peer_acc)          dfzh,
       trim(dtl.summ)              zy,
       dtl.proc_flag[2]            cbmbz,
       dtl.csh_tsf_flag            xzbz,
       dtl.tlr                     jygyh,
       to_char(dtl.date, '%Y%m%d') jzrq,
       'CNY'                       bz
FROM t_inn_dtl dtl,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict,
     t_inn_ledger ledger
WHERE dtl.inst_no = mgmt.inst_no
  AND dtl.ditm_no = dict.ditm_no
  AND dtl.acc = ledger.acc
  AND dtl.date >= '2022-01-01'
  AND dtl.date <= '2022-12-01';


-- 409 个人信贷分户账 GRXDFHZ  贷款户主帐

select ledger.inst_no                        nbjgh,
       trim(mgmt.name)                       yhjgmc,
       ledger.ditm_no                        mxkmbh,
       trim(dict.ditm_name)                  mxkmmc,
       ledger.cstm_no                        khtybh,
       trim(ledger.cstm_name)                zhmc,
       trim(ledger.loan_acc)                 dkfhzh,
       trim(ledger.loan_no)                  xdjjh,
       ledger.ledger_guideline               dkje,
       ledger.bal                            dkye,
       to_char(ledger.bgnint_date, '%Y%m%d') ffrq,
       to_char(ledger.due_date, '%Y%m%d')    dqrq,
       to_char(ledger.bgn_date, '%Y%m%d')    khrq,
       to_char(ledger.end_date, '%Y%m%d')    xhrq,
       ledger.flag[2]                        dkzt,
       ledger.flag[3]                        zhzt,
       'CNY'                                 bz,
       (select round(
                       case
                           when a.rate_type = '1'
                               then a.rate_val / 100 * 360
                           when a.rate_type = '2'
                               then a.rate_val / 10 * 12
                           else a.rate_val
                           end, 4) SJLL
        from t_srm_rate a,
             (select rate.rate_no, max(rate.bgn_date) rate_date
              from t_srm_rate rate
              where iou.rate_no = rate.rate_no
                and rate.bgn_date <= iou.bgnint_date
              group by rate.rate_no) b
        where a.rate_no = b.rate_no
          and a.bgn_date = b.rate_date)      dkll
from t_loan_ledger ledger,
     t_loan_iou iou,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict
where ledger.inst_no = mgmt.inst_no
  and ledger.ditm_no = dict.ditm_no
  and iou.loan_no = ledger.loan_no
  and ledger.last_tran_date >= '2018-01-01'
  and ledger.last_tran_date <= '2019-12-31';


-- 410  个人信贷分户账明细记录 GRXDFHZMX 贷款当日明细帐

select (replace(dtl.loan_acc, " ", "") ||
        replace(dtl.tran_date, "-", "") ||
        abs(dtl.host_seqno))            jyxlh,
       dtl.op_inst                      nbjgh,
       dtl.op_inst                      ywbljgh,
       mgmt.inst_no                     yhjgmc,
       dtl.ditm_no                      mxkmbh,
       trim(dict.ditm_name)             mxkmmc,
       iou.cstm_no                      khtybh,
       trim(prsn.name)                  zhmc,
       prsn.paper_no[1]                 zjlb,
       trim(prsn.paper_no[2, 19])       zjhm,
       trim(dtl.loan_acc)               dkfhzh,
       dtl.loan_no                      xdjjh,
       to_char(dtl.tran_date, '%Y%m%d') hxjyrq,
       to_char(dtl.tran_time, '%H%M%S') hxjysj,
       dtl.dr_cr_flag                   jyjdbz,
       dtl.amt                          jyje,
       trim(dtl.peer_acc)               dfzh,
       trim(dtl.peer_inst)              dfxh,
       trim(dtl.summ)                   zy,
       dtl.flag[1]                      cbmbz,
       dtl.tlr                          jygyh,
       dtl.csh_tsf_flag                 xzbz,
       'CNY'                            bz
from t_loan_dtl dtl,
     t_loan_iou iou,
     t_cstm_prsn_base prsn,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict
where dtl.loan_no = iou.loan_no
  and dtl.op_inst = mgmt.inst_no
  and prsn.cstm_no = iou.cstm_no
  and dtl.ditm_no = dict.ditm_no
  and dtl.tran_date >= '2018-01-01'
  and dtl.tran_date <= '2019-12-31';


-- 410  个人信贷分户账明细记录 GRXDFHZMX  还息明细登记簿

select (replace(intdtl.loan_acc, " ", "") ||
        replace(intdtl.acc_date, "-", "") ||
        abs(intdtl.host_seqno))           jyxlh,
       intdtl.inst_no                     nbjgh,
       intdtl.inst_no                     ywbljgh,
       mgmt.inst_no                       yhjgmc,
       intdtl.ditm_no                     mxkmbh,
       trim(dict.ditm_name)               mxkmmc,
       iou.cstm_no                        khtybh,
       trim(prsn.name)                    zhmc,
       prsn.paper_no[1]                   zjlb,
       trim(prsn.paper_no[2, 19])         zjhm,
       trim(intdtl.loan_acc)              dkfhzh,
       intdtl.loan_no                     xdjjh,
       intdtl.ret_date                    hxjyrq,
       dtl.tran_time                      hxjysj,
       to_char(intdtl.ret_date, '%Y%m%d') hxjyrq,
       to_char(dtl.tran_time, '%H%M%S')   hxjysj,
       dtl.dr_cr_flag                     jyjdbz,
       intdtl.ret_int                     jyje,
       trim(intdtl.recvint_acc)           dfzh,
       trim(dtl.peer_inst)                dfxh,
       trim(dtl.summ)                     zy,
       dtl.flag[1]                        cbmbz,
       dtl.tlr                            jygyh,
       intdtl.flag[5]                     xzbz,
       'CNY'                              bz
from t_loan_iou iou,
     t_loan_dtl dtl,
     t_loan_retint_dtl intdtl,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict,
     t_cstm_prsn_base prsn
where dtl.loan_no = iou.loan_no
  and intdtl.loan_no = iou.loan_no
  and intdtl.inst_no = mgmt.inst_no
  and dtl.op_inst = mgmt.inst_no
  and prsn.cstm_no = iou.cstm_no
  and dtl.ditm_no = dict.ditm_no
  and dtl.tran_date >= '2018-01-01'
  and dtl.tran_date <= '2018-12-31';


-- 411	对公信贷分户账	DGXDFHZ
select ledger.inst_no                        nbjgh,
       trim(mgmt.name)                       yhjgmc,
       ledger.ditm_no                        mxkmbh,
       trim(dict.ditm_name)                  mxkmmc,
       ledger.cstm_no                        khtybh,
       trim(ledger.cstm_name)                zhmc,
       trim(ledger.loan_acc)                 dkfhzh,
       ledger.loan_no                        xdjjh,
       ledger.ledger_guideline               dkje,
       ledger.bal                            dkye,
       to_char(ledger.bgnint_date, '%Y%m%d') ffrq,
       to_char(ledger.due_date, '%Y%m%d')    dqrq,
       to_char(ledger.bgn_date, '%Y%m%d')    khrq,
       to_char(ledger.end_date, '%Y%m%d')    xhrq,
       ledger.flag[2]                        dkzt,
       ledger.flag[3]                        zhzt,
       concat('', #{endDate})                cjrq,
       'CNY'                                 bz,
       '***********'                         xdhth,
       (select round(
                       case
                           when a.rate_type = '1'
                               then a.rate_val / 100 * 360
                           when a.rate_type = '2'
                               then a.rate_val / 10 * 12
                           else a.rate_val
                           end, 4) SJLL
        from t_srm_rate a,
             (select rate.rate_no, max(rate.bgn_date) rate_date
              from t_srm_rate rate
              where iou.rate_no = rate.rate_no
                and rate.bgn_date  <=  iou.bgnint_date
              group by rate.rate_no) b
        where a.rate_no = b.rate_no
          and a.bgn_date = b.rate_date)      sjll
from t_loan_ledger ledger,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict,
     t_loan_iou iou
where ledger.last_tran_date >= '2018-01-01'
  and ledger.last_tran_date <= '2018-12-31'
  and ledger.inst_no = mgmt.inst_no
  and ledger.ditm_no = dict.ditm_no
  and iou.loan_no = ledger.loan_no;


-- 412  对公信贷分户账明细记录	DGXDFHZMX 贷款当日明细帐
select (replace(dtl.loan_acc, " ", "") ||
        replace(dtl.tran_date, "-", "") ||
        dtl.host_seqno)                 jyxlh,
       dtl.op_inst                      nbjgh,
       dtl.op_inst                      ywbljgh,
       mgmt.inst_no                     yhjgmc,
       dtl.ditm_no                      mxkmbh,
       trim(dict.ditm_name)             mxkmmc,
       iou.cstm_no                      khtybh,
       trim(prsn.name)                  zhmc,
       trim(dtl.loan_acc)               dkfhzh,
       dtl.loan_no                      xdjjh,
       to_char(dtl.tran_date, '%Y%m%d') hxjyrq,
       to_char(dtl.tran_time, '%H%M%S') hxjysj,
       dtl.dr_cr_flag                   jyjdbz,
       dtl.amt                          jyje,
       trim(dtl.peer_acc)               dfzh,
       trim(dtl.peer_inst)              dfxh,
       trim(dtl.summ)                   zy,
       dtl.flag[1]                      cbmbz,
       dtl.tlr                          jygyh,
       dtl.csh_tsf_flag                 xzbz,
       'CNY'                            bz
from t_loan_dtl dtl,
     t_loan_iou iou,
     t_cstm_prsn_base prsn,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict
where dtl.tran_date >= '2018-01-01'
  and dtl.tran_date <= '2018-12-31'
  and dtl.loan_no = iou.loan_no
  and dtl.op_inst = mgmt.inst_no
  and prsn.cstm_no = iou.cstm_no
  and dtl.ditm_no = dict.ditm_no;


-- 412  对公信贷分户账明细记录	DGXDFHZMX 还息明细登记簿
select (replace(intdtl.loan_acc, " ", "") ||
        replace(intdtl.acc_date, "-", "") ||
        intdtl.host_seqno)                jyxlh,
       intdtl.inst_no                     nbjgh,
       intdtl.inst_no                     ywbljgh,
       mgmt.inst_no                       yhjgmc,
       intdtl.ditm_no                     mxkmbh,
       trim(dict.ditm_name)               mxkmmc,
       iou.cstm_no                        khtybh,
       trim(prsn.name)                    zhmc,
       trim(intdtl.loan_acc)              dkfhzh,
       intdtl.loan_no                     xdjjh,
       to_char(intdtl.ret_date, '%Y%m%d') hxjyrq,
       to_char(dtl.tran_time, '%H%M%S')   hxjysj,
       dtl.dr_cr_flag                     jyjdbz,
       intdtl.ret_int                     jyje,
       trim(intdtl.recvint_acc)           dfzh,
       trim(dtl.peer_inst)                dfxh,
       trim(dtl.summ)                     zy,
       dtl.flag[1]                        cbmbz,
       dtl.tlr                            jygyh,
       intdtl.flag[5]                     xzbz,
       'CNY'                              bz
from t_loan_iou iou,
     t_loan_dtl dtl,
     t_loan_retint_dtl intdtl,
     t_srm_inst_mgmt mgmt,
     t_srm_ditm_dict dict,
     t_cstm_prsn_base prsn
where intdtl.ret_date >= '2018-01-01'
  and intdtl.ret_date <= '2018-12-31'
  and dtl.loan_no = iou.loan_no
  and intdtl.loan_no = iou.loan_no
  and intdtl.inst_no = mgmt.inst_no
  and dtl.op_inst = mgmt.inst_no
  and prsn.cstm_no = iou.cstm_no
  and dtl.ditm_no = dict.ditm_no;
