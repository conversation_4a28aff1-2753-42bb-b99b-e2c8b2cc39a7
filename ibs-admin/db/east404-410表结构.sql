/*
T_EAST_GRCKFHZMX	个人存款分户账明细记录
T_EAST_DGCKFHZ	    对公存款分户账
T_EAST_DGCKFHZMX	对公存款分户账明细记录
T_EAST_NBFHZ	    内部分户账
T_EAST_NBFHZMX	    内部分户账明细记录
T_EAST_GRXDFHZ	    个人信贷分户账
T_EAST_GRXDFHZMX	个人信贷分户账明细记录
*/

-- 个人存款分户账明细记录
CREATE TABLE T_EAST_GRCKFHZMX
(
    ID          NUMBER(20),
    JYXLH       VARCHAR2(100),
    JRXKZH      VARCHAR2(30),
    NBJGH       VARCHAR2(30),
    YWBLJGH     VARCHAR2(30),
    YHJGMC      VARCHAR2(450),
    MXKMBH      VARCHAR2(60),
    MXKMMC      VARCHAR2(300),
    KHTYBH      VARCHAR2(60),
    ZHMC        VARCHAR2(450),
    ZJLB        VARCHAR2(60),
    ZJHM        VARCHAR2(70),
    GRCKZH      VARCHAR2(60),
    <PERSON><PERSON><PERSON><PERSON>        VARCHAR2(60),
    JYLX        VARCHAR2(60),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>      VARCHAR2(3),
    HXJYRQ      VARCHAR2(8),
    HXJYSJ      VARCHAR2(6),
    BZ          VARCHAR2(3),
    JYJE        NUMBER(20, 2),
    ZHYE        NUMBER(20, 2),
    DFZH        VARCHAR2(60),
    DFHM        VARCHAR2(450),
    DFXH        VARCHAR2(30),
    DFXM        VARCHAR2(450),
    ZY          VARCHAR2(600),
    FY          VARCHAR2(600),
    CBMBZ       VARCHAR2(9),
    XZBZ        VARCHAR2(3),
    JYQD        VARCHAR2(60),
    IPDZ        VARCHAR2(40),
    MACDZ       VARCHAR2(60),
    DBRXM       VARCHAR2(150),
    DBRZJLB     VARCHAR2(60),
    DBRZJHM     VARCHAR2(70),
    JYGYH       VARCHAR2(30),
    SQGYH       VARCHAR2(30),
    BBZ         VARCHAR2(600),
    CJRQ        VARCHAR2(8),
    CREATOR     NUMBER(20),
    CREATE_DATE DATE,
    UPDATER     NUMBER(20),
    UPDATE_DATE DATE,
    PRIMARY KEY (ID)
);

create index "GRCKFHZMX_JYXLH_index" on T_EAST_GRCKFHZMX (JYXLH, ZJHM, GRCKZH, HXJYRQ, HXJYSJ, CJRQ);

COMMENT ON TABLE T_EAST_GRCKFHZMX IS '个人存款分户账明细记录';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.ID IS 'ID';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.JYXLH IS '交易序列号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.JRXKZH IS '金融许可证号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.NBJGH IS '内部机构号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.YWBLJGH IS '业务办理机构号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.YHJGMC IS '银行机构名称';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.MXKMBH IS '明细科目编号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.MXKMMC IS '明细科目名称';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.KHTYBH IS '客户统一编号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.ZHMC IS '账户名称';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.ZJLB IS '证件类别';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.ZJHM IS '证件号码';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.GRCKZH IS '个人存款账号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.WBZH IS '外部账号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.JYLX IS '交易类型';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.JYJDBZ IS '交易借贷标志';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.HXJYRQ IS '核心交易日期';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.HXJYSJ IS '核心交易时间';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.BZ IS '币种';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.JYJE IS '交易金额';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.ZHYE IS '账户余额';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.DFZH IS '对方账号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.DFHM IS '对方户名';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.DFXH IS '对方行号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.DFXM IS '对方行名';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.ZY IS '摘要';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.FY IS '附言';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.CBMBZ IS '冲补抹标志';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.XZBZ IS '现转标志';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.JYQD IS '交易渠道';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.IPDZ IS 'IP地址';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.MACDZ IS 'MAC地址';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.DBRXM IS '代办人姓名';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.DBRZJLB IS '代办人证件类别';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.DBRZJHM IS '代办人证件号码';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.JYGYH IS '交易柜员号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.SQGYH IS '授权柜员号';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.BBZ IS '备注';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.CJRQ IS '采集日期';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.CREATOR IS '创建人';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.UPDATER IS '更新人';
COMMENT ON COLUMN T_EAST_GRCKFHZMX.UPDATE_DATE IS '更新时间';


-- 对公存款分户账
CREATE TABLE T_EAST_DGCKFHZ
(
    ID          NUMBER(20),
    JRXKZH      VARCHAR2(30),
    NBJGH       VARCHAR2(30),
    YHJGMC      VARCHAR2(450),
    MXKMBH      VARCHAR2(60),
    MXKMMC      VARCHAR2(300),
    KHTYBH      VARCHAR2(60),
    ZHMC        VARCHAR2(450),
    DGCKZH      VARCHAR2(60),
    DGCKZHLX    VARCHAR2(60),
    BZJZHBZ     VARCHAR2(3),
    LL          NUMBER(20, 4),
    BZ          VARCHAR2(3),
    CKYE        NUMBER(20, 2),
    KHRQ        VARCHAR2(8),
    KHGYH       VARCHAR2(30),
    XHRQ        VARCHAR2(8),
    SCDHRQ      VARCHAR2(8),
    CHLB        VARCHAR2(12),
    ZHZT        VARCHAR2(30),
    BBZ         VARCHAR2(600),
    CJRQ        VARCHAR2(8),
    CREATOR     NUMBER(20),
    CREATE_DATE DATE,
    UPDATER     NUMBER(20),
    UPDATE_DATE DATE,
    PRIMARY KEY (ID)
);
create index "DGCKFHZ_DGCKZH_index"
    on T_EAST_DGCKFHZ (DGCKZH, BZ, CHLB, CJRQ);

COMMENT ON TABLE T_EAST_DGCKFHZ IS '对公存款分户账';
COMMENT ON COLUMN T_EAST_DGCKFHZ.ID IS 'ID';
COMMENT ON COLUMN T_EAST_DGCKFHZ.JRXKZH IS '金融许可证号';
COMMENT ON COLUMN T_EAST_DGCKFHZ.NBJGH IS '内部机构号';
COMMENT ON COLUMN T_EAST_DGCKFHZ.YHJGMC IS '银行机构名称';
COMMENT ON COLUMN T_EAST_DGCKFHZ.MXKMBH IS '明细科目编号';
COMMENT ON COLUMN T_EAST_DGCKFHZ.MXKMMC IS '明细科目名称';
COMMENT ON COLUMN T_EAST_DGCKFHZ.KHTYBH IS '客户统一编号';
COMMENT ON COLUMN T_EAST_DGCKFHZ.ZHMC IS '账户名称';
COMMENT ON COLUMN T_EAST_DGCKFHZ.DGCKZH IS '对公存款账号';
COMMENT ON COLUMN T_EAST_DGCKFHZ.DGCKZHLX IS '对公存款账户类型';
COMMENT ON COLUMN T_EAST_DGCKFHZ.BZJZHBZ IS '保证金账户标志';
COMMENT ON COLUMN T_EAST_DGCKFHZ.LL IS '利率';
COMMENT ON COLUMN T_EAST_DGCKFHZ.BZ IS '币种';
COMMENT ON COLUMN T_EAST_DGCKFHZ.CKYE IS '存款余额';
COMMENT ON COLUMN T_EAST_DGCKFHZ.KHRQ IS '开户日期';
COMMENT ON COLUMN T_EAST_DGCKFHZ.KHGYH IS '开户柜员号';
COMMENT ON COLUMN T_EAST_DGCKFHZ.XHRQ IS '销户日期';
COMMENT ON COLUMN T_EAST_DGCKFHZ.SCDHRQ IS '上次动户日期';
COMMENT ON COLUMN T_EAST_DGCKFHZ.CHLB IS '钞汇类别';
COMMENT ON COLUMN T_EAST_DGCKFHZ.ZHZT IS '账户状态';
COMMENT ON COLUMN T_EAST_DGCKFHZ.BBZ IS '备注';
COMMENT ON COLUMN T_EAST_DGCKFHZ.CJRQ IS '采集日期';
COMMENT ON COLUMN T_EAST_DGCKFHZ.CREATOR IS '创建人';
COMMENT ON COLUMN T_EAST_DGCKFHZ.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN T_EAST_DGCKFHZ.UPDATER IS '更新人';
COMMENT ON COLUMN T_EAST_DGCKFHZ.UPDATE_DATE IS '更新时间';


-- 对公存款分户账明细记录
CREATE TABLE T_EAST_DGCKFHZMX
(
    ID          NUMBER(20),
    JYXLH       VARCHAR2(100),
    JRXKZH      VARCHAR2(30),
    NBJGH       VARCHAR2(30),
    YWBLJGH     VARCHAR2(30),
    YHJGMC      VARCHAR2(450),
    MXKMBH      VARCHAR2(60),
    MXKMMC      VARCHAR2(300),
    KHTYBH      VARCHAR2(60),
    ZHMC        VARCHAR2(450),
    DGCKZH      VARCHAR2(60),
    WBZH        VARCHAR2(60),
    JYLX        VARCHAR2(60),
    JYJDBZ      VARCHAR2(3),
    HXJYRQ      VARCHAR2(8),
    HXJYSJ      VARCHAR2(6),
    BZ          VARCHAR2(3),
    JYJE        NUMBER(20, 2),
    ZHYE        NUMBER(20, 2),
    DFZH        VARCHAR2(60),
    DFHM        VARCHAR2(450),
    DFXH        VARCHAR2(30),
    DFXM        VARCHAR2(450),
    ZY          VARCHAR2(600),
    FY          VARCHAR2(600),
    CBMBZ       VARCHAR2(9),
    XZBZ        VARCHAR2(3),
    JYQD        VARCHAR2(60),
    IPDZ        VARCHAR2(40),
    MACDZ       VARCHAR2(60),
    JYGYH       VARCHAR2(30),
    SQGYH       VARCHAR2(30),
    BBZ         VARCHAR2(600),
    CJRQ        VARCHAR2(8),
    CREATOR     NUMBER(20),
    CREATE_DATE DATE,
    UPDATER     NUMBER(20),
    UPDATE_DATE DATE,
    PRIMARY KEY (ID)
);
create index "DGCKFHZMX_JYXLH_index"
    on T_EAST_DGCKFHZMX (JYXLH, DGCKZH, HXJYRQ, HXJYSJ, CJRQ);
COMMENT ON TABLE T_EAST_DGCKFHZMX IS '对公存款分户账明细记录';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.ID IS 'ID';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.JYXLH IS '交易序列号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.JRXKZH IS '金融许可证号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.NBJGH IS '内部机构号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.YWBLJGH IS '业务办理机构号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.YHJGMC IS '银行机构名称';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.MXKMBH IS '明细科目编号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.MXKMMC IS '明细科目名称';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.KHTYBH IS '客户统一编号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.ZHMC IS '账户名称';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.DGCKZH IS '对公存款账号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.WBZH IS '外部账号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.JYLX IS '交易类型';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.JYJDBZ IS '交易借贷标志';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.HXJYRQ IS '核心交易日期';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.HXJYSJ IS '核心交易时间';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.BZ IS '币种';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.JYJE IS '交易金额';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.ZHYE IS '账户余额';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.DFZH IS '对方账号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.DFHM IS '对方户名';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.DFXH IS '对方行号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.DFXM IS '对方行名';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.ZY IS '摘要';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.FY IS '附言';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.CBMBZ IS '冲补抹标志';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.XZBZ IS '现转标志';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.JYQD IS '交易渠道';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.IPDZ IS 'IP地址';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.MACDZ IS 'MAC地址';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.JYGYH IS '交易柜员号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.SQGYH IS '授权柜员号';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.BBZ IS '备注';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.CJRQ IS '采集日期';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.CREATOR IS '创建人';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.UPDATER IS '更新人';
COMMENT ON COLUMN T_EAST_DGCKFHZMX.UPDATE_DATE IS '更新时间';

-- 内部分户账
CREATE TABLE T_EAST_NBFHZ
(
    ID          NUMBER(20),
    JRXKZH      VARCHAR2(30),
    NBJGH       VARCHAR2(30),
    YHJGMC      VARCHAR2(450),
    MXKMBH      VARCHAR2(60),
    MXKMMC      VARCHAR2(300),
    ZHMC        VARCHAR2(450),
    NBFHZZH     VARCHAR2(60),
    BZ          VARCHAR2(3),
    JDBZ        VARCHAR2(12),
    JFYE        NUMBER(20, 2),
    DFYE        NUMBER(20, 2),
    JXBZ        VARCHAR2(3),
    JXFS        VARCHAR2(60),
    LL          NUMBER(20, 4),
    KHRQ        VARCHAR2(8),
    XHRQ        VARCHAR2(8),
    ZHZT        VARCHAR2(30),
    BBZ         VARCHAR2(600),
    CJRQ        VARCHAR2(8),
    CREATOR     NUMBER(20),
    CREATE_DATE DATE,
    UPDATER     NUMBER(20),
    UPDATE_DATE DATE,
    PRIMARY KEY (ID)
);
create index "NBFHZ_NBFHZZH_index"
    on T_EAST_NBFHZ (NBFHZZH, BZ, CJRQ);
COMMENT ON TABLE T_EAST_NBFHZ IS '内部分户账';
COMMENT ON COLUMN T_EAST_NBFHZ.ID IS 'ID';
COMMENT ON COLUMN T_EAST_NBFHZ.JRXKZH IS '金融许可证号';
COMMENT ON COLUMN T_EAST_NBFHZ.NBJGH IS '内部机构号';
COMMENT ON COLUMN T_EAST_NBFHZ.YHJGMC IS '银行机构名称';
COMMENT ON COLUMN T_EAST_NBFHZ.MXKMBH IS '明细科目编号';
COMMENT ON COLUMN T_EAST_NBFHZ.MXKMMC IS '明细科目名称';
COMMENT ON COLUMN T_EAST_NBFHZ.ZHMC IS '账户名称';
COMMENT ON COLUMN T_EAST_NBFHZ.NBFHZZH IS '内部分户账账号';
COMMENT ON COLUMN T_EAST_NBFHZ.BZ IS '币种';
COMMENT ON COLUMN T_EAST_NBFHZ.JDBZ IS '借贷标志';
COMMENT ON COLUMN T_EAST_NBFHZ.JFYE IS '借方余额';
COMMENT ON COLUMN T_EAST_NBFHZ.DFYE IS '贷方余额';
COMMENT ON COLUMN T_EAST_NBFHZ.JXBZ IS '计息标志';
COMMENT ON COLUMN T_EAST_NBFHZ.JXFS IS '计息方式';
COMMENT ON COLUMN T_EAST_NBFHZ.LL IS '利率';
COMMENT ON COLUMN T_EAST_NBFHZ.KHRQ IS '开户日期';
COMMENT ON COLUMN T_EAST_NBFHZ.XHRQ IS '销户日期';
COMMENT ON COLUMN T_EAST_NBFHZ.ZHZT IS '账户状态';
COMMENT ON COLUMN T_EAST_NBFHZ.BBZ IS '备注';
COMMENT ON COLUMN T_EAST_NBFHZ.CJRQ IS '采集日期';
COMMENT ON COLUMN T_EAST_NBFHZ.CREATOR IS '创建人';
COMMENT ON COLUMN T_EAST_NBFHZ.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN T_EAST_NBFHZ.UPDATER IS '更新人';
COMMENT ON COLUMN T_EAST_NBFHZ.UPDATE_DATE IS '更新时间';


-- 内部分户账明细记录
CREATE TABLE T_EAST_NBFHZMX
(
    ID          NUMBER(20),
    JYXLH       VARCHAR2(100),
    JRXKZH      VARCHAR2(30),
    NBJGH       VARCHAR2(30),
    YHJGMC      VARCHAR2(450),
    MXKMBH      VARCHAR2(60),
    MXKMMC      VARCHAR2(300),
    ZHMC        VARCHAR2(450),
    NBFHZZH     VARCHAR2(60),
    HXJYRQ      VARCHAR2(8),
    HXJYSJ      VARCHAR2(6),
    BZ          VARCHAR2(3),
    JYLX        VARCHAR2(60),
    JYJDBZ      VARCHAR2(3),
    JYJE        NUMBER(20, 2),
    JFYE        NUMBER(20, 2),
    DFYE        NUMBER(20, 2),
    DFZH        VARCHAR2(60),
    DFKMBH      VARCHAR2(60),
    DFKMMC      VARCHAR2(300),
    DFHM        VARCHAR2(450),
    DFXH        VARCHAR2(30),
    DFXM        VARCHAR2(450),
    ZY          VARCHAR2(600),
    CBMBZ       VARCHAR2(9),
    JYQD        VARCHAR2(60),
    XZBZ        VARCHAR2(3),
    JYGYH       VARCHAR2(30),
    SQGYH       VARCHAR2(30),
    JZRQ        VARCHAR2(8),
    XZRQ        VARCHAR2(8),
    BBZ         VARCHAR2(600),
    CJRQ        VARCHAR2(8),
    CREATOR     NUMBER(20),
    CREATE_DATE DATE,
    UPDATER     NUMBER(20),
    UPDATE_DATE DATE,
    PRIMARY KEY (ID)
);
create index "NBFHZMX_JYXLH_index"
    on T_EAST_NBFHZMX (JYXLH, NBFHZZH, HXJYRQ, HXJYSJ, CJRQ);
COMMENT ON TABLE T_EAST_NBFHZMX IS '内部分户账明细记录';
COMMENT ON COLUMN T_EAST_NBFHZMX.ID IS 'ID';
COMMENT ON COLUMN T_EAST_NBFHZMX.JYXLH IS '交易序列号';
COMMENT ON COLUMN T_EAST_NBFHZMX.JRXKZH IS '金融许可证号';
COMMENT ON COLUMN T_EAST_NBFHZMX.NBJGH IS '内部机构号';
COMMENT ON COLUMN T_EAST_NBFHZMX.YHJGMC IS '银行机构名称';
COMMENT ON COLUMN T_EAST_NBFHZMX.MXKMBH IS '明细科目编号';
COMMENT ON COLUMN T_EAST_NBFHZMX.MXKMMC IS '明细科目名称';
COMMENT ON COLUMN T_EAST_NBFHZMX.ZHMC IS '账户名称';
COMMENT ON COLUMN T_EAST_NBFHZMX.NBFHZZH IS '内部分户账账号';
COMMENT ON COLUMN T_EAST_NBFHZMX.HXJYRQ IS '核心交易日期';
COMMENT ON COLUMN T_EAST_NBFHZMX.HXJYSJ IS '核心交易时间';
COMMENT ON COLUMN T_EAST_NBFHZMX.BZ IS '币种';
COMMENT ON COLUMN T_EAST_NBFHZMX.JYLX IS '交易类型';
COMMENT ON COLUMN T_EAST_NBFHZMX.JYJDBZ IS '交易借贷标志';
COMMENT ON COLUMN T_EAST_NBFHZMX.JYJE IS '交易金额';
COMMENT ON COLUMN T_EAST_NBFHZMX.JFYE IS '借方余额';
COMMENT ON COLUMN T_EAST_NBFHZMX.DFYE IS '贷方余额';
COMMENT ON COLUMN T_EAST_NBFHZMX.DFZH IS '对方账号';
COMMENT ON COLUMN T_EAST_NBFHZMX.DFKMBH IS '对方科目编号';
COMMENT ON COLUMN T_EAST_NBFHZMX.DFKMMC IS '对方科目名称';
COMMENT ON COLUMN T_EAST_NBFHZMX.DFHM IS '对方户名';
COMMENT ON COLUMN T_EAST_NBFHZMX.DFXH IS '对方行号';
COMMENT ON COLUMN T_EAST_NBFHZMX.DFXM IS '对方行名';
COMMENT ON COLUMN T_EAST_NBFHZMX.ZY IS '摘要';
COMMENT ON COLUMN T_EAST_NBFHZMX.CBMBZ IS '冲补抹标志';
COMMENT ON COLUMN T_EAST_NBFHZMX.JYQD IS '交易渠道';
COMMENT ON COLUMN T_EAST_NBFHZMX.XZBZ IS '现转标志';
COMMENT ON COLUMN T_EAST_NBFHZMX.JYGYH IS '交易柜员号';
COMMENT ON COLUMN T_EAST_NBFHZMX.SQGYH IS '授权柜员号';
COMMENT ON COLUMN T_EAST_NBFHZMX.JZRQ IS '进账日期';
COMMENT ON COLUMN T_EAST_NBFHZMX.XZRQ IS '销账日期';
COMMENT ON COLUMN T_EAST_NBFHZMX.BBZ IS '备注';
COMMENT ON COLUMN T_EAST_NBFHZMX.CJRQ IS '采集日期';
COMMENT ON COLUMN T_EAST_NBFHZMX.CREATOR IS '创建人';
COMMENT ON COLUMN T_EAST_NBFHZMX.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN T_EAST_NBFHZMX.UPDATER IS '更新人';
COMMENT ON COLUMN T_EAST_NBFHZMX.UPDATE_DATE IS '更新时间';


-- 个人信贷分户账
CREATE TABLE T_EAST_GRXDFHZ
(
    ID          NUMBER(20),
    JRXKZH      VARCHAR2(30),
    NBJGH       VARCHAR2(30),
    YHJGMC      VARCHAR2(450),
    MXKMBH      VARCHAR2(60),
    MXKMMC      VARCHAR2(300),
    KHTYBH      VARCHAR2(60),
    ZHMC        VARCHAR2(450),
    DKFHZH      VARCHAR2(60),
    XDHTH       VARCHAR2(100),
    XDJJH       VARCHAR2(100),
    DKLL        NUMBER(20, 4),
    BZ          VARCHAR2(3),
    DKJE        NUMBER(20, 2),
    DKYE        NUMBER(20, 2),
    FFRQ        VARCHAR2(8),
    DQRQ        VARCHAR2(8),
    KHRQ        VARCHAR2(8),
    XHRQ        VARCHAR2(8),
    DKZT        VARCHAR2(30),
    ZHZT        VARCHAR2(30),
    BBZ         VARCHAR2(600),
    CJRQ        VARCHAR2(8),
    CREATOR     NUMBER(20),
    CREATE_DATE DATE,
    UPDATER     NUMBER(20),
    UPDATE_DATE DATE,
    PRIMARY KEY (ID)
);
create index "GRXDFHZ_DKFHZH_index"
    on T_EAST_GRXDFHZ (DKFHZH, XDJJH, BZ, CJRQ);
COMMENT ON TABLE T_EAST_GRXDFHZ IS '个人信贷分户账';
COMMENT ON COLUMN T_EAST_GRXDFHZ.ID IS 'ID';
COMMENT ON COLUMN T_EAST_GRXDFHZ.JRXKZH IS '金融许可证号';
COMMENT ON COLUMN T_EAST_GRXDFHZ.NBJGH IS '内部机构号';
COMMENT ON COLUMN T_EAST_GRXDFHZ.YHJGMC IS '银行机构名称';
COMMENT ON COLUMN T_EAST_GRXDFHZ.MXKMBH IS '明细科目编号';
COMMENT ON COLUMN T_EAST_GRXDFHZ.MXKMMC IS '明细科目名称';
COMMENT ON COLUMN T_EAST_GRXDFHZ.KHTYBH IS '客户统一编号';
COMMENT ON COLUMN T_EAST_GRXDFHZ.ZHMC IS '账户名称';
COMMENT ON COLUMN T_EAST_GRXDFHZ.DKFHZH IS '贷款分户账号';
COMMENT ON COLUMN T_EAST_GRXDFHZ.XDHTH IS '信贷合同号';
COMMENT ON COLUMN T_EAST_GRXDFHZ.XDJJH IS '信贷借据号';
COMMENT ON COLUMN T_EAST_GRXDFHZ.DKLL IS '实际利率';
COMMENT ON COLUMN T_EAST_GRXDFHZ.BZ IS '币种';
COMMENT ON COLUMN T_EAST_GRXDFHZ.DKJE IS '贷款金额';
COMMENT ON COLUMN T_EAST_GRXDFHZ.DKYE IS '贷款余额';
COMMENT ON COLUMN T_EAST_GRXDFHZ.FFRQ IS '发放日期';
COMMENT ON COLUMN T_EAST_GRXDFHZ.DQRQ IS '到期日期';
COMMENT ON COLUMN T_EAST_GRXDFHZ.KHRQ IS '开户日期';
COMMENT ON COLUMN T_EAST_GRXDFHZ.XHRQ IS '销户日期';
COMMENT ON COLUMN T_EAST_GRXDFHZ.DKZT IS '贷款状态';
COMMENT ON COLUMN T_EAST_GRXDFHZ.ZHZT IS '账户状态';
COMMENT ON COLUMN T_EAST_GRXDFHZ.BBZ IS '备注';
COMMENT ON COLUMN T_EAST_GRXDFHZ.CJRQ IS '采集日期';
COMMENT ON COLUMN T_EAST_GRXDFHZ.CREATOR IS '创建人';
COMMENT ON COLUMN T_EAST_GRXDFHZ.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN T_EAST_GRXDFHZ.UPDATER IS '更新人';
COMMENT ON COLUMN T_EAST_GRXDFHZ.UPDATE_DATE IS '更新时间';


-- 个人信贷分户账明细记录
CREATE TABLE T_EAST_GRXDFHZMX
(
    ID          NUMBER(20),
    JYXLH       VARCHAR2(100),
    JRXKZH      VARCHAR2(30),
    NBJGH       VARCHAR2(30),
    YWBLJGH     VARCHAR2(30),
    YHJGMC      VARCHAR2(450),
    MXKMBH      VARCHAR2(60),
    MXKMMC      VARCHAR2(300),
    KHTYBH      VARCHAR2(60),
    ZHMC        VARCHAR2(450),
    ZJLB        VARCHAR2(60),
    ZJHM        VARCHAR2(70),
    DKFHZH      VARCHAR2(60),
    XDJJH       VARCHAR2(100),
    HXJYRQ      VARCHAR2(8),
    HXJYSJ      VARCHAR2(6),
    JYLX        VARCHAR2(60),
    JYJDBZ      VARCHAR2(3),
    BZ          VARCHAR2(3),
    JYJE        NUMBER(20, 2),
    ZHYE        NUMBER(20, 2),
    DFZH        VARCHAR2(60),
    DFHM        VARCHAR2(450),
    DFXH        VARCHAR2(30),
    DFXM        VARCHAR2(450),
    ZY          VARCHAR2(600),
    JYQD        VARCHAR2(60),
    CBMBZ       VARCHAR2(9),
    DBRXM       VARCHAR2(150),
    DBRZJLB     VARCHAR2(60),
    DBRZJHM     VARCHAR2(70),
    JYGYH       VARCHAR2(30),
    SQGYH       VARCHAR2(30),
    XZBZ        VARCHAR2(3),
    BBZ         VARCHAR2(600),
    CJRQ        VARCHAR2(8),
    CREATOR     NUMBER(20),
    CREATE_DATE DATE,
    UPDATER     NUMBER(20),
    UPDATE_DATE DATE,
    PRIMARY KEY (ID)
);
create index "GRXDFHZMX_JYXLH_index"
    on T_EAST_GRXDFHZMX (JYXLH, ZJHM, DKFHZH, XDJJH, HXJYRQ, HXJYSJ, CJRQ);
COMMENT ON TABLE T_EAST_GRXDFHZMX IS '个人信贷分户账明细记录';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.ID IS 'ID';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.JYXLH IS '交易序列号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.JRXKZH IS '金融许可证号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.NBJGH IS '内部机构号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.YWBLJGH IS '业务办理机构号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.YHJGMC IS '银行机构名称';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.MXKMBH IS '明细科目编号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.MXKMMC IS '明细科目名称';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.KHTYBH IS '客户统一编号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.ZHMC IS '账户名称';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.ZJLB IS '证件类别';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.ZJHM IS '证件号码';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DKFHZH IS '贷款分户账号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.XDJJH IS '信贷借据号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.HXJYRQ IS '核心交易日期';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.HXJYSJ IS '核心交易时间';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.JYLX IS '交易类型';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.JYJDBZ IS '交易借贷标志';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.BZ IS '币种';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.JYJE IS '交易金额';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.ZHYE IS '账户余额';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DFZH IS '对方账号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DFHM IS '对方户名';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DFXH IS '对方行号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DFXM IS '对方行名';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.ZY IS '摘要';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.JYQD IS '交易渠道';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.CBMBZ IS '冲补抹标志';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DBRXM IS '代办人姓名';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DBRZJLB IS '代办人证件类别';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.DBRZJHM IS '代办人证件号码';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.JYGYH IS '交易柜员号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.SQGYH IS '授权柜员号';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.XZBZ IS '现转标志';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.BBZ IS '备注';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.CJRQ IS '采集日期';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.CREATOR IS '创建人';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.CREATE_DATE IS '创建时间';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.UPDATER IS '更新人';
COMMENT ON COLUMN T_EAST_GRXDFHZMX.UPDATE_DATE IS '更新时间';

