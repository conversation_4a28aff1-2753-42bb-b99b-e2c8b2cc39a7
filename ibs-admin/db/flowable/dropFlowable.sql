drop table ACT_GE_PROPERTY ;

drop table ACT_GE_BYTEARRAY ;

drop table ACT_RU_ENTITYLINK;

drop table ACT_HI_ENTITYLINK ;


drop table ACT_RU_IDENTITYLINK ;


drop table ACT_HI_IDENTITYLINK ;

drop table ACT_RU_JOB ;

drop table ACT_RU_TIMER_JOB ;

drop table ACT_RU_SUSPENDED_JOB ;

drop table ACT_RU_DEADLETTER_JOB ;

drop table ACT_RU_HISTORY_JOB ;

drop table FLW_RU_BATCH ;

drop table FLW_RU_BATCH_PART ;


drop table ACT_RU_TASK ;


drop table ACT_HI_TASKINST ;

drop table ACT_HI_TSK_LOG ;


drop table ACT_RU_VARIABLE ;


drop table ACT_HI_VARINST ;



drop table ACT_RU_EVENT_SUBSCR ;


drop table ACT_RE_DEPLOYMENT ;

drop table ACT_RE_MODEL ;

drop table ACT_RU_EXECUTION ;

drop table ACT_RE_PROCDEF ;

drop table ACT_EVT_LOG ;

drop table ACT_PROCDEF_INFO ;

drop table ACT_RU_ACTINST ;


drop table ACT_HI_PROCINST ;

drop table ACT_HI_ACTINST ;

drop table ACT_HI_DETAIL ;

drop table ACT_HI_COMMENT ;

drop table ACT_HI_ATTACHMENT ;

drop table ACT_ID_PROPERTY ;


drop table ACT_ID_BYTEARRAY ;

drop table ACT_ID_GROUP ;

drop table ACT_ID_MEMBERSHIP ;

drop table ACT_ID_USER ;

drop table ACT_ID_INFO ;

drop table ACT_ID_TOKEN ;

drop table ACT_ID_PRIV ;

drop table ACT_ID_PRIV_MAPPING ;



drop TABLE ACT_APP_DATABASECHANGELOGLOCK ;

drop TABLE ACT_APP_DATABASECHANGELOG ;

drop TABLE ACT_APP_DEPLOYMENT ;

drop TABLE ACT_APP_DEPLOYMENT_RESOURCE ;


drop TABLE ACT_APP_APPDEF ;


drop TABLE ACT_CMMN_DATABASECHANGELOGLOCK ;

drop TABLE ACT_CMMN_DATABASECHANGELOG ;

drop TABLE ACT_CMMN_DEPLOYMENT ;

drop TABLE ACT_CMMN_DEPLOYMENT_RESOURCE ;

drop TABLE ACT_CMMN_CASEDEF ;

drop TABLE ACT_CMMN_RU_CASE_INST ;

drop TABLE ACT_CMMN_RU_PLAN_ITEM_INST ;

drop TABLE ACT_CMMN_RU_SENTRY_PART_INST ;

drop TABLE ACT_CMMN_RU_MIL_INST ;

drop TABLE ACT_CMMN_HI_CASE_INST;

drop TABLE ACT_CMMN_HI_MIL_INST ;


drop TABLE ACT_CMMN_HI_PLAN_ITEM_INST ;


drop TABLE FLW_EV_DATABASECHANGELOGLOCK ;

drop TABLE FLW_EV_DATABASECHANGELOG ;

drop TABLE FLW_EVENT_DEPLOYMENT ;

drop TABLE FLW_EVENT_RESOURCE ;

drop TABLE FLW_EVENT_DEFINITION;

drop TABLE FLW_CHANNEL_DEFINITION ;


drop TABLE ACT_DMN_DATABASECHANGELOGLOCK ;

drop TABLE ACT_DMN_DATABASECHANGELOG ;

drop TABLE ACT_DMN_DEPLOYMENT ;

drop TABLE ACT_DMN_DEPLOYMENT_RESOURCE ;

drop TABLE ACT_DMN_DECISION_TABLE ;

drop TABLE ACT_DMN_HI_DECISION_EXECUTION ;

drop TABLE ACT_FO_DATABASECHANGELOGLOCK ;

drop TABLE ACT_FO_DATABASECHANGELOG ;

drop TABLE ACT_FO_FORM_DEPLOYMENT ;

drop TABLE ACT_FO_FORM_RESOURCE ;

drop TABLE ACT_FO_FORM_DEFINITION ;

drop TABLE ACT_FO_FORM_INSTANCE ;


drop TABLE ACT_CO_DATABASECHANGELOGLOCK;

drop TABLE ACT_CO_DATABASECHANGELOG ;

drop TABLE ACT_CO_CONTENT_ITEM ;

DROP TABLE DATABASECHANGELOGLOCK;

DROP TABLE ACT_DE_MODEL_HISTORY;

ALTER TABLE ACT_DE_MODEL_RELATION
  DROP CONSTRAINT FK_RELATION_PARENT;

ALTER TABLE ACT_DE_MODEL_RELATION
  DROP CONSTRAINT FK_RELATION_CHILD;

DROP TABLE ACT_DE_MODEL;

DROP TABLE ACT_CMMN_RU_CASE_INST;

DROP TABLE ACT_CMMN_CASEDEF;

DROP TABLE ACT_CMMN_DEPLOYMENT;

DROP TABLE ACT_DE_DATABASECHANGELOGLOCK;

DROP TABLE ACT_DE_MODEL_RELATION;

DROP TABLE ACT_DE_DATABASECHANGELOG;



drop sequence act_evt_log_seq;

DROP SEQUENCE ACT_HI_TASK_EVT_LOG_SEQ;
