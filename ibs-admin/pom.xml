<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.ibs</groupId>
        <artifactId>integrated-report-platform</artifactId>
        <version>3.3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ibs-admin</artifactId>
    <packaging>jar</packaging>
    <description>ibs-admin</description>

    <properties>
        <quartz.version>2.3.2</quartz.version>
        <shiro.version>1.9.0</shiro.version>
        <captcha.version>1.6.2</captcha.version>
        <easyexcel.version>3.0.5</easyexcel.version>
        <qiniu.version>7.2.27</qiniu.version>
        <aliyun.oss.version>2.8.3</aliyun.oss.version>
        <aliyun.core.version>3.2.2</aliyun.core.version>
        <aliyun.dysmsapi.version>1.1.0</aliyun.dysmsapi.version>
        <qcloud.cos.version>5.4.4</qcloud.cos.version>
        <qcloud.qcloudsms.version>1.0.5</qcloud.qcloudsms.version>
        <fastdfs.version>1.26.2</fastdfs.version>
        <minio.version>6.0.13</minio.version>
        <mail.version>1.6.2</mail.version>
        <freemarker.version>2.3.31</freemarker.version>
        <ureport2.version>2.2.9</ureport2.version>
        <IJPay.version>2.7.1</IJPay.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>2.0.2-beta</version>
        </dependency>
        <dependency>
            <groupId>io.ibs</groupId>
            <artifactId>ibs-common</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>io.ibs</groupId>
            <artifactId>ibs-dynamic-datasource</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${quartz.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP-java6</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
            <version>${shiro.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
            <version>${captcha.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.qiniu</groupId>-->
<!--            <artifactId>qiniu-java-sdk</artifactId>-->
<!--            <version>${qiniu.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun.oss</groupId>-->
<!--            <artifactId>aliyun-sdk-oss</artifactId>-->
<!--            <version>${aliyun.oss.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.qcloud</groupId>-->
<!--            <artifactId>cos_api</artifactId>-->
<!--            <version>${qcloud.cos.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.slf4j</groupId>-->
<!--                    <artifactId>slf4j-log4j12</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>aliyun-java-sdk-core</artifactId>-->
<!--            <version>${aliyun.core.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>-->
<!--            <version>${aliyun.dysmsapi.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.github.qcloudsms</groupId>-->
<!--            <artifactId>qcloudsms</artifactId>-->
<!--            <version>${qcloud.qcloudsms.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>${mail.version}</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.tobato</groupId>
            <artifactId>fastdfs-client</artifactId>
            <version>${fastdfs.version}</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bstek.ureport</groupId>
            <artifactId>ureport2-console</artifactId>
            <version>${ureport2.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.github.javen205</groupId>-->
<!--            <artifactId>IJPay-AliPay</artifactId>-->
<!--            <version>${IJPay.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>cn.hutool</groupId>-->
<!--                    <artifactId>hutool-core</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <!-- 工作流 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>4.2.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.ibm.informix/jdbc -->
        <dependency>
            <groupId>com.ibm.informix</groupId>
            <artifactId>jdbc</artifactId>
            <version>4.50.3</version>
        </dependency>
        <!-- 征信 -->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>
        <dependency>
            <groupId>org.pbccrc.collectclient.api</groupId>
            <artifactId>collectclient-api</artifactId>
            <version>0.5.3.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/client-collect-api-0.5.3.0.jar</systemPath>
        </dependency>

        <!--加密算法-->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.77</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>integrated-report-platform</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>${docker.plugin.version}</version>
                <configuration>
                    <imageName>renren/${project.artifactId}</imageName>
                    <dockerDirectory>${project.basedir}/</dockerDirectory>
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                        </resource>
                    </resources>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>