package io.ibs.common.aspect;

import java.util.Collection;

public abstract interface IDataProcessor {
    enum stage{
        insert,
        update,
        delete
    }

    /*
     * 事务执行前切入执行
     * @data 变化的数据
     *       when stage = insert/update：type = Collection<entity or DTO>
     *       when stage = delete: type = Collection<ID_TYPE>
     * 无论是否批量的CURD均以集合形式入参
     * 注意：如果此方法抛错，则不会继续执行后续事务，对参数的修改会影响事务执行结果
     * */
    default void process(Collection<Object> data, stage stage) {

    }
    /*
     * 事务执行后切入执行
     * @data 变化的数据
     *       when stage = insert/update：type = Collection<entity or DTO>
     *       when stage = delete: type = Collection<ID_TYPE>
     * 无论是否批量的CURD均以集合形式入参
     * */
    default void processed(Collection<Object> data, stage stage) {

    }
}
