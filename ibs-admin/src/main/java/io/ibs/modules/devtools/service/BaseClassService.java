
package io.ibs.modules.devtools.service;


import io.ibs.common.page.PageData;
import io.ibs.common.service.BaseService;
import io.ibs.modules.devtools.entity.BaseClassEntity;

import java.util.List;
import java.util.Map;

/**
 * 基类管理
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
public interface BaseClassService extends BaseService<BaseClassEntity> {

    PageData<BaseClassEntity> page(Map<String, Object> params);

    List<BaseClassEntity> list();
}