
package io.ibs.modules.devtools.service;


import io.ibs.common.page.PageData;
import io.ibs.common.service.BaseService;
import io.ibs.modules.devtools.entity.TableInfoEntity;

import java.util.Map;

/**
 * 表
 *
 * <AUTHOR>
 */
public interface TableInfoService extends BaseService<TableInfoEntity> {

    PageData<TableInfoEntity> page(Map<String, Object> params);

    TableInfoEntity getByTableName(String tableName);

    void deleteByTableName(String tableName);

    void deleteBatchIds(Long[] ids);
}