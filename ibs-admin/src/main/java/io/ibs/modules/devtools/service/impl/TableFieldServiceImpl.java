
package io.ibs.modules.devtools.service.impl;

import io.ibs.common.service.impl.BaseServiceImpl;
import io.ibs.modules.devtools.dao.TableFieldDao;
import io.ibs.modules.devtools.entity.TableFieldEntity;
import io.ibs.modules.devtools.service.TableFieldService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 表
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
@Service
public class TableFieldServiceImpl extends BaseServiceImpl<TableFieldDao, TableFieldEntity> implements TableFieldService {

    @Override
    public List<TableFieldEntity> getByTableName(String tableName) {
        return baseDao.getByTableName(tableName);
    }

    @Override
    public void deleteByTableName(String tableName) {
        baseDao.deleteByTableName(tableName);
    }

    @Override
    public void deleteBatchTableIds(Long[] tableIds) {
        baseDao.deleteBatchTableIds(tableIds);
    }

}