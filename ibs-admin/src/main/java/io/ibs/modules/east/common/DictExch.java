package io.ibs.modules.east.common;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 核心数据与EAST字典映射关系
 *
 * <AUTHOR>
 * @since 20240319
 */
@Slf4j
public class DictExch {
    /**
     * 核心机构类型和EAST机构类别映射
     *
     * @param hostType 核心机构类型
     * @return east机构类别
     */
    public static String instTypeExch(String hostType) {
        String instType = "";
        if ("0".equals(hostType)) {
            instType = "管理机构";
        } else if ("1".equals(hostType)) {
            instType = "营业机构";
        } else {
            instType = "内设机构";
        }

        return instType;
    }

    /**
     * 核心机构运行状态和east机构营业状态映射
     *
     * @param hostStatus 机构运作状况
     * @return east营业状态
     */
    public static String instStatus(String hostStatus) {
        String status = "";
        if ("1".equals(hostStatus)) {
            status = "营业";
        } else {
            status = "停业";
        }

        return status;
    }

    /**
     * 核心个人证件类别与east证件类别映射
     * 核心：0-身份证;1-驾驶证;2-军人证;3-营业执照;4-其他企事业单位和社会团体代码;5-社会保障号;6-护照;7-回乡证;8-户口簿;9-警察证;z-其它证件;
     * 个人客户：居民身份证,军官证,文职干部证,警官证,士兵证,户口簿,临时身份证,其他有效通行旅行证件,护照,学生证,无证件,其他-XX
     *
     * @param hostIDType 核心个人证件类型
     * @return east个人证件类型
     */
    public static String idTypeExch(String hostIDType) {
        String idType;
        switch (hostIDType) {
            case "0":
                idType = "居民身份证";
                break;
            case "1":
                idType = "其他-驾驶证";
                break;
            case "2":
                idType = "军官证";
                break;
            case "3":
                idType = "其他-营业执照";
                break;
            case "4":
                idType = "其他-其他企事业单位和社会团体代码";
                break;
            case "5":
                idType = "其他-社会保障号";
                break;
            case "6":
                idType = "护照";
                break;
            case "7":
                idType = "其他-回乡证";
                break;
            case "8":
                idType = "户口簿";
                break;
            case "9":
                idType = "警官证";
                break;
            default:
                idType = "其他-其他证件";
        }

        return idType;
    }

    /**
     * 核心企业证件类别与east证件类别映射
     * 对公客户：统一社会信用代码，组织机构代码，营业执照（工商注册号），公司注册证书，全球法人识别码，其他-XX。
     *
     * @param hostIdNo 核心证件号码
     * @return east证件类别
     */
    public static String idTypeExchEnt(String hostIdNo) {
        String hostIdType = hostIdNo.substring(0, 1);
        hostIdNo = hostIdNo.substring(1).trim();
        String idType;
        if (hostIdType.equals("3")) {
            if (hostIdNo.length() == 18) {
                idType = "统一社会信用代码";
            } else {
                idType = "营业执照（工商注册号）";
            }
        } else {
            idType = "其他-其他证件";
        }
        return idType;
    }

    /**
     * 征信二代ecif的个人证件类型转换成east证件类型
     * ecif: 01:身份证 02:户口簿 03:护照 04:军官证 05:士兵证 06:港澳居民来往内地通行证 07:台湾居民来往大陆通行证 08:临时身份证 09:外国人居留证 10:警官证 11:个体工商户营业执照 12:港澳台居民居住证 13:其他证件
     * east: 居民身份证,军官证,文职干部证,警官证,士兵证,户口簿,临时身份证,其他有效通行旅行证件,护照,学生证,无证件,其他-XX
     *
     * @return
     */
    public static String ecifIDTypeExch(String pbccrcIDType) {
        switch (pbccrcIDType) {
            case "01":
                return "居民身份证";
            case "02":
                return "户口簿";
            case "03":
                return "护照";
            case "04":
                return "军官证";
            case "05":
                return "士兵证";
            case "06":
                return "其他-港澳居民来往内地通行证";
            case "07":
                return "其他-台湾居民来往大陆通行证";
            case "08":
                return "临时身份证";
            case "09":
                return "其他-外国人居留证";
            case "10":
                return "警官证";
            case "11":
                return "其他-个体工商户营业执照";
            case "12":
                return "其他-港澳台居民居住证";
            default:
                return "其他-其他证件";
        }
    }

    /**
     * ecif企业证件类别与east证件类别映射
     * ecif：10-中征码（原贷款卡编码）20-统一社会信用代码30-组织机构代码
     * 对公客户：统一社会信用代码，组织机构代码，营业执照（工商注册号），公司注册证书，全球法人识别码，其他-XX。
     *
     * @param ecifIdType ecif证件类型
     * @return east证件类别
     */
    public static String ecifIdTypeExchEnt(String ecifIdType) {
        switch (ecifIdType) {
            case "10":
                return "中征码";
            case "20":
                return "统一社会信用代码";
            case "30":
                return "组织机构代码";
            default:
                return "其他-其他证件";
        }
    }

    /**
     * ecif:
     * 1-企业;11-公司;13-非公司制企业法人;15-企业分支机构;17-个人独资企业、合伙企业;19-其他企业;
     * 3-机关;31-中国共产党;32-国家权力机关法人;33-国家行政机关法人;34-国家司法机关法人;35-政协组织;36-民主党派;37-人民解放军、武警部;39-其他机关;
     * 5-事业单位;51-事业单位法人;53-事业单位分支、派出机构;59-其他事业单位;
     * 7-社会团体;71-社会团体法人;73-社会团体分支、代表机构;79-其他社会团体;
     * 9-其他组织机构;91-民办非企业单位;93-基金会;94-宗教活动场所;95-农村村民委员会;96-城市居民委员会;97-自定义区;99-其他未列明的组织机构;
     * A-个体工商户'
     * <p>
     * east:
     * 集团客户，单一法人客户，同业客户，政府机关，事业单位，社会团体，境外机构，其他-银行自定义类型。无法以枚举类型填报的，以“其他-XX”填报，其中“XX”为银行自定义类型。
     */
    public static String entKhlx(String orgType) {
        if (orgType == null) {
            return "其他-其他类型";
        }
        switch (orgType) {
            case "1":
            case "11":
            case "13":
            case "15":
            case "17":
            case "19":
                return "单一法人客户";
            case "5":
            case "51":
            case "53":
            case "59":
                return "事业单位";
            case "7":
            case "71":
            case "73":
            case "79":
                return "社会团体";
            default:
                return "其他-其他类型";
        }
    }

    /**
     * 企业分类
     * ecif:
     * entScale 2-大型企业 3-中型企业 4-小型企业 5-微型企业 9-无需划型 X-未知
     * <p>
     * orgType
     * 1-企业;11-公司;13-非公司制企业法人;15-企业分支机构;17-个人独资企业、合伙企业;19-其他企业;
     * 3-机关;31-中国共产党;32-国家权力机关法人;33-国家行政机关法人;34-国家司法机关法人;35-政协组织;36-民主党派;37-人民解放军、武警部;39-其他机关;
     * 5-事业单位;51-事业单位法人;53-事业单位分支、派出机构;59-其他事业单位;
     * 7-社会团体;71-社会团体法人;73-社会团体分支、代表机构;79-其他社会团体;
     * 9-其他组织机构;91-民办非企业单位;93-基金会;94-宗教活动场所;95-农村村民委员会;96-城市居民委员会;97-自定义区;99-其他未列明的组织机构;
     * A-个体工商户'
     * <p>
     * east:
     * 大型企业，中型企业，小型企业，微型企业，政府机关，事业单位，社会团体，境外机构，其他组织机构
     */
    public static String entQyfl(String qyfl) {
        if (qyfl == null) {
            return "其他组织机构";
        }
        String orgType = qyfl.split("-", -1)[0];
        String entScale = qyfl.split("-", -1)[1];
        if ("2".equals(entScale)) {
            return "大型企业";
        }
        if ("3".equals(entScale)) {
            return "中型企业";
        }
        if ("4".equals(entScale)) {
            return "小型企业";
        }
        if ("5".equals(entScale)) {
            return "微型企业";
        }
        if ("5".equals(orgType) || "51".equals(orgType) || "53".equals(orgType) || "59".equals(orgType)) {
            return "事业单位";
        }
        if ("7".equals(orgType) || "71".equals(orgType) || "73".equals(orgType) || "79".equals(orgType)) {
            return "社会团体";
        }
        return "其他组织机构";
    }

    /**
     * 信用评级
     * host: A-高 B-中 C-低
     * <p>
     * east:
     * 优先填报外部专业性的评级公司对融资人主体或资产的信用评级结果，如无，则由填报机构内部对融资人主体或资产的信用评级结果。
     */
    public static String entXypj(String xypj) {
        if (xypj == null) {
            return "";
        }
        switch (xypj) {
            case "A":
                return "高";
            case "B":
                return "中";
            case "C":
                return "低";
            default:
                return "";
        }
    }

    /**
     * 核心存折状态和east存折状态映射
     *
     * @param hostBookStatus
     * @return
     */
    public static String bookStatus(String hostBookStatus) {
        String status;
        String closeFlag = hostBookStatus.substring(0, 1);
        String frezzFlag = hostBookStatus.substring(1, 2);
        String lossFlag = hostBookStatus.substring(2, 3);
        if ("1".equals(closeFlag)) {
            status = "注销";
        } else if ("0".equals(closeFlag)) {
            if (!"0".equals(frezzFlag)) {
                status = "冻结";
            } else {
                if (!"0".equals(lossFlag)) {
                    status = "挂失";
                } else {
                    status = "正常";
                }
            }
        } else {
            status = "止付";
        }

        return status;
    }

    /**
     * 核心存折类型和east存折类型映射
     *
     * @param bookType 核心:第一位为产品表的凭证格式码：0-旧活期存折、1-多功能存折、2-存单、3-定期一本通存折、9-财政补贴一折通存折
     *                 第二位为主账表的标志位第7位 一本通标志
     *                 east:普通存折，存单，一本通，大额定期存单，其他-银行自定义
     * @return
     */
    public static String bookTypeExch(String bookType) {
        String type;
        String hostBookType = bookType.substring(0, 1);
        String bookFlag = bookType.substring(1, 2);
        if ("1".equals(bookFlag)) {
            type = "一本通";
        } else {
            if ("0".equals(hostBookType) || "1".equals(hostBookType)) {
                type = "普通存折";
            } else if ("2".equals(hostBookType)) {
                type = "存单";
            } else if ("3".equals(hostBookType)) {
                type = "一本通";
            } else {
                type = "其他";
            }
        }


        return type;
    }

    /**
     * 核心贷款状态转换
     * 状态标志；0-正常、1-逾期、2-呆滞、3-呆帐
     * east:正常、结清、逾期、核销、转让，其他-银行自定义。
     *
     * @param status 贷款状态
     */
    public static String loanStatusExch(String status) {
        String res;
        switch (status) {
            case "0":
                res = "正常";
                break;
            case "1":
                res = "逾期";
                break;
            case "2":
                res = "其他-呆滞";
                break;
            case "3":
                res = "其他-呆帐";
                break;
            default:
                res = "其他-其他状态";
        }
        return res;
    }

    /**
     * 征信二代贷款状态转换
     * 征信二代：1-正常 2-逾期 3-关闭（包括但不限于贷款结清） 41-呆账（未核销） 42-呆账（已核销） 6-担保物不足 8-司法追偿
     * east:正常、结清、逾期、核销、转让，其他-银行自定义。
     *
     * @param status 贷款状态
     */
    public static String pbccrcLoanStatusExch(String status) {
        String res = "正常";
        switch (status) {
            case "1":
                res = "正常";
                break;
            case "2":
                res = "逾期";
                break;
            case "3":
                res = "结清";
                break;
            case "41":
                res = "其他-呆帐";
                break;
            case "42":
                res = "核销";
                break;
            case "6":
                res = "其他-担保物不足";
                break;
            case "8":
                res = "其他-司法追偿";
                break;
        }
        return res;
    }

    /**
     * 征信二代贷款状态转换(从企业五级分类转换)
     * 除了1其他都是逾期
     * east:正常、结清、逾期、核销、转让，其他-银行自定义。
     *
     * @param status 五级分类 (必须要有值)
     */
    public static String pbccrcEntLoanStatusExch(String status) {
        if ("1".equals(status) || StrUtil.isEmpty(status)) {
            return "正常";
        }
        return "逾期";
    }

    /**
     * 核心账户状态转换
     * 状态标志；0-正常、1-销户
     * east:正常，预销户，销户，冻结，止付，其他-银行自定义
     *
     * @param status 账户状态
     */
    public static String accStatusExch(String status) {
        String res = "正常";
        switch (status) {
            case "0":
                res = "正常";
                break;
            case "1":
                res = "销户";
                break;
        }
        return res;
    }

    /**
     * 核心账户状态转换 对公 结算活期户主帐
     * [0]帐户状态：0-正常，1-销户 2-不动户  3-未激活(只针对基本户)
     * [2]冻结类型：0-正常，1-不收不付，2-只收不付，3-部分冻结
     * east:正常，预销户，销户，冻结，止付，其他-银行自定义
     *
     * @param status 账户状态
     */
    public static String accStatusEnt(String status) {
        String closeFlag = status.substring(0, 1);
        String frezzFlag = status.substring(1, 2);
        if ("1".equals(closeFlag)) {
            return "销户";
        }
        if ("3".equals(frezzFlag)) {
            return "冻结";
        }
        if ("1".equals(frezzFlag) || "2".equals(frezzFlag)) {
            return "止付";
        }
        return "正常";
    }

    /**
     * 征信二代账户状态转换
     * 征信二代状态标志；
     * 1-正常
     * 2-逾期
     * 3-关闭（包括但不限于贷款结清）
     * 41-呆账（进入核销认定程序，等待核销）
     * 42-呆账（已核销）
     * 6-担保物不足
     * 8-司法追偿
     * east:正常，预销户，销户，冻结，止付，其他-银行自定义
     *
     * @param status 账户状态
     */
    public static String pbccrcAccStatusExch(String status) {
        String res = "正常";
        switch (status) {
            case "1":
                res = "正常";
                break;
            case "2":
                res = "其他-逾期";
                break;
            case "3":
                res = "销户";
                break;
            case "41":
            case "42":
                res = "其他-呆账";
                break;
            case "6":
                res = "其他-担保物不足";
                break;
            case "8":
                res = "其他-司法追偿";
                break;
        }
        return res;
    }

    /**
     * 征信二代账户状态转换(企业)
     * 征信二代状态标志；
     * 10-正常活动
     * 21-关闭
     * 31-呆账（已核销）
     * 32-呆账（进入核销认定程序，等待核销）
     * east:正常，预销户，销户，冻结，止付，其他-银行自定义
     *
     * @param status 账户状态
     */
    public static String pbccrcEntAccStatusExch(String status) {
        String res = "正常";
        switch (status) {
            case "10":
                res = "正常";
                break;
            case "21":
                res = "销户";
                break;
            case "31":
            case "32":
                res = "其他-呆账";
                break;
        }
        return res;
    }

    /**
     * 核心科目帐类和east科目归属业务大类映射
     * 核心:1-资产类科目、2-贷款科目、3-负债类科目、4-储蓄存款科目、5-对公存款科目、6-资产负债共同类、7-往来科目、8-所有者权益类、9-损益类、a-表外科目类、+-合计项
     * east:资产、负债、所有者权益、损益、资产负债共同类、表外、其他
     *
     * @param hostAccType
     * @return
     */
    public static String itmAccTypeExch(String hostAccType) {
        String type = "";
        switch (hostAccType) {
            case "1":
            case "2":
                type = "资产";
                break;
            case "3":
            case "4":
            case "5":
                type = "负债";
                break;
            case "6":
                type = "资产负债共同类";
                break;
            case "7":
                type = "其他";
                break;
            case "8":
                type = "所有者权益";
                break;
            case "9":
                type = "损益";
                break;
            case "a":
                type = "表外";
                break;
            default:
                type = "其他";
                break;
        }

        return type;
    }

    /**
     * 核心借贷标识与EAST映射
     *
     * @param dr_cr_flag
     * @return
     */
    public static String drcrFlagExch(String dr_cr_flag) {
        String flag = "";
        if ("1".equals(dr_cr_flag) || "3".equals(dr_cr_flag)) {
            flag = "借";
        } else if ("2".equals(dr_cr_flag) || "4".equals(dr_cr_flag)) {
            flag = "贷";
        } else {
            flag = "借贷并列";
        }
        return flag;
    }

    /**
     * 冲帐标志转换
     * 冲帐标志：0-非冲帐明细、1-冲帐明细
     * east:正常，冲补抹
     *
     * @param data 冲帐标志
     */
    public static String cbmbzExch(String data) {
        String res = "正常";
        switch (data) {
            case "0":
                res = "正常";
                break;
            case "1":
                res = "冲补抹";
                break;
        }
        return res;
    }

    /**
     * 现转标志转换
     * 现转标志：0-现金 1-转帐
     * east:现，转
     *
     * @param data 现转标志
     */
    public static String xzbzExch(String data) {
        String res = "正常";
        switch (data) {
            case "0":
                res = "现";
                break;
            case "1":
                res = "转";
                break;
        }
        return res;
    }

    /**
     * 计息方式 转换
     * 批量结息时间：0-不批量结息，1-按月结息，2-按季结息，3-按年结息(6月份)，4-按年结息(9月份)，5-按年结息(12月份)
     * east:按月结息，按季结息，按半年结息，按年结息，不定期结息，不记利息，利随本清，其他-银行自定义
     *
     * @param data 计息方式
     */
    public static String jxfsExch(String data) {
        String res;
        switch (data) {
            case "1":
                res = "按月结息";
                break;
            case "2":
                res = "按季结息";
                break;
            case "3":
            case "4":
            case "5":
                res = "按年结息";
                break;
            case "0":
                res = "利随本清";
                break;
            default:
                res = "不定期结息";
        }
        return res;
    }

    /**
     * 计息标志 转换
     * 利息核算方法: 0-不计息，1-活期计息，2-双整定期计息，3-贷款计息，4-透支计息，5-特殊定期计息
     * east:是，否
     *
     * @param data 计息标志
     */
    public static String jxbzExch(String data) {
        if ("0".equals(data)) {
            return "否";
        }
        return "是";
    }

    /**
     * 抵押物类型转换
     * 按照以下押品项目填报（序号+名称）：
     * 1.1现金及其等价物；
     * 1.2贵金属；
     * 1.3.1国债；1.3.2地方政府债；1.3.3央票；1.3.4政府机构债券；1.3.5政策性金融债；1.3.6商业性金融债；1.3.7非金融企业债；
     * 1.4票据；
     * 1.5股票（权）/基金；
     * 1.6保单；
     * 1.7资产管理产品；
     * 1.8其他金融质押品；
     * 2.应收账款类；
     * 3.1居住用房地产；
     * 3.2经营性房地产；
     * 3.3居住用房地产建设用地使用权；
     * 3.4经营性房地产建设用地使用权；
     * 3.5房产类在建工程；
     * 3.6其他房地产类押品；
     * 4.1存货、仓单和提单；
     * 4.2机器设备；
     * 4.3交通运输设备；
     * 4.4资源资产；
     * 4.5知识产权；
     * 4.6其他-银行自定义。
     * 无法以枚举类型填写的，填报为“4.6其他-XX”，其中“XX”为银行自定义类型。
     *
     * @param pledgeType 信贷抵押物类型
     * @return east押品类型
     */
    public static String pledgeTypeExch(String pledgeType) {
        switch (pledgeType) {
            case "1":  //	1.1、现金及其等价物
                return "1.1现金及其等价物";
            case "3":  //	4.1、存货、仓单和提单
                return "4.1存货、仓单和提单";
            case "10":  //	1.2、贵金属
                return "1.2贵金属";
            case "11":  //	1.3.1、国债
                return "1.3.1国债";
            case "12":  //	1.3.2、地方政府债
                return "1.3.2地方政府债";
            case "13":  //	1.3.3、央票
                return "1.3.3央票";
            case "14":  //	1.3.4、政府机构债券
                return "1.3.4政府机构债券";
            case "15":  //	1.3.5、政策性金融债
                return "1.3.5政策性金融债";
            case "16":  //	1.3.6、商业性金融债
                return "1.3.6商业性金融债";
            case "17":  //	1.3.8、其他债券
                return "4.6其他-其他债券";
            case "18":  //	1.4、票据
                return "1.4票据";
            case "19":  //	1.6、保单
                return "1.6保单";
            case "2":  //	1.7、资产管理产品（不含公募基金）
                return "1.7资产管理产品";
            case "20":  //	1.8、其他金融质押品
                return "4.6其他-其他金融质押品";
            case "21":  //	2.1、普通应收账款
            case "22":  //	2.2、各类收费权
            case "23":  //	2.3、其他应收账款
                return "2.应收账款类";
            case "24":  //	3.1、居住用房地产
                return "3.1居住用房地产";
            case "25":  //	3.2、经营性房地产
                return "3.2经营性房地产";
            case "26":  //	3.3、居住用房地产建设用地使用权
                return "3.3居住用房地产建设用地使用权";
            case "27":  //	3.4、经营性房地产建设用地使用权
                return "3.4经营性房地产建设用地使用权";
            case "28":  //	3.5、房产类在建工程
                return "3.5房产类在建工程";
            case "29":  //	3.6、其他房地产类押品
                return "3.6其他房地产类押品";
            case "7":  //	4.3.1、车辆
            case "8":  //	4.3.2、飞行器
            case "9":  //	4.3.3、船舶
            case "30":  //	4.3.4、其他交通运输设备
                return "4.3交通运输设备";
            case "31":  //	4.5.1、专利权
            case "32":  //	4.5.2、商标权
            case "33":  //	4.5.4、其他知识产权
            case "34":  //	4.5.3、著作权
                return "4.5知识产权";
            case "4":  //	4.2、机器设备
                return "4.2机器设备";
            case "5":  //	4.4、资源资产
                return "4.4资源资产";
            case "6":  //	4.6、其他以上未包括的押品
            default:
                return "4.6其他-其他押品";
        }
    }

    /**
     * 柜员身份映射
     *
     * @param tellerType 核心柜员身份
     * @return east柜员身份
     */
    public static String tellerTypeExch(String tellerType) {
        switch (tellerType) {
            case "0":
                return "一般全能柜员";
            case "1":
                return "全能综合柜员";
            case "2":
                return "储蓄综合柜员";
            case "3":
                return "对公综合柜员";
            case "4":
                return "一般储蓄柜员";
            case "5":
                return "一般对公柜员";
            case "6":
                return "主管会计";
            case "7":
                return "会计柜员";
            case "8":
                return "联行柜员";
            case "9":
                return "票交柜员";
            case "a":
                return "主管出纳";
            case "b":
                return "出纳柜员";
            case "c":
                return "库管员";
            case "f":
                return "虚拟柜员";
            case "g":
                return "后台维护员";
            case "u":
                return "业务管理员(主办会计)";
            case "v":
                return "稽核员";
            case "w":
                return "信贷员";
            case "x":
                return "中心日终操作员";
            case "y":
                return "超级管理员";
            case "z":
                return "柜员卡发卡柜员";
            default:
                return "其他-其他柜员";
        }
    }

    /**
     * 柜员状态映射
     *
     * @param tellerStatus 核心柜员状态 0-正常 1-离职 2-值班 3-休假
     * @return east柜员状态
     */
    public static String tellerStatusExch(String tellerStatus) {
        if ("1".equals(tellerStatus)) {
            return "离岗";
        } else {
            return "在岗";
        }
    }

    /**
     * 信贷证件类型转换
     *
     * @param zjlx 证件类型
     * @return s
     */
    public static String creditZjlx(String zjlx) {
        if (zjlx == null) {
            return "";
        }
        switch (zjlx) {
            case "0":
                return "居民身份证";
            case "1":
                return "驾驶证";
            case "2":
                return "军人证";
            case "5":
                return "社会保障号";
            case "6":
                return "护照";
            case "7":
                return "回乡证";
            case "8":
                return "户口簿";
            case "9":
                return "警察证";
            default:
                return "其他";
        }
    }

    /**
     * 信贷贷款品种转换
     *
     * @param dkpz 贷款品种
     * @return
     */
    public static String creditDkpz(String dkpz) {
        switch (dkpz) {
            case "0":
                return "抵押";
            case "1":
                return "保证";
            case "2":
                return "质押";
            case "3":
                return "信用";
            case "4":
                return "按揭";
            case "5":
                return "小额信用";
            case "6":
                return "限额信用";
            default:
                return "其他";
        }
    }

    /**
     * 五级分类转换 正常，关注，次级，可疑，损失
     *
     * @return east五级分类
     */
    public static String fiveCate(String fiveCate) {
        switch (fiveCate) {
            case "2":
                return "关注";
            case "3":
                return "次级";
            case "4":
                return "可疑";
            case "5":
                return "损失";
            default:
                return "正常";
        }
    }

    /**
     * 按月，按季，按半年，按年，利随本清，分期付息一次还本，其他-银行自定义
     *
     * @param repayMode   还款方式
     * @param repayFreqcy 还款频率
     * @return east还款方式
     */
    public static String repayFreqcy(String repayMode, String repayFreqcy) {
        if ("03".equals(repayFreqcy)) {
            return "按月";
        }
        if ("04".equals(repayFreqcy)) {
            return "按季";
        }
        if ("05".equals(repayFreqcy)) {
            return "按半年";
        }
        if ("06".equals(repayFreqcy)) {
            return "按年";
        }
        if ("21".equals(repayMode)) {
            return "利随本清";
        }
        return "其他-其他方式";
    }

    /**
     * 核心的产品核算方式映射系统的个人存款账户类型
     *
     * @param type 核算方式：核心 0-普通活期、1-双整、2-零整、3-整零、4-存本、5-定活、6-旧通知、
     *             7-结算活期、8-新通知、a-大额可转让 b-普通大额式 d-教育储蓄存款
     *             e-大额外币储蓄存款 f-协议定期存款 g-卡系统、h-按天计息定期
     * @return 个人存款账户类型 east
     * 个人活期存款，个人定期存款，定活两便存款，个人通知存款，个人协议存款，个人协定存款，个人保证金存款，个人结构性存款（不含保本理财），个人其他存款
     */
    public static String depositeAccType(String type) {
        switch (type) {
            case "0":// 普通活期
                return "个人活期存款";
            case "1":// 双整
            case "2":// 零整
            case "3":// 整零
            case "4":// 存本
                return "个人定期存款";
            case "5":// 定活
                return "定活两便存款";
            case "6":// 旧通知
                return "个人通知存款";
            case "7":// 结算活期
                return "个人活期存款";
            case "8":// 新通知
                return "个人通知存款";
            case "a":// 大额可转让
            case "b":// 普通大额式
            case "d":// 教育储蓄存款
            case "e":// 大额外币储蓄存款
                return "个人其他存款";
            case "f":// 协议定期存款
                return "个人协定存款";
            case "g":// 卡系统
                return "个人活期存款";
            case "h":// 按天计息定期
                return "个人定期存款";
            default:
                return "个人其他存款";
        }

    }

    /**
     * 信贷担保类型
     * 核心：2-质押 0-抵押 1-保证 除上述情况外（行内没有组合的方式）4-信用/无担保
     * EAST：抵押，质押，保证，信用，混合，其他-银行自定义
     *
     * @return east担保类型
     */
    public static String dblx(String dblx) {
        switch (dblx) {
            case "0":
                return "抵押";
            case "1":
                return "保证";
            case "2":
                return "质押";
            default:
                return "信用";
        }
    }

    /**
     * 核心的产品核算方式映射系统的个人存款账户类型
     *
     * @param type 核算方式：核心 0-普通活期、1-双整、2-零整、3-整零、4-存本、5-定活、6-旧通知、
     *             7-结算活期、8-新通知、a-大额可转让 b-普通大额式 d-教育储蓄存款
     *             e-大额外币储蓄存款 f-协议定期存款 g-卡系统、h-按天计息定期
     * @return 个人存款账户类型 east
     * 单位活期存款，单位定期存款，单位通知存款，单位协议存款，单位协定存款，单位保证金存款，单位结构性存款（不含保本理财），单位其他存款，同业存放款、保险公司存放款、社会保障基金，其他-银行自定义
     */
    public static String depositeAccTypeEnt(String type) {
        switch (type) {
            case "0":// 普通活期
            case "7":// 结算活期
            case "g":// 卡系统
                return "单位活期存款";
            case "1":// 双整
            case "2":// 零整
            case "3":// 整零
            case "4":// 存本
            case "5":// 定活
            case "h":// 按天计息定期
                return "单位定期存款";
            case "6":// 旧通知
            case "8":// 新通知
                return "单位通知存款";
            case "a":// 大额可转让
            case "b":// 普通大额式
            case "d":// 教育储蓄存款
            case "e":// 大额外币储蓄存款
                return "单位其他存款";
            case "f":// 协议定期存款
                return "单位协议存款";
            default:
                return "单位其他存款";
        }
    }

    /**
     * 借记卡卡片状态
     * 核心：0—正常、1—销户申请、2—可销卡、3—销卡
     * EAST：未激活，正常，注销，冻结，睡眠，挂失，其他-银行自定义
     *
     * @return east担保类型
     */
    public static String kpzt(String kpzt) {
        switch (kpzt) {
            case "1":
                return "其他-销户申请";
            case "2":
                return "其他-可销卡";
            case "3":
                return "销卡";
            default:
                return "正常";
        }
    }

    public static String sktCorpClassExch(String stkClass) {
        if (stkClass == null) {
            return "S9529";
        }
        switch (stkClass) {
            case "1A":// 各类专业-技术人员
                return "I6599";
            case "1B":// 国家机关-党群组织-企事业单位的负责人
                return "S9529";
            case "1C":// 办事人员和有关人员
                return "S9529";
            case "1D":// 商业工作人员
                return "S9529";
            case "1E":// 服务性工作人员
                return "O8290";
            case "1F":// 农林牧渔劳动者
                return "A0190";// 其他农业
            case "1G":// 生产工作-运输工作和部分体力劳动者
                return "G5449";
            case "1H":// 不便分类的其他劳动者
            default:
                return "S9529";
        }
    }

    /**
     * 民族
     *
     * @param nat
     * @return
     */
    public static String natExch(String nat) {
        if (StringUtils.isBlank(nat)) {
            return "汉族";
        }
        switch (nat) {
            case "01":
                return "汉族";
            case "02":
                return "蒙古族";
            case "03":
                return "回族";
            case "04":
                return "藏族";
            case "05":
                return "维吾尔族";
            case "06":
                return "苗族";
            case "07":
                return "彝族";
            case "08":
                return "壮族";
            case "09":
                return "布依族";
            case "10":
                return "朝鲜族";
            case "11":
                return "满族";
            case "12":
                return "侗族";
            case "13":
                return "瑶族";
            case "14":
                return "白族";
            case "15":
                return "土家族";
            case "16":
                return "哈尼族";
            case "17":
                return "哈萨克族";
            case "18":
                return "傣族";
            case "19":
                return "黎族";
            case "20":
                return "傈僳族";
            case "21":
                return "佤族";
            case "22":
                return "畲族";
            case "23":
                return "高山族";
            case "24":
                return "拉祜族";
            case "25":
                return "水族";
            case "26":
                return "东乡族";
            case "27":
                return "纳西族";
            case "28":
                return "景颇族";
            case "29":
                return "柯尔克孜族";
            case "30":
                return "土族";
            case "31":
                return "达斡尔族";
            case "32":
                return "仫佬族";
            case "33":
                return "羌族";
            case "34":
                return "布朗族";
            case "35":
                return "撒拉族";
            case "36":
                return "毛南族";
            case "37":
                return "仡佬族";
            case "38":
                return "锡伯族";
            case "39":
                return "阿昌族";
            case "40":
                return "普米族";
            case "41":
                return "塔吉克族";
            case "42":
                return "怒族";
            case "43":
                return "乌孜别克族";
            case "44":
                return "俄罗斯族";
            case "45":
                return "鄂温克族";
            case "46":
                return "德昂族";
            case "47":
                return "保安族";
            case "48":
                return "裕固族";
            case "49":
                return "京族";
            case "50":
                return "塔塔尔族";
            case "51":
                return "独龙族";
            case "52":
                return "鄂伦春族";
            case "53":
                return "赫哲族";
            case "54":
                return "门巴族";
            case "55":
                return "珞巴族";
            case "56":
                return "基诺族";
            case "57":
                return "其它";
            case "58":
                return "外国血统中国籍人士";
            default:
                return "汉族";
        }
    }

    /**
     * @param eduLvl
     * @return
     */
    public static String eduLvlExch(String eduLvl) {
        if (eduLvl == null) {
            return "其他";
        }
        switch (eduLvl) {
            case "10":
                return "研究生教育";
            case "20":
                return "大学本科";
            case "30":
                return "专科教育";
            case "40":
                return "中等职业教育";
            case "60":
                return "普通高级中学教育";
            case "70":
                return "初级中学教育";
            case "80":
                return "小学教育";
            default:
                return "其他";// 其他
        }
    }

    public static String marrigeExch(String marr) {
        if (StringUtils.isBlank(marr)) {
            return "否";
        }
        switch (marr) {
            case "20":
            case "21":
            case "22":
            case "23":
                return "是";
            //            case "10":
            //            case "30":
            //            case "40":
            //            case "90":
            //            case "91":
            //            case "99":
            //                return "否";
            default:
                return "否";
        }
    }

    /**
     * east:国有企业、民营企业、政府机关、事业单位、社会团体、境外机构、其他-
     *
     * @param cpnType
     * @return
     */
    public static String compTypeExch(String cpnType) {
        if (StringUtils.isBlank(cpnType)) {
            return "其他";
        }
        switch (cpnType) {
            case "10":// 机关、事业单位
                return "事业单位";
            case "20":// 国有企业
                return "国有企业";
            case "30":// 外资企业
                return "其他-外资企业";
            case "40":// 个体、私营企业
                return "民营企业";
            case "50":// 其他（包括三资企业、民营企业、民间团体等）
                return "民营企业";
            case "99":// 未知
                return "其他";
            default:
                return "其他";
        }
    }

    /**
     * 职业
     *
     * @param occu
     * @return
     */
    public static String occupExch(String occu) {
        if (StringUtils.isBlank(occu)) {
            return "未知";
        }
        switch (occu) {
            case "0":
                return "国家机关、党群组织、企业、事业单位负责人";
            case "1":
                return "专业技术人员";
            case "3":
                return "办事人员和有关人员";
            case "4":
                return "商业、服务业人员";
            case "5":
                return "农、林、牧、渔、水利业生产人员";
            case "6":
                return "生产、运输设备操作人员及有关人员";
            case "X":
                return "军人";
            case "Y":
                return "不便分类的其他从业人员";
            case "Z":
                return "未知";
            default:
                return "未知";
        }
    }

    /**
     * 职务
     *
     * @param title
     * @return
     */
    public static String titleExch(String title) {
        if (StringUtils.isBlank(title)) {
            return "其他";
        }
        switch (title) {
            case "1":
                return "高级领导";
            case "2":
                return "中级领导";
            case "3":
                return "一般员工";
            default:
                return "其他-其他职务";
        }
    }


    /**
     * 贷款投向行业映射
     * <p>
     * 核心：001-农、林、牧、渔业 002-采矿业 003-制造业 004-电力燃气及水的生产和工业 005-建筑业
     * 006-交通运输、仓储和邮政业 007-信息传输、计算机服务和软件业 008-批发和零售业
     * 009-住宿和餐饮业 010-金融业 011-房地产业 012-租赁和商务服务业
     * 013-科学研究、技术服务和地质勘探 014-水利、环境和公共设施管理
     * 015-居民服务和其他服务业 016-教育 017-卫生、社会保障和社会福利
     * 018-文化、体育和娱乐业 019-公共管理和社会组织 020-国际组织 021-其他
     * <p>
     * EAST：与1104报表G01_VII《贷款分行业情况表》口径一致，使用以下枚举值填报（序号+名称）：
     * 2.1农、林、牧、渔业；2.2采矿业；2.3制造业；2.4电力、热力、燃气及水的生产和供应业；2.5建筑业；2.6批发和零售业；
     * 2.7交通运输、仓储和邮政业；2.8住宿和餐饮业；2.9信息传输、软件和信息技术服务业；2.10金融业；2.11房地产业；
     * 2.12租赁和商务服务业；2.13科学研究和技术服务业；2.14水利、环境和公共设施管理业；2.15居民服务、修理和其他服务业；
     * 2.16教育；2.17卫生和社会工作；2.18文化、体育和娱乐业；2.19公共管理、社会保障和社会组织；2.20国际组织；
     * 2.21.1个人贷款-信用卡；2.21.2个人贷款-汽车；2.21.3个人贷款-住房按揭贷款；2.21.4个人贷款-其他；2.22买断式转贴现；
     * 2.23买断其他票据类资产；3.对境外贷款。
     *
     * @return east担保类型
     */
    public static String dktxhy(String dktxhy) {
        if (dktxhy == null) {
            return "";
        }
        String[] tmp = dktxhy.split("\\.", -1);
        if (tmp.length == 3) {
            if ("2".equals(tmp[0])) {
                String second = tmp[1];
                switch (second) {
                    case "1":
                        return "2.1农、林、牧、渔业";
                    case "2":
                        return "2.2采矿业";
                    case "3":
                        return "2.3制造业";
                    case "4":
                        return "2.4电力、热力、燃气及水的生产和供应业";
                    case "5":
                        return "2.5建筑业";
                    case "6":
                        return "2.6批发和零售业";
                    case "7":
                        return "2.7交通运输、仓储和邮政业";
                    case "8":
                        return "2.8住宿和餐饮业";
                    case "9":
                        return "2.9信息传输、软件和信息技术服务业";
                    case "10":
                        return "2.10金融业";
                    case "11":
                        return "2.11房地产业";
                    case "12":
                        return "2.12租赁和商务服务业";
                    case "13":
                        return "2.13科学研究和技术服务业";
                    case "14":
                        return "2.14水利、环境和公共设施管理业";
                    case "15":
                        return "2.15居民服务、修理和其他服务业";
                    case "16":
                        return "2.16教育";
                    case "17":
                        return "2.17卫生和社会工作";
                    case "18":
                        return "2.18文化、体育和娱乐业";
                    case "19":
                        return "2.19公共管理、社会保障和社会组织";
                    case "20":
                        return "2.20国际组织";
                    case "21": {
                        String[] tmp2 = dktxhy.split("、");
                        if (tmp2.length > 0) {
                            switch (tmp2[0]) {
                                case "2.21.1":
                                    return "2.21.1个人贷款-信用卡";
                                case "2.21.2":
                                    return "2.21.2个人贷款-汽车";
                                case "2.21.3":
                                    return "2.21.3个人贷款-住房按揭贷款";
                                case "2.21.4":
                                    return "2.21.4个人贷款-其他";
                            }
                        }
                    }
                    case "22":
                        return "2.22买断式转贴现";
                    case "23":
                        return "2.23买断其他票据类资产";
                }
            } else if ("3".equals(tmp[0])) {
                return "3.对境外贷款";
            }
        }
        return "2.21.4个人贷款-其他";
        //        switch (dktxhy) {
        //            case "001":
        //                return "2.1农、林、牧、渔业";
        //            case "002":
        //                return "2.2采矿业";
        //            case "003":
        //                return "2.3制造业";
        //            case "004":
        //                return "2.4电力、热力、燃气及水的生产和供应业";
        //            case "005":
        //                return "2.5建筑业";
        //            case "006":
        //                return "2.7交通运输、仓储和邮政业";
        //            case "007":
        //                return "2.9信息传输、软件和信息技术服务业";
        //            case "008":
        //                return "2.6批发和零售业";
        //            case "009":
        //                return "2.8住宿和餐饮业";
        //            case "010":
        //                return "2.10金融业";
        //            case "011":
        //                return "2.11房地产业";
        //            case "012":
        //                return "2.12租赁和商务服务业";
        //            case "013":
        //                return "2.13科学研究和技术服务业";
        //            case "014":
        //                return "2.14水利、环境和公共设施管理业";
        //            case "015":
        //                return "2.15居民服务、修理和其他服务业";
        //            case "016":
        //                return "2.16教育";
        //            case "017":
        //                return "2.17卫生和社会工作";
        //            case "018":
        //                return "2.18文化、体育和娱乐业";
        //            case "019":
        //                return "2.19公共管理、社会保障和社会组织";
        //            case "020":
        //                return "2.20国际组织";
        //            default:
        //                return "2.21.4个人贷款-其他";
        //        }
    }

    /**
     * 股东或关联方类型
     * <p>
     * 核心：
     * <p>
     * EAST：自然人（非中国公民），境内非金融机构、境内银行业金融机构、境内非银行金融机构、境外银行，金融产品，纯国有企业，
     * 国有控股企业，国有参股企业，民营企业，政府机关，事业单位，社会团体，中外合资企业、外商独资企业、境外机构，其他-银行自定义。
     * 无法以枚举类型填报的，以“其他-XX”填报，其中“XX”为银行自定义类型
     *
     * @return east担保类型
     */
    public static String gdhglflx(String gdhglflx) {
        switch (gdhglflx) {
            case "001":
                return "其他-公司";
            case "002":
                return "其他-合伙企业";
            case "003":
                return "其他-信托公司";
            case "004":
                return "其他-基金公司";
            case "005":
                return "其他-个体工商户";
            case "006":
                return "其他-个人独资企业";
            case "007":
                return "其他-不具备法人资格的专业服务机构";
            case "008":
                return "其他-经营农林渔牧产业的非公司制农民专业合作组织";
            case "009":
                return "其他-收政府控制的企事业单位";
            case "010":
                return "其他-机关";
            default:
                return "其他-其他单位";
        }
    }

    /**
     * East贷款发放类型
     * 新增，无还本续贷，借新还旧，重组贷款，其他-银行自定义。无法以枚举类型填报的，以“其他-XX”填报，其中“XX”为银行自定义类型。
     *
     * @param jjdkyt 贷款用途
     * @return 贷款发放类型
     */
    public static String dkfflx(String jjdkyt) {
        if (jjdkyt == null) {
            return "新增";
        }
        if (jjdkyt.contains("无还本续贷")) {
            return "无还本续贷";
        }
        //        if (jjdkyt.contains("借新还旧")) {
        //            return "借新还旧";
        //        }
        if (jjdkyt.contains("重组贷款") || jjdkyt.contains("贷款重组")) {
            return "贷款重组";
        }
        return "新增";
    }

    public static String xdywzl(String xdywzl) {
        if (xdywzl == null) {
            return "其他-其他贷款";
        }
        if (xdywzl.contains("经营性")) {
            return "个人经营性贷款";
        }
        if (xdywzl.contains("消费性")) {
            return "消费贷款";
        }
        return "其他-" + xdywzl;
    }

    /**
     * 是否上市公司 1-是 0-否
     *
     * @param flag
     * @return
     */
    public static String corpIPOFlag(String flag) {
        if ("1".equals(flag)) {
            return "是";
        } else {
            return "否";
        }
    }

    /**
     * 核心交易码映射
     * 0411    贷款发放      贷款发放
     * 0412    贷款收回      贷款还本  贷款还息
     * 0420    贷款多笔还息  贷款还息
     * 0429    贷款单笔还息   贷款还息
     * 0431    贷款转挂      其他-贷款转挂
     * 0451    贷款核销      其他-贷款核销
     * 6141    贷款逾期处理  批量交易
     * 6143    贷款到期处理  批量交易
     * 6165    逾期结转呆滞  批量交易
     * 6188    贷款利息计算  结息
     * 9026    公共冲账交易  其他-公共冲账交易
     *
     * @param tranType 交易码
     * @return 交易类型
     */
    public static String tranType(String tranType) {
        switch (tranType) {
            case "6105":
            case "6109":
            case "6113":
            case "6121":
            case "6131":
            case "6133":
            case "6136":
            case "6137":
            case "6141":
            case "6143":
            case "6149":
            case "6165":
            case "6183":
            case "6188":
            case "6134":
            case "6180":
                return "批量交易";
            case "0101":
            case "0111":
            case "0301":
            case "0311":
            case "0341":
            case "0351":
            case "2505":
            case "5780":
            case "0211":
                return "存现";
            case "0102":
            case "0112":
            case "0302":
            case "0303":
            case "0343":
            case "0352":
            case "2507":
            case "5785":
            case "0212":
            case "0214":
                return "取现";
            case "0115":
            case "0139":
            case "5730":
            case "5731":
            case "5771":
            case "5773":
            case "5777":
            case "5778":
            case "2506":
            case "0911":
            case "0914":
            case "1101":
            case "1102":
            case "1613":
            case "4111":
            case "5732":
            case "5772":
            case "5774":
            case "8104":
            case "8121":
            case "9009":
                return "转账";
            case "0131":
            case "0134":
            case "0207":
            case "0331":
            case "0335":
            case "0361":
            case "0362":
            case "1601":
            case "1401":
            case "1402":
            case "1407":
            case "1408":
            case "1617":
            case "4113":
                return "非账务";
            case "0411":
                return "贷款发放";
            case "0412":
                return "贷款还本";
            case "0420":
            case "0429":
                return "贷款还息";
            case "0431":
                return "其他-贷款转挂";
            case "0451":
                return "其他-贷款核销";
            case "0507":
                return "代扣";
            case "0510":
                return "代发";
            case "9026":
                return "其他-公共冲账交易";
            default:
                log.warn("未映射的交易类型:{}", tranType);
                return "其他-其他交易";
        }

    }

    /**
     * 资产负债子类映射
     * <p>
     * 核心：
     * 2-现金及周转金;3-存放中央银行款项;4-专项央行票据;
     * 5-央行专项扶持资金;6-存放同业款项;7-存放联行款项;8-拆放同业;9-拆放金融性公司;10-买入返售资产;11-短期贷款;12-待处理抵债资产;
     * 13-应收账款;14-拨付营运资金;15-其他应收款;16-贴现;17-短期投资;19-待处理流动资产净损失21-预计资产;
     * 25-中长期贷款;26-逾期贷款;27-呆滞贷款;28-呆账贷款;29-减:呆账准备; 31-长期投资;32-固定资产原值;
     * 33-减:累计折旧; 37-固定资产清理;38-在建工程;39-待处理固定资产净损失43-无形资产;44-递延资产;52-短期存款;
     * 53-短期储蓄存款;54-财政性款项;55-向中央银行借款;56-央行拨付专项票据资金57-同业存放款项;59-同业拆入;60-卖出回购资产款;
     * 61-金融性公司拆入;62-应解汇款;63-汇出汇款;64-应付账款;65-拨入营运资金;66-其他应付款;67-应付工资;
     * 68-应付福利费;69-应交税金;70-应缴代扣利息税;71-应付利润;72-预提费用;74-一年内到期的长期负债75-预计负债;
     * 78-长期存款;79-长期储蓄存款;80-保证金;81-发行长期债券;82-长期借款;83-长期应付款;84-待转资产价值;
     * 88-实收资本;89-资本公积;90-盈余公积;91-其中:公益金; 92-一般准备;93-未分配利润;98-代保管证券(面值):
     * 99-质押品:; 100-抵押品:;
     * <p>
     * EAST：
     * 资产类业务子类：1现金，2贵金属，3存放中央银行款项，4存放同业款项，5应收利息，6贷款，7贸易融资，8贴现及买断式转贴现，9其他贷款，10拆放同业，11其他应收款，12投资，13买入返售资产，14长期待摊费用，15固定资产原价，16减：累计折旧，17固定资产净值，18固定资产清理，19在建工程，20无形资产，21抵债资产，22递延所得税资产，23其他资产，24各项资产减值损失准备。
     * 负债类业务子类：25单位存款，26储蓄存款，27向中央银行借款，28同业存放款项，29同业拆入，30卖出回购款项，31汇出汇款，32应解汇款，33存入保证金，34应付利息，35应交税费，36应付职工薪酬，37应付福利费，38应付股利，39其他应付款，40预提费用，41递延收益，42预计负债，43转贷款资金，44应付债券，45其他负债，46递延所得税负债。
     * 所有者权益类业务子类：47实收资本，48资本公积，49盈余公积，50一般风险准备，51信托赔偿准备，52未分配利润，53其他综合收益，54其他权益工具。
     * 损益类业务子类：55利息收入，56利息支出，57手续费及佣金收入，58手续费及佣金支出，59投资损益，60公允价值变动损益，61汇兑损益，62资产处置损益，63其他业务收入，64营业支出，65营业外收入，66营业外支出，67资产减值损失，68所得税，69少数股东损益。
     * 表外业务类别：70承兑汇票，71跟单信用证，72保函，73信用风险仍在银行的销售与购买协议，74其他担保类业务，75可随时无条件撤销的贷款承诺，76不可无条件撤销的贷款承诺，77未使用的信用卡授信额度，78其他承诺，79发行非保本理财产品，80委托贷款，81委托投资，82代理代销业务，83资产托管，84其他金融资产服务业务，85金融衍生品类。
     *
     * @param prgNo 项
     * @return east子类
     */
    public static String mnaccBalshGsywzl(String prgNo) {
        if (prgNo == null) {
            return "";
        }
        switch (prgNo) {
            case "2":
                return "1现金";
            case "3":
                return "3存放中央银行款项";
            case "6":
                return "4存放同业款项";
            case "8":
                return "10拆放同业";
            case "10":
                return "13买入返售资产";
            case "11":
            case "25":
            case "26":
            case "27":
            case "28":
                return "6贷款";
            case "12":
                return "21抵债资产";
            case "13":
            case "15":
                return "11其他应收款";
            case "16":
                return "8贴现及买断式转贴现";
            case "17":
            case "31":
                return "12投资";
            case "29":
                return "24各项资产减值损失准备";
            case "32":
                return "15固定资产原价";
            case "33":
                return "16减：累计折旧";
            case "37":
                return "18固定资产清理";
            case "38":
                return "19在建工程";
            case "43":
                return "20无形资产";
            case "52":
            case "53":
            case "78":
            case "19":
                return "26储蓄存款";
            case "55":
                return "27向中央银行借款";
            case "57":
                return "28同业存放款项";
            case "59":
                return "29同业拆入";
            case "60":
                return "30卖出回购款项";
            case "62":
                return "32应解汇款";
            case "63":
                return "31汇出汇款";
            case "64":
            case "66":
            case "70":
            case "71":
            case "83":
                return "39其他应付款";
            case "67":
                return "36应付职工薪酬";
            case "68":
                return "37应付福利费";
            case "69":
                return "35应交税费";
            case "72":
                return "40预提费用";
            case "74":
                return "45其他负债";
            case "75":
                return "42预计负债";
            case "80":
                return "33存入保证金";
            case "88":
                return "47实收资本";
            case "89":
                return "48资本公积";
            case "90":
                return "49盈余公积";
            case "93":
                return "52未分配利润";
            default:
                return "";
        }
    }

    /**
     * 损益表子类映射
     * 核心
     * 5011利息收入√ 5021金融机构往来收入√ 5111手续费收入√ 5121其他营业收入√ 5211利息支出√ 5221金融机构往来支出× 5311手续费支出√
     * 5321营业费用× 5341其他营业支出√ 5331营业税及附加× 5141投资收益√ 5151营业外收入√ 5361营业外支出√ 5601以前年度损益调整× 5501所得税√
     * <p>
     * East
     * 损益类业务子类：55利息收入，56利息支出，57手续费及佣金收入，58手续费及佣金支出，59投资损益，60公允价值变动损益，61汇兑损益，
     * 62资产处置损益，63其他业务收入，64营业支出，65营业外收入，66营业外支出，67资产减值损失，68所得税，69少数股东损益。
     *
     * @param prgNo 项
     * @return east子类
     */
    public static String ploshGsywzl(String prgNo) {
        if (prgNo == null) {
            return "";
        }
        switch (prgNo) {
            case "5011":
                return "55利息收入";
            case "5021":
            case "5121":
                return "63其他业务收入";
            case "5111":
                return "57手续费及佣金收入";
            case "5211":
                return "56利息支出";
            case "5311":
                return "58手续费及佣金支出";
            case "5341":
                return "64营业支出";
            case "5141":
                return "59投资损益";
            case "5151":
                return "65营业外收入";
            case "5361":
                return "66营业外支出";
            case "5501":
                return "68所得税";
            default:
                return "";
        }
    }

    /**
     * 非自然人受益所有人信息受益类型与east关系类型映射
     * 核心:1-直接或者间接拥有超过25%公司股权或者表决权的自然人;2-通过人事、财务等其它方式对公司进行控制的自然人;3-公司高级管理人员
     * east:该企业的主要投资者个人及与其关系密切的家庭成员;该企业或其母公司的关键管理人员及与其关系密切的家庭成员
     *
     * @param hostType
     * @return
     */
    public static String relationType(String hostType) {
        if ("1".equals(hostType)) {
            return "该企业的主要投资者个人及与其关系密切的家庭成员";
        } else if ("2".equals(hostType) || "3".equals(hostType)) {
            return "该企业或其母公司的关键管理人员及与其关系密切的家庭成员";
        } else {
            return "其他";
        }
    }

    /**
     * 柜面，ATM，VTM，POS，网银，手机银行，银联交易，第三方支付-银行自定义
     */
    public static String eastJyqd(String summ, String tlr) {
        if (("普通汇入".equals(summ) || "工行网银汇入".equals(summ)) && "000128".equals(tlr)) {
            return "其他-银银通";
        }
        if ("网银转账".equals(summ)) {
            return "网银";
        } else {
            return "柜面";
        }
    }
}
