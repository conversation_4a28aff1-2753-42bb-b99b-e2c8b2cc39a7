package io.ibs.modules.east.common;

import cn.hutool.core.date.DateUtil;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import java.util.List;

/**
 * Created by AileYoung on 2024/3/5.
 */
public class FileUtils {
    // 行分隔符：数据文件的一行数据对应一条数据库记录，各行之间分隔符为UNIX样式的换行符（ASCII码0x0A）
    public static char NEW_LINE_CHAR = 0x0A;
    // 数据项分隔符：数据项之间以^A（SOH，ASCII码0x01）进行分隔，每行末尾不需要添加数据项分隔符
    public static char DATA_SEPARATOR_CHAR = 0x01;
    // 数据项内子项分隔符：单个数据项中需要填报多个子项内容时，子项内容之间使用英文半角分号“;”
    public static char DATA_CHILD_SEPARATOR_CHAR = 0x3B;

    /**
     * 将字符串列表按行写成文件
     * 请先检查路径，使用 TranUtil.judeDirExists(String dir);
     *
     * @param filePath    文件路径
     * @param list        字符串列表（每行明细）
     * @param charsetName 编码格式
     * @param isEnd       分批写文件时是否结束标志 true - 是 是的话就说明是最后一批，则最后一笔不进行换行
     * @param isAppend    追加标志 true - 追加
     * @throws Exception 各种异常
     */
    public static void writeFileToLineByStringList(String filePath, List<String> list, Charset charsetName, boolean isEnd, boolean isAppend) throws Exception {
        FileOutputStream fileOutputStream = new FileOutputStream(filePath, isAppend);
        OutputStreamWriter writer = new OutputStreamWriter(fileOutputStream, charsetName);
        BufferedWriter bufferedWriter = new BufferedWriter(writer);
        try {
            //按行写文件
            for (int i = 0; i < list.size(); i++) {
                bufferedWriter.write(list.get(i));
                //不是最后一批 或 不是列表的最后一条，就要换行
                if (!isEnd || i != (list.size() - 1)) {
                    bufferedWriter.write(NEW_LINE_CHAR);
                }
            }
        } finally {
            bufferedWriter.close();
            writer.close();
            fileOutputStream.close();
        }
    }

    public static String exportExcelName(EastTableInfo eastTableInfo){
        return eastTableInfo.getName() + "-" + DateUtil.today();
    }
}
