package io.ibs.modules.east.common;

import cn.hutool.crypto.digest.SM3;
import io.ibs.common.exception.RenException;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * 隐私保护工具类
 * Created by AileYoung on 2024/3/5.
 */
public final class PrivacyProtectionUtils {

    // 涉密单位（含军队、武装警察部队）：客户统一编号必填，客户名称均填报为“*********”。证件号码、地址、电话填报为空。
    public static final String CLASSIFIED_UNIT_NAME = "*********";
    private static final SM3 sm3 = SM3.create();

    /**
     * （1）取个人姓名的第一个汉字（如果姓名是外文，取其UTF-8编码的前3个字节），后接身份证件号码（证件号码中的英文统一转换成大写），
     * 形成一个字符串（UTF-8编码，以居民身份证号码为例，为21字节）；
     * （2）取上述字符串的SM3杂凑值，为64字节的字符串（小写形式表示）。
     * SM3是GB/T 32905-2016信息安全技术SM3密码杂凑算法中定义的密码杂凑算法；
     * （3）取身份证件号码UTF-8编码的前6个字节，后接上述SM3杂凑值，得到70字节的字符串，为脱敏的最终值。
     *
     * @param identityNo 证件号码
     * @param username   用户姓名
     * @return 脱敏数据
     */
    public static String identityNo(String identityNo, String username) {
        if (StringUtils.isBlank(identityNo) || StringUtils.isBlank(username)) {
            throw new RenException("证件号码[" + identityNo + "]或客户姓名[" + username + "]不能为空!");
        } else {
            if (identityNo.length() < 6) {
                throw new RenException("证件号码[" + identityNo + "]长度不符!");
            }
            if (username.getBytes(StandardCharsets.UTF_8).length < 3) {
                throw new RenException("客户姓名[" + username + "]长度不符!");
            }
        }
        identityNo = identityNo.toUpperCase();
        byte[] usernameBytes = username.getBytes(StandardCharsets.UTF_8);
        byte[] identityNoBytes = identityNo.getBytes(StandardCharsets.UTF_8);
        byte[] nameBytes = new byte[3];
        System.arraycopy(usernameBytes, 0, nameBytes, 0, 3);
        // 取个人姓名的第一个汉字（如果姓名是外文，取其UTF-8编码的前3个字节），后接身份证件号码（证件号码中的英文统一转换成大写），形成一个字符串
        String oriIdentityNo = new String(nameBytes, StandardCharsets.UTF_8) + identityNo;
        // 取上述字符串的SM3杂凑值
        String smsResult = sm3.digestHex(oriIdentityNo);
        // 取身份证件号码UTF-8编码的前6个字节
        byte[] result = new byte[6];
        System.arraycopy(identityNoBytes, 0, result, 0, 6);
        // 后接上述SM3杂凑值，得到70字节的字符串，为脱敏的最终值
        return new String(result, StandardCharsets.UTF_8) + smsResult;
    }

    /**
     * 电话号码：
     * 变形输出=SM3(电话号码全文UTF-8编码)（64字符，英文按小写输出）。银行机构联系电话、机构负责人联系电话、员工联系电话不做变形
     *
     * @param phone 电话号码
     * @return 脱敏数据
     */
    public static String phone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return "";
        }
        return sm3.digestHex(phone);
    }

    /**
     * 个人客户姓名：
     * 个人客户姓名做有限暴露方式的脱敏处理，对于中文姓名只保留中文姓名最后一个汉字，对于英文姓名只保留最后三个英文字符，
     * 对公客户名称不变形。银行机构员工、业务办理人员、自然人股东和单位负责人姓名不变形
     *
     * @param username 客户姓名
     * @return 脱敏数据
     */
    public static String username(String username) {
        if (StringUtils.isBlank(username)) {
            return "";
        }
        byte[] usernameBytes = username.getBytes(StandardCharsets.UTF_8);
        if (usernameBytes.length < 3) {
            throw new RenException("客户姓名[" + username + "]长度不符!");
        }
        byte[] result = new byte[3];
        System.arraycopy(usernameBytes, usernameBytes.length - 3, result, 0, 3);
        return new String(result, StandardCharsets.UTF_8);
    }

}
