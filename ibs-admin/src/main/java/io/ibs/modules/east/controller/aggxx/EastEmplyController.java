package io.ibs.modules.east.controller.aggxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.aggxx.EastEmplyDTO;
import io.ibs.modules.east.excel.aggxx.EastEmplyExcel;
import io.ibs.modules.east.service.aggxx.EastEmplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
* 员工表
*
* <AUTHOR>  
* @since 1.0 2024-03-12
*/
@RestController
@RequestMapping("east/eastemply")
@Api(tags="员工表")
public class EastEmplyController {
    @Autowired
    private EastEmplyService eastEmplyService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastemply:page")
    public Result<PageData<EastEmplyDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastEmplyDTO> page = eastEmplyService.page(params);

        return new Result<PageData<EastEmplyDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastemply:info")
    public Result<EastEmplyDTO> get(@PathVariable("id") Long id){
        EastEmplyDTO data = eastEmplyService.get(id);

        return new Result<EastEmplyDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastemply:save")
    public Result save(@RequestBody EastEmplyDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastEmplyService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastemply:update")
    public Result update(@RequestBody EastEmplyDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastEmplyService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastemply:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastEmplyService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastemply:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastEmplyDTO> list = eastEmplyService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_102), "员工表", list, EastEmplyExcel.class);
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    @LogOperation("导入")
    @RequiresPermissions("east:eastemply:import")
    public Result importExcel(@RequestBody Map<String, Object> params){
        String filePath = Optional.ofNullable((String)params.get("filePath")).orElseGet(String::new);//文件路径
        if (StringUtils.isBlank(filePath)){
            return new Result().error("文件上传异常!");
        }
        String cjrq = (String) params.get("cjrq");
        eastEmplyService.importExcel(filePath, cjrq);
        return new Result();
    }

}