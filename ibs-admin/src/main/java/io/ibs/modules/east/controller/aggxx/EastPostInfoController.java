package io.ibs.modules.east.controller.aggxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.aggxx.EastPostInfoDTO;
import io.ibs.modules.east.excel.aggxx.EastPostInfoExcel;
import io.ibs.modules.east.service.aggxx.EastPostInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
* 岗位信息表
*
* @<NAME_EMAIL>
* @since 1.0 2024-03-13
*/
@RestController
@RequestMapping("east/eastpostinfo")
@Api(tags="岗位信息表")
public class EastPostInfoController {
    @Autowired
    private EastPostInfoService eastPostInfoService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastpostinfo:page")
    public Result<PageData<EastPostInfoDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastPostInfoDTO> page = eastPostInfoService.page(params);

        return new Result<PageData<EastPostInfoDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastpostinfo:info")
    public Result<EastPostInfoDTO> get(@PathVariable("id") Long id){
        EastPostInfoDTO data = eastPostInfoService.get(id);

        return new Result<EastPostInfoDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastpostinfo:save")
    public Result save(@RequestBody EastPostInfoDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastPostInfoService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastpostinfo:update")
    public Result update(@RequestBody EastPostInfoDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastPostInfoService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastpostinfo:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastPostInfoService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastpostinfo:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastPostInfoDTO> list = eastPostInfoService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_104), "岗位信息表", list, EastPostInfoExcel.class);
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    @LogOperation("导入")
    @RequiresPermissions("east:eastpostinfo:import")
    public Result importExcel(@RequestBody Map<String, Object> params){
        String filePath = Optional.ofNullable((String)params.get("filePath")).orElseGet(String::new);//文件路径
        if (StringUtils.isBlank(filePath)){
            return new Result().error("文件上传异常!");
        }
        String cjrq = (String) params.get("cjrq");
        eastPostInfoService.importExcel(filePath, cjrq);
        return new Result();
    }
}