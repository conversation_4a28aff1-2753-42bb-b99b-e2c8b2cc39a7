package io.ibs.modules.east.controller.aggxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.aggxx.EastShareholderDTO;
import io.ibs.modules.east.excel.aggxx.EastShareholderExcel;
import io.ibs.modules.east.service.aggxx.EastShareholderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
* 股东及关联方信息表
*
* <AUTHOR> 
* @since 1.0 2024-03-12
*/
@RestController
@RequestMapping("east/eastshareholder")
@Api(tags="股东及关联方信息表")
public class EastShareholderController {
    @Autowired
    private EastShareholderService eastShareholderService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastshareholder:page")
    public Result<PageData<EastShareholderDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastShareholderDTO> page = eastShareholderService.page(params);

        return new Result<PageData<EastShareholderDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastshareholder:info")
    public Result<EastShareholderDTO> get(@PathVariable("id") Long id){
        EastShareholderDTO data = eastShareholderService.get(id);

        return new Result<EastShareholderDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastshareholder:save")
    public Result save(@RequestBody EastShareholderDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastShareholderService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastshareholder:update")
    public Result update(@RequestBody EastShareholderDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastShareholderService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastshareholder:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastShareholderService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastshareholder:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastShareholderDTO> list = eastShareholderService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_106), "股东及关联方信息表", list, EastShareholderExcel.class);
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    @LogOperation("导入")
    @RequiresPermissions("east:eastshareholder:import")
    public Result importExcel(@RequestBody Map<String, Object> params){
        String filePath = Optional.ofNullable((String)params.get("filePath")).orElseGet(String::new);//文件路径
        if (StringUtils.isBlank(filePath)){
            return new Result().error("文件上传异常!");
        }
        String cjrq = (String) params.get("cjrq");
        eastShareholderService.importExcel(filePath, cjrq);
        return new Result();
    }
}