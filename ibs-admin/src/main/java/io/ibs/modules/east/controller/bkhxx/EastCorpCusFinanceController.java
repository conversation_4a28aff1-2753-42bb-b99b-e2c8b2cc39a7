package io.ibs.modules.east.controller.bkhxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.bkhxx.EastCorpCusFinanceDTO;
import io.ibs.modules.east.excel.bkhxx.EastCorpCusFinanceExcel;
import io.ibs.modules.east.service.bkhxx.EastCorpCusFinanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
* 对公客户财务信息表
*
* <AUTHOR> 
* @since 1.0 2024-03-12
*/
@RestController
@RequestMapping("east/eastcorpcusfinance")
@Api(tags="对公客户财务信息表")
public class EastCorpCusFinanceController {
    @Autowired
    private EastCorpCusFinanceService eastCorpCusFinanceService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastcorpcusfinance:page")
    public Result<PageData<EastCorpCusFinanceDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastCorpCusFinanceDTO> page = eastCorpCusFinanceService.page(params);

        return new Result<PageData<EastCorpCusFinanceDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastcorpcusfinance:info")
    public Result<EastCorpCusFinanceDTO> get(@PathVariable("id") Long id){
        EastCorpCusFinanceDTO data = eastCorpCusFinanceService.get(id);

        return new Result<EastCorpCusFinanceDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastcorpcusfinance:save")
    public Result save(@RequestBody EastCorpCusFinanceDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastCorpCusFinanceService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastcorpcusfinance:update")
    public Result update(@RequestBody EastCorpCusFinanceDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastCorpCusFinanceService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastcorpcusfinance:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastCorpCusFinanceService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastcorpcusfinance:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastCorpCusFinanceDTO> list = eastCorpCusFinanceService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_204), "对公客户财务信息表", list, EastCorpCusFinanceExcel.class);
    }

}