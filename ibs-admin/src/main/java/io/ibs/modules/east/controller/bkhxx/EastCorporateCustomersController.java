package io.ibs.modules.east.controller.bkhxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.bkhxx.EastCorporateCustomersDTO;
import io.ibs.modules.east.excel.bkhxx.EastCorporateCustomersExcel;
import io.ibs.modules.east.service.bkhxx.EastCorporateCustomersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
* 对公客户信息表
*
* <AUTHOR> 
* @since 1.0 2024-03-12
*/
@RestController
@RequestMapping("east/eastcorporatecustomers")
@Api(tags="对公客户信息表")
public class EastCorporateCustomersController {
    @Autowired
    private EastCorporateCustomersService eastCorporateCustomersService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastcorporatecustomers:page")
    public Result<PageData<EastCorporateCustomersDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastCorporateCustomersDTO> page = eastCorporateCustomersService.page(params);

        return new Result<PageData<EastCorporateCustomersDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastcorporatecustomers:info")
    public Result<EastCorporateCustomersDTO> get(@PathVariable("id") Long id){
        EastCorporateCustomersDTO data = eastCorporateCustomersService.get(id);

        return new Result<EastCorporateCustomersDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastcorporatecustomers:save")
    public Result save(@RequestBody EastCorporateCustomersDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastCorporateCustomersService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastcorporatecustomers:update")
    public Result update(@RequestBody EastCorporateCustomersDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastCorporateCustomersService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastcorporatecustomers:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastCorporateCustomersService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastcorporatecustomers:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastCorporateCustomersDTO> list = eastCorporateCustomersService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_203), "对公客户信息表", list, EastCorporateCustomersExcel.class);
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    @LogOperation("导入")
    @RequiresPermissions("east:eastcorporatecustomers:import")
    public Result importExcel(@RequestBody Map<String, Object> params) {
        String filePath = Optional.ofNullable((String) params.get("filePath")).orElseGet(String::new);//文件路径
        if (StringUtils.isBlank(filePath)) {
            return new Result().error("文件上传异常!");
        }
        String cjrq = (String) params.get("cjrq");
        eastCorporateCustomersService.importExcel(filePath, cjrq);
        return new Result();
    }
}