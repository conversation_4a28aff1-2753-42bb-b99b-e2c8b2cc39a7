package io.ibs.modules.east.controller.bkhxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.bkhxx.EastGroupCustomerDTO;
import io.ibs.modules.east.excel.bkhxx.EastGroupCustomerExcel;
import io.ibs.modules.east.service.bkhxx.EastGroupCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
* 集团客户表
*
* <AUTHOR>   
* @since 1.0 2024-03-12
*/
@RestController
@RequestMapping("east/eastgroupcustomer")
@Api(tags="集团客户表")
public class EastGroupCustomerController {
    @Autowired
    private EastGroupCustomerService eastGroupCustomerService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastgroupcustomer:page")
    public Result<PageData<EastGroupCustomerDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastGroupCustomerDTO> page = eastGroupCustomerService.page(params);

        return new Result<PageData<EastGroupCustomerDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastgroupcustomer:info")
    public Result<EastGroupCustomerDTO> get(@PathVariable("id") Long id){
        EastGroupCustomerDTO data = eastGroupCustomerService.get(id);

        return new Result<EastGroupCustomerDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastgroupcustomer:save")
    public Result save(@RequestBody EastGroupCustomerDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastGroupCustomerService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastgroupcustomer:update")
    public Result update(@RequestBody EastGroupCustomerDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastGroupCustomerService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastgroupcustomer:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastGroupCustomerService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastgroupcustomer:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastGroupCustomerDTO> list = eastGroupCustomerService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_205), "集团客户表", list, EastGroupCustomerExcel.class);
    }

}