package io.ibs.modules.east.controller.dkjjzxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.dkjjzxx.EastDgckfhzmxDTO;
import io.ibs.modules.east.excel.dkjjzxx.EastDgckfhzmxExcel;
import io.ibs.modules.east.service.dkjjzxx.EastDgckfhzmxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
* 对公存款分户账明细记录
*
* @<NAME_EMAIL>
* @since 3.0 2024-03-13
*/
@RestController
@RequestMapping("east/eastdgckfhzmx")
@Api(tags="对公存款分户账明细记录")
public class EastDgckfhzmxController {
    @Autowired
    private EastDgckfhzmxService eastDgckfhzmxService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastdgckfhzmx:page")
    public Result<PageData<EastDgckfhzmxDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastDgckfhzmxDTO> page = eastDgckfhzmxService.page(params);

        return new Result<PageData<EastDgckfhzmxDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastdgckfhzmx:info")
    public Result<EastDgckfhzmxDTO> get(@PathVariable("id") Long id){
        EastDgckfhzmxDTO data = eastDgckfhzmxService.get(id);

        return new Result<EastDgckfhzmxDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastdgckfhzmx:save")
    public Result save(@RequestBody EastDgckfhzmxDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastDgckfhzmxService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastdgckfhzmx:update")
    public Result update(@RequestBody EastDgckfhzmxDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastDgckfhzmxService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastdgckfhzmx:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastDgckfhzmxService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastdgckfhzmx:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastDgckfhzmxDTO> list = eastDgckfhzmxService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_406), "对公存款分户账明细记录", list, EastDgckfhzmxExcel.class);
    }

}