package io.ibs.modules.east.controller.dkjjzxx;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.dkjjzxx.EastDgxdfhzDTO;
import io.ibs.modules.east.excel.dkjjzxx.EastDgxdfhzExcel;
import io.ibs.modules.east.service.dkjjzxx.EastDgxdfhzService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
* 对公信贷分户账
*
* <AUTHOR>  
* @since 1.0 2024-03-18
*/
@RestController
@RequestMapping("east/eastdgxdfhz")
@Api(tags="对公信贷分户账")
public class EastDgxdfhzController {
    @Autowired
    private EastDgxdfhzService eastDgxdfhzService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastdgxdfhz:page")
    public Result<PageData<EastDgxdfhzDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastDgxdfhzDTO> page = eastDgxdfhzService.page(params);

        return new Result<PageData<EastDgxdfhzDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastdgxdfhz:info")
    public Result<EastDgxdfhzDTO> get(@PathVariable("id") Long id){
        EastDgxdfhzDTO data = eastDgxdfhzService.get(id);

        return new Result<EastDgxdfhzDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastdgxdfhz:save")
    public Result save(@RequestBody EastDgxdfhzDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastDgxdfhzService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastdgxdfhz:update")
    public Result update(@RequestBody EastDgxdfhzDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastDgxdfhzService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastdgxdfhz:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastDgxdfhzService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastdgxdfhz:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastDgxdfhzDTO> list = eastDgxdfhzService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_411), "对公信贷分户账", list, EastDgxdfhzExcel.class);
    }

}