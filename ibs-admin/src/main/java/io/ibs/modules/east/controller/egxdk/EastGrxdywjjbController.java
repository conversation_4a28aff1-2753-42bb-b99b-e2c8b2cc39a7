package io.ibs.modules.east.controller.egxdk;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileUtils;
import io.ibs.modules.east.dto.egxdk.EastGrxdywjjbDTO;
import io.ibs.modules.east.excel.egxdk.EastGrxdywjjbExcel;
import io.ibs.modules.east.service.egxdk.EastGrxdywjjbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
* 个人信贷业务借据表
*
* @<NAME_EMAIL>
* @since 1.0 2024-03-14
*/
@RestController
@RequestMapping("east/eastgrxdywjjb")
@Api(tags="个人信贷业务借据表")
public class EastGrxdywjjbController {
    @Autowired
    private EastGrxdywjjbService eastGrxdywjjbService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("east:eastgrxdywjjb:page")
    public Result<PageData<EastGrxdywjjbDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<EastGrxdywjjbDTO> page = eastGrxdywjjbService.page(params);

        return new Result<PageData<EastGrxdywjjbDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:eastgrxdywjjb:info")
    public Result<EastGrxdywjjbDTO> get(@PathVariable("id") Long id){
        EastGrxdywjjbDTO data = eastGrxdywjjbService.get(id);

        return new Result<EastGrxdywjjbDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:eastgrxdywjjb:save")
    public Result save(@RequestBody EastGrxdywjjbDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastGrxdywjjbService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:eastgrxdywjjb:update")
    public Result update(@RequestBody EastGrxdywjjbDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastGrxdywjjbService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:eastgrxdywjjb:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastGrxdywjjbService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:eastgrxdywjjb:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastGrxdywjjbDTO> list = eastGrxdywjjbService.list(params);

        ExcelUtils.exportExcelToTarget(response, FileUtils.exportExcelName(EastTableInfo.TABLE_503), "个人信贷业务借据表", list, EastGrxdywjjbExcel.class);
    }

}