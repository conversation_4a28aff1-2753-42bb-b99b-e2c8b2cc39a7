package io.ibs.modules.east.controller.egxdk;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.exception.RenException;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.east.dto.egxdk.EastTzDTO;
import io.ibs.modules.east.entity.egxdk.EastTzEntity;
import io.ibs.modules.east.excel.egxdk.EastTzExcel;
import io.ibs.modules.east.service.egxdk.EastTzService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 台账
 *
 * @<NAME_EMAIL>
 * @since 3.0 2024-05-31
 */
@RestController
@RequestMapping("east/easttz")
@Api(tags = "台账")
public class EastTzController {
    @Autowired
    private EastTzService eastTzService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("east:easttz:page")
    public Result<PageData<EastTzDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<EastTzDTO> page = eastTzService.page(params);

        return new Result<PageData<EastTzDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("east:easttz:info")
    public Result<EastTzDTO> get(@PathVariable("id") Long id) {
        EastTzDTO data = eastTzService.get(id);

        return new Result<EastTzDTO>().ok(data);
    }

    @GetMapping("getClearDateList")
    @ApiOperation("获取采集日期")
    @RequiresPermissions("east:easttz:info")
    public Result<List<String>> getClearDateList() {
        List<String> list = eastTzService.list(new QueryWrapper<EastTzEntity>().select("CJRQ")).stream().distinct().map(EastTzDTO::getCjrq).collect(Collectors.toList());
        return new Result<List<String>>().ok(list);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("east:easttz:save")
    public Result save(@RequestBody EastTzDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        eastTzService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("east:easttz:update")
    public Result update(@RequestBody EastTzDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        eastTzService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("east:easttz:delete")
    public Result delete(@RequestBody Long[] ids) {
        // 效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eastTzService.delete(ids);

        return new Result();
    }

    @PostMapping("deleteByDate")
    @ApiOperation("按照采集日期删除")
    @LogOperation("按照采集日期删除")
    @RequiresPermissions("east:easttz:delete")
    public Result<Boolean> deleteByDate(@RequestBody Map<String, String> params) {
        String clearDate = Optional.ofNullable(params.get("clearDate")).orElseThrow(() -> new RenException("日期不能为空"));
        if (eastTzService.deleteByDate(clearDate)) {
            return new Result<Boolean>().ok(true);
        }
        return new Result<Boolean>().error("当前日期没有数据");
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("east:easttz:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EastTzDTO> list = eastTzService.list(params);

        ExcelUtils.exportExcelToTarget(response, "信贷台账-" + DateUtil.today(), "信贷台账", list, EastTzExcel.class);
    }

    @PostMapping("/import")
    @ApiOperation("导入")
    @LogOperation("导入")
    @RequiresPermissions("east:easttz:import")
    public Result importExcel(@RequestBody Map<String, Object> params) {
        String filePath = Optional.ofNullable((String) params.get("filePath")).orElseGet(String::new);// 文件路径
        if (StringUtils.isBlank(filePath)) {
            return new Result<>().error("文件上传异常!");
        }
        String cjrq = (String) params.get("cjrq");
        eastTzService.importExcel(filePath, cjrq);
        return new Result<>();
    }

}