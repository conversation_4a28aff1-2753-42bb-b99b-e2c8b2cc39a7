package io.ibs.modules.east.controller.report;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.common.config.RequestDataHelper;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.FileDownloadUtils;
import io.ibs.common.utils.Result;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.common.FileCreateFactory;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.aggxx.EastDeptService;
import io.ibs.modules.east.service.report.EastFileService;
import io.ibs.modules.log.dto.SysLogFileDTO;
import io.ibs.modules.log.service.SysLogFileService;
import io.ibs.modules.sys.entity.SysDictDataEntity;
import io.ibs.modules.sys.service.SysDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;

/**
 * Created by AileYoung on 2024/4/9.
 */
@RestController
@RequestMapping("east/file")
@Api(tags = "East文件管理")
@Slf4j
@RequiredArgsConstructor
public class EastFileController {
    private final SysLogFileService sysLogFileService;
    private final EastFileService eastFileService;
    private final SysDictDataService dictDataService;
    private final EastDeptService eastDeptService;

    @GetMapping("filePage")
    @ApiOperation("文件分页查询")
    @ApiImplicitParams({@ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"), @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int")})
    @RequiresPermissions("east:eastdatarecord:info")
    public Result<PageData<SysLogFileDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        log.info("查询East文件列表");
        params.put("irpFileDir", Constants.FILE_PATH);
        PageData<SysLogFileDTO> page = sysLogFileService.page(params);
        List<SysLogFileDTO> list = page.getList();
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getFileName().equals("upload")) {
                    list.remove(i);
                    break;
                }
            }
        }
        return new Result<PageData<SysLogFileDTO>>().ok(page);
    }

    @GetMapping("fileType")
    @ApiOperation("文件类型查询")
    @RequiresPermissions("east:eastdatarecord:info")
    public Result<Map<String, String>> page() {
        Map<String, String> map = new LinkedHashMap<>();
        for (EastTableInfo value : EastTableInfo.values()) {
            map.put(value.getId(), value.getName());
        }
        return new Result<Map<String, String>>().ok(map);
    }

    @PostMapping
    @ApiOperation("生成文件")
    @RequiresPermissions("east:eastdatarecord:info")
    public Result<String> createFile(@RequestBody Map<String, Object> params) {
        String fileType = MapUtil.getStr(params, "fileType");
        String cjrq = MapUtil.getStr(params, "cjrq");
        if ("All".equals(fileType)) {
            List<SysDictDataEntity> dictDataList = dictDataService.getDataByDictType("EAST_TABLE_INFO");
            for (SysDictDataEntity dictData : dictDataList) {
                if (EastTableInfo.containsId(dictData.getDictValue())) {
                    createFileByTableInfo(EastTableInfo.getById(dictData.getDictValue()), cjrq, dictData.getRemark());
                }
            }
        } else {
            SysDictDataEntity sysDictDataEntity = dictDataService.selectOne(new LambdaQueryWrapper<>(SysDictDataEntity.class)
                    .eq(SysDictDataEntity::getDictValue, fileType));
            createFileByTableInfo(EastTableInfo.getById(fileType), cjrq, sysDictDataEntity != null ? sysDictDataEntity.getRemark() : "");
        }
        return new Result<String>().ok("success");
    }

    @GetMapping("download")
    @ApiOperation("下载")
    @RequiresPermissions("east:eastdatarecord:info")
    public void download(@RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        String filePath = Constants.FILE_PATH;
        String fileName = (String) params.get("fileName");
        String level1 = (String) params.get("level1");
        fileName = filePath + level1 + File.separator + fileName;
        log.info("下载文件：" + fileName);
        FileDownloadUtils.exportFile(response, fileName);
    }

    @GetMapping("downloadBatch")
    @ApiOperation("批量下载")
    @RequiresPermissions("east:eastdatarecord:info")
    public void downloadBatch(@RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        String filePath = Constants.FILE_PATH;
        String level1 = (String) params.get("level1");
        File zipFile = ZipUtil.zip(filePath + level1);
        String fileName = zipFile.getName();
        FileUtil.move(zipFile, new File(filePath + level1 + File.separator + fileName), true);
        FileUtil.rename(new File(filePath + level1 + File.separator + fileName), "east-" + fileName, true);
        log.info("下载文件：" + "east-" + fileName);
        FileDownloadUtils.exportFile(response, filePath + level1 + File.separator + "east-" + fileName);
    }

    @GetMapping("downloadReportFile")
    @ApiOperation("下载报送文件")
    @RequiresPermissions("east:eastdatarecord:info")
    public void downloadReportFile(@RequestParam Map<String, Object> params, HttpServletResponse response) {
        String financialNo = eastDeptService.getDeptByInstNo(Constants.INST_NO).getJrxkzh();
        params.put("financialNo", financialNo);
        eastFileService.downloadReportFile(params, response);
    }

    /**
     * 根据East表信息生成文件
     *
     * @param tableInfo East表信息
     * @param cjrq      采集日期
     * @param ctrlFlag  blank-总是生成空文件；isNotBlank-必须在指定采集日期采集到数据，文件中一定有数据，否则会报错；空或其他-不做控制
     */
    @SuppressWarnings("unchecked")
    private void createFileByTableInfo(EastTableInfo tableInfo, String cjrq, String ctrlFlag) {
        String financialNo = eastDeptService.getDeptByInstNo(Constants.INST_NO).getJrxkzh();
        if ("blank".equals(ctrlFlag)) {
            log.info("检测到blank标志，强制生成空文件");
            FileCreateFactory.createFile(tableInfo, new ArrayList<>(), cjrq, financialNo, false);
        } else {
            // 设定需要扫描的包路径
            String packagePath = "io.ibs.modules.east.entity";
            // 使用ClassScaner来扫描包中的类
            final Set<Class<?>> classes = ClassUtil.scanPackageByAnnotation(packagePath, TableName.class);

            String tableName = "T_EAST_" + tableInfo.getTableName();

            boolean findClass = false;
            for (Class<?> aClass : classes) {
                if (aClass.getAnnotation(TableName.class).value().equals(tableName)) {
                    findClass = true;
                    RequestDataHelper.setRequestData(new HashMap<String, Object>() {{
                        put(RequestDataHelper.CHANGE_TABLE_NAME, tableName);
                    }});
                    List dataList = Db.list(new QueryWrapper<>(aClass)
                            .eq("CJRQ", cjrq));
                    FileCreateFactory.createFile(tableInfo, dataList, cjrq, financialNo, "isNotBlank".equals(ctrlFlag));
                    Db.update(new EastDataRecordEntity(), new LambdaUpdateWrapper<>(EastDataRecordEntity.class)
                            .setSql("CREATE_COUNT = CREATE_COUNT + 1")
                            .eq(EastDataRecordEntity::getCjrq, cjrq)
                            .eq(EastDataRecordEntity::getTableId, tableInfo.getId()));
                }
            }
            // 没有找到实体类的，生成对应的空文件
            if (!findClass) {
                FileCreateFactory.createFile(tableInfo, new ArrayList<>(), cjrq, financialNo, false);
            }
        }
    }
}
