package io.ibs.modules.east.dao.bkhxx;

import io.ibs.common.dao.BaseDao;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.east.entity.bkhxx.EastRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 关联关系表
 *
 * <AUTHOR> 
 * @since 1.0 2024-03-12
 */
@Mapper
@Repository
public interface EastRelationDao extends BaseDao<EastRelationEntity> {
    /**
     * 从核心非自然人受益所有人表中获取对公客户的关联关系
     * @param bgnDate
     * @param endDate
     * @return
     */
    @DataSource("informix")
	List<EastRelationEntity> getCorpRelation(@Param("bgnDate") String bgnDate,@Param("endDate") String endDate);

    /**
     * 查询关联人是否为本行客户
     * @return
     */
    @DataSource("informix")
    List<EastRelationEntity> getRelOfBenef();
}