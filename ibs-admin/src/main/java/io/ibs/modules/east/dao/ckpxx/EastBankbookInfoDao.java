package io.ibs.modules.east.dao.ckpxx;

import io.ibs.common.dao.BaseDao;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.east.entity.ckpxx.EastBankbookInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 存折信息表
 *
 * <AUTHOR>  
 * @since 1.0 2024-03-12
 */
@Mapper
@Repository
public interface EastBankbookInfoDao extends BaseDao<EastBankbookInfoEntity> {
    /**
     * 从核心获取存折信息
     * @param bgnDate
     * @param endDate
     * @return
     */
    @DataSource("informix")
    List<EastBankbookInfoEntity> getBookInfoOfSavingDeposit(@Param("bgnDate") String bgnDate,@Param("endDate") String endDate);

    /**
     * 从核心获取对私定期存折信息
     * @param bgnDate
     * @param endDate
     * @return
     */
    @DataSource("informix")
    List<EastBankbookInfoEntity> getBookInfoOfPTimeDeposit(@Param("bgnDate") String bgnDate,@Param("endDate") String endDate);
    /**
     * 从核心获取对公定期存折信息
     * @param bgnDate
     * @param endDate
     * @return
     */
    @DataSource("informix")
    List<EastBankbookInfoEntity> getBookInfoOfCTimeDeposit(@Param("bgnDate") String bgnDate,@Param("endDate") String endDate);
}