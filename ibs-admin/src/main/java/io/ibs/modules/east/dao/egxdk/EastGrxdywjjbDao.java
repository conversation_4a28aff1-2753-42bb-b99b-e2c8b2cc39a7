package io.ibs.modules.east.dao.egxdk;

import io.ibs.common.dao.BaseDao;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.east.entity.egxdk.EastGrxdywjjbEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 个人信贷业务借据表
 *
 * @<NAME_EMAIL>
 * @since 1.0 2024-03-14
 */
@Mapper
@Repository
public interface EastGrxdywjjbDao extends BaseDao<EastGrxdywjjbEntity> {

    @DataSource("informix")
    EastGrxdywjjbEntity getGrxdywjjbEntityByBussNum(@Param("bussNum") String bussNum, @Param("endDate") String endDate);

    @DataSource("informix")
    EastGrxdywjjbEntity getDgToGrxdywjjbEntityByBussNum(@Param("bussNum") String bussNum, @Param("endDate") String endDate);
}