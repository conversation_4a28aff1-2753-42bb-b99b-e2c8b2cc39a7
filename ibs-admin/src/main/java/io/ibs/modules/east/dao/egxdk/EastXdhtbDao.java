package io.ibs.modules.east.dao.egxdk;

import io.ibs.common.dao.BaseDao;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.east.entity.egxdk.EastXdhtbEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 信贷合同表
 *
 * <AUTHOR>
 * @since 3.0 2024-03-14
 */
@Mapper
@Repository
public interface EastXdhtbDao extends BaseDao<EastXdhtbEntity> {

    @DataSource("xdgl")
    List<EastXdhtbEntity> getDataFromCredit(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询截止bgnDate未结清的
     *
     * @param cstmType 客户类型 0-个人 1-企业 ""-全部
     * @param tranDate 开始日期
     * @return 未结清的借据号
     */
    @DataSource("informix")
    List<String> getUnCloseLoanNoList(@Param("cstmType") String cstmType, @Param("tranDate") String tranDate, @Param("addDate") String addDate);

    /**
     * 借据在指定时间范围结清的
     *
     * @param cstmType 客户类型 0-个人 1-企业
     * @param bgnDate  开始日期 （不传默认：20230101）
     * @param endDate  结束日期（不传默认：当前系统时间）
     * @return 结清的借据号
     */
    @DataSource("informix")
    List<Map<String, Object>> getCloseLoanNoList(@Param("cstmType") String cstmType, @Param("bgnDate") String bgnDate, @Param("endDate") String endDate);

    @DataSource("informix")
    Map<String, Object> checkLoanClose(@Param("bussNum") String bussNum, @Param("tranDate") String tranDate, @Param("addDate") String addDate);

}