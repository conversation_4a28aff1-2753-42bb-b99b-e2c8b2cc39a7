package io.ibs.modules.east.dao.fbnwdbxx;

import io.ibs.common.dao.BaseDao;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.east.entity.fbnwdbxx.EastBnwywdbrEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 表内外业务担保人
 *
 * <AUTHOR>
 * @since 3.0 2024-03-14
 */
@Mapper
@Repository
public interface EastBnwywdbrDao extends BaseDao<EastBnwywdbrEntity> {

    @DataSource("xdgl")
    List<EastBnwywdbrEntity> getBnwywdbrData(@Param("loanNo") String loanNo);
}