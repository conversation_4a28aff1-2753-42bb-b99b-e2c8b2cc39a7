package io.ibs.modules.east.dto.aggxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 岗位信息表
 *
 * @<NAME_EMAIL>
 * @since 1.0 2024-03-13
 */
@Data
@ApiModel(value = "岗位信息表")
public class EastPostInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "岗位编号")
    private String gwbh;
    @ApiModelProperty(value = "岗位种类")
    private String gwzl;
    @ApiModelProperty(value = "岗位名称")
    private String gwmc;
    @ApiModelProperty(value = "岗位说明")
    private String gwsm;
    @ApiModelProperty(value = "岗位状态")
    private String gwzt;
    @ApiModelProperty(value = "备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期")
    private String cjrq;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新人")
    private Long updater;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}