package io.ibs.modules.east.dto.bkhxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 对公客户财务信息表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@ApiModel(value = "对公客户财务信息表")
public class EastCorpCusFinanceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "银行机构名称")
    private String yhjgmc;
    @ApiModelProperty(value = "财务报表编号")
    private String cwbbbh;
    @ApiModelProperty(value = "客户统一编号")
    private String khtybh;
    @ApiModelProperty(value = "客户名称")
    private String khmc;
    @ApiModelProperty(value = "财务报表日期")
    private String cwbbrq;
    @ApiModelProperty(value = "是否审计")
    private String sfsj;
    @ApiModelProperty(value = "审计机构")
    private String sjjg;
    @ApiModelProperty(value = "报表口径")
    private String bbkj;
    @ApiModelProperty(value = "币种")
    private String bz;
    @ApiModelProperty(value = "资产总额")
    private BigDecimal zcze;
    @ApiModelProperty(value = "负债总额")
    private BigDecimal fzze;
    @ApiModelProperty(value = "税前利润")
    private BigDecimal sqlr;
    @ApiModelProperty(value = "所得税")
    private BigDecimal sds;
    @ApiModelProperty(value = "净利润")
    private BigDecimal jlr;
    @ApiModelProperty(value = "主营业务收入")
    private BigDecimal zyywsr;
    @ApiModelProperty(value = "现金流量净额")
    private BigDecimal xjllje;
    @ApiModelProperty(value = "应收账款")
    private BigDecimal yszk;
    @ApiModelProperty(value = "其他应收款")
    private BigDecimal qtysk;
    @ApiModelProperty(value = "报表周期")
    private String bbzq;
    @ApiModelProperty(value = "备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期")
    private String cjrq;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "更新人")
    private Long updater;

}