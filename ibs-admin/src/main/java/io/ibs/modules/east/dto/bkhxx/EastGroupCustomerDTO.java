package io.ibs.modules.east.dto.bkhxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 集团客户表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@ApiModel(value = "集团客户表")
public class EastGroupCustomerDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号;银行内部机构号。应具有标识机构的唯一性。")
    private String nbjgh;
    @ApiModelProperty(value = "银行机构名称")
    private String yhjgmc;
    @ApiModelProperty(value = "集团编号;银行为统一管理，根据既定规则生成并分配给集团客户作为唯一识别的编号。")
    private String jtbh;
    @ApiModelProperty(value = "集团名称")
    private String jtmc;
    @ApiModelProperty(value = "母公司客户统一编号;银行自定义的唯一识别客户的标识。供应链融资的填写供应链融资编码。")
    private String mgskhtybh;
    @ApiModelProperty(value = "母公司名称")
    private String mgsmc;
    @ApiModelProperty(value = "实控人名称")
    private String skrmc;
    @ApiModelProperty(value = "实控人类型")
    private String skrlx;
    @ApiModelProperty(value = "币种")
    private String bz;
    @ApiModelProperty(value = "集团资产总额;与币种相对应金额。")
    private BigDecimal jtzcze;
    @ApiModelProperty(value = "集团负债总额;与币种相对应金额。")
    private BigDecimal jtfzze;
    @ApiModelProperty(value = "集团授信额度;与币种相对应金额。")
    private BigDecimal jtsxed;
    @ApiModelProperty(value = "集团已用额度;与币种相对应金额。")
    private BigDecimal jtyyed;
    @ApiModelProperty(value = "成员客户统一编号;银行自定义的唯一识别客户的标识。供应链融资的填写供应链融资编码。")
    private String cykhtybh;
    @ApiModelProperty(value = "成员名称")
    private String cymc;
    @ApiModelProperty(value = "成员已用额度;与币种相对应金额。")
    private BigDecimal cyyyed;
    @ApiModelProperty(value = "备注;银行自定义。")
    private String bbz;
    @ApiModelProperty(value = "采集日期;YYYYMMDD，默认值99991231。")
    private String cjrq;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新人")
    private Long updater;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}