package io.ibs.modules.east.dto.bkhxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人客户关系表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@ApiModel(value = "个人客户关系表")
public class EastPersonalCustomerDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "客户统一编号")
    private String khtybh;
    @ApiModelProperty(value = "客户姓名")
    private String khxm;
    @ApiModelProperty(value = "证件类别")
    private String zjlb;
    @ApiModelProperty(value = "证件号码")
    private String zjhm;
    @ApiModelProperty(value = "关系类型")
    private String gxlx;
    @ApiModelProperty(value = "关系人客户统一编号")
    private String gxrkhtybh;
    @ApiModelProperty(value = "关系人名称")
    private String gxrmc;
    @ApiModelProperty(value = "关系人证件类别")
    private String gxrzjlb;
    @ApiModelProperty(value = "关系人证件号码")
    private String gxrzjhm;
    @ApiModelProperty(value = "关系状态")
    private String gxzt;
    @ApiModelProperty(value = "备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期")
    private String cjrq;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "更新人")
    private Long updater;

}