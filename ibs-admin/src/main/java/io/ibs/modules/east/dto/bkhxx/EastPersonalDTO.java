package io.ibs.modules.east.dto.bkhxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 个人基础信息表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@ApiModel(value = "个人基础信息表")
public class EastPersonalDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "金融许可证号;金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号;内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "银行机构名称;银行机构名称")
    private String yhjgmc;
    @ApiModelProperty(value = "客户统一编号;客户统一编号")
    private String khtybh;
    @ApiModelProperty(value = "客户姓名;客户姓名")
    private String khxm;
    @ApiModelProperty(value = "证件类别;证件类别")
    private String zjlb;
    @ApiModelProperty(value = "证件号码;证件号码")
    private String zjhm;
    @ApiModelProperty(value = "客户类型;客户类型")
    private String bxygbz;
    @ApiModelProperty(value = "国籍;国籍")
    private String gj;
    @ApiModelProperty(value = "民族;民族")
    private String mz;
    @ApiModelProperty(value = "性别;性别")
    private String xb;
    @ApiModelProperty(value = "学历;学历")
    private String xl;
    @ApiModelProperty(value = "出生年月;出生年月")
    private String csny;
    @ApiModelProperty(value = "是否已婚;是否已婚")
    private String sfyh;
    @ApiModelProperty(value = "工作单位名称;工作单位名称")
    private String gzdwmc;
    @ApiModelProperty(value = "工作单位地址;工作单位地址")
    private String gzdwdz;
    @ApiModelProperty(value = "工作单位电话;工作单位电话")
    private String gzdwdh;
    @ApiModelProperty(value = "单位性质;单位性质")
    private String dwxz;
    @ApiModelProperty(value = "职业;职业")
    private String zy;
    @ApiModelProperty(value = "职务;职务")
    private String zw;
    @ApiModelProperty(value = "个人年收入;个人年收入")
    private BigDecimal grnsr;
    @ApiModelProperty(value = "通讯地址;通讯地址")
    private String txdz;
    @ApiModelProperty(value = "联系电话;联系电话")
    private String lxdh;
    @ApiModelProperty(value = "信贷客户标志;信贷客户标志")
    private String xdkhbz;
    @ApiModelProperty(value = "首次建立信贷关系年月;首次建立信贷关系年月")
    private String scjlxdgxny;
    @ApiModelProperty(value = "是否农户;是否农户")
    private String sfnh;
    @ApiModelProperty(value = "本行员工标志;本行员工标志")
    private String bhygbz;
    @ApiModelProperty(value = "上黑名单标志;上黑名单标志")
    private String shmdbz;
    @ApiModelProperty(value = "上黑名单日期;上黑名单日期")
    private String shmdrq;
    @ApiModelProperty(value = "备注;备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期;采集日期")
    private String cjrq;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "更新人")
    private Long updater;

}