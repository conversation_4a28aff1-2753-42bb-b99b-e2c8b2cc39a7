package io.ibs.modules.east.dto.ckpxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 存折信息表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@ApiModel(value = "存折信息表")
public class EastBankbookInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "客户统一编号")
    private String khtybh;
    @ApiModelProperty(value = "客户名称")
    private String khmc;
    @ApiModelProperty(value = "证件类别")
    private String zjlb;
    @ApiModelProperty(value = "证件号码")
    private String zjhm;
    @ApiModelProperty(value = "存折号")
    private String czh;
    @ApiModelProperty(value = "存款账号")
    private String hqckzh;
    @ApiModelProperty(value = "存折类型")
    private String czlx;
    @ApiModelProperty(value = "员工标志;是，否。")
    private String ygbz;
    @ApiModelProperty(value = "启用日期;YYYYMMDD，默认值********。")
    private String qyrq;
    @ApiModelProperty(value = "启用柜员号")
    private String qygyh;
    @ApiModelProperty(value = "存折状态")
    private String czzt;
    @ApiModelProperty(value = "备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期;YYYYMMDD，默认值********。")
    private String cjrq;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新人")
    private Long updater;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}