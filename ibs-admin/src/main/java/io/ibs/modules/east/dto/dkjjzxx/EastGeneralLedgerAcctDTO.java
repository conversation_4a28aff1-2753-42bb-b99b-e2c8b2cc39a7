package io.ibs.modules.east.dto.dkjjzxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 总账会计全科目表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-13
 */
@Data
@ApiModel(value = "总账会计全科目表")
public class EastGeneralLedgerAcctDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "银行机构名称")
    private String yhjgmc;
    @ApiModelProperty(value = "会计科目编号")
    private String kjkmbh;
    @ApiModelProperty(value = "会计科目名称")
    private String kjkmmc;
    @ApiModelProperty(value = "币种")
    private String bz;
    @ApiModelProperty(value = "期初借方余额")
    private BigDecimal qcjfye;
    @ApiModelProperty(value = "期初贷方余额")
    private BigDecimal qcdfye;
    @ApiModelProperty(value = "本期借方发生额")
    private BigDecimal jffse;
    @ApiModelProperty(value = "本期贷方发生额")
    private BigDecimal dffse;
    @ApiModelProperty(value = "期末借方余额")
    private BigDecimal qmjfye;
    @ApiModelProperty(value = "期末贷方余额")
    private BigDecimal qmdfye;
    @ApiModelProperty(value = "报送周期")
    private String bszq;
    @ApiModelProperty(value = "会计日期")
    private String kjrq;
    @ApiModelProperty(value = "备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期")
    private String cjrq;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新人")
    private Long updater;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}