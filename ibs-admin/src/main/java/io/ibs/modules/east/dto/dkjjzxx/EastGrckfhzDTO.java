package io.ibs.modules.east.dto.dkjjzxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 个人存款分户账
 * <AUTHOR>
 * @since 20240418
 */
@Data
@ApiModel(value = "个人存款分户账")
public class EastGrckfhzDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "银行机构名称")
    private String yhjgmc;
    @ApiModelProperty(value = "明细科目编号")
    private String mxkmbh;
    @ApiModelProperty(value = "明细科目名称")
    private String mxkmmc;
    @ApiModelProperty(value = "客户统一编号")
    private String khtybh;
    @ApiModelProperty(value = "账户名称")
    private String zhmc;
    @ApiModelProperty(value = "个人存款账号")
    private String grckzh;
    @ApiModelProperty(value = "个人存款账户类型")
    private String grckzhlx;
    @ApiModelProperty(value = "保证金账户标志")
    private String bzjzhbz;
    @ApiModelProperty(value = "利率")
    private BigDecimal ll;
    @ApiModelProperty(value = "币种")
    private String bz;
    @ApiModelProperty(value = "存款余额")
    private BigDecimal ckye;
    @ApiModelProperty(value = "开户日期")
    private String khrq;
    @ApiModelProperty(value = "开户柜员号")
    private String khgyh;
    @ApiModelProperty(value = "上次动户日期")
    private String scdhrq;
    @ApiModelProperty(value = "销户日期")
    private String xhrq;
    @ApiModelProperty(value = "钞汇类别")
    private String chlb;
    @ApiModelProperty(value = "账户状态")
    private String zhzt;
    @ApiModelProperty(value = "备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期")
    private String cjrq;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新人")
    private Long updater;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}
