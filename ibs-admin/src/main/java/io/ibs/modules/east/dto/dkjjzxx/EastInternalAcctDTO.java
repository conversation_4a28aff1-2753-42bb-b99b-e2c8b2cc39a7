package io.ibs.modules.east.dto.dkjjzxx;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 内部科目对照表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-14
 */
@Data
@ApiModel(value = "内部科目对照表")
public class EastInternalAcctDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "金融许可证号")
    private String jrxkzh;
    @ApiModelProperty(value = "内部机构号")
    private String nbjgh;
    @ApiModelProperty(value = "银行机构名称")
    private String yhjgmc;
    @ApiModelProperty(value = "会计科目编号")
    private String kjkmbh;
    @ApiModelProperty(value = "会计科目名称")
    private String kjkmmc;
    @ApiModelProperty(value = "会计科目级次")
    private Integer kjkmjc;
    @ApiModelProperty(value = "上级科目编号")
    private String sjkmbh;
    @ApiModelProperty(value = "上级科目名称")
    private String sjkmmc;
    @ApiModelProperty(value = "科目借贷标志")
    private String kmjdbz;
    @ApiModelProperty(value = "归属业务大类")
    private String gsywdl;
    @ApiModelProperty(value = "归属业务子类")
    private String gsywzl;
    @ApiModelProperty(value = "备注")
    private String bbz;
    @ApiModelProperty(value = "采集日期")
    private String cjrq;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @ApiModelProperty(value = "更新人")
    private Long updater;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}