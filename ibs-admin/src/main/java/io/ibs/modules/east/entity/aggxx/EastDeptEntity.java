package io.ibs.modules.east.entity.aggxx;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.east.entity.EastBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 机构信息表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_EAST_JGXXB")
public class EastDeptEntity extends EastBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 银行机构代码;银行机构代码
     */
    @TableField(fill = FieldFill.INSERT)
    private String yhjgdm;
    /**
     * 内部机构号;银行内部机构号
     */
    private String nbjgh;
    /**
     * 金融许可证号
     */
    @TableField(fill = FieldFill.INSERT)
    private String jrxkzh;
    /**
     * 营业执照号;营业执照号
     */
    private String yyzzh;
    /**
     * 银行机构名称;银行机构名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String yhjgmc;
    /**
     * 机构类别;机构类别
     */
    private String jglb;
    /**
     * 行政区划代码;行政区划代码
     */
    private String xzqhdm;
    /**
     * 营业状态;营业，停业
     */
    private String yyzt;
    /**
     * 成立日期;yyyyMMDD，默认值99991231
     */
    private String clrq;
    /**
     * 机构地址;机构地址
     */
    private String jgdz;
    /**
     * 机构联系电话;电话
     */
    private String jglxdh;
    /**
     * 负责人姓名;姓名
     */
    private String fzrxm;
    /**
     * 负责人职务;职务
     */
    private String fzrzw;
    /**
     * 负责人联系电话;电话
     */
    private String fzrlxdh;
    /**
     * 备注;备注
     */
    private String bbz;
    /**
     * 采集日期
     */
    private String cjrq;

}