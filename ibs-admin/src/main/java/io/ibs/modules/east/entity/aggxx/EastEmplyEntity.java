package io.ibs.modules.east.entity.aggxx;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.east.annotation.Desensitization;
import io.ibs.modules.east.entity.EastBaseEntity;
import io.ibs.modules.east.enums.DesensitizationEnum;
import lombok.*;

import java.io.Serializable;
import java.util.Objects;

/**
 * 员工表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_EAST_YGB")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EastEmplyEntity extends EastBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 金融许可证号
     */
    @TableField(fill = FieldFill.INSERT)
    private String jrxkzh;
    /**
     * 内部机构号
     */
    private String nbjgh;
    /**
     * 银行机构名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String yhjgmc;
    /**
     * 工号
     */
    private String gh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 国籍
     */
    private String gj;
    /**
     * 证件类别
     */
    private String zjlb;
    /**
     * 证件号码
     */
    @Desensitization(type = DesensitizationEnum.ID_NO, relationField = "xm")
    private String zjhm;
    /**
     * 联系电话
     */
    private String lxdh;
    /**
     * 所属部门
     */
    private String ssbm;
    /**
     * 岗位编号
     */
    private String gwbh;
    /**
     * 岗位名称
     */
    private String gwmc;
    /**
     * 是否高管
     */
    private String sfgg;
    /**
     * 批复日期
     */
    private String pfrq;
    /**
     * 任职日期
     */
    private String rzrq;
    /**
     * 员工类型
     */
    private String yglx;
    /**
     * 员工状态
     */
    private String ygzt;
    /**
     * 备注
     */
    private String bbz;
    /**
     * 采集日期
     */
    private String cjrq;

}