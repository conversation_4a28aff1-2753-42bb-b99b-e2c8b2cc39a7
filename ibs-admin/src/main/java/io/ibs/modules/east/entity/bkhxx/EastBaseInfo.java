package io.ibs.modules.east.entity.bkhxx;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * East基础信息
 * Created by AileYoung on 2024/6/14.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EastBaseInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户统一编号;客户统一编号
     */
    private String cstmNo;
    /**
     * 客户姓名;客户姓名
     */
    private String username;
    /**
     * 证件类别;证件类别
     */
    private String idType;
    /**
     * 证件号码;证件号码
     */
    private String idNo;
    /**
     * 采集日期
     */
    private String collectionDate;
}
