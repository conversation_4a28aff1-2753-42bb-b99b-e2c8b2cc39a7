package io.ibs.modules.east.entity.ckpxx;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.east.annotation.Desensitization;
import io.ibs.modules.east.entity.EastBaseEntity;
import io.ibs.modules.east.enums.DesensitizationEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 存折信息表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "T_EAST_CZXXB")
public class EastBankbookInfoEntity extends EastBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 金融许可证号
     */
    @TableField(fill = FieldFill.INSERT)
    private String jrxkzh;
    /**
     * 内部机构号
     */
    private String nbjgh;
    /**
     * 客户统一编号
     */
    private String khtybh;
    /**
     * 客户名称
     */
    @Desensitization(type = DesensitizationEnum.CUSTOMER_NAME, conditionMethod = "desensitizeCondition")
    private String khmc;
    /**
     * 证件类别
     */
    private String zjlb;
    /**
     * 证件号码
     */
    @Desensitization(type = DesensitizationEnum.ID_NO, relationField = "khmc", conditionMethod = "desensitizeCondition")
    private String zjhm;
    /**
     * 存折号
     */
    private String czh;
    /**
     * 存款账号
     */
    private String hqckzh;
    /**
     * 存折类型
     */
    private String czlx;
    /**
     * 员工标志;是，否。
     */
    private String ygbz;
    /**
     * 启用日期;YYYYMMDD，默认值99991231。
     */
    private String qyrq;
    /**
     * 启用柜员号
     */
    private String qygyh;
    /**
     * 存折状态
     */
    private String czzt;
    /**
     * 备注
     */
    private String bbz;
    /**
     * 采集日期
     */
    private String cjrq;

    public boolean desensitizeCondition() {
        return "居民身份证".equals(this.zjlb) || "户口簿".equals(this.zjlb);
    }
}