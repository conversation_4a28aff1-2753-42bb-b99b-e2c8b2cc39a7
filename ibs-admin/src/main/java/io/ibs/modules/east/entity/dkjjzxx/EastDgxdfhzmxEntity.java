package io.ibs.modules.east.entity.dkjjzxx;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.east.entity.EastBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 对公信贷分户账明细记录
 *
 * <AUTHOR>
 * @since 1.0 2024-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_EAST_DGXDFHZMX")
public class EastDgxdfhzmxEntity extends EastBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 交易序列号
     */
    private String jyxlh;
    /**
     * 金融许可证号
     */
    @TableField(fill = FieldFill.INSERT)
    private String jrxkzh;
    /**
     * 内部机构号
     */
    private String nbjgh;
    /**
     * 业务办理机构号
     */
    private String ywbljgh;
    /**
     * 银行机构名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String yhjgmc;
    /**
     * 明细科目编号
     */
    private String mxkmbh;
    /**
     * 明细科目名称
     */
    private String mxkmmc;
    /**
     * 客户统一编号
     */
    private String khtybh;
    /**
     * 账户名称
     */
    private String zhmc;
    /**
     * 贷款分户账号
     */
    private String dkfhzh;
    /**
     * 信贷借据号
     */
    private String xdjjh;
    /**
     * 核心交易日期
     */
    private String hxjyrq;
    /**
     * 核心交易时间
     */
    private String hxjysj;
    /**
     * 交易类型
     */
    private String jylx;
    /**
     * 交易借贷标志
     */
    private String jyjdbz;
    /**
     * 币种
     */
    private String bz;
    /**
     * 交易金额
     */
    private BigDecimal jyje;
    /**
     * 账户余额
     */
    private BigDecimal zhye;
    /**
     * 对方账号
     */
    private String dfzh;
    /**
     * 对方户名
     */
    private String dfhm;
    /**
     * 对方行号
     */
    private String dfxh;
    /**
     * 对方行名
     */
    private String dfxm;
    /**
     * 摘要
     */
    private String zy;
    /**
     * 交易渠道
     */
    private String jyqd;
    /**
     * 冲补抹标志
     */
    private String cbmbz;
    /**
     * 交易柜员号
     */
    private String jygyh;
    /**
     * 授权柜员号
     */
    private String sqgyh;
    /**
     * 现转标志
     */
    private String xzbz;
    /**
     * 备注
     */
    private String bbz;
    /**
     * 采集日期
     */
    private String cjrq;

}