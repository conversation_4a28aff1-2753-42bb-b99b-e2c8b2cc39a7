package io.ibs.modules.east.entity.egxdk;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.east.annotation.NumberScaleFormat;
import io.ibs.modules.east.entity.EastBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 对公信贷业务借据表
 *
 * @<NAME_EMAIL>
 * @since 1.0 2024-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_EAST_DGXDYWJJB")
public class EastDgxdywjjbEntity extends EastBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 金融许可证号
     */
    @TableField(fill = FieldFill.INSERT)
    private String jrxkzh;
    /**
     * 内部机构号
     */
    private String nbjgh;
    /**
     * 银行机构名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String yhjgmc;
    /**
     * 明细科目编号
     */
    private String mxkmbh;
    /**
     * 明细科目名称
     */
    private String mxkmmc;
    /**
     * 客户统一编号
     */
    private String khtybh;
    /**
     * 客户名称
     */
    private String khmc;
    /**
     * 信贷合同号
     */
    private String xdhth;
    /**
     * 信贷借据号
     */
    private String xdjjh;
    /**
     * 贷款分户账号
     */
    private String dkfhzh;
    /**
     * 信贷业务种类
     */
    private String xdywzl;
    /**
     * 贷款发放类型
     */
    private String dkfflx;
    /**
     * 放款方式
     */
    private String fkfs;
    /**
     * 币种
     */
    private String bz;
    /**
     * 贷款金额
     */
    private BigDecimal dkje;
    /**
     * 贷款余额
     */
    private BigDecimal dkye;
    /**
     * 贷款五级分类
     */
    private String dkwjfl;
    /**
     * 总期数
     */
    private Integer zqs;
    /**
     * 当前期数
     */
    private Integer dqqs;
    /**
     * 展期次数
     */
    private Integer zqcs;
    /**
     * 贷款发放日期
     */
    private String dkffrq;
    /**
     * 贷款到期日期
     */
    private String dkdqrq;
    /**
     * 终结日期
     */
    private String zjrq;
    /**
     * 欠本金额
     */
    private BigDecimal qbje;
    /**
     * 欠本日期
     */
    private String qbrq;
    /**
     * 表内欠息余额
     */
    private BigDecimal bnqxye;
    /**
     * 表外欠息余额
     */
    private BigDecimal bwqxye;
    /**
     * 欠息日期
     */
    private String qxrq;
    /**
     * 连续欠款期数
     */
    private Integer lxqkqs;
    /**
     * 累计欠款期数
     */
    private Integer ljqkqs;
    /**
     * 上笔信贷借据号
     */
    private String sbxdjjh;
    /**
     * 贷款入账账号
     */
    private String dkrzzh;
    /**
     * 贷款入账户名
     */
    private String dkrzhm;
    /**
     * 入账账号所属行名称
     */
    private String rzzhsshmc;
    /**
     * 利率类型
     */
    private String lllx;
    /**
     * 实际利率
     */
    @NumberScaleFormat(scale = 4)
    private BigDecimal sjll;
    /**
     * 还款方式
     */
    private String hkfs;
    /**
     * 还款账号
     */
    private String hkzh;
    /**
     * 还款账号所属行名称
     */
    private String hkzhsshmc;
    /**
     * 计息方式
     */
    private String jxfs;
    /**
     * 下期还款日期
     */
    private String xqhkrq;
    /**
     * 下期应还本金
     */
    private BigDecimal xqyhbj;
    /**
     * 下期应还利息
     */
    private BigDecimal xqyhlx;
    /**
     * 借据贷款用途
     */
    private String jjdkyt;
    /**
     * 贷款投向地区
     */
    private String dktxdq;
    /**
     * 贷款投向行业
     */
    private String dktxhy;
    /**
     * 是否互联网贷款
     */
    private String sfhlwdk;
    /**
     * 是否绿色贷款
     */
    private String sflsdk;
    /**
     * 是否涉农贷款
     */
    private String sfsndk;
    /**
     * 是否普惠型涉农贷款
     */
    private String sfphxsndk;
    /**
     * 是否普惠型小微企业贷款
     */
    private String sfphxxwqydk;
    /**
     * 是否科技贷款
     */
    private String sfkjdk;
    /**
     * 信贷员工号
     */
    private String xdygh;
    /**
     * 贷款状态
     */
    private String dkzt;
    /**
     * 备注
     */
    private String bbz;
    /**
     * 采集日期
     */
    private String cjrq;

}