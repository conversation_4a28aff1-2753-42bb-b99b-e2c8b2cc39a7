package io.ibs.modules.east.entity.fbnwdbxx;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.east.entity.EastBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 表内外业务担保合同表
 *
 * <AUTHOR>
 * @since 3.0 2024-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_EAST_BNWYWDBHTB")
public class EastBnwywdbhtbEntity extends EastBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 金融许可证号
     */
    @TableField(fill = FieldFill.INSERT)
    private String jrxkzh;
    /**
     * 内部机构号
     */
    private String nbjgh;
    /**
     * 被担保合同号
     */
    private String bdbhth;
    /**
     * 被担保业务类型
     */
    private String bdbywlx;
    /**
     * 担保合同号
     */
    private String dbhth;
    /**
     * 担保合同类型
     */
    private String dbhtlx;
    /**
     * 担保类型
     */
    private String dblx;
    /**
     * 担保币种
     */
    private String dbbz;
    /**
     * 担保金额
     */
    private BigDecimal dbje;
    /**
     * 担保起始日期
     */
    private String dbqsrq;
    /**
     * 担保到期日期
     */
    private String dbdqrq;
    /**
     * 经办人工号
     */
    private String jbrgh;
    /**
     * 担保合同状态
     */
    private String dbhtzt;
    /**
     * 备注
     */
    private String bbz;
    /**
     * 采集日期
     */
    private String cjrq;

}