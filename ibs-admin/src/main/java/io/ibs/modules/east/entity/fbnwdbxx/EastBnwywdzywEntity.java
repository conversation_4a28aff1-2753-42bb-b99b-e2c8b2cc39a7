package io.ibs.modules.east.entity.fbnwdbxx;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.east.annotation.Desensitization;
import io.ibs.modules.east.entity.EastBaseEntity;
import io.ibs.modules.east.enums.DesensitizationEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 表内外业务抵质押物
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_EAST_BNWYWDZYW")
public class EastBnwywdzywEntity extends EastBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 金融许可证号
     */
    @TableField(fill = FieldFill.INSERT)
    private String jrxkzh;
    /**
     * 内部机构号
     */
    private String nbjgh;
    /**
     * 担保合同号
     */
    private String dbhth;
    /**
     * 押品编号
     */
    private String ypbh;
    /**
     * 押品类型
     * 按照以下押品项目填报（序号+名称）：1.1现金及其等价物；1.2贵金属；1.3.1国债；1.3.2地方政府债；1.3.3央票；1.3.4政府机构债券；1.3.5政策性金融债；1.3.6商业性金融债；1.3.7非金融企业债；
     * 1.4票据；1.5股票（权）/基金；1.6保单；1.7资产管理产品；1.8其他金融质押品；
     * 2.应收账款类；3.1居住用房地产；3.2经营性房地产；3.3居住用房地产建设用地使用权；3.4经营性房地产建设用地使用权；
     * 3.5房产类在建工程；3.6其他房地产类押品；4.1存货、仓单和提单；4.2机器设备；4.3交通运输设备；
     * 4.4资源资产；4.5知识产权；4.6其他-银行自定义。无法以枚举类型填写的，填报为“4.6其他-XX”，其中“XX”为银行自定义类型。
     */
    private String yplx;
    /**
     * 押品名称
     */
    private String ypmc;
    /**
     * 抵质押物状态
     * 正常，冻结，查封，扣押，其他-银行自定义。无法以枚举类型填报的，以“其他-XX”填报，其中“XX”为银行自定义抵质押物状态。
     */
    private String dzywzt;
    /**
     * 币种
     */
    private String bz;
    /**
     * 起始估值
     */
    private BigDecimal pgjz;
    /**
     * 最新估值
     */
    private BigDecimal yxrdjz;
    /**
     * 已抵押价值
     */
    private BigDecimal ydyjz;
    /**
     * 抵质押率
     */
    private String dzyl;
    /**
     * 处置权顺位
     * 第一顺位，第二顺位，第三顺位...以此类推。
     */
    private String czqsw;
    /**
     * 押品所有人名称
     */
    @Desensitization(type = DesensitizationEnum.CUSTOMER_NAME, conditionMethod = "desensitizeCondition")
    private String ypsyrmc;
    /**
     * 押品所有人证件类别
     * 同业客户：银行机构代码，金融许可证号，SWIFT编码，其他-XX。
     * 对公客户：统一社会信用代码，组织机构代码，营业执照（工商注册号），公司注册证书，全球法人识别码，其他-XX。
     * 个人客户：居民身份证，军官证，文职干部证，警官证，士兵证，户口簿，临时身份证，其他有效通行旅行证件，护照，学生证，无证件，其他-XX。
     * 无法以枚举类型填报的，以“其他-XX”报送，其中“XX”为银行自定义类型。
     * 对公客户优先填报统一社会信用代码，没有统一社会信用代码的对公客户填报组织机构代码。组织机构代码、统一社会信用代码均没有的可以填报客户提供的批文文号、开户主证件、上级机构统一社会信用代码等，以“其他-XX”填报。
     */
    private String ypsyrzjlb;
    /**
     * 押品所有人证件号码
     */
    @Desensitization(type = DesensitizationEnum.ID_NO, relationField = "ypsyrmc", conditionMethod = "desensitizeCondition")
    private String ypsyrzjhm;
    /**
     * 质押票证号码
     */
    private String zypzhm;
    /**
     * 质押票证签发机构
     */
    private String zypzqfjg;
    /**
     * 权证登记号码
     */
    private String qzdjhm;
    /**
     * 权证登记面积
     */
    private BigDecimal qzdjmj;
    /**
     * 担保合同状态
     * 有效，失效。
     */
    private String dbhtzt;
    /**
     * 备注
     */
    private String bbz;
    /**
     * 采集日期
     */
    private String cjrq;

    /**
     * 所有权人为个人时，则为隐私，需要变形
     */
    public boolean desensitizeCondition() {
        return "居民身份证".equals(this.ypsyrzjlb) || "户口簿".equals(this.ypsyrzjlb);
    }
}