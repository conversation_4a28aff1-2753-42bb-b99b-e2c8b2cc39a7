package io.ibs.modules.east.enums;

import lombok.Getter;

/**
 * Created by AileYoung on 2024/7/8.
 */
@Getter
public enum EastFiveCate {
    CATE_1("正常类", "1"),
    CATE_2("关注类", "2"),
    CATE_3("次级类", "3"),
    CATE_4("可疑类", "4"),
    CATE_5("损失类", "5");
    private final String cateName;
    private final String cateValue;

    EastFiveCate(String cateName, String cateValue) {
        this.cateName = cateName;
        this.cateValue = cateValue;
    }

    public static EastFiveCate getByCateName(String cateName) {
        for (EastFiveCate value : EastFiveCate.values()) {
            if (value.getCateName().contains(cateName)) {
                return value;
            }
        }
        return CATE_1;
    }
}
