package io.ibs.modules.east.excel.bkhxx;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 个人客户关系表
 *
 * <AUTHOR> 
 * @since 1.0 2024-03-12
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EastPersonalCustomerExcel {
    @ExcelProperty(value = "金融许可证号", index = 0)
    private String jrxkzh;
    @ExcelProperty(value = "内部机构号", index = 1)
    private String nbjgh;
    @ExcelProperty(value = "客户统一编号", index = 2)
    private String khtybh;
    @ExcelProperty(value = "客户姓名", index = 3)
    private String khxm;
    @ExcelProperty(value = "证件类别", index = 4)
    private String zjlb;
    @ExcelProperty(value = "证件号码", index = 5)
    private String zjhm;
    @ExcelProperty(value = "关系类型", index = 6)
    private String gxlx;
    @ExcelProperty(value = "关系人客户统一编号", index = 7)
    private String gxrkhtybh;
    @ExcelProperty(value = "关系人名称", index = 8)
    private String gxrmc;
    @ExcelProperty(value = "关系人证件类别", index = 9)
    private String gxrzjlb;
    @ExcelProperty(value = "关系人证件号码", index = 10)
    private String gxrzjhm;
    @ExcelProperty(value = "关系状态", index = 11)
    private String gxzt;
    @ExcelProperty(value = "备注", index = 12)
    private String bbz;
    @ExcelProperty(value = "采集日期", index = 13)
    private String cjrq;
}