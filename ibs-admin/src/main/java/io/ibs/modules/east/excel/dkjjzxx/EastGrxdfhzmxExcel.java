package io.ibs.modules.east.excel.dkjjzxx;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 个人信贷分户账明细记录
 *
 * <AUTHOR>
 * @since 3.0 2024-03-13
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EastGrxdfhzmxExcel {
    @ExcelProperty(value = "交易序列号", index = 0)
    private String jyxlh;
    @ExcelProperty(value = "金融许可证号", index = 1)
    private String jrxkzh;
    @ExcelProperty(value = "内部机构号", index = 2)
    private String nbjgh;
    @ExcelProperty(value = "业务办理机构号", index = 3)
    private String ywbljgh;
    @ExcelProperty(value = "银行机构名称", index = 4)
    private String yhjgmc;
    @ExcelProperty(value = "明细科目编号", index = 5)
    private String mxkmbh;
    @ExcelProperty(value = "明细科目名称", index = 6)
    private String mxkmmc;
    @ExcelProperty(value = "客户统一编号", index = 7)
    private String khtybh;
    @ExcelProperty(value = "账户名称", index = 8)
    private String zhmc;
    @ExcelProperty(value = "证件类别", index = 9)
    private String zjlb;
    @ExcelProperty(value = "证件号码", index = 10)
    private String zjhm;
    @ExcelProperty(value = "贷款分户账号", index = 11)
    private String dkfhzh;
    @ExcelProperty(value = "信贷借据号", index = 12)
    private String xdjjh;
    @ExcelProperty(value = "核心交易日期", index = 13)
    private String hxjyrq;
    @ExcelProperty(value = "核心交易时间", index = 14)
    private String hxjysj;
    @ExcelProperty(value = "交易类型", index = 15)
    private String jylx;
    @ExcelProperty(value = "交易借贷标志", index = 16)
    private String jyjdbz;
    @ExcelProperty(value = "币种", index = 17)
    private String bz;
    @ExcelProperty(value = "交易金额", index = 18)
    private BigDecimal jyje;
    @ExcelProperty(value = "账户余额", index = 19)
    private BigDecimal zhye;
    @ExcelProperty(value = "对方账号", index = 20)
    private String dfzh;
    @ExcelProperty(value = "对方户名", index = 21)
    private String dfhm;
    @ExcelProperty(value = "对方行号", index = 22)
    private String dfxh;
    @ExcelProperty(value = "对方行名", index = 23)
    private String dfxm;
    @ExcelProperty(value = "摘要", index = 24)
    private String zy;
    @ExcelProperty(value = "交易渠道", index = 25)
    private String jyqd;
    @ExcelProperty(value = "冲补抹标志", index = 26)
    private String cbmbz;
    @ExcelProperty(value = "代办人姓名", index = 27)
    private String dbrxm;
    @ExcelProperty(value = "代办人证件类别", index = 28)
    private String dbrzjlb;
    @ExcelProperty(value = "代办人证件号码", index = 29)
    private String dbrzjhm;
    @ExcelProperty(value = "交易柜员号", index = 30)
    private String jygyh;
    @ExcelProperty(value = "授权柜员号", index = 31)
    private String sqgyh;
    @ExcelProperty(value = "现转标志", index = 32)
    private String xzbz;
    @ExcelProperty(value = "备注", index = 33)
    private String bbz;
    @ExcelProperty(value = "采集日期", index = 34)
    private String cjrq;
}