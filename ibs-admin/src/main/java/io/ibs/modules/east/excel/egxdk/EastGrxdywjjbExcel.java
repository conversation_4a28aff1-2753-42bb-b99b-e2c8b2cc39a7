package io.ibs.modules.east.excel.egxdk;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 个人信贷业务借据表
 *
 * @<NAME_EMAIL>
 * @since 1.0 2024-03-14
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EastGrxdywjjbExcel {
    @ExcelProperty(value = "金融许可证号", index = 0)
    private String jrxkzh;
    @ExcelProperty(value = "内部机构号", index = 1)
    private String nbjgh;
    @ExcelProperty(value = "银行机构名称", index = 2)
    private String yhjgmc;
    @ExcelProperty(value = "明细科目编号", index = 3)
    private String mxkmbh;
    @ExcelProperty(value = "明细科目名称", index = 4)
    private String mxkmmc;
    @ExcelProperty(value = "客户统一编号", index = 5)
    private String khtybh;
    @ExcelProperty(value = "客户名称", index = 6)
    private String khmc;
    @ExcelProperty(value = "证件类别", index = 7)
    private String zjlb;
    @ExcelProperty(value = "证件号码", index = 8)
    private String zjhm;
    @ExcelProperty(value = "信贷合同号", index = 9)
    private String xdhth;
    @ExcelProperty(value = "信贷借据号", index = 10)
    private String xdjjh;
    @ExcelProperty(value = "贷款分户账号", index = 11)
    private String dkfhzh;
    @ExcelProperty(value = "信贷业务种类", index = 12)
    private String xdywzl;
    @ExcelProperty(value = "贷款发放类型", index = 13)
    private String dkfflx;
    @ExcelProperty(value = "放款方式", index = 14)
    private String fkfs;
    @ExcelProperty(value = "币种", index = 15)
    private String bz;
    @ExcelProperty(value = "贷款金额", index = 16)
    private BigDecimal dkje;
    @ExcelProperty(value = "贷款余额", index = 17)
    private BigDecimal dkye;
    @ExcelProperty(value = "贷款五级分类", index = 18)
    private String dkwjfl;
    @ExcelProperty(value = "总期数", index = 19)
    private Integer zqs;
    @ExcelProperty(value = "当前期数", index = 20)
    private Integer dqqs;
    @ExcelProperty(value = "展期次数", index = 21)
    private Integer zqcs;
    @ExcelProperty(value = "贷款发放日期", index = 22)
    private String dkffrq;
    @ExcelProperty(value = "贷款到期日期", index = 23)
    private String dkdqrq;
    @ExcelProperty(value = "终结日期", index = 24)
    private String zjrq;
    @ExcelProperty(value = "欠本金额", index = 25)
    private BigDecimal qbje;
    @ExcelProperty(value = "欠本日期", index = 26)
    private String qbrq;
    @ExcelProperty(value = "表内欠息余额", index = 27)
    private BigDecimal bnqxye;
    @ExcelProperty(value = "表外欠息余额", index = 28)
    private BigDecimal bwqxye;
    @ExcelProperty(value = "欠息日期", index = 29)
    private String qxrq;
    @ExcelProperty(value = "连续欠款期数", index = 30)
    private Integer lxqkqs;
    @ExcelProperty(value = "累计欠款期数", index = 31)
    private Integer ljqkqs;
    @ExcelProperty(value = "上笔信贷借据号", index = 32)
    private String sbxdjjh;
    @ExcelProperty(value = "贷款入账账号", index = 33)
    private String dkrzzh;
    @ExcelProperty(value = "贷款入账户名", index = 34)
    private String dkrzhm;
    @ExcelProperty(value = "入账账号所属行名称", index = 35)
    private String rzzhsshmc;
    @ExcelProperty(value = "利率类型", index = 36)
    private String lllx;
    @ExcelProperty(value = "实际利率", index = 37)
    private BigDecimal sjll;
    @ExcelProperty(value = "还款方式", index = 38)
    private String hkfs;
    @ExcelProperty(value = "还款账号", index = 39)
    private String hkzh;
    @ExcelProperty(value = "还款账号所属行名称", index = 40)
    private String hkzhsshmc;
    @ExcelProperty(value = "计息方式", index = 41)
    private String jxfs;
    @ExcelProperty(value = "下期还款日期", index = 42)
    private String xqhkrq;
    @ExcelProperty(value = "下期应还本金", index = 43)
    private BigDecimal xqyhbj;
    @ExcelProperty(value = "下期应还利息", index = 44)
    private BigDecimal xqyhlx;
    @ExcelProperty(value = "借据贷款用途", index = 45)
    private String jjdkyt;
    @ExcelProperty(value = "贷款投向地区", index = 46)
    private String dktxdq;
    @ExcelProperty(value = "贷款投向行业", index = 47)
    private String dktxhy;
    @ExcelProperty(value = "是否互联网贷款", index = 48)
    private String sfhlwdk;
    @ExcelProperty(value = "是否绿色贷款", index = 49)
    private String sflsdk;
    @ExcelProperty(value = "是否涉农贷款", index = 50)
    private String sfsndk;
    @ExcelProperty(value = "是否普惠型涉农贷款", index = 51)
    private String sfphxsndk;
    @ExcelProperty(value = "是否普惠型小微企业贷款", index = 52)
    private String sfphxxwqydk;
    @ExcelProperty(value = "是否科技贷款", index = 53)
    private String sfkjdk;
    @ExcelProperty(value = "信贷员工号", index = 54)
    private String xdygh;
    @ExcelProperty(value = "贷款状态", index = 55)
    private String dkzt;
    @ExcelProperty(value = "备注", index = 56)
    private String bbz;
    @ExcelProperty(value = "采集日期", index = 57)
    private String cjrq;
}