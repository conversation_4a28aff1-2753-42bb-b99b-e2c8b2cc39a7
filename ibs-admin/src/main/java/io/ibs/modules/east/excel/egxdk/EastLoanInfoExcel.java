package io.ibs.modules.east.excel.egxdk;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * East借据信息表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2024-07-08
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EastLoanInfoExcel {
    @ExcelProperty(value = "借据号", index = 0)
    private String loanNo;
    @ExcelProperty(value = "五级分类", index = 1)
    private String fiveCate;
    @ExcelProperty(value = "采集日期", index = 2)
    private String cjrq;
}