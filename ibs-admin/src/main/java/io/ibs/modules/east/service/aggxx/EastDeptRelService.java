package io.ibs.modules.east.service.aggxx;

import io.ibs.common.service.CrudService;
import io.ibs.modules.east.dto.aggxx.EastDeptRelDTO;
import io.ibs.modules.east.entity.aggxx.EastDeptRelEntity;

import java.util.List;

/**
 * 机构关系表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
public interface EastDeptRelService extends CrudService<EastDeptRelEntity, EastDeptRelDTO> {


    List<EastDeptRelDTO> getDataFormHost(String startDate, String endDate);

    /**
     * 上传机构关系表数据,并将数据放入表中
     *
     * @param filePath
     */
    void importExcel(String filePath, String cjrq);

    /**
     * 获取截止endDate 之前最大的一个采集日期的数据
     *
     * @param endDate 采集日期
     */
    List<EastDeptRelEntity> getLastData(String endDate);
}