package io.ibs.modules.east.service.aggxx;

import io.ibs.common.service.CrudService;
import io.ibs.modules.east.dto.aggxx.EastDeptDTO;
import io.ibs.modules.east.entity.aggxx.EastDeptEntity;

import java.util.List;
import java.util.Map;

/**
 * 机构信息表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
public interface EastDeptService extends CrudService<EastDeptEntity, EastDeptDTO> {
    /**
     * 从核心获取机构信息
     *
     * @return
     */
    List<EastDeptEntity> getDeptFromHost();

    /**
     * 获取机构信息
     * key-机构号 value-机构名称
     *
     * @return map
     */
    Map<String, String> getDeptMapFromHost();

    /**
     * 上传机构信息数据,并将数据放入表中
     *
     * @param filePath
     */
    void importExcel(String filePath, String cjrq);

    EastDeptEntity getDeptByInstNo(String instNo);

    /**
     * 获取截止endDate 之前最大的一个采集日期的数据
     *
     * @param endDate 采集日期
     */
    List<EastDeptEntity> getLastData(String endDate);
}