package io.ibs.modules.east.service.aggxx.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.common.validator.AssertUtils;
import io.ibs.modules.east.dao.aggxx.EastDeptRelDao;
import io.ibs.modules.east.dto.aggxx.EastDeptRelDTO;
import io.ibs.modules.east.entity.aggxx.EastDeptRelEntity;
import io.ibs.modules.east.excel.aggxx.EastDeptRelExcel;
import io.ibs.modules.east.service.aggxx.EastDeptRelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 机构关系表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Slf4j
@Service
public class EastDeptRelServiceImpl extends CrudServiceImpl<EastDeptRelDao, EastDeptRelEntity, EastDeptRelDTO> implements EastDeptRelService {

    private final EastDeptRelDao eastDeptRelDao;

    public EastDeptRelServiceImpl(EastDeptRelDao eastDeptRelDao) {
        this.eastDeptRelDao = eastDeptRelDao;
    }

    @Override
    public QueryWrapper<EastDeptRelEntity> getWrapper(Map<String, Object> params) {
        String yhjgdm = (String) params.get("yhjgdm");
        String nbjgh = (String) params.get("nbjgh");
        String yhjgmc = (String) params.get("yhjgmc");
        String yyzt = (String) params.get("yyzt");
        QueryWrapper<EastDeptRelEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNoneBlank(yhjgdm), "YHJGDM", yhjgdm);
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        wrapper.eq(StringUtils.isNoneBlank(yyzt), "YYZT", yyzt);
        wrapper.like(StringUtils.isNoneBlank(yhjgmc), "YHJGMC", yhjgmc);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }

    @Override
    public List<EastDeptRelDTO> getDataFormHost(String startDate, String endDate) {
        List<EastDeptRelEntity> deptRelEntities = eastDeptRelDao.getDataFromHost(startDate, endDate);
        return ConvertUtils.sourceToTarget(deptRelEntities, EastDeptRelDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importExcel(String filePath, String cjrq) {
        log.info("机构关系表数据导入，采集日期：{}", cjrq);
        if (StringUtils.isNotBlank(cjrq)) {
            baseDao.delete(new QueryWrapper<EastDeptRelEntity>().lambda()
                    .eq(EastDeptRelEntity::getCjrq, cjrq));
        }
        EasyExcel.read(filePath, EastDeptRelExcel.class, new PageReadListener<EastDeptRelExcel>(datalist -> {
            for (EastDeptRelExcel deptRel : datalist) {
                EastDeptRelEntity entity = ConvertUtils.sourceToTarget(deptRel, EastDeptRelEntity.class);
                AssertUtils.isBlank(entity.getNbjgh(), "导入失败，内部机构号不允许为空");
                AssertUtils.isBlank(entity.getCjrq(), "导入失败，采集日期不允许为空");
                AssertUtils.isEquals(entity.getCjrq(), cjrq, "导入失败，采集日期不一致");
                baseDao.insert(entity);
            }
        })).sheet().doRead();

    }

    @Override
    public List<EastDeptRelEntity> getLastData(String endDate) {
        EastDeptRelEntity deptEntity = baseDao.selectOne(new QueryWrapper<EastDeptRelEntity>().select("max(cjrq) cjrq").lt("CJRQ", endDate));
        if (ObjectUtil.isNotEmpty(deptEntity)) {
            List<EastDeptRelEntity> list = baseDao.selectList(new LambdaQueryWrapper<EastDeptRelEntity>().eq(EastDeptRelEntity::getCjrq, deptEntity.getCjrq()));
            list.forEach(entity -> {
                entity.setId(null);
                entity.setCjrq(endDate);
            });
            return list;
        }
        return new ArrayList<>();
    }
}