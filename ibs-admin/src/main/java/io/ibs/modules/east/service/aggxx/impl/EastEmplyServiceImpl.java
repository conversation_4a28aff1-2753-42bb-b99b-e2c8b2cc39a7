package io.ibs.modules.east.service.aggxx.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.common.validator.AssertUtils;
import io.ibs.modules.east.common.DictExch;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.dao.aggxx.EastEmplyDao;
import io.ibs.modules.east.dto.aggxx.EastEmplyDTO;
import io.ibs.modules.east.entity.aggxx.EastEmplyEntity;
import io.ibs.modules.east.entity.aggxx.EastPostInfoEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.excel.aggxx.EastEmplyExcel;
import io.ibs.modules.east.service.aggxx.EastEmplyService;
import io.ibs.modules.sys.service.SysParamsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 员工表
 * Created by AileYoung on 20240529
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EastEmplyServiceImpl extends CrudServiceImpl<EastEmplyDao, EastEmplyEntity, EastEmplyDTO> implements EastEmplyService {
    private final SysParamsService sysParamsService;

    @Override
    public QueryWrapper<EastEmplyEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastEmplyEntity> wrapper = new QueryWrapper<>();

        String gh = (String) params.get("gh");
        wrapper.eq(StringUtils.isNotBlank(gh), "GH", gh);
        String xm = (String) params.get("xm");
        wrapper.like(StringUtils.isNotBlank(xm), "XM", xm);
        String zjhm = (String) params.get("zjhm");
        wrapper.eq(StringUtils.isNotBlank(zjhm), "ZJHM", zjhm);
        String ygzt = (String) params.get("ygzt");
        wrapper.eq(StringUtils.isNotBlank(ygzt), "YGZT", ygzt);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        wrapper.orderByAsc("GH");
        return wrapper;
    }

    @Override
    public List<EastEmplyEntity> getEmplyFromHost(String startDate, String endDate) {
        List<EastEmplyEntity> result = new ArrayList<>();
        List<EastEmplyEntity> lastList = new ArrayList<>();
        EastEmplyEntity lastEntity = baseDao.selectOne(new QueryWrapper<>(EastEmplyEntity.class)
                .select("max(cjrq) cjrq")
                .lt("CJRQ", endDate));
        if (lastEntity != null) {
            lastList = baseDao.selectList(new LambdaQueryWrapper<>(EastEmplyEntity.class)
                    .eq(EastEmplyEntity::getCjrq, lastEntity.getCjrq())
                    .eq(EastEmplyEntity::getYgzt, "在岗"));
        }
        List<String> ghFilter = new ArrayList<>();
        String ygbFilter = sysParamsService.getValue("EAST_YGB_FILTER");
        if (StringUtils.isNotBlank(ygbFilter)) {
            ghFilter = Arrays.asList(ygbFilter.split(",", -1));
        }
        List<EastEmplyEntity> emplyList = baseDao.getEmplyFromHost(startDate, endDate);
        // 遍历本期员工，如果上一期已经采集到，则将上一期员工的部分信息复制给本期员工；如果是新员工，则直接添加到结果集
        for (EastEmplyEntity emply : emplyList) {
            // 过滤不采集的员工号
            if (ghFilter.contains(emply.getGh())) {
                continue;
            }
            emply.setYgzt(DictExch.tellerStatusExch(emply.getYgzt()));
            if (CollectionUtil.contains(lastList, emp -> emp.getGh().equals(emply.getGh()))) {
                EastEmplyEntity lastEmply = lastList.get(CollectionUtil.indexOf(lastList, emp -> emp.getGh().equals(emply.getGh())));
                emply.setZjhm(lastEmply.getZjhm());// 证件号码
                emply.setSsbm(lastEmply.getSsbm());// 所属部门
                emply.setGwbh(lastEmply.getGwbh());// 岗位编号
                emply.setGwmc(lastEmply.getGwmc());// 岗位名称
                emply.setSfgg(lastEmply.getSfgg());// 是否高管
                emply.setLxdh(lastEmply.getLxdh());// 联系电话
                emply.setPfrq(lastEmply.getPfrq());// 批复日期
                emply.setRzrq(lastEmply.getRzrq());// 任职日期
                result.add(emply);
            } else {
                List<EastPostInfoEntity> postInfoEntityList = Db.list(new LambdaQueryWrapper<>(EastPostInfoEntity.class)
                        .eq(EastPostInfoEntity::getGwbh, emply.getGwbh())
                        .orderByDesc(EastPostInfoEntity::getCjrq));
                if (!postInfoEntityList.isEmpty()) {
                    emply.setGwmc(postInfoEntityList.get(0).getGwmc());
                }
                result.add(emply);
            }
        }

        // 遍历上一期员工信息，将本期未采集到的上一期的员工合并到结果中
        for (EastEmplyEntity lastEmply : lastList) {
            if (!CollectionUtil.contains(result, emply -> emply.getGh().equals(lastEmply.getGh())) &&
                    !ghFilter.contains(lastEmply.getGh())) {
                lastEmply.setId(null);
                lastEmply.setCjrq(endDate);
                result.add(lastEmply);
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importExcel(String filePath, String cjrq) {
        log.info("员工数据导入，采集日期：{}", cjrq);
        if (StringUtils.isNotBlank(cjrq)) {
            baseDao.delete(new QueryWrapper<EastEmplyEntity>().lambda()
                    .eq(EastEmplyEntity::getCjrq, cjrq));
        }
        EasyExcel.read(filePath, EastEmplyExcel.class, new PageReadListener<EastEmplyExcel>(datalist -> {
            for (EastEmplyExcel emply : datalist) {
                EastEmplyEntity entity = ConvertUtils.sourceToTarget(emply, EastEmplyEntity.class);
                AssertUtils.isBlank(entity.getGh(), "导入失败，员工工号不能为空");
                AssertUtils.isBlank(entity.getCjrq(), "导入失败，采集日期不能为空");
                AssertUtils.isEquals(cjrq, entity.getCjrq(), "导入失败，采集日期不一致");
                baseDao.insert(entity);
            }
        })).sheet().doRead();
        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(cjrq);
        eastDataRecord.setTableId(EastTableInfo.TABLE_102.getId());
        eastDataRecord.setParams("import");
        Db.save(eastDataRecord);
    }
}