package io.ibs.modules.east.service.bkhxx;

import io.ibs.common.service.CrudService;
import io.ibs.modules.east.dto.bkhxx.EastPersonalCustomerDTO;
import io.ibs.modules.east.entity.bkhxx.EastPersonalCustomerEntity;

import java.util.List;

/**
 * 个人客户关系表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
public interface EastPersonalCustomerService extends CrudService<EastPersonalCustomerEntity, EastPersonalCustomerDTO> {

    List<EastPersonalCustomerEntity> getEastPersonalCustomerData(String cjrq);

    /**
     * 上传个人客户关系表数据,并将数据放入表中
     *
     * @param filePath
     */
    void importExcel(String filePath, String cjrq);
}