package io.ibs.modules.east.service.bkhxx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.dao.bkhxx.EastGroupCustomerDao;
import io.ibs.modules.east.dto.bkhxx.EastGroupCustomerDTO;
import io.ibs.modules.east.entity.bkhxx.EastGroupCustomerEntity;
import io.ibs.modules.east.service.bkhxx.EastGroupCustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 集团客户表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Service
public class EastGroupCustomerServiceImpl extends CrudServiceImpl<EastGroupCustomerDao, EastGroupCustomerEntity, EastGroupCustomerDTO> implements EastGroupCustomerService {

    @Override
    public QueryWrapper<EastGroupCustomerEntity> getWrapper(Map<String, Object> params) {
        String jrxkzh = (String) params.get("jrxkzh");
        String nbjgh = (String) params.get("nbjgh");
        String yhjgmc = (String) params.get("yhjgmc");
        String jtbh = (String) params.get("jtbh");
        String cykhtybh = (String) params.get("cykhtybh");
        QueryWrapper<EastGroupCustomerEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(jrxkzh), "JRXKZH", jrxkzh);
        wrapper.eq(StringUtils.isNotBlank(nbjgh), "NBJGH", nbjgh);
        wrapper.eq(StringUtils.isNotBlank(jtbh), "JTBH", jtbh);
        wrapper.eq(StringUtils.isNotBlank(cykhtybh), "CYKHTYBH", cykhtybh);
        wrapper.like(StringUtils.isNotBlank(yhjgmc), "YHJGMC", yhjgmc);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }


}