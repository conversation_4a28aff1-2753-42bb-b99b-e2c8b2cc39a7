package io.ibs.modules.east.service.ckpxx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.dao.ckpxx.EastBankbookInfoDao;
import io.ibs.modules.east.dto.ckpxx.EastBankbookInfoDTO;
import io.ibs.modules.east.entity.ckpxx.EastBankbookInfoEntity;
import io.ibs.modules.east.service.ckpxx.EastBankbookInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 存折信息表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
@Service
public class EastBankbookInfoServiceImpl extends CrudServiceImpl<EastBankbookInfoDao, EastBankbookInfoEntity, EastBankbookInfoDTO> implements EastBankbookInfoService {

    @Override
    public QueryWrapper<EastBankbookInfoEntity> getWrapper(Map<String, Object> params) {
        String khtybh = (String) params.get("khtybh");
        String khmc = (String) params.get("khmc");
        String czzt = (String) params.get("czzt");
        String czh = (String) params.get("czh");
        String hqckzh = (String) params.get("hqckzh");
        QueryWrapper<EastBankbookInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(khtybh), "KHTYBH", khtybh);
        wrapper.eq(StringUtils.isNotBlank(czh), "CZH", czh);
        wrapper.eq(StringUtils.isNotBlank(hqckzh), "HQCKZH", hqckzh);
        wrapper.eq(StringUtils.isNotBlank(czzt), "CZZT", czzt);
        wrapper.like(StringUtils.isNotBlank(khmc), "KHMC", khmc);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }


    @Override
    public List<EastBankbookInfoEntity> getBookInfoOfSavingDeposit(String bgnDate, String endDate) {
        return baseDao.getBookInfoOfSavingDeposit(bgnDate, endDate);
    }

    @Override
    public List<EastBankbookInfoEntity> getBookInfoOfPTimeDeposit(String bgnDate, String endDate) {
        return baseDao.getBookInfoOfPTimeDeposit(bgnDate, endDate);
    }

    @Override
    public List<EastBankbookInfoEntity> getBookInfoOfCTimeDeposit(String bgnDate, String endDate) {
        return baseDao.getBookInfoOfCTimeDeposit(bgnDate, endDate);
    }
}