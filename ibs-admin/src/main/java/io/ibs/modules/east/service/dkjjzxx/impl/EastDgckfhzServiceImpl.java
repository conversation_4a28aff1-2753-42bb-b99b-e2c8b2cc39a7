package io.ibs.modules.east.service.dkjjzxx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.dao.dkjjzxx.EastDgckfhzDao;
import io.ibs.modules.east.dto.dkjjzxx.EastDgckfhzDTO;
import io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzEntity;
import io.ibs.modules.east.entity.other.AccBalEntity;
import io.ibs.modules.east.service.dkjjzxx.EastDgckfhzService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 对公存款分户账
 *
 * <AUTHOR>
 * @since 3.0 2024-03-13
 */
@Service
public class EastDgckfhzServiceImpl extends CrudServiceImpl<EastDgckfhzDao, EastDgckfhzEntity, EastDgckfhzDTO> implements EastDgckfhzService {

    @Override
    public QueryWrapper<EastDgckfhzEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastDgckfhzEntity> wrapper = new QueryWrapper<>();

        String jrxkzh = (String) params.get("jrxkzh");
        wrapper.eq(StringUtils.isNotBlank(jrxkzh), "JRXKZH", jrxkzh);
        String khtybh = (String) params.get("khtybh");
        wrapper.eq(StringUtils.isNotBlank(khtybh), "KHTYBH", khtybh);
        String zhzt = (String) params.get("zhzt");
        wrapper.eq(StringUtils.isNotBlank(zhzt), "ZHZT", zhzt);
        String zhmc = (String) params.get("zhmc");
        wrapper.like(StringUtils.isNotBlank(zhmc), "ZHMC", zhmc);
        String dgckzh = (String) params.get("dgckzh");
        wrapper.like(StringUtils.isNotBlank(dgckzh), "DGCKZH", dgckzh);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate) &&
                        StringUtils.isNotBlank(khtybh) && StringUtils.isNotBlank(zhzt) && StringUtils.isNotBlank(jrxkzh) &&
                        StringUtils.isNotBlank(zhmc) && StringUtils.isNotBlank(dgckzh) && StringUtils.isNoneBlank(nbjgh),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }

    @Override
    public List<EastDgckfhzEntity> getGrckfhzOfSavingDeposite(String bgnDate, String endDate) {
        return baseDao.getGrckfhzOfSavingDeposite(bgnDate, endDate);
    }

    @Override
    public List<EastDgckfhzEntity> getGrckfhzOfTimeDeposite(String bgnDate, String endDate) {
        return baseDao.getGrckfhzOfTimeDeposite(bgnDate, endDate);
    }

    @Override
    public List<EastDgckfhzEntity> getGrckfhzOfEarnestMoney(String bgnDate, String endDate) {
        return baseDao.getGrckfhzOfEarnestMoney(bgnDate, endDate);
    }

    @Override
    public List<AccBalEntity> getBalUntilDay(String endDate) {
        return baseDao.getBalUntilDay(endDate);
    }

    @Override
    public List<EastDgckfhzEntity> getTempTimeDeposite() {
        return baseDao.getTempTimeDeposite();
    }

    @Override
    public List<EastDgckfhzEntity> getTDOfAutoTransfer(String endDate) {
        return baseDao.getTDOfAutoTransfer(endDate);
    }

    @Override
    public List<AccBalEntity> getTDATBal(List<String> list, String endDate) {
        return baseDao.getTDATBal(list, endDate);
    }

    @Override
    public List<String> getTDPartDraw(String endDate) {
        return baseDao.getTDPartDraw(endDate);
    }
}