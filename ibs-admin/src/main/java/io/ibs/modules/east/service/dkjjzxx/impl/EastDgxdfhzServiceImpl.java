package io.ibs.modules.east.service.dkjjzxx.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.dao.dkjjzxx.EastDgxdfhzDao;
import io.ibs.modules.east.dto.dkjjzxx.EastDgxdfhzDTO;
import io.ibs.modules.east.entity.dkjjzxx.EastDgxdfhzEntity;
import io.ibs.modules.east.entity.other.AccBalEntity;
import io.ibs.modules.east.entity.other.LoanRateEntity;
import io.ibs.modules.east.service.common.EastCommonService;
import io.ibs.modules.east.service.dkjjzxx.EastDgxdfhzService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 对公信贷分户账
 *
 * <AUTHOR>
 * @since 1.0 2024-03-18
 */
@Service
@RequiredArgsConstructor
public class EastDgxdfhzServiceImpl extends CrudServiceImpl<EastDgxdfhzDao, EastDgxdfhzEntity, EastDgxdfhzDTO> implements EastDgxdfhzService {
    private final EastCommonService eastCommonService;

    @Override
    public QueryWrapper<EastDgxdfhzEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastDgxdfhzEntity> wrapper = new QueryWrapper<>();

        String xdjjh = (String) params.get("xdjjh");
        wrapper.eq(StringUtils.isNotBlank(xdjjh), "XDJJH", xdjjh);
        String khtybh = (String) params.get("khtybh");
        wrapper.eq(StringUtils.isNotBlank(khtybh), "KHTYBH", khtybh);
        String dkfhzh = (String) params.get("dkfhzh");
        wrapper.eq(StringUtils.isNotBlank(dkfhzh), "DKFHZH", dkfhzh);
        String zhzt = (String) params.get("zhzt");
        wrapper.eq(StringUtils.isNotBlank(zhzt), "ZHZT", zhzt);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate) && StringUtils.isNotBlank(khtybh) && StringUtils.isNotBlank(zhzt) && StringUtils.isNotBlank(xdjjh) && StringUtils.isNotBlank(dkfhzh) && StringUtils.isNoneBlank(nbjgh), "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }

    @Override
    public List<EastDgxdfhzEntity> getCorpLoanLedgerList(String startDate, String endDate, List<String> list) {
        List<EastDgxdfhzEntity> entitylist = new ArrayList<>();
        // 未结清借据号
        List<String> uncloseList = new ArrayList<>(eastCommonService.getUnCloseLoanNoList("1", endDate));
        // 排除对公转个人的借据
        uncloseList.removeIf(list::contains);
        if (!uncloseList.isEmpty()) {
            List<EastDgxdfhzEntity> tmp = this.getCorpLoanLedgerListByLoanList(startDate, endDate, uncloseList);
            if (!tmp.isEmpty()) {
                entitylist.addAll(tmp);
            }
        }

        // 结清借据号
        List<Map<String, Object>> closeList = eastCommonService.getCloseLoanNoList("1", startDate, endDate);
        List<String> closeBussNumList = new ArrayList<>();
        for (Map<String, Object> map : closeList) {
            closeBussNumList.add(map.get("loanno").toString());
        }
        // 排除对公转个人的借据
        closeBussNumList.removeIf(list::contains);
        if (!closeBussNumList.isEmpty()) {
            List<EastDgxdfhzEntity> tmp = this.getCorpLoanLedgerListByLoanList(startDate, endDate, closeBussNumList.stream().distinct().collect(Collectors.toList()));
            if (!tmp.isEmpty()) {
                for (EastDgxdfhzEntity eastDgxdfhz : tmp) {
                    closeList.forEach(closeMap -> {
                        if (eastDgxdfhz.getXdjjh().equals(closeMap.get("loanno").toString())) {
                            eastDgxdfhz.setXhrq(closeMap.get("lasttrandate").toString());
                        }
                    });
                }
                entitylist.addAll(tmp);
            }
        }

        return entitylist;
    }

    @Override
    public List<EastDgxdfhzEntity> getCorpLoanLedgerListByLoanList(String startDate, String endDate, List<String> loanList) {
        List<EastDgxdfhzEntity> result = new ArrayList<>();
        for (List<String> list : ListUtil.partition(loanList, 100)) {
            List<EastDgxdfhzEntity> entityList = baseDao.getCorpLoanLedgerListByLoanList(startDate, endDate, list);
            if (!entityList.isEmpty()) {
                result.addAll(entityList);
            }
        }
        return result;
    }


    @Override
    public List<AccBalEntity> getCorpLoanAccBal(String endDate, List<String> list) {
        return baseDao.getCorpLoanAccBal(endDate, list);
    }

    @Override
    public List<LoanRateEntity> getCorpLoanRate(String startDate, String endDate, List<String> list) {
        List<String> bussNumList = new ArrayList<>();
        List<LoanRateEntity> rateEntityList = new ArrayList<>();
        // 未结清借据号
        List<String> uncloseList = eastCommonService.getUnCloseLoanNoList("1", endDate);
        // 结清借据号
        List<Map<String, Object>> closeList = eastCommonService.getCloseLoanNoList("1", startDate, endDate);
        List<String> closeBussNumList = new ArrayList<>();
        for (Map<String, Object> map : closeList) {
            closeBussNumList.add(map.get("loanno").toString());
        }
        bussNumList.addAll(list);
        bussNumList.addAll(uncloseList);
        bussNumList.addAll(closeBussNumList);
        bussNumList = bussNumList.stream().distinct().collect(Collectors.toList());
        List<LoanRateEntity> tmp = this.getCorploanRateByLoanList(startDate, endDate, bussNumList);
        if (!tmp.isEmpty()) {
            rateEntityList.addAll(tmp);
        }

        return rateEntityList;
    }

    @Override
    public List<LoanRateEntity> getCorploanRateByLoanList(String startDate, String endDate, List<String> loanList) {
        List<LoanRateEntity> result = new ArrayList<>();
        for (List<String> list : ListUtil.partition(loanList, 100)) {
            List<LoanRateEntity> entityList = baseDao.getCorploanRateByLoanList(startDate, endDate, list);
            if (!entityList.isEmpty()) {
                result.addAll(entityList);
            }
        }
        return result;
    }

    @Override
    public List<LoanRateEntity> getCorpLoanInterestBal(List<String> list, String endDate) {
        return baseDao.getCorpLoanInterestBal(list, endDate);
    }

    @Override
    public List<AccBalEntity> getDueAccDlvTotl(List<String> list, String endDate) {
        return baseDao.getDueAccDlvTotl(list, endDate);
    }

    @Override
    public List<String> getWrongEndDateLoanOfCorp(String startDate, String endDate) {
        return baseDao.getWrongEndDateLoanOfCorp(startDate, endDate);
    }
}