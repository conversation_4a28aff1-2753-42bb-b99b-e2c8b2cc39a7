package io.ibs.modules.east.service.dkjjzxx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.dao.dkjjzxx.EastGeneralLedgerAcctDao;
import io.ibs.modules.east.dto.dkjjzxx.EastGeneralLedgerAcctDTO;
import io.ibs.modules.east.entity.dkjjzxx.EastGeneralLedgerAcctEntity;
import io.ibs.modules.east.entity.other.MNAccEntity;
import io.ibs.modules.east.service.dkjjzxx.EastGeneralLedgerAcctService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 总账会计全科目表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-13
 */
@Service
public class EastGeneralLedgerAcctServiceImpl extends CrudServiceImpl<EastGeneralLedgerAcctDao, EastGeneralLedgerAcctEntity, EastGeneralLedgerAcctDTO> implements EastGeneralLedgerAcctService {

    @Override
    public QueryWrapper<EastGeneralLedgerAcctEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastGeneralLedgerAcctEntity> wrapper = new QueryWrapper<>();

        String kjkmbh = (String) params.get("kjkmbh");
        wrapper.eq(StringUtils.isNotBlank(kjkmbh), "KJKMBH", kjkmbh);
        String kjkmmc = (String) params.get("kjkmmc");
        wrapper.like(StringUtils.isNotBlank(kjkmmc), "KJKMMC", kjkmmc);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate) &&
                        StringUtils.isNotBlank(kjkmbh) && StringUtils.isNotBlank(kjkmmc) && StringUtils.isNoneBlank(nbjgh),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }


    @Override
    public List<EastGeneralLedgerAcctEntity> getDailyGNLOflowestLvlItm(String accDate) {
        return baseDao.getDailyGNLOflowestLvlItm(accDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getDailyGNLOfHighLvlItm(String accDate) {
        return baseDao.getDailyGNLOfHighLvlItm(accDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getMonthlyGNL(String bgnDate, String endDate) {
        return baseDao.getMonthlyGNL(bgnDate, endDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getQuarterlyGNLOfHighLvlItm(String accDate) {
        return baseDao.getQuarterlyGNLOfHighLvlItm(accDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getSemiyearlyGNLOfHighLvlItm(String accDate) {
        return baseDao.getSemiyearlyGNLOfHighLvlItm(accDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getYearlyGNLOfHighLvlItm(String accDate) {
        return baseDao.getYearlyGNLOfHighLvlItm(accDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getGNLOflowestLvlItmPart(String bgnDate, String endDate) {
        return baseDao.getGNLOflowestLvlItmPart(bgnDate, endDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getGNLOflowestLvlItmOther(String bgnDate, String endDate) {
        return baseDao.getGNLOflowestLvlItmOther(bgnDate, endDate);
    }

    @Override
    public List<EastGeneralLedgerAcctEntity> getYearlyGNLOflowestLvlItm(String bgnDate, String endDate) {
        return baseDao.getYearlyGNLOflowestLvlItm(bgnDate, endDate);
    }

    @Override
    public List<MNAccEntity> getDiffOfMnaccAndDtl() {
        return baseDao.getDiffOfMnaccAndDtl();
    }
}