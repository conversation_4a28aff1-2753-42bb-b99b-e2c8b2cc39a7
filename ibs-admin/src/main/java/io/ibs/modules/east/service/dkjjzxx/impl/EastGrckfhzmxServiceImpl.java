package io.ibs.modules.east.service.dkjjzxx.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.DictExch;
import io.ibs.modules.east.dao.dkjjzxx.EastGrckfhzmxDao;
import io.ibs.modules.east.dto.dkjjzxx.EastGrckfhzmxDTO;
import io.ibs.modules.east.entity.aggxx.EastDeptEntity;
import io.ibs.modules.east.entity.bkhxx.EastBaseInfo;
import io.ibs.modules.east.entity.dkjjzxx.EastGrckfhzmxEntity;
import io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo;
import io.ibs.modules.east.service.common.EastCommonService;
import io.ibs.modules.east.service.common.impl.EastCommonServiceImpl;
import io.ibs.modules.east.service.dkjjzxx.EastGrckfhzmxService;
import io.ibs.modules.irp.pbccrc2.common.informix.entity.TFxjHist;
import io.ibs.modules.irp.pbccrc2.common.informix.service.TFxjHistService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 个人存款分户账明细记录
 *
 * @<NAME_EMAIL>
 * @since 3.0 2024-03-13
 */
@Service
@RequiredArgsConstructor
public class EastGrckfhzmxServiceImpl extends CrudServiceImpl<EastGrckfhzmxDao, EastGrckfhzmxEntity, EastGrckfhzmxDTO> implements EastGrckfhzmxService {
    private final EastCommonService eastCommonService;
    private final TFxjHistService fxjHistService;

    @Override
    public QueryWrapper<EastGrckfhzmxEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastGrckfhzmxEntity> wrapper = new QueryWrapper<>();

        String jyxlh = (String) params.get("jyxlh");
        wrapper.like(StringUtils.isNotBlank(jyxlh), "JYXLH", jyxlh);
        String grckzh = (String) params.get("grckzh");
        wrapper.eq(StringUtils.isNotBlank(grckzh), "GRCKZH", grckzh);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNotBlank(nbjgh), "NBJGH", nbjgh);
        String ywbljgh = (String) params.get("ywbljgh");
        wrapper.eq(StringUtils.isNotBlank(ywbljgh), "YWBLJGH", ywbljgh);
        String khtybh = (String) params.get("khtybh");
        wrapper.eq(StringUtils.isNotBlank(khtybh), "KHTYBH", khtybh);
        String zhmc = (String) params.get("zhmc");
        wrapper.like(StringUtils.isNotBlank(zhmc), "ZHMC", zhmc);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate) &&
                        StringUtils.isNotBlank(khtybh) && StringUtils.isNotBlank(jyxlh) && StringUtils.isNotBlank(grckzh) &&
                        StringUtils.isNotBlank(zhmc) && StringUtils.isNotBlank(ywbljgh) && StringUtils.isNoneBlank(nbjgh),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }

    @Override
    public List<EastGrckfhzmxEntity> getDataList(String startDate, String endDate) {
        List<EastGrckfhzmxEntity> list = baseDao.getCdmDtlList(startDate, endDate);
        List<EastGrckfhzmxEntity> fixList = baseDao.getFixDtlList(startDate, endDate);
        list.addAll(fixList);
        List<String> accList = list.parallelStream()
                .filter(l ->
                        StrUtil.isBlank(l.getDfzh()) ||
                                (StrUtil.isNotBlank(l.getDfzh()) && StrUtil.isBlank(l.getDfxh()))
                ).map(EastGrckfhzmxEntity::getWbzh).distinct().collect(Collectors.toList());
        List<TFxjHist> fxjList = new ArrayList<>();
        List<Map<String, String>> authTlrNoList = new ArrayList<>();
        List<EastPeerInfo> fixPeerAccList = new ArrayList<>();
        List<EastPeerInfo> tlrEntryAccList = new ArrayList<>();
        List<List<String>> accListPartition = ListUtil.partition(accList, 1000);
        for (List<String> pList : accListPartition) {
            fxjList.addAll(
                    fxjHistService.list(new QueryWrapper<TFxjHist>().lambda()
                            .in(TFxjHist::getAcc, pList)
                            .ge(TFxjHist::getTranDate, startDate)
                            .le(TFxjHist::getTranDate, endDate)));
            fixPeerAccList.addAll(eastCommonService.getFixPeerAcc(pList, startDate, endDate));
            tlrEntryAccList.addAll(eastCommonService.getPeerAccByTlrEntry(pList, startDate, endDate));
        }
        List<List<String>> allAccListPart = ListUtil.partition(list.parallelStream().map(EastGrckfhzmxEntity::getWbzh).distinct().collect(Collectors.toList()), 1000);
        allAccListPart.forEach(pList -> authTlrNoList.addAll(eastCommonService.getAuthTlrNoList(pList, startDate, endDate)));
        list.forEach(entity -> {
            // 从East基础信息表中同步基础信息
            EastBaseInfo eastBaseInfo = eastCommonService.getEastBaseInfo(entity.getKhtybh(), endDate);
            if (eastBaseInfo != null) {
                entity.setZhmc(eastBaseInfo.getUsername());
                entity.setZjlb(eastBaseInfo.getIdType());
                entity.setZjhm(eastBaseInfo.getIdNo());
            }
            int hostSeq = Integer.parseInt(entity.getJyxlh().substring(entity.getJyxlh().length() - 6));
            fxjList.parallelStream().filter(f ->
                    entity.getHxjyrq().equals(DateUtil.format(f.getTranDate(), Constants.EAST_DATE_PATTERN)) && hostSeq == f.getHostSeqno()
            ).findAny().ifPresent(e -> {
                entity.setDfzh(StrUtil.isBlank(e.getPeerAcc()) ? entity.getDfzh() : StrUtil.trim(e.getPeerAcc())); // 对方账号
                entity.setDfhm(StrUtil.isBlank(e.getPeerName()) ? entity.getDfhm() : StrUtil.trim(e.getPeerName())); // 对方户名
                entity.setDfxh(StrUtil.isBlank(e.getPeerInstno()) ? entity.getDfxh() : StrUtil.trim(e.getPeerInstno())); // 对方行号
                entity.setDfxm(StrUtil.isBlank(e.getPeerInstName()) ? entity.getDfxm() : StrUtil.trim(e.getPeerInstName())); // 对方行名
            });
            if (StrUtil.isBlank(entity.getDfzh())) {
                tlrEntryAccList.parallelStream().filter(t -> entity.getWbzh().equals(t.getAcc())
                                && hostSeq == t.getHostSeqno() && entity.getHxjyrq().equals(t.getTranDate()))
                        .map(EastPeerInfo::getDfzh).findFirst().ifPresent(acc -> {
                            EastPeerInfo peerInfo = eastCommonService.checkLedgerNo(acc);
                            entity.setDfzh(peerInfo.getAcc());
                            entity.setDfhm(peerInfo.getName());
                        });
            }
            // 发放工资交易对方信息
            if ("0510".equals(entity.getJylx())) {
                EastPeerInfo peerInfo = eastCommonService.getPeerAccByItm(entity.getNbjgh(), "263100");
                entity.setDfzh(peerInfo.getAcc());
                entity.setDfhm(peerInfo.getName());
            }
            // 结息交易对方信息
            if ("6183".equals(entity.getJylx())) {
                EastPeerInfo peerInfo = eastCommonService.getPeerAccByItm(entity.getNbjgh(), "811100");
                entity.setDfzh(peerInfo.getAcc());
                entity.setDfhm(peerInfo.getName());
            }
            // 无折转支交易对方信息
            if (StrUtil.isBlank(entity.getDfzh()) && "6105".equals(entity.getJylx())) {
                entity.setDfzh(fixPeerAccList.parallelStream()
                        .filter(f -> entity.getWbzh().equals(f.getDfzh()) && entity.getHxjyrq().equals(f.getTranDate()))
                        .map(EastPeerInfo::getAcc).findFirst().orElse(""));
                entity.setDfhm(entity.getZhmc());
            }
            // 转帐支出 转帐销户
            if (StrUtil.isBlank(entity.getDfzh()) && "1".equals(entity.getXzbz()) && ("0112".equals(entity.getJylx()) || "0302".equals(entity.getJylx()))) {
                entity.setXzbz("0");
                entity.setZy(StrUtil.replace(entity.getZy(), "转帐", ""));
            }
            EastPeerInfo checkLedgerNo = eastCommonService.checkLedgerNo(entity.getDfzh());
            if (checkLedgerNo != null && ObjectUtil.isNotEmpty(checkLedgerNo.getInstNo())) {
                entity.setDfhm(checkLedgerNo.getName());
                EastDeptEntity deptEntity = EastCommonServiceImpl.getDeptByInstNo(checkLedgerNo.getInstNo());
                entity.setDfxh(deptEntity.getYhjgdm());// 对方行号
                entity.setDfxm(deptEntity.getYhjgmc());// 对方行名
            }
            entity.setJygyh(Constants.VIRTUAL_EMPLOY.equals(entity.getJygyh()) ? Constants.VIRTUAL_TELLER : entity.getJygyh());
            entity.setJylx(DictExch.tranType(entity.getJylx()));
            entity.setJyjdbz(DictExch.drcrFlagExch(entity.getJyjdbz()));
            entity.setCbmbz(DictExch.cbmbzExch(entity.getCbmbz()));
            entity.setXzbz(DictExch.xzbzExch(entity.getXzbz()));
            String tlrIndex = entity.getWbzh() + entity.getHxjyrq() + hostSeq;
            entity.setSqgyh(authTlrNoList.stream().filter(map -> tlrIndex.equals(map.get("tlr_index"))).findFirst().orElse(new HashMap<>()).get("auth_tlr"));
            entity.setJyqd(DictExch.eastJyqd(entity.getZy(), entity.getJygyh())); // 交易渠道
            entity.setIpdz(""); // IP地址 当交易渠道为网银、手机银行，IP地址不允许为空
            entity.setMacdz("");// MAC地址 当交易渠道为网银、手机银行，MAC地址不允许为空
            entity.setCreator(Constants.UPDATER_FOR_TASK);
            entity.setUpdater(Constants.UPDATER_FOR_TASK);
            entity.setCjrq(endDate);
            // 重新映射 为转账
            if ("转".equals(entity.getXzbz()) && StrUtil.isNotBlank(entity.getDfzh()) && ("存现".equals(entity.getJylx()) || "取现".equals(entity.getJylx()))) {
                entity.setJylx("转账");
            }
            if ("现".equals(entity.getXzbz()) && StrUtil.isNotBlank(entity.getDfzh()) && "转账".equals(entity.getJylx()) && "借".equals(entity.getJyjdbz())) {
                entity.setJylx("取现");
            }
            if ("现".equals(entity.getXzbz()) && StrUtil.isNotBlank(entity.getDfzh()) && "转账".equals(entity.getJylx()) && "贷".equals(entity.getJyjdbz())) {
                entity.setJylx("存现");
            }
        });
        return list;
    }
}