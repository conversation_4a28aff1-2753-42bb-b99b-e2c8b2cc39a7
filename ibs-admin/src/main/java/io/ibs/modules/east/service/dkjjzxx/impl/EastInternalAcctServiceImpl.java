package io.ibs.modules.east.service.dkjjzxx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.common.DictExch;
import io.ibs.modules.east.dao.common.EastCommonDao;
import io.ibs.modules.east.dao.dkjjzxx.EastInternalAcctDao;
import io.ibs.modules.east.dto.dkjjzxx.EastInternalAcctDTO;
import io.ibs.modules.east.entity.dkjjzxx.EastInternalAcctEntity;
import io.ibs.modules.east.entity.other.DitmNoEntity;
import io.ibs.modules.east.service.dkjjzxx.EastInternalAcctService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 内部科目对照表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-14
 */
@Service
public class EastInternalAcctServiceImpl extends CrudServiceImpl<EastInternalAcctDao, EastInternalAcctEntity, EastInternalAcctDTO> implements EastInternalAcctService {

    @Autowired
    EastCommonDao eastCommonDao;

    @Override
    public QueryWrapper<EastInternalAcctEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastInternalAcctEntity> wrapper = new QueryWrapper<>();

        String kjkmbh = (String) params.get("kjkmbh");
        wrapper.eq(StringUtils.isNotBlank(kjkmbh), "KJKMBH", kjkmbh);
        String kjkmmc = (String) params.get("kjkmmc");
        wrapper.like(StringUtils.isNotBlank(kjkmmc), "KJKMMC", kjkmmc);
        String kjkmjc = (String) params.get("kjkmjc");
        wrapper.eq(StringUtils.isNotBlank(kjkmjc), "KJKMJC", kjkmjc);
        String sjkmbh = (String) params.get("sjkmbh");
        wrapper.eq(StringUtils.isNotBlank(sjkmbh), "SJKMBH", sjkmbh);
        String sjkmmc = (String) params.get("sjkmmc");
        wrapper.like(StringUtils.isNotBlank(sjkmmc), "SJKMMC", sjkmmc);
        String kmjdbz = (String) params.get("kmjdbz");
        wrapper.eq(StringUtils.isNotBlank(kmjdbz), "KMJDBZ", kmjdbz);
        String gsywdl = (String) params.get("gsywdl");
        wrapper.eq(StringUtils.isNotBlank(gsywdl), "GSYWDL", gsywdl);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate) &&
                        StringUtils.isNotBlank(kjkmbh) && StringUtils.isNotBlank(kjkmmc) && StringUtils.isNotBlank(kjkmjc) &&
                        StringUtils.isNotBlank(sjkmbh) && StringUtils.isNotBlank(sjkmmc) && StringUtils.isNoneBlank(nbjgh) &&
                        StringUtils.isNotBlank(kmjdbz) && StringUtils.isNotBlank(gsywdl),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }


    @Override
    public List<EastInternalAcctEntity> getInterAcct(String startDate, String endDate) {
        // 按照当期的总账会计科目来抽取一二三级科目
        List<DitmNoEntity> ditmList = eastCommonDao.getMonthDitm(startDate, endDate);
        if (ditmList == null || ditmList.size() == 0) {
            return new ArrayList<>();
        }

        List<EastInternalAcctEntity> list = baseDao.getInterAcct();

        list.removeIf(acc -> acc.getKjkmbh().length() == 8 && ditmList.stream().filter(ditm -> ditm.getInstNo().equals(acc.getNbjgh()) && ditm.getDitmNo().equals(acc.getKjkmbh())).count() == 0);

        List<Map<String, String>> mnaccBalshInfo = baseDao.getMnaccBalshInfo();
        List<Map<String, String>> ploshDescInfo = baseDao.getPloshDescInfo();

        list.forEach(itm -> {
            itm.setKjkmmc(itm.getKjkmmc().replace("�", ""));// 会计科目名称 解决乱码问题
            if (itm.getKjkmjc() == null) {
                itm.setKjkmjc(0);// 会计科目级次
            }
            itm.setGsywdl(DictExch.itmAccTypeExch(itm.getGsywdl()));// 归属业务大类
            itm.setKmjdbz(DictExch.drcrFlagExch(itm.getKmjdbz()));// 科目借贷标志
            // 三级科目则判断其业务子类
            if (itm.getKjkmbh() != null) {
                Map<String, String> mnaccBalshMap = mnaccBalshInfo.stream().filter(map -> itm.getKjkmbh().equals(map.get("ditmno"))).findFirst().orElse(null);
                if (mnaccBalshMap != null) {
                    // 如果业务大类是损益类，优先进行损益类子类映射
                    if ("损益".equals(itm.getGsywdl())) {
                        Map<String, String> ploshMap = ploshDescInfo.stream().filter(map -> itm.getKjkmbh().equals(map.get("ditmno"))).findFirst().orElse(null);
                        if (ploshMap != null) {
                            itm.setGsywzl(DictExch.ploshGsywzl(ploshMap.get("prgno")));
                        }
                    }
                    // 损益类映射失败时，进行资产负债子类映射
                    if (StringUtils.isBlank(itm.getGsywzl())) {
                        itm.setGsywzl(DictExch.mnaccBalshGsywzl(mnaccBalshMap.get("prgno")));
                    }
                } else {
                    Map<String, String> ploshMap = ploshDescInfo.stream().filter(map -> itm.getKjkmbh().equals(map.get("ditmno"))).findFirst().orElse(null);
                    if (ploshMap != null) {
                        itm.setGsywzl(DictExch.ploshGsywzl(ploshMap.get("prgno")));
                    }
                }
            }
            itm.setCjrq(endDate);
        });
        return list;
    }
}