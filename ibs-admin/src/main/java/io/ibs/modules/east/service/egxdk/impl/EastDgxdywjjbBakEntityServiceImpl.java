package io.ibs.modules.east.service.egxdk.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.dao.egxdk.EastDgxdywjjbBakEntityMapper;
import io.ibs.modules.east.dto.egxdk.EastDgxdywjjbDTO;
import io.ibs.modules.east.entity.egxdk.EastDgxdywjjbBakEntity;
import io.ibs.modules.east.service.egxdk.EastDgxdywjjbBakEntityService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 对公信贷业务借据表 资源服务实现类
 * Created by AileYoung on 2024-07-08 using generator
 */
@Service
public class EastDgxdywjjbBakEntityServiceImpl extends CrudServiceImpl<EastDgxdywjjbBakEntityMapper, EastDgxdywjjbBakEntity, EastDgxdywjjbDTO> implements EastDgxdywjjbBakEntityService {

    @Override
    public QueryWrapper<EastDgxdywjjbBakEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastDgxdywjjbBakEntity> wrapper = new QueryWrapper<>();

        String khmc = (String) params.get("khmc");
        wrapper.like(StringUtils.isNotBlank(khmc), "KHMC", khmc);
        String xdjjh = (String) params.get("xdjjh");
        wrapper.eq(StringUtils.isNotBlank(xdjjh), "XDJJH", xdjjh);
        String khtybh = (String) params.get("khtybh");
        wrapper.eq(StringUtils.isNotBlank(khtybh), "KHTYBH", khtybh);
        String xdywzl = (String) params.get("xdywzl");
        wrapper.eq(StringUtils.isNotBlank(xdywzl), "XDYWZL", xdywzl);
        String dkwjfl = (String) params.get("dkwjfl");
        wrapper.eq(StringUtils.isNotBlank(dkwjfl), "DKWJFL", dkwjfl);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String dkzt = (String) params.get("dkzt");
        wrapper.eq(StringUtils.isNotBlank(dkzt), "DKZT", dkzt);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }
}
