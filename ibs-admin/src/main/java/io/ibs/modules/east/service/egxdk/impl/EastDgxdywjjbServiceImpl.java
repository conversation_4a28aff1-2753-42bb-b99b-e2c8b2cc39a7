package io.ibs.modules.east.service.egxdk.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.exception.RenException;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.DictExch;
import io.ibs.modules.east.dao.dkjjzxx.EastDgxdfhzDao;
import io.ibs.modules.east.dao.egxdk.EastDgxdywjjbDao;
import io.ibs.modules.east.dto.egxdk.EastDgxdywjjbDTO;
import io.ibs.modules.east.entity.aggxx.EastDeptEntity;
import io.ibs.modules.east.entity.bkhxx.EastBaseInfo;
import io.ibs.modules.east.entity.dkjjzxx.EastDgxdfhzEntity;
import io.ibs.modules.east.entity.egxdk.EastDgxdywjjbEntity;
import io.ibs.modules.east.entity.egxdk.EastTzEntity;
import io.ibs.modules.east.entity.egxdk.TGfyhLoanNewRet;
import io.ibs.modules.east.entity.gxdglxx.EastZchxbEntity;
import io.ibs.modules.east.service.common.EastCommonService;
import io.ibs.modules.east.service.common.impl.EastCommonServiceImpl;
import io.ibs.modules.east.service.egxdk.EastDgxdywjjbService;
import io.ibs.modules.east.service.egxdk.EastTzService;
import io.ibs.modules.east.service.egxdk.LoanCommonService;
import io.ibs.modules.east.service.gxdglxx.EastZchxbService;
import io.ibs.modules.irp.pbccrc2.common.entity.EntLoanMonthEntity;
import io.ibs.modules.irp.pbccrc2.common.entity.LoanRetInfoEntity;
import io.ibs.modules.irp.pbccrc2.common.informix.entity.TLoanIou;
import io.ibs.modules.irp.pbccrc2.common.informix.entity.TLoanLedger;
import io.ibs.modules.irp.pbccrc2.common.informix.service.TLoanIouService;
import io.ibs.modules.irp.pbccrc2.common.informix.service.TLoanLedgerService;
import io.ibs.modules.irp.util.CacheUtil;
import io.ibs.modules.sys.service.SysParamsService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 对公信贷业务借据表
 *
 * @<NAME_EMAIL>
 * @since 1.0 2024-03-14
 */
@Service
@RequiredArgsConstructor
public class EastDgxdywjjbServiceImpl extends CrudServiceImpl<EastDgxdywjjbDao, EastDgxdywjjbEntity, EastDgxdywjjbDTO> implements EastDgxdywjjbService {
    private final LoanCommonService loanCommonService;
    private final TLoanLedgerService tLoanLedgerService;
    private final EastCommonService eastCommonService;
    private final EastDgxdfhzDao eastDgxdfhzDao;
    private final TLoanIouService tLoanIouService;
    private final SysParamsService sysParamsService;
    private final EastTzService eastTzService;
    private final EastZchxbService eastZchxbService;

    @Override
    public QueryWrapper<EastDgxdywjjbEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastDgxdywjjbEntity> wrapper = new QueryWrapper<>();

        String khmc = (String) params.get("khmc");
        wrapper.like(StringUtils.isNotBlank(khmc), "KHMC", khmc);
        String xdjjh = (String) params.get("xdjjh");
        wrapper.eq(StringUtils.isNotBlank(xdjjh), "XDJJH", xdjjh);
        String khtybh = (String) params.get("khtybh");
        wrapper.eq(StringUtils.isNotBlank(khtybh), "KHTYBH", khtybh);
        String xdywzl = (String) params.get("xdywzl");
        wrapper.eq(StringUtils.isNotBlank(xdywzl), "XDYWZL", xdywzl);
        String dkwjfl = (String) params.get("dkwjfl");
        wrapper.eq(StringUtils.isNotBlank(dkwjfl), "DKWJFL", dkwjfl);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String dkzt = (String) params.get("dkzt");
        wrapper.eq(StringUtils.isNotBlank(dkzt), "DKZT", dkzt);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate), "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }

    @Override
    public List<EastDgxdywjjbEntity> getDgxdywjjbData(String startDate, String endDate) {
        // 处理对公转个人的借据
        List<String> dgToGrLoanNoList = new ArrayList<>();
        String dgToGrLoanNos = sysParamsService.getValue("EAST_DG_TO_GR_LOAN_NOS");
        if (StringUtils.isNotBlank(dgToGrLoanNos)) {
            dgToGrLoanNoList = Arrays.asList(dgToGrLoanNos.split(",", -1));
        }
        List<EastDgxdywjjbEntity> dgxdywjjbEntityList = new ArrayList<>();
        // 处理未结清的借据
        List<String> bussNumList = new ArrayList<>(eastCommonService.getUnCloseLoanNoList("1", endDate));
        List<String> finalDgToGrLoanNoList = dgToGrLoanNoList;
        CollectionUtil.removeWithAddIf(bussNumList, finalDgToGrLoanNoList::contains);
        for (String bussNum : bussNumList) {
            dgxdywjjbEntityList.add(handle(bussNum, Constants.EAST_DEFAULT_DATE, endDate));
        }
        // 处理结清的借据
        List<Map<String, Object>> closeList = eastCommonService.getCloseLoanNoList("1", startDate, endDate);
        CollectionUtil.removeWithAddIf(closeList, map -> finalDgToGrLoanNoList.contains(map.get("loanno").toString()));
        for (Map<String, Object> map : closeList) {
            dgxdywjjbEntityList.add(handle(map.get("loanno").toString(), map.get("lasttrandate").toString(), endDate));
        }

        return this.handleZchx(dgxdywjjbEntityList, startDate, endDate);
    }

    private EastDgxdywjjbEntity handle(String bussNum, String closeDate, String endDate) {
        try {
            EastTzEntity eastTz = eastTzService.getEastTzByLoanNoAndCjrq(bussNum, endDate);
            EastDgxdywjjbEntity eastEntity = baseDao.getDgxdywjjbEntityByBussNum(bussNum, endDate);
            LoanRetInfoEntity loanRetInfo = loanCommonService.getLoanRetInfoEntity(bussNum, endDate);
            EntLoanMonthEntity loanMonthEntity = loanCommonService.getEntLoanMonthEntity(bussNum, endDate);
            // 从East基础信息表中同步基础信息
            EastBaseInfo eastBaseInfo = eastCommonService.getEastBaseInfo(eastEntity.getKhtybh(), endDate);
            if (eastBaseInfo != null) {
                eastEntity.setKhmc(eastBaseInfo.getUsername());
            }
            eastEntity.setXdhth(bussNum);
            eastEntity.setXdywzl(DictExch.xdywzl(eastTz.getLoanSx()));// 信贷业务种类
            eastEntity.setDkfflx(DictExch.dkfflx(eastEntity.getJjdkyt()));// 贷款发放类型
            eastEntity.setQbje(loanRetInfo.getOverdPrinc());// 欠本金额
            // 部分借据有问题，如果算出欠息余额小于0，则置为0
            eastEntity.setBnqxye(loanRetInfo.getOverdueIntInn().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : loanRetInfo.getOverdueIntInn());// 表内欠息余额
            eastEntity.setBwqxye(loanRetInfo.getOverdueIntOut().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : loanRetInfo.getOverdueIntOut());// 表外欠息余额
            eastEntity.setDkye(loanRetInfo.getBal());
            eastEntity.setDkwjfl(DictExch.fiveCate(loanRetInfo.getFiveCate()));// 贷款五级分类
            eastEntity.setZqs((int) loanRetInfo.getTolTerm());// 总期数
            eastEntity.setDqqs((int) loanRetInfo.getCurTerm());// 当前期数
            eastEntity.setQbrq(StringUtils.isBlank(eastEntity.getQbrq()) ? Constants.EAST_DEFAULT_DATE : eastEntity.getQbrq());
            // 部分借据有问题，如果算出欠息余额为0，则欠息日期置为默认值
            if (loanRetInfo.getOverdueIntInn().compareTo(BigDecimal.ZERO) == 0 && loanRetInfo.getOverdueIntOut().compareTo(BigDecimal.ZERO) == 0) {
                eastEntity.setQxrq(Constants.EAST_DEFAULT_DATE);// 欠息日期
            } else {
                eastEntity.setQxrq(StringUtils.isBlank(eastEntity.getQxrq()) ? Constants.EAST_DEFAULT_DATE : eastEntity.getQxrq());// 欠息日期
            }
            eastEntity.setLxqkqs(loanRetInfo.getContinuousOverdueTerm());
            eastEntity.setLjqkqs(loanRetInfo.getOverdueTerm());
            eastEntity.setZjrq(closeDate);// 终结日期
            EastDeptEntity deptEntity = EastCommonServiceImpl.getDeptByInstNo(eastEntity.getNbjgh());
            eastEntity.setRzzhsshmc(deptEntity.getYhjgmc());
            eastEntity.setHkzhsshmc(deptEntity.getYhjgmc());
            eastEntity.setJxfs(DictExch.jxfsExch(eastEntity.getJxfs()));
            //判断还款方式
            String repayMode;
            TLoanIou tLoanIou = CacheUtil.get("getTLoanIouByBussNum", "tLoanIou-" + bussNum, TLoanIou.class, () -> tLoanIouService.getOne(new QueryWrapper<TLoanIou>().lambda().eq(TLoanIou::getLoanNo, bussNum)));
            if (tLoanIou.getMngFlag().charAt(3) != '3') {
                repayMode = "按月";
                eastEntity.setJxfs("按月结息");
            } else {
                if (eastEntity.getZqs() == 1) {
                    char intCalFlag3 = tLoanIou.getIntCalFlag().charAt(3);
                    if (intCalFlag3 == '0') {
                        repayMode = "利随本清";
                    } else {
                        repayMode = "分期付息一次还本";
                    }
                } else {
                    repayMode = "其他-分期还本";
                }
            }
            eastEntity.setHkfs(repayMode);// 还款方式
            TGfyhLoanNewRet nxtRet = loanRetInfo.getNxtRet();
            if (nxtRet != null) {
                eastEntity.setXqhkrq(nxtRet.getDueDate());
                eastEntity.setXqyhbj(nxtRet.getYhbj());
                eastEntity.setXqyhlx(nxtRet.getYrate());
            } else {
                eastEntity.setXqhkrq(Constants.EAST_DEFAULT_DATE);
                eastEntity.setXqyhbj(BigDecimal.ZERO);
                eastEntity.setXqyhlx(BigDecimal.ZERO);
            }
            String acctStatus = ("N".equals(loanRetInfo.getRpyStatus()) || "*".equals(loanRetInfo.getRpyStatus()) ? "1" : "2");// 账户状态 2-逾期
            if (loanMonthEntity.getAcctBal().compareTo(BigDecimal.ZERO) == 0 && loanMonthEntity.getTotOverd().compareTo(BigDecimal.ZERO) == 0) {
                acctStatus = "3";// 3-关闭
                eastEntity.setXqhkrq(Constants.EAST_DEFAULT_DATE);
                eastEntity.setXqyhbj(BigDecimal.ZERO);
                eastEntity.setXqyhlx(BigDecimal.ZERO);
            }
            eastEntity.setDktxhy(DictExch.dktxhy(eastTz.getSubIndustryType()));// 贷款投向行业
            eastEntity.setDkzt(DictExch.pbccrcLoanStatusExch(acctStatus));

            // 同一条数据涉及多个明细科目的，仅填报该笔业务指向的主要科目，如：一笔贷款仅包含正常本金时，填报正常本金科目，如一笔贷款既包含正常本金也包含逾期本金时，填报逾期本金科目。关联数据项：总账会计全科目表.会计科目编号。
            List<TLoanLedger> tLoanLedgerList = CacheUtil.getList("tLoanLedgerListByBussNum", "tLoanLedgerList-" + bussNum, () -> tLoanLedgerService.list(new QueryWrapper<TLoanLedger>().lambda().eq(TLoanLedger::getLoanNo, bussNum).orderByDesc(TLoanLedger::getBgnintDate)));
            if (!tLoanLedgerList.isEmpty()) {
                String dkfhzh = "";
                if ("逾期".equals(eastEntity.getDkzt())) {
                    TLoanLedger ledger = tLoanLedgerList.stream().filter(tLoanLedger -> tLoanLedger.getFlag().charAt(1) != '0').findFirst().orElse(null);
                    if (ledger != null) {
                        dkfhzh = ledger.getLoanAcc().trim();
                    }
                }
                // 正常分户的flag[2] = 0
                if (dkfhzh.isEmpty()) {
                    TLoanLedger ledger = tLoanLedgerList.stream().filter(tLoanLedger -> tLoanLedger.getFlag().charAt(1) == '0').findFirst().orElse(null);
                    if (ledger == null) {
                        dkfhzh = tLoanLedgerList.get(tLoanLedgerList.size() - 1).getLoanAcc().trim();
                    } else {
                        dkfhzh = ledger.getLoanAcc().trim();
                    }
                }
                eastEntity.setDkfhzh(dkfhzh);// 贷款分户账号
                EastDgxdfhzEntity eastDgxdfhz = eastDgxdfhzDao.getCorpLoanLedgerInfo(dkfhzh);
                if (eastDgxdfhz != null) {
                    eastEntity.setMxkmbh(eastDgxdfhz.getMxkmbh());// 明细科目编号
                    eastEntity.setMxkmmc(eastDgxdfhz.getMxkmmc());// 明细科目名称
                }
            }

            // 检查涉农标志
            eastEntity.setSfsndk("涉农".equals(eastTz.getShenong()) ? "是" : "否");
            // 满足涉农，且企业规模不是大型和中型的
            eastEntity.setSfphxsndk(("涉农".equals(eastTz.getShenong()) && ((eastTz.getEntScale() == null) || (!eastTz.getEntScale().contains("大型") && !eastTz.getEntScale().contains("中型")))) ? "是" : "否");
            eastEntity.setSfphxxwqydk(eastEntity.getSfphxsndk());
            eastEntity.setXdygh(eastCommonService.getXczyByKhh(tLoanIou.getCstmNo(), bussNum, endDate));//信贷员工号
            return eastEntity;
        } catch (Exception e) {
            log.error("[" + endDate + "]处理借据[" + bussNum + "]失败", e);
            throw new RenException("[" + endDate + "]处理借据[" + bussNum + "]失败");
        }
    }

    /**
     * 部分资产核销表里的借据没有采集，如果本次没有采集到，则虚拟一笔正常的借据，已达到资产核销表与借据表关联
     *
     * @param dgxdywjjbEntities 本次采集的借据信息
     * @param startDate         开始日期
     * @param endDate           结束日期
     */
    private List<EastDgxdywjjbEntity> handleZchx(List<EastDgxdywjjbEntity> dgxdywjjbEntities, String startDate, String endDate) {
        List<EastZchxbEntity> zchxbEntityList = eastZchxbService.getZchxbData(startDate, endDate);
        if (!zchxbEntityList.isEmpty()) {
            List<String> zchxLoanNoList = zchxbEntityList.stream().filter(eastZchxbEntity -> "对公贷款".equals(eastZchxbEntity.getZclx())).map(EastZchxbEntity::getJjh).distinct().collect(Collectors.toList());
            List<String> jjbLoanNoList = dgxdywjjbEntities.stream().map(EastDgxdywjjbEntity::getXdjjh).distinct().collect(Collectors.toList());
            zchxLoanNoList.removeAll(jjbLoanNoList);
            for (String bussNum : zchxLoanNoList) {
                try {
                    EastTzEntity eastTz = eastTzService.getEastTzByLoanNoAndCjrq(bussNum, endDate);
                    EastDgxdywjjbEntity eastEntity = baseDao.getDgxdywjjbEntityByBussNum(bussNum, endDate);
                    TLoanIou tLoanIou = CacheUtil.get("getTLoanIouByBussNum", "tLoanIou-" + bussNum, TLoanIou.class,
                            () -> tLoanIouService.getOne(new QueryWrapper<TLoanIou>().lambda().eq(TLoanIou::getLoanNo, bussNum)));
                    // 从East基础信息表中同步基础信息
                    EastBaseInfo eastBaseInfo = eastCommonService.getEastBaseInfo(eastEntity.getKhtybh(), endDate);
                    if (eastBaseInfo != null) {
                        eastEntity.setKhmc(eastBaseInfo.getUsername());
                    }
                    eastEntity.setXdhth(bussNum);
                    eastEntity.setXdywzl(DictExch.xdywzl(eastTz.getLoanSx()));// 信贷业务种类
                    eastEntity.setDkfflx(DictExch.dkfflx(eastEntity.getJjdkyt()));// 贷款发放类型
                    eastEntity.setQbje(BigDecimal.ZERO);// 欠本金额
                    // 部分借据有问题，如果算出欠息余额小于0，则置为0
                    eastEntity.setBnqxye(BigDecimal.ZERO);// 表内欠息余额
                    eastEntity.setBwqxye(BigDecimal.ZERO);// 表外欠息余额
                    eastEntity.setDkye(BigDecimal.ZERO);
                    eastEntity.setDkwjfl("正常");// 贷款五级分类
                    eastEntity.setZqs(1);// 总期数
                    eastEntity.setDqqs(1);// 当前期数
                    eastEntity.setQbrq(StringUtils.isBlank(eastEntity.getQbrq()) ? Constants.EAST_DEFAULT_DATE : eastEntity.getQbrq());
                    eastEntity.setQxrq(Constants.EAST_DEFAULT_DATE);// 欠息日期
                    eastEntity.setLxqkqs(0);
                    eastEntity.setLjqkqs(0);
                    eastEntity.setZjrq(Constants.EAST_DEFAULT_DATE);// 终结日期
                    EastDeptEntity deptEntity = EastCommonServiceImpl.getDeptByInstNo(eastEntity.getNbjgh());
                    eastEntity.setRzzhsshmc(deptEntity.getYhjgmc());
                    eastEntity.setHkzhsshmc(deptEntity.getYhjgmc());
                    eastEntity.setJxfs(DictExch.jxfsExch(eastEntity.getJxfs()));
                    //判断还款方式
                    String repayMode;
                    if (tLoanIou.getMngFlag().charAt(3) != '3') {
                        repayMode = "按月";
                        eastEntity.setJxfs("按月结息");
                    } else {
                        if (eastEntity.getZqs() == 1) {
                            char intCalFlag3 = tLoanIou.getIntCalFlag().charAt(3);
                            if (intCalFlag3 == '0') {
                                repayMode = "利随本清";
                            } else {
                                repayMode = "分期付息一次还本";
                            }
                        } else {
                            repayMode = "其他-分期还本";
                        }
                    }
                    eastEntity.setHkfs(repayMode);// 还款方式
                    eastEntity.setXqhkrq(Constants.EAST_DEFAULT_DATE);
                    eastEntity.setXqyhbj(BigDecimal.ZERO);
                    eastEntity.setXqyhlx(BigDecimal.ZERO);
                    eastEntity.setDktxhy(DictExch.dktxhy(eastTz.getSubIndustryType()));// 贷款投向行业
                    eastEntity.setDkzt("核销");

                    // 同一条数据涉及多个明细科目的，仅填报该笔业务指向的主要科目，如：一笔贷款仅包含正常本金时，填报正常本金科目，如一笔贷款既包含正常本金也包含逾期本金时，填报逾期本金科目。关联数据项：总账会计全科目表.会计科目编号。
                    List<TLoanLedger> tLoanLedgerList = CacheUtil.getList("tLoanLedgerListByBussNum", "tLoanLedgerList-" + bussNum, () -> tLoanLedgerService.list(new QueryWrapper<TLoanLedger>().lambda().eq(TLoanLedger::getLoanNo, bussNum).orderByDesc(TLoanLedger::getBgnintDate)));
                    if (!tLoanLedgerList.isEmpty()) {
                        String dkfhzh = "";
                        if ("逾期".equals(eastEntity.getDkzt())) {
                            TLoanLedger ledger = tLoanLedgerList.stream().filter(tLoanLedger -> tLoanLedger.getFlag().charAt(1) != '0').findFirst().orElse(null);
                            if (ledger != null) {
                                dkfhzh = ledger.getLoanAcc().trim();
                            }
                        }
                        // 正常分户的flag[2] = 0
                        if (dkfhzh.isEmpty()) {
                            TLoanLedger ledger = tLoanLedgerList.stream().filter(tLoanLedger -> tLoanLedger.getFlag().charAt(1) == '0').findFirst().orElse(null);
                            if (ledger == null) {
                                dkfhzh = tLoanLedgerList.get(tLoanLedgerList.size() - 1).getLoanAcc().trim();
                            } else {
                                dkfhzh = ledger.getLoanAcc().trim();
                            }
                        }
                        eastEntity.setDkfhzh(dkfhzh);// 贷款分户账号
                        EastDgxdfhzEntity eastDgxdfhz = eastDgxdfhzDao.getCorpLoanLedgerInfo(dkfhzh);
                        if (eastDgxdfhz != null) {
                            eastEntity.setMxkmbh(eastDgxdfhz.getMxkmbh());// 明细科目编号
                            eastEntity.setMxkmmc(eastDgxdfhz.getMxkmmc());// 明细科目名称
                        }
                    }

                    // 检查涉农标志
                    eastEntity.setSfsndk("涉农".equals(eastTz.getShenong()) ? "是" : "否");
                    // 满足涉农，且企业规模不是大型和中型的
                    eastEntity.setSfphxsndk(("涉农".equals(eastTz.getShenong()) && ((eastTz.getEntScale() == null) || (!eastTz.getEntScale().contains("大型") && !eastTz.getEntScale().contains("中型")))) ? "是" : "否");
                    eastEntity.setSfphxxwqydk(eastEntity.getSfphxsndk());
                    eastEntity.setXdygh(eastCommonService.getXczyByKhh(tLoanIou.getCstmNo(), bussNum, endDate));//信贷员工号

                    dgxdywjjbEntities.add(eastEntity);
                } catch (Exception e) {
                    log.error("[" + endDate + "]处理核销借据[" + bussNum + "]失败", e);
                    throw new RenException("[" + endDate + "]处理核销借据[" + bussNum + "]失败");
                }
            }
        }
        return dgxdywjjbEntities;
    }
}