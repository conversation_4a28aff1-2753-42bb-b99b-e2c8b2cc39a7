package io.ibs.modules.east.service.fbnwdbxx;

import io.ibs.common.service.CrudService;
import io.ibs.modules.east.dto.fbnwdbxx.EastBnwywdbhtbDTO;
import io.ibs.modules.east.entity.fbnwdbxx.EastBnwywdbhtbEntity;

import java.util.List;

/**
 * 表内外业务担保合同表
 *
 * <AUTHOR>
 * @since 3.0 2024-03-14
 */
public interface EastBnwywdbhtbService extends CrudService<EastBnwywdbhtbEntity, EastBnwywdbhtbDTO> {

    List<EastBnwywdbhtbEntity> getModuleList(String endDate);

    /**
     * 从信贷获取数据
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return list
     */
    List<EastBnwywdbhtbEntity> getBnwywdbhtbList(String startDate, String endDate);
}