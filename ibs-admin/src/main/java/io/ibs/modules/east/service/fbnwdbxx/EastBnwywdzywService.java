package io.ibs.modules.east.service.fbnwdbxx;

import io.ibs.common.service.CrudService;
import io.ibs.modules.east.dto.fbnwdbxx.EastBnwywdzywDTO;
import io.ibs.modules.east.entity.fbnwdbxx.EastBnwywdzywEntity;

import java.util.List;

/**
 * 表内外业务抵质押物
 *
 * <AUTHOR>
 * @since 1.0 2024-03-12
 */
public interface EastBnwywdzywService extends CrudService<EastBnwywdzywEntity, EastBnwywdzywDTO> {

    List<EastBnwywdzywEntity> getModuleList(String endDate);

    List<EastBnwywdzywEntity> getBnwywdzywData(String startDate, String endDate);
}