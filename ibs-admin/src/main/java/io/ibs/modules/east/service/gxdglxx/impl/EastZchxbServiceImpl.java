package io.ibs.modules.east.service.gxdglxx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.dao.gxdglxx.EastZchxbDao;
import io.ibs.modules.east.dto.gxdglxx.EastZchxbDTO;
import io.ibs.modules.east.entity.gxdglxx.EastZchxbEntity;
import io.ibs.modules.east.service.gxdglxx.EastZchxbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 资产核销表
 *
 * <AUTHOR>
 * @since 1.0 2024-03-13
 */
@Service
@RequiredArgsConstructor
public class EastZchxbServiceImpl extends CrudServiceImpl<EastZchxbDao, EastZchxbEntity, EastZchxbDTO> implements EastZchxbService {

    @Override
    public QueryWrapper<EastZchxbEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EastZchxbEntity> wrapper = new QueryWrapper<>();

        String khmc = (String) params.get("khmc");
        wrapper.like(StringUtils.isNotBlank(khmc), "KHMC", khmc);
        String jjh = (String) params.get("jjh");
        wrapper.eq(StringUtils.isNotBlank(jjh), "JJH", jjh);
        String hxzt = (String) params.get("hxzt");
        wrapper.eq(StringUtils.isNotBlank(hxzt), "HXZT", hxzt);
        String shbz = (String) params.get("shbz");
        wrapper.eq(StringUtils.isNotBlank(shbz), "SHBZ", shbz);
        String nbjgh = (String) params.get("nbjgh");
        wrapper.eq(StringUtils.isNoneBlank(nbjgh), "NBJGH", nbjgh);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.apply(StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate),
                "CJRQ = (SELECT TO_CHAR(MAX(TO_DATE(CJRQ,'YYYYMMDD')),'YYYYMMDD')  FROM " + TableInfoHelper.getTableInfo(this.currentModelClass()).getTableName() + ")");
        wrapper.apply(StringUtils.isNotBlank(startDate), "TO_DATE(CJRQ,'YYYYMMDD')  >= TO_DATE({0},'YYYYMMDD')", startDate);
        wrapper.apply(StringUtils.isNotBlank(endDate), "TO_DATE(CJRQ,'YYYYMMDD')  <= TO_DATE({0},'YYYYMMDD')", endDate);
        return wrapper;
    }

    @Override
    public List<EastZchxbEntity> getZchxbData(String startDate, String endDate) {
        List<EastZchxbEntity> entityList = baseDao.getZchxbData(startDate, endDate);
        for (EastZchxbEntity eastZchxbEntity : entityList) {
            eastZchxbEntity.setId(null);
            String shbz;
            String hxzt;
            // 收回日期比采集日期大的，说明采集日期当天还没有收回，收回日期置为默认值
            if (Integer.parseInt(eastZchxbEntity.getShrq()) > Integer.parseInt(endDate)) {
                eastZchxbEntity.setShrq(Constants.EAST_DEFAULT_DATE);
                eastZchxbEntity.setShbj(BigDecimal.ZERO);
                eastZchxbEntity.setShlx(BigDecimal.ZERO);
                shbz = "未收回";
                hxzt = "账销案存";
            } else {
                BigDecimal rbAmt = eastZchxbEntity.getHxbj().add(eastZchxbEntity.getShbnlx()).add(eastZchxbEntity.getShbwlx());
                BigDecimal actAmt = eastZchxbEntity.getShbj().add(eastZchxbEntity.getShlx());

                // 收回本金为0：未回收
                if (actAmt.compareTo(BigDecimal.ZERO) < 1) {
                    shbz = "未收回";
                    hxzt = "账销案存";
                }
                // 收回本金大于等于核销本金：完全收回
                else if (actAmt.subtract(rbAmt).compareTo(BigDecimal.ZERO) > -1) {
                    shbz = "完全收回";
                    hxzt = "完全终结";
                }
                // 除上述情况外：部分收回
                else {
                    shbz = "部分收回";
                    hxzt = "账销案存";
                }
            }
            eastZchxbEntity.setShbz(shbz);
            eastZchxbEntity.setHxzt(hxzt);
            eastZchxbEntity.setCjrq(endDate);
        }
        return entityList;
    }
}