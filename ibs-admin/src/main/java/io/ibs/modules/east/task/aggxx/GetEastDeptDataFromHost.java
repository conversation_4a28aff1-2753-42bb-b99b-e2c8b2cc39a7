package io.ibs.modules.east.task.aggxx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.dto.aggxx.EastDeptDTO;
import io.ibs.modules.east.entity.aggxx.EastDeptEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.aggxx.EastDeptService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 从核心以及信贷等系统获取数据
 * 101 机构信息表
 *
 * @since 20230315
 */
@Service
@Slf4j
public class GetEastDeptDataFromHost extends BaseTask {

    private EastDeptService eastDeptService;

    /**
     * 构造函数
     */
    @Autowired
    public GetEastDeptDataFromHost(EastDeptService eastDeptService) {
        super("101 机构信息表", GetEastDeptDataFromHost.class);
        this.eastDeptService = eastDeptService;
    }

    /**
     * @param params 时间段内yyyyMMdd-yyyyMMdd或者某一天yyyyMMdd或者无参数,无参数默认为当天
     */
    @Override
    public void execute(String params) throws Exception {
        log.info("====================101 机构信息表 开始====================");
        params = super.checkDateParam(params, "yyyyMMdd");
        String startDate = null, endDate = null;
        if (StringUtils.isBlank(params)) {
            String date = DateUtil.format(new Date(), "yyyyMMdd");
            startDate = date;
            endDate = date;
        } else if (params.contains("-")) {
            String[] split = params.split("-");
            startDate = split[0];
            endDate = split[1];
        }
        // 判断是否已经抽过
        List<EastDeptDTO> dtoList = eastDeptService.list(new QueryWrapper<EastDeptEntity>().eq("CJRQ", endDate));
        if (CollectionUtil.isNotEmpty(dtoList)) {
            log.info("101 机构信息表,采集日期[{}]已经抽过，不再重复抽取", endDate);
            return;
        }
        List<EastDeptEntity> deptList = eastDeptService.getLastData(endDate);
        if (CollectionUtil.isNotEmpty(deptList)) {
            eastDeptService.insertBatch(deptList);

            log.info("登记采集记录表");
            EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
            eastDataRecord.setCjrq(endDate);
            eastDataRecord.setTableId(EastTableInfo.TABLE_101.getId());
            eastDataRecord.setParams(params);
            Db.save(eastDataRecord);
        }
        log.info("====================101 机构信息表 结束====================");
    }


}
