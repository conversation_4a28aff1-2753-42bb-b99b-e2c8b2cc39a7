package io.ibs.modules.east.task.bkhxx;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.DictExch;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.bkhxx.EastRelationEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.bkhxx.EastRelationService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 从核心同步对公客户的关联关系
 *
 * <AUTHOR>
 * @since 20240620
 */
@Service
@Slf4j
public class GetEastRelationDataTask extends BaseTask {

    private static final String name = StrUtil.format("EAST-{}:", EastTableInfo.TABLE_206.getName());
    private final EastRelationService eastRelationService;

    public GetEastRelationDataTask(EastRelationService eastRelationService) {
        super(name, GetEastRelationDataTask.class);
        this.eastRelationService = eastRelationService;
    }

    @Override
    public void execute(String params) throws Exception {

        if (StringUtils.isBlank(params)) {
            String msg = name + "参数不能为空！";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start(name);
        //集中采集,持续采集均为全量采集

        String bgnDate;
        String endDate;
        params = super.checkDateParam(params, Constants.EAST_DATE_PATTERN);
        if (params.contains("-")) {
            bgnDate = params.split("-")[0];
            endDate = params.split("-")[1];
        } else {
            bgnDate = params;
            endDate = params;
        }

        //从核心非自然人受益所有人表中获取对公客户的关联关系
        List<EastRelationEntity> list = this.eastRelationService.getCorpRelation(bgnDate, endDate);
        if (list == null || list.isEmpty()) {
            log.error("从核心非自然人受益所有人表中获取对公客户的关联关系失败,数据为0条");
            return;
        }

        //查询关联人是否为本行客户
        List<EastRelationEntity> paperList = this.eastRelationService.getRelOfBenef();
        if (paperList != null && !paperList.isEmpty()) {
            list.forEach(re -> {
                if (paperList.stream().filter(p -> p.getGlrzjhm().equals(re.getGlrzjhm())).count() > 0) {
                    re.setGlrkhtybh(paperList.stream().filter(p -> p.getGlrzjhm().equals(re.getGlrzjhm())).findFirst().get().getGlrkhtybh());
                }
            });
        }


        list.forEach(re -> {
            re.setKhzjlb(DictExch.idTypeExchEnt(re.getKhzjhm()));
            re.setKhzjhm(re.getKhzjhm().substring(1).trim());
            re.setGxlx(DictExch.relationType(re.getGxlx()));
            if ("z".equals(re.getGlrzjlb())) {
                re.setGlrlb("民营企业");
                if (re.getGlrzjhm().length() == 18) {
                    re.setGlrzjlb("统一社会信用代码");
                } else {
                    re.setGlrzjlb("营业执照（工商注册号）");
                }
            } else {
                re.setGlrlb("自然人");
                re.setGlrzjlb(DictExch.idTypeExch(re.getGlrzjlb()));
            }
            if ("1".equals(re.getBbz())) {
                re.setGxzt("无效");
                re.setBbz("");
            } else {
                re.setGxzt("有效");
                re.setBbz("");
            }

            re.setCjrq(endDate);
        });

        this.eastRelationService.insertBatch(list);

        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_206.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);

        log.info("====================206-对公客户关联关系抽数结束====================");
    }
}
