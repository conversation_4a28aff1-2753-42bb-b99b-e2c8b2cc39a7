package io.ibs.modules.east.task.ckpxx;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.DictExch;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.bkhxx.EastBaseInfo;
import io.ibs.modules.east.entity.ckpxx.EastDebitInfoEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.ckpxx.EastDebitInfoService;
import io.ibs.modules.east.service.common.EastCommonService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 从核心获取借记卡的信息
 *
 * <AUTHOR>
 * @since 20240325
 */
@Service
@Slf4j
public class GetEastDebitInfoDataTask extends BaseTask {
    private final String name = EastTableInfo.TABLE_301.getId() + EastTableInfo.TABLE_301.getName();
    private final EastDebitInfoService eastDebitInfoService;
    private final EastCommonService eastCommonService;

    /**
     * @param eastDebitInfoService
     */
    public GetEastDebitInfoDataTask(EastDebitInfoService eastDebitInfoService,
                                    EastCommonService eastCommonService) {
        super("获取借记卡的信息", GetEastDebitInfoDataTask.class);
        this.eastDebitInfoService = eastDebitInfoService;
        this.eastCommonService = eastCommonService;
    }

    @Override
    public void execute(String params) throws Exception {
        String msg;
        if (StringUtils.isBlank(params)) {
            msg = name + "参数不能为空!";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start(name);

        String bgnDate;
        String endDate;
        params = super.checkDateParam(params, Constants.EAST_DATE_PATTERN);
        if (params.contains("-")) {
            bgnDate = params.split("-")[0];
            endDate = params.split("-")[1];
        } else {
            msg = "参数错误,请使用yyyyMMdd-yyyyMMdd格式或者lastMonthBetween参数";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }

        //从核心获取借记卡的信息
        List<EastDebitInfoEntity> list = this.eastDebitInfoService.getDebitInfo(bgnDate, endDate);
        if (list == null || list.isEmpty()) {
            msg = "查询借记卡信息失败!或系统内无数据";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }

        list.forEach(card -> {
            // 从East基础信息表中同步基础信息
            EastBaseInfo eastBaseInfo = eastCommonService.getEastBaseInfo(card.getKhtybh(), endDate);
            if (eastBaseInfo != null) {
                card.setKhmc(eastBaseInfo.getUsername());
                card.setZjlb(eastBaseInfo.getIdType());
                card.setZjhm(eastBaseInfo.getIdNo());
            }
            card.setXnkbz("否");//虚拟卡标志
            card.setYgkbz("否");//员工卡标志
            card.setYgkbz(eastCommonService.isStaff(card.getZjhm(), endDate) ? "是" : "否");//员工卡标志
            card.setKpzt(DictExch.kpzt(card.getKpzt()));
            card.setCjrq(endDate);
        });
        this.eastDebitInfoService.insertBatch(list, 1000);

        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_301.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);
        stopWatch.stop();
        log.info("***--{}任务执行结果--***", stopWatch.getLastTaskName());
        log.info("-----总耗时: {}", DateUtil.secondToTime((int) stopWatch.getTotalTimeSeconds()));
    }
}
