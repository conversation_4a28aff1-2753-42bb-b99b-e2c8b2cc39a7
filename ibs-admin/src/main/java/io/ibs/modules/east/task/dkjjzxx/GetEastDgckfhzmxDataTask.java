package io.ibs.modules.east.task.dkjjzxx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzmxEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.dkjjzxx.EastDgckfhzmxService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 406	对公存款分户账明细记录	DGCKFHZMX
 * 增量表，报送上一采集日至采集日期间新增的数据。
 * 除计息、扣利息税外，所有影响对公存款账户余额变动的交易信息，包括结息交易，不包括查询交易。
 */
@Slf4j
@Service
public class GetEastDgckfhzmxDataTask extends BaseTask {
    private static final String name = StrUtil.format("EAST-{}:", EastTableInfo.TABLE_406.getName());

    private final EastDgckfhzmxService dgckfhzmxService;

    public GetEastDgckfhzmxDataTask(EastDgckfhzmxService dgckfhzmxService) {
        super(name, GetEastDgckfhzmxDataTask.class);
        this.dgckfhzmxService = dgckfhzmxService;
    }

    @Override
    public void execute(String params) throws Exception {
        if (StringUtils.isBlank(params)) {
            String msg = name + "参数不能为空！";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }
        String startDate;
        String endDate;
        params = super.checkDateParam(params, Constants.EAST_DATE_PATTERN);
        if (params.contains("-")) {
            startDate = params.split("-")[0];
            endDate = params.split("-")[1];
        } else {
            startDate = params;
            endDate = params;
        }
        // 结算活期 / 定期 当日明细帐
        List<EastDgckfhzmxEntity> list = dgckfhzmxService.getDataList(startDate, endDate);
        if (CollectionUtil.isEmpty(list)) {
            log.info(name + "没有需要报送的数据");
            return;
        }
        dgckfhzmxService.insertBatch(list, 1000);
        log.info(name + "共有{}条记录", list.size());


        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_406.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);
    }
}
