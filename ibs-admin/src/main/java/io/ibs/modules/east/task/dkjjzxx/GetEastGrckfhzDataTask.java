package io.ibs.modules.east.task.dkjjzxx;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.DictExch;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.bkhxx.EastBaseInfo;
import io.ibs.modules.east.entity.dkjjzxx.EastGrckfhzEntity;
import io.ibs.modules.east.entity.other.AccBalEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.common.EastCommonService;
import io.ibs.modules.east.service.dkjjzxx.EastGrckfhzService;
import io.ibs.modules.irp.util.DateUtils;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 从核心获取个人存款分户账
 * 包括定期和活期
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Slf4j
@Service
public class GetEastGrckfhzDataTask extends BaseTask {
    private final static String name = EastTableInfo.TABLE_403.getId() + EastTableInfo.TABLE_403.getName();
    private final EastGrckfhzService eastGrckfhzService;
    private final EastCommonService eastCommonService;

    public GetEastGrckfhzDataTask(EastGrckfhzService eastGrckfhzService,
                                  EastCommonService eastCommonService) {
        super("个人存款分户账", GetEastGrckfhzDataTask.class);
        this.eastGrckfhzService = eastGrckfhzService;
        this.eastCommonService = eastCommonService;
    }

    @Override
    public void execute(String params) throws Exception {
        if (StringUtils.isBlank(params)) {
            String msg = name + "参数不能为空！";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }
        String bgnDate;
        String endDate;
        params = super.checkDateParam(params, Constants.EAST_DATE_PATTERN);
        if (params.contains("-")) {
            bgnDate = params.split("-")[0];
            endDate = params.split("-")[1];
        } else {
            bgnDate = params;
            endDate = params;
        }

        StopWatch stopWatch = new StopWatch();
        stopWatch.start(name);

        String msg = "";
        //从核心获取活期存款信息
        List<EastGrckfhzEntity> savingList = this.eastGrckfhzService.getGrckfhzOfSavingDeposite(bgnDate, endDate);
        if (savingList == null || savingList.size() == 0) {
            msg = "查询活期存款信息失败!或系统内无数据";
            log.error(msg);
            savingList = new ArrayList<>();
        }

        List<EastGrckfhzEntity> timeList = this.eastGrckfhzService.getGrckfhzOfTimeDeposite(bgnDate, endDate);
        if (timeList == null || timeList.size() == 0) {
            msg = "查询定期存款信息失败!或系统内无数据";
            log.error(msg);
            timeList = new ArrayList<>();
        } else {
            List<String> partDrawList = this.eastGrckfhzService.getTDPartDraw(endDate);
            if (partDrawList != null && partDrawList.size() > 0) {
                Iterator<EastGrckfhzEntity> iterable = timeList.iterator();
                while (iterable.hasNext()) {
                    EastGrckfhzEntity entity = iterable.next();
                    if (partDrawList.contains(entity.getGrckzh())) {
                        iterable.remove();
                    }
                }
            }
        }
        savingList.addAll(timeList);

        List<AccBalEntity> accBalList = this.eastGrckfhzService.getBalUntilDay(endDate);
        if (accBalList == null || accBalList.size() == 0) {
            msg = "查询存款余额失败!或系统内无数据";
            log.error(msg);
        }

        //定期自动转存后账户不变的(未销户未产生新的账号),但是开户日期变为转存当天的日期,如果自动转存后发生了销户,那么转存前的月份历史数据用getGrckfhzOfTimeDeposite查不出来
        List<EastGrckfhzEntity> autoTList = this.eastGrckfhzService.getTDOfAutoTransfer(endDate);
        List<String> autoTAccList;// 收集定期自动转存账号
        if (autoTList == null || autoTList.size() == 0) {
            msg = "查询定期自动转存失败!或系统内无数据";
            log.error(msg);
            autoTAccList = new ArrayList<>();
        } else {
            List<AccBalEntity> atBalList = this.eastGrckfhzService.getTDATBal(autoTList.stream().map(EastGrckfhzEntity::getGrckzh).collect(Collectors.toList()), endDate);
            if (atBalList == null || atBalList.size() == 0) {
                msg = "查询定期自动转存的既定日期前余额失败!或系统内无数据";
                log.error(msg);
                autoTAccList = new ArrayList<>();
            } else {
                autoTList.stream().forEach(a -> {
                    a.setCkye(atBalList.stream().filter(at -> at.getAcc().equals(a.getGrckzh())).findFirst().get().getBal());
                    a.setKhrq(atBalList.stream().filter(at -> at.getAcc().equals(a.getGrckzh())).findFirst().get().getOpdate());
                });
                savingList.addAll(autoTList);
                autoTAccList = autoTList.stream().map(EastGrckfhzEntity::getGrckzh).distinct().collect(Collectors.toList());
            }
        }

        /*List<EastGrckfhzEntity> tempList=this.eastGrckfhzService.getTempTimeDeposite();
        if(tempList==null||tempList.size()==0){
            msg = "临时查询存款余额失败!或系统内无数据";
            log.error(msg);
        }else{
            savingList.addAll(tempList);
        }*/

        savingList.forEach(s -> {
            // 从East基础信息表中同步基础信息
            EastBaseInfo eastBaseInfo = eastCommonService.getEastBaseInfo(s.getKhtybh(), endDate);
            if (eastBaseInfo != null) {
                s.setZhmc(eastBaseInfo.getUsername());
            }
            s.setBz("CNY");
            s.setGrckzhlx(DictExch.depositeAccType(s.getGrckzhlx()));
            s.setBzjzhbz("否");//保证金账户标志 统一设置为否
            if ("18991231".equals(s.getXhrq()) || "".equals(s.getXhrq())) {
                s.setXhrq("99991231");//未销户时设置为99991231
            } else {
                //当定期发生到期转存后，原账户的余额不会为0,但是状态为销户
                if (s.getCkye().compareTo(BigDecimal.ZERO) != 0) {
                    s.setCkye(BigDecimal.ZERO);
                }
            }
            s.setKhrq(DateUtils.format(DateUtils.parse(s.getKhrq(), "yyyy-MM-dd"), "yyyyMMdd"));
            if (accBalList.stream().filter(ab -> ab.getAcc().equals(s.getGrckzh())).count() > 0) {
                //如果交易为不动户处理,那么取交易金额作为账户余额
                if ("6149".equals(accBalList.stream().filter(ab -> ab.getAcc().equals(s.getGrckzh())).findFirst().get().getTrancode())) {
                    s.setCkye(accBalList.stream().filter(ab -> ab.getAcc().equals(s.getGrckzh())).findFirst().get().getAmt());

                    // 受部分冻结影响，部分不动户交易的交易金额与账户余额不符。若存款明细的最后一条恰好是不动户处理，则取其上一条的明细余额作为账户余额
                    eastGrckfhzService.check6149Bal(s, endDate);
                } else {
                    s.setCkye(accBalList.stream().filter(ab -> ab.getAcc().equals(s.getGrckzh())).findFirst().get().getBal());
                }
            } else {
                // 除自动转存账号外，账户自开户以来都没有进行过交易，则余额为0
                if (!autoTAccList.contains(s.getGrckzh())) {
                    s.setCkye(BigDecimal.ZERO);
                }
            }
            s.setChlb("人民币");//钞汇标志

            String zhzt = DictExch.bookStatus(s.getZhzt());
            // 核心已经是销户状态，但是销户日期不是本月，则将账户状态更新为正常（抽数的时候已经销户了，但是采集当月还没有销户的情况）
            if ("注销".equals(zhzt) && "99991231".equals(s.getXhrq())) {
                s.setZhzt("正常");// 账户状态
            } else {
                // 注销映射为销户
                s.setZhzt("注销".equals(zhzt) ? "销户" : zhzt);// 账户状态
            }
            s.setCjrq(endDate);
        });

        this.eastGrckfhzService.insertBatch(savingList, 1000);

        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_403.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);

        stopWatch.stop();
        log.info("***--{}任务执行结果--***", stopWatch.getLastTaskName());
        log.info("-----总耗时: {}", DateUtil.secondToTime((int) stopWatch.getTotalTimeSeconds()));
    }
}
