package io.ibs.modules.east.task.dkjjzxx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.Constants;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.dkjjzxx.EastGrxdfhzmxEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.dkjjzxx.EastGrxdfhzmxService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 410	个人信贷分户账明细记录	GRXDFHZMX
 * 增量表，报送上一采集日至采集日期间新增的数据。
 * 所有影响个人信贷账户余额或利息变动的交易信息，包括还本、还息，不包括查询交易。
 * 贷款核销或者转让（包括资产证券化）也应该在本表体现：
 * 明细科目填报本金科目，交易金额为核销或转让前本金余额，余额填报为0，交易对手填写借款人自身信息，摘要中标明核销或者转让交易。
 */
@Slf4j
@Service
public class GetEastGrxdfhzmxDataTask extends BaseTask {
    private static final String name = StrUtil.format("EAST-{}:", EastTableInfo.TABLE_410.getName());
    private final EastGrxdfhzmxService grxdfhzmxService;

    public GetEastGrxdfhzmxDataTask(EastGrxdfhzmxService grxdfhzmxService) {
        super(name, GetEastGrxdfhzmxDataTask.class);
        this.grxdfhzmxService = grxdfhzmxService;
    }

    @Override
    public void execute(String params) throws Exception {
        if (StringUtils.isBlank(params)) {
            String msg = name + "参数不能为空！";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }
        String startDate;
        String endDate;
        params = super.checkDateParam(params, Constants.EAST_DATE_PATTERN);
        if (params.contains("-")) {
            startDate = params.split("-")[0];
            endDate = params.split("-")[1];
        } else {
            startDate = params;
            endDate = params;
        }

        // 贷款当日明细帐
        List<EastGrxdfhzmxEntity> list = grxdfhzmxService.getLoanDtlList(startDate, endDate);
        // 还息明细登记簿
        List<EastGrxdfhzmxEntity> retIntDtlList = grxdfhzmxService.getRetIntDtlList(startDate, endDate);
        list.addAll(retIntDtlList);
        if (CollectionUtil.isEmpty(list)) {
            log.info(name + "没有需要报送的数据");
            return;
        }
        grxdfhzmxService.insertBatch(list, 1000);
        log.info(name + "共有{}条记录", list.size());

        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_410.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);
    }
}
