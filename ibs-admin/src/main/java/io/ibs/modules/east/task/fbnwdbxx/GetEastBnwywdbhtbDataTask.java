package io.ibs.modules.east.task.fbnwdbxx;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.fbnwdbxx.EastBnwywdbhtbEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.fbnwdbxx.EastBnwywdbhtbService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10
 * 表内外业务担保合同表任务
 */
@Slf4j
@Service
public class GetEastBnwywdbhtbDataTask extends BaseTask {

    private final EastBnwywdbhtbService eastBnwywdbhtbService;

    public GetEastBnwywdbhtbDataTask(EastBnwywdbhtbService bnwywdbhtbService) {
        super("601-表内外业务担保合同表", GetEastBnwywdbhtbDataTask.class);
        this.eastBnwywdbhtbService = bnwywdbhtbService;
    }

    @Override
    public void execute(String params) throws Exception {
        log.info("===================601-表内外业务担保合同表抽数开始===================");
        params = super.checkDateParam(params, "yyyyMMdd");
        String startDate = null, endDate = null;
        if (StringUtils.isBlank(params)) {
            String date = DateUtil.format(new Date(), "yyyyMMdd");
            startDate = date;
            endDate = date;
        } else if (params.contains("-")) {
            String[] split = params.split("-");
            startDate = split[0];
            endDate = split[1];
        }
        log.info("查询参数：{}-{}", startDate, endDate);
        List<EastBnwywdbhtbEntity> bnwywdbhtbModuleList = this.eastBnwywdbhtbService.getModuleList(endDate);

        List<EastBnwywdbhtbEntity> bnwywdbhtbList = eastBnwywdbhtbService.getBnwywdbhtbList(startDate, endDate);
        if (!bnwywdbhtbModuleList.isEmpty()) {
            for (EastBnwywdbhtbEntity bnwywdbr : bnwywdbhtbList) {
                EastBnwywdbhtbEntity module = bnwywdbhtbModuleList.stream().filter(cmEntity ->
                        cmEntity.getBdbhth().equals(bnwywdbr.getBdbhth()) && cmEntity.getDbhth().equals(bnwywdbr.getDbhth())).findFirst().orElse(null);
                if (module != null) {
                    bnwywdbr.setBdbhth(module.getBdbhth());
                    bnwywdbr.setBdbywlx(module.getBdbywlx());
                    bnwywdbr.setDbhth(module.getDbhth());
                    bnwywdbr.setDblx(module.getDblx());
                    bnwywdbr.setDbbz(module.getDbbz());
                    bnwywdbr.setDbje(module.getDbje());
                    bnwywdbr.setDbqsrq(module.getDbqsrq());
                    bnwywdbr.setDbdqrq(module.getDbdqrq());
                }
            }
        }

        eastBnwywdbhtbService.insertBatch(bnwywdbhtbList);

        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_601.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);
    }
}
