package io.ibs.modules.east.task.fbnwdbxx;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.fbnwdbxx.EastBnwywdbrEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.fbnwdbxx.EastBnwywdbrService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11
 */
@Slf4j
@Service
public class GetEastBnwywdbrDataTask extends BaseTask {

    private final EastBnwywdbrService eastBnwywdbrService;

    public GetEastBnwywdbrDataTask(EastBnwywdbrService eastBnwywdbrService) {
        super("602-表内外业务担保人", GetEastBnwywdbrDataTask.class);
        this.eastBnwywdbrService = eastBnwywdbrService;

    }

    @Override
    public void execute(String params) throws Exception {
        log.info("===================602-表内外业务担保人抽数开始===================");
        params = super.checkDateParam(params, "yyyyMMdd");
        String startDate = null, endDate = null;
        if (StringUtils.isBlank(params)) {
            String date = DateUtil.format(new Date(), "yyyyMMdd");
            startDate = date;
            endDate = date;
        } else if (params.contains("-")) {
            String[] split = params.split("-");
            startDate = split[0];
            endDate = split[1];
        }
        log.info("查询参数：{}-{}", startDate, endDate);

        List<EastBnwywdbrEntity> bnwywdbrModuleList = this.eastBnwywdbrService.getModuleList(endDate);

        List<EastBnwywdbrEntity> bnwywdbrList = eastBnwywdbrService.getBnwywdbrList(startDate, endDate);
        if (!bnwywdbrModuleList.isEmpty()) {
            for (EastBnwywdbrEntity bnwywdbr : bnwywdbrList) {
                EastBnwywdbrEntity module = bnwywdbrModuleList.stream().filter(cmEntity ->
                        cmEntity.getDbhth().equals(bnwywdbr.getDbhth()) && cmEntity.getDbrzjhm().equals(bnwywdbr.getDbrzjhm())).findFirst().orElse(null);
                if (module != null) {
                    bnwywdbr.setBzrlb(module.getBzrlb());
                    bnwywdbr.setBzrmc(module.getBzrmc());
                    bnwywdbr.setDbrzjlb(module.getDbrzjlb());
                    bnwywdbr.setDbrzjhm(module.getDbrzjhm());
                    bnwywdbr.setDbrjzcbz(module.getDbrjzcbz());
                    bnwywdbr.setDbrjzc(module.getDbrjzc());
                }
            }
        }
        eastBnwywdbrService.insertBatch(bnwywdbrList);

        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_602.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);
        log.info("===================602-表内外业务担保人抽数结束===================");
    }
}
