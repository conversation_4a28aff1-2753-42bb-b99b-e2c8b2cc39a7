package io.ibs.modules.east.task.fbnwdbxx;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.modules.east.common.EastTableInfo;
import io.ibs.modules.east.entity.fbnwdbxx.EastBnwywdzywEntity;
import io.ibs.modules.east.entity.report.EastDataRecordEntity;
import io.ibs.modules.east.service.fbnwdbxx.EastBnwywdzywService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * EAST采集表内外业务抵质押物数据
 * Created by AileYoung on 2024/3/21.
 */
@Service
@Slf4j
public class GetEastBnwywdzywDataTask extends BaseTask {
    private static final String name = StrUtil.format("[EAST-{}]", EastTableInfo.TABLE_603.getName());

    private final EastBnwywdzywService bnwywdzywService;

    /**
     * 构造函数
     */
    @Autowired
    public GetEastBnwywdzywDataTask(EastBnwywdzywService bnwywdzywService) {
        super(name, GetEastBnwywdzywDataTask.class);
        this.bnwywdzywService = bnwywdzywService;
    }

    @Override
    public void execute(String params) throws Exception {
        if (StringUtils.isBlank(params)) {
            String msg = name + "参数不能为空！";
            log.error(msg);
            throw new IllegalArgumentException(msg);
        }
        params = super.checkDateParam(params, "yyyyMMdd");
        String startDate;
        String endDate;
        if (params.contains("-")) {
            startDate = params.split("-")[0];
            endDate = params.split("-")[1];
        } else {
            startDate = params;
            endDate = params;
        }

        List<EastBnwywdzywEntity> bnwywdzywModuleList = this.bnwywdzywService.getModuleList(endDate);
        List<EastBnwywdzywEntity> bnwywdzywEntityList = new ArrayList<>(bnwywdzywService.getBnwywdzywData(startDate, endDate));

        if (!bnwywdzywModuleList.isEmpty()) {
            for (EastBnwywdzywEntity bnwywdzyw : bnwywdzywEntityList) {
                EastBnwywdzywEntity module = bnwywdzywModuleList.stream().filter(cmEntity ->
                        cmEntity.getDbhth().equals(bnwywdzyw.getDbhth()) &&
                                cmEntity.getYpbh().equals(bnwywdzyw.getYpbh())).findFirst().orElse(null);
                if (module != null) {
                    bnwywdzyw.setYplx(module.getYplx());
                    bnwywdzyw.setYpmc(module.getYpmc());
                    bnwywdzyw.setDzywzt(module.getDzywzt());
                    bnwywdzyw.setBz(module.getBz());
                    bnwywdzyw.setPgjz(module.getPgjz());
                    bnwywdzyw.setYxrdjz(module.getYxrdjz());
                    bnwywdzyw.setYdyjz(module.getYdyjz());
                    bnwywdzyw.setDzyl(module.getDzyl());
                    bnwywdzyw.setCzqsw(module.getCzqsw());
                    bnwywdzyw.setYpsyrmc(module.getYpsyrmc());
                    bnwywdzyw.setYpsyrzjlb(module.getYpsyrzjlb());
                    bnwywdzyw.setYpsyrzjhm(module.getYpsyrzjhm());
                    bnwywdzyw.setZypzhm(module.getZypzhm());
                    bnwywdzyw.setZypzqfjg(module.getZypzqfjg());
                    bnwywdzyw.setQzdjhm(module.getQzdjhm());
                    bnwywdzyw.setQzdjmj(module.getQzdjmj());
                }
            }
        }

        bnwywdzywService.insertBatch(bnwywdzywEntityList);

        log.info("登记采集记录表");
        EastDataRecordEntity eastDataRecord = new EastDataRecordEntity();
        eastDataRecord.setCjrq(endDate);
        eastDataRecord.setTableId(EastTableInfo.TABLE_603.getId());
        eastDataRecord.setParams(params);
        Db.save(eastDataRecord);
    }
}
