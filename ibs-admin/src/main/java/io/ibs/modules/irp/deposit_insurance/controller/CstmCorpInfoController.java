package io.ibs.modules.irp.deposit_insurance.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.Result;
import io.ibs.modules.irp.deposit_insurance.domain.TCstmCorpInfo;
import io.ibs.modules.irp.deposit_insurance.service.TCstmCorpInfoService;
import io.ibs.modules.security.user.SecurityUser;
import io.ibs.modules.sys.dto.SysDeptDTO;
import io.ibs.modules.sys.service.SysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * Created by AileYoung on 2022/6/1.
 */
@RestController
@RequestMapping("report/cstmCorpInfo")
@Api(tags = "单位客户资料管理")
public class CstmCorpInfoController {
    private SysDeptService sysDeptService;
    private TCstmCorpInfoService cstmCorpInfoService;

    @Autowired
    public CstmCorpInfoController(SysDeptService sysDeptService,
                                  TCstmCorpInfoService cstmCorpInfoService) {
        this.sysDeptService = sysDeptService;
        this.cstmCorpInfoService = cstmCorpInfoService;
    }

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
    })
    @RequiresPermissions("report:cstmCorpInfo:page")
    public Result<PageData<TCstmCorpInfo>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        SysDeptDTO deptDTO = sysDeptService.get(SecurityUser.getDeptId());
        String instNo = deptDTO == null ? "" : deptDTO.getInstNo();
        Page<TCstmCorpInfo> page = cstmCorpInfoService.page(new Page<>(Long.valueOf(params.get(Constant.PAGE).toString()), Long.valueOf(params.get(Constant.LIMIT).toString())),
                new QueryWrapper<TCstmCorpInfo>().lambda().
                        eq(params.get("cstmNo") != null && StringUtils.isNotBlank(params.get("cstmNo").toString()), TCstmCorpInfo::getCstmNo, params.get("cstmNo").toString()).
                        like(params.get("cstmName") != null && StringUtils.isNotBlank(params.get("cstmName").toString()), TCstmCorpInfo::getCstmName, params.get("cstmName").toString()).
                        likeRight(StringUtils.isNotBlank(instNo), TCstmCorpInfo::getCstmNo, instNo));
        PageData<TCstmCorpInfo> result = new PageData<>(page.getRecords(), page.getTotal());
        return new Result<PageData<TCstmCorpInfo>>().ok(result);
    }

    @GetMapping("{cstmNo}")
    @ApiOperation("信息")
    @RequiresPermissions("report:cstmCorpInfo:info")
    public Result<TCstmCorpInfo> get(@PathVariable("cstmNo") String cstmNo) {
        TCstmCorpInfo data = cstmCorpInfoService.getOne(new QueryWrapper<TCstmCorpInfo>().lambda().
                eq(TCstmCorpInfo::getCstmNo, cstmNo));
        return new Result<TCstmCorpInfo>().ok(data);
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("report:cstmCorpInfo:update")
    public Result update(@RequestBody TCstmCorpInfo cstmInfo) {
        cstmCorpInfoService.update(cstmInfo, new UpdateWrapper<TCstmCorpInfo>().lambda().
                eq(TCstmCorpInfo::getCstmNo, cstmInfo.getCstmNo()));
        return new Result();
    }

}
