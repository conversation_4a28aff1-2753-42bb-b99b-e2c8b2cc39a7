package io.ibs.modules.irp.deposit_insurance.dao;

import io.ibs.modules.irp.deposit_insurance.domain.CorpDepositInfo;
import io.ibs.modules.irp.deposit_insurance.domain.CstmCorpLoanInfo;
import io.ibs.modules.irp.deposit_insurance.domain.CstmDepositInfo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by AileYoung on 2022/5/27.
 */
@Repository
@Mapper
public interface ReportDataMapper {

    /**
     * 获取存款人信息及账户信息报表数据
     *
     * @param params 参数
     * @return 报表数据
     */
    List<CstmDepositInfo> getCstmDepositReportInfo(Map<String, Object> params);

    /**
     * 获取存款单位信息及账户信息报表数据
     *
     * @param params 参数
     * @return 报表数据
     */
    List<CorpDepositInfo> getCstmCorpDepositReportInfo(Map<String, Object> params);

    /**
     * 获取存款单位贷款账户信息报表数据
     *
     * @param params 参数
     * @return 报表数据
     */
    List<CstmCorpLoanInfo> getCstmCorpLoanReportInfo(Map<String, Object> params);

}
