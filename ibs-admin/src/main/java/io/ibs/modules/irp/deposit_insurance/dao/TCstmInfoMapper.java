package io.ibs.modules.irp.deposit_insurance.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.ibs.modules.irp.deposit_insurance.domain.TCstmInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 个人客户资料表 Dao资源访问类
 * Created by AileYoung on 2022-05-25 using generator
 */
@Repository
@Mapper
public interface TCstmInfoMapper extends BaseMapper<TCstmInfo> {

    /**
     * 第一次从核心数据库获取用户信息
     *
     * @return 用户信息
     */
    List<TCstmInfo> getCstmInfoListFromCBSFirst();

    /**
     * 从核心数据库获取用户信息
     *
     * @param lastModifyDate 上一次修改日期
     * @return 用户信息
     */
    List<TCstmInfo> getCstmInfoListFromCBS(@Param("lastModifyDate") Date lastModifyDate);

}
