package io.ibs.modules.irp.deposit_insurance.dao;


import io.ibs.common.dao.BaseDao;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.irp.deposit_insurance.entity.TLoanPledgeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 借据抵质押信息表
*
* @<NAME_EMAIL>
* @since 3.0 2022-06-22
*/
@Mapper
public interface TLoanPledgeDao extends BaseDao<TLoanPledgeEntity> {
    /**
     * 根据指定借据号获取对应的抵质押信息
     * @param loanNoList
     * @return
     */
    @DataSource("xdgl")
    List<TLoanPledgeEntity> getPledgeFirst(List<String> loanNoList);

    /**
     * 获取每天新增贷款的抵质押信息
     * @return
     */
    @DataSource("xdgl")
    List<TLoanPledgeEntity> getPledgeByDay(@Param("tranDate") String tranDate);
}