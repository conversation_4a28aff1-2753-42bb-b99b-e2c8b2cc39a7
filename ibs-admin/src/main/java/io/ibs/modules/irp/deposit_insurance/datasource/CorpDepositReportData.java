package io.ibs.modules.irp.deposit_insurance.datasource;

import io.ibs.modules.irp.deposit_insurance.domain.CorpDepositInfo;
import io.ibs.modules.irp.deposit_insurance.domain.CstmDepositInfo;
import io.ibs.modules.irp.deposit_insurance.service.ReportDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by AileYoung on 2022/5/19.
 */
@Component
@Slf4j
public class CorpDepositReportData extends ReportData {
    private ReportDataService reportDataService;

    @Autowired
    public CorpDepositReportData(ReportDataService reportDataService) {
        this.reportDataService = reportDataService;
    }

    /**
     * 获取单位存款人信息及账户信息报表数据
     *
     * @param dsName      数据源
     * @param datasetName 数据集
     * @param parameters  参数
     * @return 单位存款人信息及账户信息报表数据
     */
    public List<CorpDepositInfo> getCorpDepositReportInfo(String dsName, String datasetName, Map<String, Object> parameters) {
        log.info("开始获取单位存款人信息及账户信息报表数据");
        parameters.put("accFlag", "2");//2-单位
        String reportMonth = (String) parameters.get("reportMonth");
        parameters.put("reportMonths", seasonToMonths(reportMonth));
        List<CorpDepositInfo> CstmCorpDepositInfos = reportDataService.getCstmCorpDepositReportInfo(parameters);
        return CstmCorpDepositReportInfoGroup(CstmCorpDepositInfos);
    }

    private List<CorpDepositInfo> CstmCorpDepositReportInfoGroup(List<CorpDepositInfo> CstmCorpDepositInfos) {
        int i = 0;
        int j = 1;
        String tmpCsmtNo = "";
        //客户本息合计（一个客户下会有多个账户）
        BigDecimal total = new BigDecimal("0.00");
        //按客户存放本息合计
        HashMap<String, BigDecimal> cstmMap = new HashMap<String, BigDecimal>();
        for (CorpDepositInfo info : CstmCorpDepositInfos) {
            if (i == 0) {
                tmpCsmtNo = info.getCstmNo();
                info.setSeq(i++);
            }
            if (!tmpCsmtNo.equals(info.getCstmNo())) {
                i++;
                j = 1;
                tmpCsmtNo = info.getCstmNo();
                //客户号变化，合计清零
                total = new BigDecimal("0.00");
            }

            //单个账户本息合计
            info.setBalAndIntr( info.getBal().add( info.getIntr() ) );
            //统计本息合计：各存款账户本息合计 = 该存款人所有账户本息合计之和
            total = total.add(info.getBal());//累加本金
            total = total.add(info.getIntr());//累加利息
            BigDecimal cstmTotal = total;
            cstmMap.put(tmpCsmtNo, cstmTotal);

            info.setSeq(i);
            info.setSubSeq(j++);
        }
        //再次遍历，更新本息合计、被保险存款本息合计(非高管）、受保存款本息合计(非高管)
        /** 各存款账户本息合计 = 该存款人所有账户本息合计之和
         *  被保险存款本息合计 = 该存款人所有账户本息合计之和
         *  受保存款本息合计大于或等于50万，则=50万；小于50万则 = 被保险存款本息合计
         */
        BigDecimal fifty = new BigDecimal("500000.00");
        for (CorpDepositInfo info : CstmCorpDepositInfos) {
            BigDecimal balIntrTotal = cstmMap.get( info.getCstmNo() );
            info.setTotal( balIntrTotal ); //各存款账户本息合计
            info.setInsuTotal( balIntrTotal );  //被保险存款本息合计
            if( balIntrTotal.compareTo(fifty) == -1 ) {
                //小于50万
                info.setAcptInsuTotal(balIntrTotal);
            }else {
                info.setAcptInsuTotal( fifty );
            }
        }
        return CstmCorpDepositInfos;
    }


}
