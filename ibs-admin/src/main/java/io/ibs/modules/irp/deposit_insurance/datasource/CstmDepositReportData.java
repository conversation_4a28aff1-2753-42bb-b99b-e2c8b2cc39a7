package io.ibs.modules.irp.deposit_insurance.datasource;

import io.ibs.modules.irp.deposit_insurance.domain.CstmDepositInfo;
import io.ibs.modules.irp.deposit_insurance.service.ReportDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by AileYoung on 2022/5/19.
 */
@Component
@Slf4j
public class CstmDepositReportData extends ReportData {
    private ReportDataService reportDataService;

    @Autowired
    public CstmDepositReportData(ReportDataService reportDataService) {
        this.reportDataService = reportDataService;
    }

    /**
     * 获取个人存款账户信息及客户信息报表数据
     *
     * @param dsName      数据源
     * @param datasetName 数据集
     * @param parameters  参数
     * @return 存款人信息及账户信息报表数据
     */
    public List<CstmDepositInfo> getPersonalCstmDepositReportInfo(String dsName, String datasetName, Map<String, Object> parameters) {
        log.info("开始获取个人存款人信息及账户信息报表数据");
        parameters.put("accFlag", "1");//1-个人
        parameters.put("dutyFlag", "0");//0-非高管
        String reportMonth = (String) parameters.get("reportMonth");
        parameters.put("reportMonths", seasonToMonths(reportMonth));
        List<CstmDepositInfo> cstmDepositInfos = reportDataService.getCstmDepositReportInfo(parameters);
        return cstmDepositReportInfoGroup(cstmDepositInfos);
    }

    /**
     * 获取高管存款人信息及账户信息报表数据
     *
     * @param dsName      数据源
     * @param datasetName 数据集
     * @param parameters  参数
     * @return 存款人信息及账户信息报表数据
     */
    public List<CstmDepositInfo> getDutyCstmDepositReportInfo(String dsName, String datasetName, Map<String, Object> parameters) {
        log.info("开始获取高管存款人信息及账户信息报表数据");
        parameters.put("accFlag", "1");//1-个人
        parameters.put("dutyFlag", "1");//1-高管
        String reportMonth = (String) parameters.get("reportMonth");
        parameters.put("reportMonths", seasonToMonths(reportMonth));
        List<CstmDepositInfo> cstmDepositInfos = reportDataService.getCstmDepositReportInfo(parameters);
        return dutyInfoGroup(cstmDepositInfos);
    }

    /**
     * 个人存款分组排序
     * 遍历数据库list,添加序号:seq 客户序号；subSeq：客户账号序号
     *
     * @param cstmDepositInfos
     * @return
     */
    private List<CstmDepositInfo> cstmDepositReportInfoGroup(List<CstmDepositInfo> cstmDepositInfos) {
        int i = 0;
        int j = 1;
        String tmpCsmtNo = "";
        //客户本息合计（一个客户下会有多个账户）
        BigDecimal total = new BigDecimal("0.00");
        //按客户存放本息合计
        HashMap<String, BigDecimal> cstmMap = new HashMap<String, BigDecimal>();
        for (CstmDepositInfo info : cstmDepositInfos) {
            if (i == 0) {
                tmpCsmtNo = info.getCstmNo();
                info.setSeq(i++);
            }
            if (!tmpCsmtNo.equals(info.getCstmNo())) {
                i++;
                j = 1;
                tmpCsmtNo = info.getCstmNo();
                //客户号变化，合计清零
                total = new BigDecimal("0.00");
            }
            //单个账户本息合计
            info.setBalAndIntr( info.getBal().add( info.getIntr() ) );
            //统计本息合计：各存款账户本息合计 = 该存款人所有账户本息合计之和
            total = total.add(info.getBal());//累加本金
            total = total.add(info.getIntr());//累加利息
            BigDecimal cstmTotal = total;
            cstmMap.put(tmpCsmtNo, cstmTotal);

            info.setSeq(i);
            info.setSubSeq(j++);
        }

        //再次遍历，更新本息合计、被保险存款本息合计(非高管）、受保存款本息合计(非高管)
        /** 各存款账户本息合计 = 该存款人所有账户本息合计之和
         *  被保险存款本息合计 = 该存款人所有账户本息合计之和
         *  受保存款本息合计大于或等于50万，则=50万；小于50万则 = 被保险存款本息合计
         */
        BigDecimal fifty = new BigDecimal("500000.00");
        for (CstmDepositInfo info : cstmDepositInfos) {
            BigDecimal balIntrTotal = cstmMap.get( info.getCstmNo() );
            info.setTotal( balIntrTotal ); //各存款账户本息合计
            info.setInsuTotal( balIntrTotal );  //被保险存款本息合计
            if( balIntrTotal.compareTo(fifty) == -1 ) {
                //小于50万
                info.setAcptInsuTotal(balIntrTotal);
            }else {
                info.setAcptInsuTotal( fifty );
            }
        }
        return cstmDepositInfos;
    }

    /**
     * 高管分组排序,统计本息和(非高管合计通过报表表达式配置实现)
     * 遍历数据库list,添加序号:seq 客户序号；subSeq：客户账号序号
     *
     * @param cstmDepositInfos
     * @return
     */
    private List<CstmDepositInfo> dutyInfoGroup(List<CstmDepositInfo> cstmDepositInfos) {
        int i = 0;  //客户序号
        int j = 1;  //客户账号序号
        String tmpCsmtNo = "";
        BigDecimal total = new BigDecimal("0.00");
        HashMap<String, BigDecimal> cstmMap = new HashMap<String, BigDecimal>();
        for (CstmDepositInfo info : cstmDepositInfos) {
            if (i == 0) {
                tmpCsmtNo = info.getCstmNo();
                info.setSeq(i++);
            }
            if (!tmpCsmtNo.equals(info.getCstmNo())) {
                i++;
                j = 1;
                tmpCsmtNo = info.getCstmNo();
                total = new BigDecimal("0.00");//合计清零
            }
            //单个账户本息合计
            info.setBalAndIntr( info.getBal().add( info.getIntr() ) );
            //统计本息合计
            total = total.add(info.getBal());//累加本金
            total = total.add(info.getIntr());//累加利息
            BigDecimal cstmTotal = total;
            log.info("cstmNo:" + tmpCsmtNo + ",cstmTotal:" + cstmTotal.doubleValue() + ",total=" + total.doubleValue());
            cstmMap.put(tmpCsmtNo, cstmTotal);

            info.setSeq(i);
            info.setSubSeq(j++);
        }
        //再次遍历，更新本息合计
        for (CstmDepositInfo info : cstmDepositInfos) {
            info.setTotal(cstmMap.get(info.getCstmNo()));
        }
        return cstmDepositInfos;
    }
}
