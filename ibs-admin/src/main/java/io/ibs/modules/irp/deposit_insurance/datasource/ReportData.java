package io.ibs.modules.irp.deposit_insurance.datasource;

import io.ibs.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by AileYoung on 2022/7/26.
 */
@Slf4j
public class ReportData {

    public List<Map<String, Object>> getRptDate(String dsName, String datasetName, Map<String, Object> parameters) {
        String reportMonth = (String) parameters.get("reportMonth");
        log.info("报送季度:" + reportMonth);
        reportMonth = seasonToMonth(reportMonth);
        log.info("报送月份:" + reportMonth);
        String monthLastDay = DateUtils.format(DateUtils.getMonthLastDay(reportMonth), "yyyy年MM月dd日");
        log.info("报送日期:" + monthLastDay);
        Map<String, Object> map = new HashMap<>();
        map.put("rpt_date", monthLastDay);
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(map);
        return list;
    }

    /**
     * 季度转月份，返回当前季度的最后一个月
     * <p>
     * 例如：
     * 传入2022-1，返回202203
     * 传入2022-2，返回202206
     * 传入2022-3，返回202209
     * 传入2022-4，返回202212
     *
     * @param reportSeason 季度 格式 yyyy-S
     * @return 当前季度的最后一个月
     */
    protected String seasonToMonth(String reportSeason) {
        String reportMonth;
        String[] tmp = reportSeason.split("-");
        String year = tmp[0];
        String season = tmp[1];
        switch (season) {
            case "1":
                reportMonth = year + "03";
                break;
            case "2":
                reportMonth = year + "06";
                break;
            case "3":
                reportMonth = year + "09";
                break;
            case "4":
                reportMonth = year + "12";
                break;
            default:
                throw new IllegalArgumentException("错误的报表报送季度：[" + reportSeason + "]");
        }
        return reportMonth;
    }

    /**
     * 季度转月份，返回当前季度的组成月份
     * <p>
     * 例如：
     * 传入2022-1，返回["202201", "202202", "202203"]
     * 传入2022-2，返回["202204", "202205", "202206"]
     * 传入2022-3，返回["202207", "202208", "202209"]
     * 传入2022-4，返回["202210", "202211", "202212"]
     *
     * @param reportSeason 季度 格式 yyyy-S
     * @return 当前季度的组成月份
     */
    protected String[] seasonToMonths(String reportSeason) {
        String reportMonths[] = new String[3];
        String[] tmp = reportSeason.split("-");
        String year = tmp[0];
        String season = tmp[1];
        switch (season) {
            case "1":
                reportMonths[0] = year + "01";
                reportMonths[1] = year + "02";
                reportMonths[2] = year + "03";
                break;
            case "2":
                reportMonths[0] = year + "04";
                reportMonths[1] = year + "05";
                reportMonths[2] = year + "06";
                break;
            case "3":
                reportMonths[0] = year + "07";
                reportMonths[1] = year + "08";
                reportMonths[2] = year + "09";
                break;
            case "4":
                reportMonths[0] = year + "10";
                reportMonths[1] = year + "11";
                reportMonths[2] = year + "12";
                break;
            default:
                throw new IllegalArgumentException("错误的报表报送季度：[" + reportSeason + "]");
        }
        return reportMonths;
    }
}
