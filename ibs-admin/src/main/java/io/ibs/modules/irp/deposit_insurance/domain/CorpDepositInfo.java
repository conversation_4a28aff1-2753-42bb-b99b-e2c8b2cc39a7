package io.ibs.modules.irp.deposit_insurance.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by AileYoung on 2022/5/27.
 */
@Data
public class CorpDepositInfo {
    private Integer seq;
    private String cstmNo;        //客户号
    private String cstmName;      //客户名称
    private String paperType;     //证件类型 01:营业执照 02:统一社会信用代码证 03:政府批文或证明 04:法人登记证书 05:相关单位或部门证明
    private String uscc;          //统一社会信用代码
    private Date usccExpire;      //证件有效期
    private String corpReprName;  //法定代表人姓名
    private String corpReprIdType;//法定代表人证件类型 01:身份证 02:户口簿 03:护照 04:军官证 05:士兵证 06:港澳居民来往内地通行证 07:台湾居民来往大陆通行证 08:临时身份证 09:外国人居留证 10:警官证 11:个体工商户营业执照 12:港澳台居民居住证 13:其他证件
    private String corpReprId;    //法定代表人证件号码
    private String corpTelephone; //单位联系电话
    private String corpAddr;      //单位联系地址

    private Integer subSeq;
    private String rptDate;
    private String acc;
    private String accFlag;
    private String accState;
    private String accType;
    private String prdType;
    private String accLevel;
    private BigDecimal bal;
    private BigDecimal intr;

    private BigDecimal balAndIntr;  //单账户本息合计
    private BigDecimal total;       //所有存款账户本息合计
    private BigDecimal insuTotal;   //被保险存款本息合计
    private BigDecimal acptInsuTotal;   //受保存款本息合计
}
