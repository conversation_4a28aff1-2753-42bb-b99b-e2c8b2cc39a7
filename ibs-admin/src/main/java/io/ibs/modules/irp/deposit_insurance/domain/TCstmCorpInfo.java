package io.ibs.modules.irp.deposit_insurance.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 单位客户资料表实体类
 * Created by AileYoung on 2022-06-01 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_CSTM_CORP_INFO")
public class TCstmCorpInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户号
     */
    @TableId("CSTM_NO")
    private String cstmNo;

    /**
     * 客户名称
     */
    @TableField("CSTM_NAME")
    private String cstmName;

    /**
     * 证件类型 01:营业执照 02:统一社会信用代码证 03:政府批文或证明 04:法人登记证书 05:相关单位或部门证明
     */
    @TableField("PAPER_TYPE")
    private String paperType;

    /**
     * 统一社会信用代码
     */
    @TableField("USCC")
    private String uscc;

    /**
     * 证件有效期
     */
    @TableField("USCC_EXPIRE")
    private Date usccExpire;

    /**
     * 法定代表人姓名
     */
    @TableField("CORP_REPR_NAME")
    private String corpReprName;

    /**
     * 法定代表人证件类型 01:身份证 02:户口簿 03:护照 04:军官证 05:士兵证 06:港澳居民来往内地通行证 07:台湾居民来往大陆通行证 08:临时身份证 09:外国人居留证 10:警官证 11:个体工商户营业执照 12:港澳台居民居住证 13:其他证件
     */
    @TableField("CORP_REPR_ID_TYPE")
    private String corpReprIdType;

    /**
     * 法定代表人证件号码
     */
    @TableField("CORP_REPR_ID")
    private String corpReprId;

    /**
     * 单位联系电话
     */
    @TableField("CORP_TELEPHONE")
    private String corpTelephone;

    /**
     * 单位联系地址
     */
    @TableField("CORP_ADDR")
    private String corpAddr;

}
