package io.ibs.modules.irp.deposit_insurance.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人客户资料表实体类
 * Created by AileYoung on 2022-05-25 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_CSTM_INFO")
public class TCstmInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户号
     */
    @TableId("CSTM_NO")
    private String cstmNo;

    /**
     * 客户名称
     */
    @TableField("CSTM_NAME")
    private String cstmName;

    /**
     * 证件类型
     */
    @TableField("IDENTITY_TYPE")
    private String identityType;

    /**
     * 证件号码
     */
    @TableField("IDENTITY")
    private String identity;

    /**
     * 证件有效期
     */
    @TableField("IDENTITY_EXPIRE")
    private Date identityExpire;

    /**
     * 性别
     */
    @TableField("GENDER")
    private String gender;

    /**
     * 职业
     */
    @TableField("OCCUPATION")
    private String occupation;

    /**
     * 联系电话
     */
    @TableField("TELEPHONE")
    private String telephone;

    /**
     * 联系地址
     */
    @TableField("ADDR")
    private String addr;

    /**
     * 高管标志 0-非高管 1-高管
     */
    @TableField("DUTY_FLAG")
    private String dutyFlag;
}
