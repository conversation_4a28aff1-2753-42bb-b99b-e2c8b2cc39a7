package io.ibs.modules.irp.deposit_insurance.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 同一人存款账户信息表实体类
 * Created by AileYoung on 2022-05-25 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_DEPOSIT_INFO")
public class TDepositInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报送日期
     */
    @TableField("RPT_DATE")
    private String rptDate;

    /**
     * 账号
     */
    @TableField("ACC")
    private String acc;

    /**
     * 账户标志
     */
    @TableField("ACC_FLAG")
    private String accFlag;

    /**
     * 账户状态
     */
    @TableField("ACC_STATE")
    private String accState;

    /**
     * 账户类型
     */
    @TableField("ACC_TYPE")
    private String accType;

    /**
     * 客户号
     */
    @TableField("CSTM_NO")
    private String cstmNo;

    /**
     * 存款产品类别
     */
    @TableField("PRD_TYPE")
    private String prdType;

    /**
     * 存款账户类型
     */
    @TableField("ACC_LEVEL")
    private String accLevel;

    /**
     * 存款本金
     */
    @TableField("BAL")
    private BigDecimal bal;

    /**
     * 应付利息
     */
    @TableField("INTR")
    private BigDecimal intr;

}
