package io.ibs.modules.irp.deposit_insurance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @createTime 2022/7/26 17:37
 * @description 同一人存款账户信息统计
 * @Version 1.0
 */
@Data
@ApiModel(value = "同一人存款账户信息统计")
public class TDepositInfoStatsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期")
    private String rptDate;
    @ApiModelProperty(value = "总户数")
    private Integer count;
    @ApiModelProperty(value = "总和（存款+利息）")
    private BigDecimal total;
    @ApiModelProperty(value = "本金总和")
    private BigDecimal balTotal;
    @ApiModelProperty(value = "利息总和")
    private BigDecimal totalIntr;
}
