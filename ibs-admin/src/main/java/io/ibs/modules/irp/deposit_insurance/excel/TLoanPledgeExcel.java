package io.ibs.modules.irp.deposit_insurance.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 借据质押信息表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-06-22
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class TLoanPledgeExcel {
    @ExcelProperty(value = "借据号", index = 0)
    private String loanNo;
    @ExcelProperty(value = "序号", index = 1)
    private String seqNo;
    @ExcelProperty(value = "抵质押物类型", index = 2)
    private String pledgeType;
    @ExcelProperty(value = "抵质押合同号", index = 3)
    private String pledgeNo;
    @ExcelProperty(value = "抵质押物金额", index = 4)
    private BigDecimal pledgeAmt;
}