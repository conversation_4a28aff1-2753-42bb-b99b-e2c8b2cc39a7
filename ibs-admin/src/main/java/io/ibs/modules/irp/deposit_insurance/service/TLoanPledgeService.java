package io.ibs.modules.irp.deposit_insurance.service;


import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.deposit_insurance.dto.TLoanPledgeDTO;
import io.ibs.modules.irp.deposit_insurance.entity.TLoanPledgeEntity;

import java.util.List;

/**
 * 借据质押信息表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-06-22
 */
public interface TLoanPledgeService extends CrudService<TLoanPledgeEntity, TLoanPledgeDTO> {

    /**
     * 根据借据号和抵质押物合同号获取抵质押物信息
     *
     * @param dto 抵质押物dto
     * @return 抵质押物信息
     */
    TLoanPledgeDTO getByLoanNoAndPledgeNo(TLoanPledgeDTO dto);

    TLoanPledgeDTO getByLoanNoAndSeqNo(TLoanPledgeDTO dto);

    // TLoanPledgeDTO updateByLoanNoAndPledgeNo(TLoanPledgeDTO dto);

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    int deleteByLoanNoAndPledgeNo(String[] ids);

    /**
     * 首次获取抵质押物信息
     *
     * @param loanNoList
     * @return
     */
    List<TLoanPledgeEntity> getPledgeFirst(List<String> loanNoList);

    /**
     * 获取指定日期新增的贷款抵质押信息
     *
     * @return
     */
    List<TLoanPledgeEntity> getPledgeByDay(String tranDate);
}