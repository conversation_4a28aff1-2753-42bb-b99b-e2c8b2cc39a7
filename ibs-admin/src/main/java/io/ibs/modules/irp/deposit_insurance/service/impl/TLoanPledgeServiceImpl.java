package io.ibs.modules.irp.deposit_insurance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.deposit_insurance.dao.TLoanPledgeDao;
import io.ibs.modules.irp.deposit_insurance.dto.TLoanPledgeDTO;
import io.ibs.modules.irp.deposit_insurance.entity.TLoanPledgeEntity;
import io.ibs.modules.irp.deposit_insurance.service.TLoanPledgeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 借据质押信息表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-06-22
 */
@Service
public class TLoanPledgeServiceImpl extends CrudServiceImpl<TLoanPledgeDao, TLoanPledgeEntity, TLoanPledgeDTO> implements TLoanPledgeService {

    @Override
    public QueryWrapper<TLoanPledgeEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<TLoanPledgeEntity> wrapper = new QueryWrapper<>();
        String seqNo = (String) params.get("seqNo");
        wrapper.eq(StringUtils.isNotBlank(seqNo), "SEQ_NO", seqNo);
        String loanNo = (String) params.get("loanNo");
        wrapper.eq(StringUtils.isNotBlank(loanNo), "LOAN_NO", loanNo);
        String pledgeType = (String) params.get("pledgeType");
        wrapper.eq(StringUtils.isNotBlank(pledgeType), "PLEDGE_TYPE", pledgeType);
        String pledgeNo = (String) params.get("pledgeNo");
        wrapper.like(StringUtils.isNotBlank(pledgeNo), "PLEDGE_NO", pledgeNo);

        return wrapper;
    }

    @Override
    public TLoanPledgeDTO getByLoanNoAndPledgeNo(TLoanPledgeDTO dto) {
        TLoanPledgeEntity entity = baseDao.selectOne(new QueryWrapper<TLoanPledgeEntity>().lambda().
                eq(TLoanPledgeEntity::getLoanNo, dto.getLoanNo()).
                eq(TLoanPledgeEntity::getPledgeNo, dto.getPledgeNo()));
        return ConvertUtils.sourceToTarget(entity, TLoanPledgeDTO.class);
    }

    @Override
    public TLoanPledgeDTO getByLoanNoAndSeqNo(TLoanPledgeDTO dto) {
        TLoanPledgeEntity entity = baseDao.selectOne(new QueryWrapper<TLoanPledgeEntity>().lambda().
                eq(TLoanPledgeEntity::getLoanNo, dto.getLoanNo()).
                eq(TLoanPledgeEntity::getSeqNo, dto.getSeqNo()));
        return ConvertUtils.sourceToTarget(entity, TLoanPledgeDTO.class);
    }

/*    @Override
    public TLoanPledgeDTO updateByLoanNoAndPledgeNo(TLoanPledgeDTO dto) {
        TLoanPledgeEntity entity = new TLoanPledgeEntity();

        int update = baseDao.update(ConvertUtils.sourceToTarget(dto, TLoanPledgeEntity.class), new QueryWrapper<TLoanPledgeEntity>().lambda().
                eq(TLoanPledgeEntity::getLoanNo, dto.getLoanNo()).
                eq(TLoanPledgeEntity::getPledgeNo, dto.getPledgeNo()));
        return ConvertUtils.sourceToTarget(entity, TLoanPledgeDTO.class);
    }*/

    @Override
    public int deleteByLoanNoAndPledgeNo(String[] ids) {
        int delete = 0;
        for (int i = 0; i < ids.length; i++) {
            System.out.println(ids[i]);
            String[] s = ids[i].split("#@#");
            String loanNo = s[0];
            String seqNo = s[1];
            delete = baseDao.delete(new QueryWrapper<TLoanPledgeEntity>().lambda().
                    eq(TLoanPledgeEntity::getLoanNo, loanNo).
                    eq(TLoanPledgeEntity::getSeqNo, seqNo));
        }
        return delete;
    }


    /**
     * 首次获取抵质押物信息
     *
     * @param loanNoList
     * @return
     */
    @Override
    public List<TLoanPledgeEntity> getPledgeFirst(List<String> loanNoList) {
        return baseDao.getPledgeFirst(loanNoList);
    }

    /**
     * 获取指定日期新增的贷款抵质押信息
     *
     * @param tranDate
     * @return
     */
    @Override
    public List<TLoanPledgeEntity> getPledgeByDay(String tranDate) {
        return baseDao.getPledgeByDay(tranDate);
    }

}