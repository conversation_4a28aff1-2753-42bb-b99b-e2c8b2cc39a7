package io.ibs.modules.irp.deposit_insurance.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.common.utils.DateUtils;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.modules.irp.deposit_insurance.domain.TCstmInfo;
import io.ibs.modules.irp.deposit_insurance.service.TCstmInfoService;
import io.ibs.modules.job.task.ITask;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据同步定时任务
 * Created by AileYoung on 2022/5/25.
 */
@Service
@Slf4j
public class CstmInfoSync implements ITask {
    private TransactionHelper transactionHelper;
    private TCstmInfoService tCstmInfoService;

    @Autowired
    public CstmInfoSync(TransactionHelper transactionHelper,
                        TCstmInfoService tCstmInfoService) {
        this.transactionHelper = transactionHelper;
        this.tCstmInfoService = tCstmInfoService;
    }

    /**
     * 定时任务：个人客户资料表数据同步
     *
     * @param params 当参数为"first"时，代表是定时任务首次执行，从核心获取全部数据并插入数据库；
     *               为参数为"yesterday"时，去核心获取“最后更改日期”为昨天的数据；
     *               当参数为指定日期(yyyyMMdd)时，则去核心获取“最后更改日期”为传入日期的数据
     */
    @Override
    public void run(String params) {
        MDC.put("txcode", "task/CstmInfoSync");
        try {
            log.info("========定时任务：个人客户资料表数据同步========");
            log.info("定时任务参数为：[{}]", params);

            Date date;
            //首次查询
            if ("first".equals(params)) {
                log.info("当前为首次数据同步，去核心获取数据");
                List<TCstmInfo> cstmInfoList = tCstmInfoService.getCstmInfoListFromCBSFirst();
                cstmInfoSyncHandle(cstmInfoList);
                log.info("========定时任务：个人客户资料表数据同步 结束========\n");
                return;
            } else if ("yesterday".equals(params)) {
                Date today = DateUtils.parse(DateUtils.format(new Date(), "yyyyMMdd"), "yyyyMMdd");
                date = DateUtils.addDateDays(today, -1);
            } else {
                date = DateUtils.parse(params, "yyyyMMdd");
            }
            log.info("去核心获取[{}]数据", DateUtils.format(date, "yyyyMMdd"));
            List<TCstmInfo> cstmInfoList = tCstmInfoService.getCstmInfoListFromCBS(date);
            cstmInfoSyncHandle(cstmInfoList);
            log.info("========定时任务：个人客户资料表数据同步 结束========\n");
        } finally {
            MDC.clear();
        }
    }

    private void cstmInfoSyncHandle(List<TCstmInfo> cstmInfoList) {
        //进行事务管理
        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();
        List<TCstmInfo> saveList = new ArrayList<>();
        try {
            for (TCstmInfo cstmInfo : cstmInfoList) {
                String cstmName = cstmInfo.getCstmName();
                log.info("从核心获取到的cstmName：【{}】", cstmName);
                String tmp = new String(cstmName.getBytes(), "GB18030");
                log.info("转码后的cstmName：【{}】", tmp);
                cstmInfo.setCstmName(tmp);

                //存在更新，不存在插入
                TCstmInfo tCstmInfo = tCstmInfoService.getOne(new QueryWrapper<TCstmInfo>().lambda().
                        eq(TCstmInfo::getCstmNo, cstmInfo.getCstmNo()));
                if (tCstmInfo == null) {
                    //每1000条插入一次
                    saveList.add(cstmInfo);
                    if (saveList.size() == 1000) {
                        tCstmInfoService.saveBatch(saveList);
                        saveList.clear();
                    }
                } else {
                    tCstmInfoService.update(cstmInfo, new UpdateWrapper<TCstmInfo>().lambda().
                            eq(TCstmInfo::getCstmNo, cstmInfo.getCstmNo()));
                }
            }
            if (saveList.size() > 0) {
                tCstmInfoService.saveBatch(saveList);
            }
            transactionHelper.commit(tm);
        } catch (Exception e) {
            log.error("个人客户资料表数据同步时捕获到异常", e);
            transactionHelper.rollback(tm);
        }
    }
}
