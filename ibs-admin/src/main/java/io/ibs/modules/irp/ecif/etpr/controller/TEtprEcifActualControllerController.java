package io.ibs.modules.irp.ecif.etpr.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.ecif.etpr.dto.TEtprEcifActualControllerDTO;
import io.ibs.modules.irp.ecif.etpr.excel.TEtprEcifActualControllerExcel;
import io.ibs.modules.irp.ecif.etpr.service.TEtprEcifActualControllerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 企业基础信息表-实际控制人段
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@RestController
@RequestMapping("ecif/tetprecifactualcontroller")
@Api(tags = "企业基础信息表-实际控制人段")
public class TEtprEcifActualControllerController {
    @Autowired
    private TEtprEcifActualControllerService tEtprEcifActualControllerService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("ecif:tetprecifactualcontroller:page")
    public Result<PageData<TEtprEcifActualControllerDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<TEtprEcifActualControllerDTO> page = tEtprEcifActualControllerService.page(params);

        return new Result<PageData<TEtprEcifActualControllerDTO>>().ok(page);
    }

    @GetMapping("list/{cstmNo}")
    @ApiOperation("列表信息")
    @RequiresPermissions("ecif:tetprecifactualcontroller:info")
    public Result<List<TEtprEcifActualControllerDTO>> getList(@PathVariable("cstmNo") String cstmNo) {
        List<TEtprEcifActualControllerDTO> data = tEtprEcifActualControllerService.getListByCstmNos(new String[]{cstmNo});
        return new Result<List<TEtprEcifActualControllerDTO>>().ok(data);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("ecif:tetprecifactualcontroller:info")
    public Result<TEtprEcifActualControllerDTO> get(@PathVariable("id") String id) {
        String[] params = id.split("\\|", -1);
        String cstmNo = params[0];
        String actuCotrlIdNum = params[1];
        TEtprEcifActualControllerDTO data = tEtprEcifActualControllerService.getByPK(cstmNo, actuCotrlIdNum);
        return new Result<TEtprEcifActualControllerDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("ecif:tetprecifactualcontroller:save")
    public Result save(@RequestBody TEtprEcifActualControllerDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        tEtprEcifActualControllerService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("ecif:tetprecifactualcontroller:update")
    public Result update(@RequestBody TEtprEcifActualControllerDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        tEtprEcifActualControllerService.updateByPK(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("ecif:tetprecifactualcontroller:delete")
    public Result delete(@RequestBody String id) {
        String[] params = id.split("\\|", -1);
        String cstmNo = params[0];
        String actuCotrlIdNum = params[1];
        tEtprEcifActualControllerService.deleteByPK(cstmNo, actuCotrlIdNum);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("ecif:tetprecifactualcontroller:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TEtprEcifActualControllerDTO> list = tEtprEcifActualControllerService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "企业基础信息表-实际控制人段", list, TEtprEcifActualControllerExcel.class);
    }

}