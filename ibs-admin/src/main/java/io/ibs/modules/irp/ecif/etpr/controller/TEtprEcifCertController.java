package io.ibs.modules.irp.ecif.etpr.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.ecif.etpr.dto.TEtprEcifCertDTO;
import io.ibs.modules.irp.ecif.etpr.excel.TEtprEcifCertExcel;
import io.ibs.modules.irp.ecif.etpr.service.TEtprEcifCertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 企业基础信息表-身份标识整合记录
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@RestController
@RequestMapping("ecif/tetprecifcert")
@Api(tags = "企业基础信息表-身份标识整合记录")
public class TEtprEcifCertController {
    @Autowired
    private TEtprEcifCertService tEtprEcifCertService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("ecif:tetprecifcert:page")
    public Result<PageData<TEtprEcifCertDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<TEtprEcifCertDTO> page = tEtprEcifCertService.page(params);

        return new Result<PageData<TEtprEcifCertDTO>>().ok(page);
    }

    @GetMapping("{cstmNo}")
    @ApiOperation("信息")
    @RequiresPermissions("ecif:tetprecifcert:info")
    public Result<TEtprEcifCertDTO> get(@PathVariable("cstmNo") String cstmNo) {
        TEtprEcifCertDTO data = tEtprEcifCertService.getByCstmNo(cstmNo);
        if (data == null) {
            //查询为空时返回一条空数据
            data = new TEtprEcifCertDTO();
            data.setCstmNo(cstmNo);
        }
        return new Result<TEtprEcifCertDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("ecif:tetprecifcert:save")
    public Result save(@RequestBody TEtprEcifCertDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        tEtprEcifCertService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("ecif:tetprecifcert:update")
    public Result update(@RequestBody TEtprEcifCertDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        tEtprEcifCertService.updateByCstmNo(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("ecif:tetprecifcert:delete")
    public Result delete(@RequestBody String[] cstmNo) {
        //效验数据
        AssertUtils.isArrayEmpty(cstmNo, "cstmNo");

        tEtprEcifCertService.deleteByCstmNo(cstmNo);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("ecif:tetprecifcert:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TEtprEcifCertDTO> list = tEtprEcifCertService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "企业基础信息表-身份标识整合记录", list, TEtprEcifCertExcel.class);
    }

}