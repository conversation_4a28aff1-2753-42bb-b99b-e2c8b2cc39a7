package io.ibs.modules.irp.ecif.etpr.dto;

import io.ibs.modules.irp.ecif.common.dto.EcifBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 企业基础信息表-基础段
*
* <AUTHOR> 1
* @since 3.0 2022-07-14
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "企业基础信息表-基础段")
public class TEtprEcifBaseDTO extends EcifBaseDTO implements  Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机构号")
    private String instNo;
    @ApiModelProperty(value = "企业名称")
    private String entName;
    @ApiModelProperty(value = "企业身份标识类型")
    private String entCertType;
    @ApiModelProperty(value = "企业身份标识号码")
    private String entCertNum;
    @ApiModelProperty(value = "企业身份标识到期日")
    private Date entCertExpDate;
    @ApiModelProperty(value = "客户资料类型")
    private String customerType;
    @ApiModelProperty(value = "存续状态")
    private String etpsts;
    @ApiModelProperty(value = "组织机构类型")
    private String orgType;
    @ApiModelProperty(value = "经营状态")
    private String state;
    @ApiModelProperty(value = "客户信用级别")
    private String creditLvl;
    @ApiModelProperty(value = "员工人数")
    private Integer staffNum;
    @ApiModelProperty(value = "是否上市公司")
    private String isIpo;
    @ApiModelProperty(value = "总资产额")
    private BigDecimal assets;
    @ApiModelProperty(value = "营业收额")
    private BigDecimal revenue;
    @ApiModelProperty(value = "实收资本")
    private BigDecimal capital;
    @ApiModelProperty(value = "基本账户开户行")
    private String openBankno;
    @ApiModelProperty(value = "基本账户帐号")
    private String basicAccount;
    @ApiModelProperty(value = "开户许可证号")
    private String openLicenseNo;
    @ApiModelProperty(value = "1-企业 2-同业 默认：1-企业")
    private String entType;
    @ApiModelProperty(value = "报告时点说明")
    private String rptDateCode;
    @ApiModelProperty(value = "信息修改标识 1-客户经理修改;0-原始数据")
    private String infoChangeType;
    @ApiModelProperty(value = "开户日期")
    private String openDate;
    @ApiModelProperty(value = "最近一次更新日期")
    private String lastModifyDate;
}