package io.ibs.modules.irp.ecif.etpr.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * 企业基础信息表-实际控制人段
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_ETPR_ECIF_ACTUAL_CONTROLLER")
public class TEtprEcifActualControllerEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 客户号
	*/
	@TableId
	private String cstmNo;
	/**
	* 实际控制人身份类别 1-自然人 2-组织机构
	*/
	private String actuCotrlIdType;
	/**
	* 实际控制人名称
	*/
	private String actuCotrlName;
	/**
	* 实际控制人身份标识类型
	*/
	private String actuCotrlCertIdType;
	/**
	* 实际控制人身份标识号码
	*/
	private String actuCotrlIdNum;
	/**
	* 修改日期
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updator;
}