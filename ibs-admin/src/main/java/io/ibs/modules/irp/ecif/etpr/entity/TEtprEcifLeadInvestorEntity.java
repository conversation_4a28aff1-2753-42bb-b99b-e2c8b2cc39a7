package io.ibs.modules.irp.ecif.etpr.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业基础信息表-主要出资人段
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_ETPR_ECIF_LEAD_INVESTOR")
public class TEtprEcifLeadInvestorEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 客户号
     */
    @TableId
    private String cstmNo;
    /**
     * 出资人类型
     * 10-股东 21-普通合伙人 22-有限合伙人 30-个体工商户参与经营者
     */
    private String sharhodType;
    /**
     * 出资人身份类别
     * 1-自然人 2-组织机构
     */
    private String sharhodIdType;
    /**
     * 出资人名称
     */
    private String sharhodName;
    /**
     * 出资人身份标识类型
     */
    private String sharhodCertIdType;
    /**
     * 出资人身份标识号码
     */
    private String sharhodIdNum;
    /**
     * 出资比例
     */
    private BigDecimal invRatio;
    /**
     * 修改日期
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;
}