package io.ibs.modules.irp.ecif.etpr.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * 企业基础信息表-其他标识段
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_ETPR_ECIF_OTHER_CERT")
public class TEtprEcifOtherCertEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 客户号
	*/
	@TableId
	private String cstmNo;
	/**
	* 企业身份标识类型
	*/
	private String othEntCertType;
	/**
	* 企业身份标识号码
	*/
	private String othEntCertNum;
	/**
	* 修改日期
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updator;
}