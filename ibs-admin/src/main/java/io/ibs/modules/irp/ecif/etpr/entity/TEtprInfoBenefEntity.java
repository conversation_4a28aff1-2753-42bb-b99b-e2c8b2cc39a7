package io.ibs.modules.irp.ecif.etpr.entity;

import lombok.Data;

/**
 * 企业客户受益人信息
 * 通过获取受益人信息来判断补充出资人或者实际控制人信息
 *
 * <AUTHOR>
 * @since 20220802
 */
@Data
public class TEtprInfoBenefEntity {
    /**
     * 客户号
     */
    private String cstmNo;
    /**
     * 受益人类型
     * 1 - 直接或间接拥有超过25%公司股权或表决权的自然人;2 - 通过人事、财务等其它方式对公司进行控制的自然人
     * 3 - 公司高级管理人员（总经理、副总经理、财务负责人、上市公司董事会秘书或公司章程规定的其它人员);4 - 授权办理业务人员
     */
    private String benefType;
    /**
     * 受益人名称
     */
    private String benefName;
    /**
     * 受益人证件类型
     */
    private String benefIdType;
    /**
     * 受益人证件号码
     */
    private String benefIdNo;
    /**
     * 受益人持股比例
     */
    private String benefHoldrate;
    /**
     * 受益人职位
     */
    private String benefPosition;
}
