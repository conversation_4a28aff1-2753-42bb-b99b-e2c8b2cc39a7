package io.ibs.modules.irp.ecif.etpr.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 企业基础信息表-身份标识整合记录
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class TEtprEcifCertExcel {
    @ExcelProperty(value = "客户号", index = 0)
    private String cstmNo;
    @ExcelProperty(value = "企业名称", index = 1)
    private String entName;
    @ExcelProperty(value = "企业身份标识类型", index = 2)
    private String entCertType;
    @ExcelProperty(value = "企业身份标识号码", index = 3)
    private String entCertNum;
    @ExcelProperty(value = "企业其他身份标识类型", index = 4)
    private String othEntCertType;
    @ExcelProperty(value = "企业其他身份标识号码", index = 5)
    private String othEntCertNum;
    @ExcelProperty(value = "身份标识关系有效标志", index = 6)
    private String certAssFlg;
    @ExcelProperty(value = "修改日期", index = 7)
    private Date updateTime;
    @ExcelProperty(value = "修改人", index = 8)
    private Long updator;
}