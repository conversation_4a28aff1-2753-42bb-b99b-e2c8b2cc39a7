package io.ibs.modules.irp.ecif.etpr.service;

import io.ibs.modules.irp.ecif.common.service.TEcifCommonService;
import io.ibs.modules.irp.ecif.etpr.dto.TEtprEcifMainMemberDTO;
import io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifMainMemberEntity;

/**
 * 企业基础信息表-主要成员段
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
public interface TEtprEcifMainMemberService extends TEcifCommonService<TEtprEcifMainMemberEntity, TEtprEcifMainMemberDTO> {

    TEtprEcifMainMemberDTO getByPK(String cstmNo, String mmbIdNum);

    void updateByPK(TEtprEcifMainMemberDTO dto);

    void deleteByPK(String cstmNo, String mmbIdNum);
}