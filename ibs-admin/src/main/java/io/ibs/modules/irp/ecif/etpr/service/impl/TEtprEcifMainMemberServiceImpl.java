package io.ibs.modules.irp.ecif.etpr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.ecif.common.service.impl.TEcifCommonServiceImpl;
import io.ibs.modules.irp.ecif.etpr.dao.TEtprEcifMainMemberDao;
import io.ibs.modules.irp.ecif.etpr.dto.TEtprEcifMainMemberDTO;
import io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifMainMemberEntity;
import io.ibs.modules.irp.ecif.etpr.service.TEtprEcifMainMemberService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 企业基础信息表-主要成员段
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@Service
public class TEtprEcifMainMemberServiceImpl extends TEcifCommonServiceImpl<TEtprEcifMainMemberDao, TEtprEcifMainMemberEntity, TEtprEcifMainMemberDTO> implements TEtprEcifMainMemberService {

    @Override
    public QueryWrapper<TEtprEcifMainMemberEntity> getWrapper(Map<String, Object> params){
        QueryWrapper<TEtprEcifMainMemberEntity> wrapper = new QueryWrapper<>();
        String cstmNo = (String) params.get("cstmNo");
        wrapper.eq(StringUtils.isNotBlank(cstmNo), "CSTM_NO", cstmNo);
        String mmbIdNum = (String) params.get("mmbIdNum");
        wrapper.eq(StringUtils.isNotBlank(mmbIdNum), "MMB_ID_NUM", mmbIdNum);

        return wrapper;
    }

    @Override
    public TEtprEcifMainMemberDTO getByPK(String cstmNo, String mmbIdNum) {
        TEtprEcifMainMemberEntity entity = baseDao.selectOne(new QueryWrapper<TEtprEcifMainMemberEntity>().lambda().
                eq(TEtprEcifMainMemberEntity::getCstmNo, cstmNo).
                eq(TEtprEcifMainMemberEntity::getMmbIdNum, mmbIdNum));
        return ConvertUtils.sourceToTarget(entity, super.currentDtoClass());
    }

    @Override
    public void updateByPK(TEtprEcifMainMemberDTO dto) {
        TEtprEcifMainMemberEntity entity = ConvertUtils.sourceToTarget(dto, TEtprEcifMainMemberEntity.class);
        //更新人和更新日期为空时会由自动填充策略进行填充
        entity.setUpdator(null);
        entity.setUpdateTime(null);
        baseDao.update(entity, new UpdateWrapper<TEtprEcifMainMemberEntity>().lambda().
                eq(TEtprEcifMainMemberEntity::getCstmNo, dto.getCstmNo()).
                eq(TEtprEcifMainMemberEntity::getMmbIdNum, dto.getMmbIdNum()));
    }

    @Override
    public void deleteByPK(String cstmNo, String mmbIdNum) {
        baseDao.delete(new UpdateWrapper<TEtprEcifMainMemberEntity>().lambda().
                eq(TEtprEcifMainMemberEntity::getCstmNo, cstmNo).
                eq(TEtprEcifMainMemberEntity::getMmbIdNum, mmbIdNum));
    }
}