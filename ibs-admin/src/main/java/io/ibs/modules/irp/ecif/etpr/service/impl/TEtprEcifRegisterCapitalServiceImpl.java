package io.ibs.modules.irp.ecif.etpr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.modules.irp.ecif.common.service.impl.TEcifCommonServiceImpl;
import io.ibs.modules.irp.ecif.etpr.dao.TEtprEcifRegisterCapitalDao;
import io.ibs.modules.irp.ecif.etpr.dto.TEtprEcifRegisterCapitalDTO;
import io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifRegisterCapitalEntity;
import io.ibs.modules.irp.ecif.etpr.service.TEtprEcifRegisterCapitalService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 企业基础信息表-注册资本段
 *
 * <AUTHOR> mail
 * @since 3.0 2022-07-14
 */
@Service
public class TEtprEcifRegisterCapitalServiceImpl extends TEcifCommonServiceImpl<TEtprEcifRegisterCapitalDao, TEtprEcifRegisterCapitalEntity, TEtprEcifRegisterCapitalDTO> implements TEtprEcifRegisterCapitalService {

    @Override
    public QueryWrapper<TEtprEcifRegisterCapitalEntity> getWrapper(Map<String, Object> params){
        QueryWrapper<TEtprEcifRegisterCapitalEntity> wrapper = new QueryWrapper<>();


        return wrapper;
    }


}