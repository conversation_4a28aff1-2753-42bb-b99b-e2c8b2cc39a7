package io.ibs.modules.irp.ecif.etpr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.common.utils.DateUtils;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.modules.irp.ecif.common.service.impl.TEcifCommonServiceImpl;
import io.ibs.modules.irp.ecif.etpr.dao.TEtprInfoGetDataDao;
import io.ibs.modules.irp.ecif.etpr.dto.TEtprInfoGetDataDTO;
import io.ibs.modules.irp.ecif.etpr.entity.*;
import io.ibs.modules.irp.ecif.etpr.service.*;
import io.ibs.modules.irp.ecif.etpr.util.EtprZipExch;
import io.ibs.modules.irp.etprlevel.entity.TEtprLvBsSgmtEntity;
import io.ibs.modules.irp.etprlevel.service.TEtprLvBsSgmtService;
import io.ibs.modules.irp.pbccrc2.common.xdgl.entity.Dksq;
import io.ibs.modules.irp.pbccrc2.common.xdgl.service.DksqService;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.irp.util.CstmInfoExch;
import io.ibs.modules.irp.util.FieldUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createTime 2022/7/14 16:02
 * @description
 * @Version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TEtprInfoGetDataServiceImpl extends TEcifCommonServiceImpl<TEtprInfoGetDataDao, TEtprInfoGetDataEntity, TEtprInfoGetDataDTO> implements TEtprInfoGetDataService {

    private final TEtprEcifActualControllerService actualControllerService;
    private final TEtprEcifBaseService baseService;
    private final TEtprEcifBasicProfileService basicProfileService;
    private final TEtprEcifCertService certService;
    private final TEtprEcifContactWayService contactWayService;
    private final TEtprEcifEnterprisesRelationshipService relationshipService;
    private final TEtprEcifLeadInvestorService leadInvestorService;
    private final TEtprEcifMainMemberService mainMemberService;
    private final TEtprEcifOtherCertService otherCertService;
    private final TEtprEcifRegisterCapitalService registerCapitalService;
    private final TEtprEcifSuperiorOrganizationService superiorOrganizationService;
    private final TEtprLvBsSgmtService bsSgmtService;
    private final TransactionHelper transactionHelper;
    private final DksqService dksqService;

    @Override
    public QueryWrapper<TEtprInfoGetDataEntity> getWrapper(Map<String, Object> params) {
        return null;
    }

    /**
     * 一次性全量获取企业信息中所有段的明细并插入本地数据库中
     * <p>
     * 已执行 以后不再使用
     */
    @Override
    public void getDataFromHost() {
//        List<TEtprInfoGetDataEntity> list = baseDao.getCorpInfoInFirst();
//        List<TEtprInfoBenefEntity> benefList = baseDao.getCorpMember();
//        Map<String, List<TEtprInfoBenefEntity>> benef = benefList.stream().collect(Collectors.groupingBy(TEtprInfoBenefEntity::getCstmNo));
//        /*for(String str:benef.keySet()){
//            log.info(str+":"+benef.get(str).size());
//            for(TEtprInfoBenefEntity entity:benef.get(str)){
//                log.info(entity.getCstmNo()+":"+entity.getBenefName()+":"+entity.getBenefIdNo());
//            }
//        }*/
//        List<String> unclearLoanCstmList = baseDao.getCorpCstmOfLoanUnclear();
//        List<String> basicAccList = baseDao.getCorpCstmOfBasicAcc();
//        insertByList(list, benef, unclearLoanCstmList, basicAccList, null);
    }

    /**
     * 获取新增的数据
     */
    @Override
    public void getDataOfNew(String tranDate) {
        List<TEtprInfoGetDataEntity> list;
        if (tranDate.contains("-")) {
            String bgnDate = tranDate.substring(0, tranDate.lastIndexOf("-"));
            String endDate = tranDate.substring(tranDate.lastIndexOf("-") + 1);
            list = baseDao.getDateBetweenTwoDate(bgnDate, endDate);
        } else {
            list = baseDao.getDataOfNew(tranDate);
        }

        //20230324 add by m 已径开立的客户号有新开贷款或者新开基本户的,修改基础段的资料类型
        List<Map<String, String>> LoanOrBasicAcc;
        if (tranDate.contains("-")) {
            String bgnDate = tranDate.substring(0, tranDate.lastIndexOf("-"));
            String endDate = tranDate.substring(tranDate.lastIndexOf("-") + 1);
            LoanOrBasicAcc = baseDao.getCorpCstmOfNewLoanOrBasicAcc(bgnDate, endDate);
        } else {
            LoanOrBasicAcc = baseDao.getCorpCstmOfNewLoanOrBasicAcc(tranDate, tranDate);
        }

        List<String> loanCstmList = baseDao.getCorpCstmOfLoanUnclear();
        List<String> basicAccList = baseDao.getCorpCstmOfBasicAcc();
        // 去信贷系统查询贷款申请表，获取企业中征码
        List<String> khh = LoanOrBasicAcc.stream().filter(L -> "2".equals(L.get("cstmtype"))).map(loan -> loan.get("cstmno")).collect(Collectors.toList());
        List<Dksq> dksqList = new ArrayList<>();
        if (!khh.isEmpty()) {
            dksqList = dksqService.list(new QueryWrapper<Dksq>().lambda()
                    .select(Dksq::getKhh, Dksq::getQyzzm)
                    .in(Dksq::getKhh, khh));
        }

        Map<String, List<TEtprInfoBenefEntity>> benef = new HashMap<>();
        if (list != null && !list.isEmpty()) {
            List<TEtprInfoBenefEntity> benefList = baseDao.getCorpMemberOfNew(list.stream().map(TEtprInfoGetDataEntity::getCstmNo).collect(Collectors.toList()));
            benef = benefList.stream().collect(Collectors.groupingBy(TEtprInfoBenefEntity::getCstmNo));
        }
        insertByList(list, benef, loanCstmList, basicAccList, LoanOrBasicAcc, dksqList);
    }

    /**
     * 将新增的数据插入到各个表中
     */
    private void insertByList(List<TEtprInfoGetDataEntity> list,
                              Map<String, List<TEtprInfoBenefEntity>> map,
                              List<String> loanCstmList,
                              List<String> basicAccList,
                              List<Map<String, String>> LoanOrBasicAcc,
                              List<Dksq> dksqList) {
        //进行事务管理
        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();
        //声明计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("企业信息数据拉取");
        final int[] i = {0};
        try {
            List<TEtprEcifBaseEntity> baseList = new ArrayList<>();
            List<TEtprEcifActualControllerEntity> actualList = new ArrayList<>();
            List<TEtprEcifBasicProfileEntity> basicList = new ArrayList<>(); //企业基础信息表-基本概况信息段
            List<TEtprEcifCertEntity> certList = new ArrayList<>();
            List<TEtprEcifContactWayEntity> contactList = new ArrayList<>();
            List<TEtprEcifEnterprisesRelationshipEntity> relationList = new ArrayList<>();
            List<TEtprEcifLeadInvestorEntity> leadList = new ArrayList<>();
            List<TEtprEcifMainMemberEntity> mainList = new ArrayList<>();
            List<TEtprEcifOtherCertEntity> otherList = new ArrayList<>();
            List<TEtprEcifRegisterCapitalEntity> registerList = new ArrayList<>();
            List<TEtprEcifSuperiorOrganizationEntity> superiorList = new ArrayList<>();

            List<TEtprLvBsSgmtEntity> bsSgmtEntityList = new ArrayList<>();//企业评级基础段
            // 匹配诸如：1、23、34.0、56.78 之类的非负的整数和浮点数
            String pattern = "[1-9]\\d*\\.?\\d*";
            TEtprEcifBaseEntity tEtprEcifBaseEntityExist = new TEtprEcifBaseEntity();
            list.stream().filter(ent -> !baseService.exists(tEtprEcifBaseEntityExist.setEntCertNumCrud(ent.getEntCertNum()))).forEach(ent -> {
                //基础段
                TEtprEcifBaseEntity base = new TEtprEcifBaseEntity();
                base.setCstmNo(ent.getCstmNo());
                base.setInstNo(ent.getCstmNo().substring(0, 4));
                base.setEntName(ent.getEntName());//企业名称
                base.setEntCertType("20");//企业身份标识类型 20-统一社会信用代码
                base.setEntCertNum(ent.getEntCertNum());//企业身份标识号码
                base.setCustomerType("X");//客户资料类型 X-未知
                if (basicAccList.contains(ent.getCstmNo())) {
                    base.setCustomerType("1");//客户资料类型 1-基本账户客户资料
                }
                if (loanCstmList.contains(ent.getCstmNo())) {
                    base.setCustomerType("2");//客户资料类型 2-授信客户资料
                    base.setEntCertType("10");//10-中征码
                    // 从贷款申请表获取中征码
                    for (Dksq dksq : dksqList) {
                        if (ent.getCstmNo().equals(dksq.getKhh())) {
                            base.setEntCertNum(dksq.getQyzzm());
                        }
                    }
                }

                base.setEtpsts("X");//存续状态
                base.setOrgType(CstmInfoExch.orgTypeExch(ent.getOrgType()));//组织机构类型
                base.setState(ent.getState());//经营状态
                base.setCreditLvl(ent.getCreditLvl());//客户信用级别
                base.setStaffNum(ent.getStaffNum());//员工人数
                base.setIsIpo(ent.getIsIpo());//是否上市公司
                if (ent.getBizEndDate() != null) {
                    base.setEntCertExpDate(ent.getBizEndDate());//企业身份标识到期日
                } else {
                    base.setEntCertExpDate(DateUtils.parse("2099-12-31", "yyyy-MM-dd"));
                }

                if (StringUtils.isBlank(ent.getAssets()) || !Pattern.matches(pattern, ent.getAssets())) {
                    base.setAssets(BigDecimal.valueOf(0));//总资产额
                } else {
                    base.setAssets(new BigDecimal(ent.getAssets()));
                }
                if (StringUtils.isBlank(ent.getRevenue()) || !Pattern.matches(pattern, ent.getRevenue())) {
                    base.setRevenue(BigDecimal.valueOf(0));//营业收额
                } else {
                    base.setRevenue(new BigDecimal(ent.getRevenue()));
                }
                if (StringUtils.isBlank(ent.getCapital()) || !Pattern.matches(pattern, ent.getCapital())) {
                    base.setCapital(BigDecimal.valueOf(0));
                } else {
                    base.setCapital(new BigDecimal(ent.getCapital()));//实收资本
                }
                // 3165行是中文
                if (StringUtils.isNoneBlank(ent.getBasicAccount()) && Pattern.matches(pattern, ent.getBasicAccount())) {
                    base.setBasicAccount(ent.getBasicAccount());//基本账户帐号
                }
                base.setOpenBankno(ent.getOpenBankno());//基本账户开户行
                base.setEntType("1");//1-企业 2-同业 默认：1-企业
                base.setUpdator(Constants.UPDATER_FOR_TASK);
                base.setUpdateTime(new Date());
                base.setOpenDate(StringUtils.isBlank(ent.getOpenDate()) ? "" : ent.getOpenDate().replaceAll("-", ""));
                base.setLastModifyDate(StringUtils.isBlank(ent.getLastModifyDate()) ? base.getOpenDate() : ent.getLastModifyDate().replaceAll("-", ""));
                baseList.add(base);

                //基本概况信息段
                TEtprEcifBasicProfileEntity basic = new TEtprEcifBasicProfileEntity();
                basic.setCstmNo(ent.getCstmNo());
                basic.setNationality("CHN");//国别代码
                basic.setRegAdd(FieldUtil.extract(ent.getRegAdd(), 200));//登记地址
                if (StringUtils.isNoneBlank(ent.getAdmDivOfReg()) && ent.getAdmDivOfReg().length() > 5) {
                    basic.setAdmDivOfReg(ent.getAdmDivOfReg().substring(0, 6));//登记地行政区划代码
                }

                basic.setEstablishDate(ent.getEstablishDate());//成立日期
                basic.setBizEndDate(ent.getBizEndDate());//营业许可证到期日
                basic.setBizRange("");//业务范围
                basic.setEcoIndusCate(ent.getEcoIndusCate());//行业分类代码
                basic.setEcoType(CstmInfoExch.ecoTypeExch(ent.getEcoType()));//经济类型代码
                basic.setEntScale(EtprZipExch.entScaleExchange(ent.getEntScale()));//企业规模
                basic.setUpdator(Constants.UPDATER_FOR_TASK);
                basic.setUpdateTime(new Date());
                basicList.add(basic);

                //主要成员段
                TEtprEcifMainMemberEntity main = new TEtprEcifMainMemberEntity();
                //法人必报
                main.setCstmNo(ent.getCstmNo());
                main.setMmbPstn(ent.getMmbPstn());//组成人员职位
                main.setMmbAlias(ent.getMmbAlias());//组成人员姓名
                main.setMmbIdType(CstmInfoExch.leadInvestorIDExchange(ent.getMmbIdType()));//组成人员证件类型
                if (StringUtils.isNoneBlank(ent.getMmbIdNum()) && Pattern.matches(pattern, ent.getMmbIdNum())) {
                    main.setMmbIdNum(ent.getMmbIdNum());//组成人员证件号码
                } else {
                    main.setMmbIdNum("0");
                }
                main.setUpdator(Constants.UPDATER_FOR_TASK);
                main.setUpdateTime(new Date());
                mainList.add(main);

                //注册资本段
                TEtprEcifRegisterCapitalEntity register = new TEtprEcifRegisterCapitalEntity();
                register.setCstmNo(ent.getCstmNo());
                register.setRegCapCurrency("CNY");//注册资本币种
                register.setRegCap(ent.getRegCap());//注册资本
                register.setUpdator(Constants.UPDATER_FOR_TASK);
                register.setUpdateTime(new Date());
                registerList.add(register);

                //主要出资人段
                if (map.containsKey(ent.getCstmNo())) {
                    List<TEtprInfoBenefEntity> benefList = map.get(ent.getCstmNo());
                    //有持股超过25%的个人或机构,将其看做出资人-股东
                    if (benefList.stream().anyMatch(x -> "1".equals(x.getBenefType()))) {
                        benefList.forEach(inv -> {
                            TEtprEcifLeadInvestorEntity lead = new TEtprEcifLeadInvestorEntity();
                            lead.setCstmNo(ent.getCstmNo());
                            lead.setUpdator(Constants.UPDATER_FOR_TASK);
                            lead.setUpdateTime(new Date());
                            if ("1".equals(inv.getBenefType())) {
                                log.info(inv.getCstmNo() + ":" + inv.getBenefName() + ":" + inv.getBenefIdNo());
                                lead.setSharhodType("10");//出资人类型 10-股东 21-普通合伙人 22-有限合伙人 30-个体工商户参与经营者
                                lead.setSharhodIdType("1");//出资人身份类别 1-自然人 2-组织机构
                                lead.setSharhodName(inv.getBenefName());//出资人名称
                                lead.setSharhodCertIdType(CstmInfoExch.leadInvestorIDExchange(inv.getBenefIdType()));//出资人身份标识类型
                                lead.setSharhodIdNum(inv.getBenefIdNo());//出资人身份标识号码
                                lead.setInvRatio(new BigDecimal(inv.getBenefHoldrate()));//出资比例
                                leadList.add(lead);
                            }

                        });
                    }
                }

                //实际控制人段
                //查看受益人列表中是否包含该客户
                if (map.containsKey(ent.getCstmNo())) {
                    List<TEtprInfoBenefEntity> benefList = map.get(ent.getCstmNo());
                    //有对企业进行实际控制的自然人
                    if (benefList.stream().anyMatch(x -> "2".equals(x.getBenefType()))) {
                        List<TEtprInfoBenefEntity> ctrlList = benefList.stream().filter(x -> "2".equals(x.getBenefType())).collect(Collectors.toList());
                        ctrlList.forEach(ctrl -> {
                            TEtprEcifActualControllerEntity actual = new TEtprEcifActualControllerEntity();
                            actual.setCstmNo(ent.getCstmNo());
                            actual.setUpdator(Constants.UPDATER_FOR_TASK);
                            actual.setUpdateTime(new Date());
                            actual.setActuCotrlIdType("1");//实际控制人身份类别 1-自然人 2-组织机构
                            actual.setActuCotrlName(ctrl.getBenefName());//实际控制人名称
                            actual.setActuCotrlCertIdType(CstmInfoExch.IDExchange(ctrl.getBenefIdType()));//实际控制人身份标识类型
                            actual.setActuCotrlIdNum(ctrl.getBenefIdNo());//实际控制人身份标识号码
                            actualList.add(actual);
                        });
                    }
                }

                //上级机构段 -> 抽不到
//                TEtprEcifSuperiorOrganizationEntity superior = new TEtprEcifSuperiorOrganizationEntity();
//                superior.setCstmNo(ent.getCstmNo());
//                superior.setSupOrgType(ent.getSupOrgType());
//                superior.setSupOrgName(ent.getSupOrgName());
//                superior.setSupOrgCertType(ent.getSupOrgCertType());
//                superior.setSupOrgCertNum(ent.getSupOrgCertNum());
//                superior.setUpdator(Constants.UPDATER_FOR_TASK);
//                superior.setUpdateTime(new Date());
//                superiorList.add(superior);

                //身份标识整合记录 -> 抽不到
//                TEtprEcifCertEntity cert = new TEtprEcifCertEntity();
//                cert.setCstmNo(ent.getCstmNo());
//                cert.setEntName(ent.getEntName());
//                cert.setEntCertType(String.valueOf(30));
//                cert.setEntCertNum(ent.getEntCertNum());
//                cert.setOthEntCertNum(ent.getOthEntCertNum());
//                cert.setOthEntCertType(ent.getOthEntCertType());
//                cert.setCertAssFlg(ent.getCertAssFlg());
//                cert.setUpdator(Constants.UPDATER_FOR_TASK);
//                cert.setUpdateTime(new Date());
//                certList.add(cert);

                //联系方式段 -> 抽不到
                TEtprEcifContactWayEntity contact = new TEtprEcifContactWayEntity();
                contact.setCstmNo(ent.getCstmNo());
                contact.setConAddDistrictCode(ent.getConAddDistrictCode());
                contact.setConAdd(ent.getConAdd());
                contact.setConPhone(ent.getConPhone());
                // contact.setFinConPhone(ent.getFinConPhone());
                contact.setUpdator(Constants.UPDATER_FOR_TASK);
                contact.setUpdateTime(new Date());
                contactList.add(contact);

                //企业间关联关系记录 -> 抽不到
//                TEtprEcifEnterprisesRelationshipEntity relation = new TEtprEcifEnterprisesRelationshipEntity();
//                relation.setCstmNo(ent.getCstmNo());
//                relation.setEntName(ent.getEntName());
//                relation.setEntCertType(ent.getEntCertType());
//                relation.setEntCertNum(ent.getEntCertNum());
//                relation.setAssoEntName(ent.getAssoEntName());
//                relation.setAssoEntCertType(ent.getAssoEntCertType());
//                relation.setAssoEntCertNum(ent.getAssoEntCertNum());
//                relation.setAssoType(ent.getAssoType());
//                relation.setAssoSign(ent.getAssoSign());
//                relation.setUpdator(Constants.UPDATER_FOR_TASK);
//                relation.setUpdateTime(new Date());
//                relationList.add(relation);

                //其他标识段 -> 抽不到
//                TEtprEcifOtherCertEntity other = new TEtprEcifOtherCertEntity();
//                other.setCstmNo(ent.getCstmNo());
//                other.setOthEntCertType(ent.getOthEntCertType());
//                other.setOthEntCertNum(ent.getOthEntCertNum());
//                other.setUpdator(Constants.UPDATER_FOR_TASK);
//                other.setUpdateTime(new Date());
//                otherList.add(other);

                //每1000条插入一次
                if (++i[0] % 1000 == 0) {
                    baseService.insertBatch(baseList);
                    baseList.clear();
                    actualControllerService.insertBatch(actualList);
                    actualList.clear();
                    basicProfileService.insertBatch(basicList);
                    basicList.clear();
                    certService.insertBatch(certList);
                    certList.clear();
                    contactWayService.insertBatch(contactList);
                    contactList.clear();
                    relationshipService.insertBatch(relationList);
                    relationList.clear();
                    leadInvestorService.insertBatch(leadList);
                    leadList.clear();
                    mainMemberService.insertBatch(mainList);
                    mainList.clear();
                    otherCertService.insertBatch(otherList);
                    otherList.clear();
                    registerCapitalService.insertBatch(registerList);
                    registerList.clear();
                    superiorOrganizationService.insertBatch(superiorList);
                    superiorList.clear();
                    bsSgmtService.insertBatch(bsSgmtEntityList);
                    bsSgmtEntityList.clear();
                }
            });
            //剩下不足1000笔的再插入一次
            if (baseList.size() > 0) {
                baseService.insertBatch(baseList);
                baseList.clear();
            }
            if (actualList.size() > 0) {
                actualControllerService.insertBatch(actualList);
                actualList.clear();
            }
            if (basicList.size() > 0) {
                basicProfileService.insertBatch(basicList);
                basicList.clear();
            }
            if (certList.size() > 0) {
                certService.insertBatch(certList);
                certList.clear();
            }
            if (contactList.size() > 0) {
                contactWayService.insertBatch(contactList);
                contactList.clear();
            }
            if (relationList.size() > 0) {
                relationshipService.insertBatch(relationList);
                relationList.clear();
            }
            if (leadList.size() > 0) {
                leadInvestorService.insertBatch(leadList);
                leadList.clear();
            }
            if (mainList.size() > 0) {
                mainMemberService.insertBatch(mainList);
                mainList.clear();
            }
            if (otherList.size() > 0) {
                otherCertService.insertBatch(otherList);
                otherList.clear();
            }
            if (registerList.size() > 0) {
                registerCapitalService.insertBatch(registerList);
                registerList.clear();
            }
            if (superiorList.size() > 0) {
                superiorOrganizationService.insertBatch(superiorList);
                superiorList.clear();
            }
            if (bsSgmtEntityList.size() > 0) {
                bsSgmtService.insertBatch(bsSgmtEntityList);
                bsSgmtEntityList.clear();
            }

            // 存量账户，如果是最近发生借贷开户或者基本户开户，则将客户资料类型进行更新成对应类型
            if (LoanOrBasicAcc != null && LoanOrBasicAcc.size() > 0) {
                List<String> loanList = LoanOrBasicAcc.stream().filter(L -> "2".equals(L.get("cstmtype"))).map(loan -> loan.get("cstmno")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(loanList)) {
                    baseService.updateLoanCustomerType(loanList);
                    if (!CollectionUtils.isEmpty(dksqList)) {
                        for (Dksq dksq : dksqList) {
                            TEtprEcifBaseEntity etprEcifBase = new TEtprEcifBaseEntity();
                            etprEcifBase.setEntCertType("10"); // 10-中征码
                            etprEcifBase.setEntCertNum(dksq.getQyzzm());
                            baseService.update(etprEcifBase, new UpdateWrapper<TEtprEcifBaseEntity>().lambda()
                                    .ne(TEtprEcifBaseEntity::getEntCertType, "10")
                                    .eq(TEtprEcifBaseEntity::getCstmNo, dksq.getKhh()));
                        }
                    }
                }
                List<String> basicacc = LoanOrBasicAcc.stream().filter(L -> "1".equals(L.get("cstmtype"))).map(loan -> loan.get("cstmno")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(basicacc)) {
                    baseService.updateBasicAccCustomerType(basicacc);
                }
            }
            transactionHelper.commit(tm);
        } catch (Exception e) {
            log.error("一次性全量获取企业信息中所有段的明细并插入本地数据库时捕获到异常", e);
            transactionHelper.rollback(tm);
        } finally {
            // 计时停止
            stopWatch.stop();
            // 输出显示计时器各种
            log.info("******--拉取数据统计--******");
            log.info("----总共记录数：" + Arrays.toString(i));
            log.info("----总耗时: " + stopWatch.getTotalTimeSeconds() / 60 + "(分钟)");
            log.info("----LastTaskName: " + stopWatch.getLastTaskName());
            log.info("----TotalTimeSeconds: " + stopWatch.getTotalTimeSeconds());
        }
    }
}
