package io.ibs.modules.irp.ecif.prsn.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.ecif.prsn.dto.TPrsnEcifMailAddressDTO;
import io.ibs.modules.irp.ecif.prsn.excel.TPrsnEcifMailAddressExcel;
import io.ibs.modules.irp.ecif.prsn.service.TPrsnEcifMailAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 个人客户信息表-通讯地址段
 *
 * <AUTHOR>
 * @since 3.0 2022-07-07
 */
@RestController
@RequestMapping("ecif/tprsnecifmailaddress")
@Api(tags = "个人客户信息表-通讯地址段")
public class TPrsnEcifMailAddressController {
    @Autowired
    private TPrsnEcifMailAddressService tPrsnEcifMailAddressService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("ecif:tprsnecifmailaddress:page")
    public Result<PageData<TPrsnEcifMailAddressDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<TPrsnEcifMailAddressDTO> page = tPrsnEcifMailAddressService.page(params);

        return new Result<PageData<TPrsnEcifMailAddressDTO>>().ok(page);
    }

    @GetMapping("{cstmNo}")
    @ApiOperation("信息")
    @RequiresPermissions("ecif:tprsnecifmailaddress:info")
    public Result<TPrsnEcifMailAddressDTO> get(@PathVariable("cstmNo") String cstmNo) {
        TPrsnEcifMailAddressDTO data = tPrsnEcifMailAddressService.getByCstmNo(cstmNo);
        if (data == null) {
            //查询为空时返回一条空数据
            data = new TPrsnEcifMailAddressDTO();
            data.setCstmNo(cstmNo);
        }
        return new Result<TPrsnEcifMailAddressDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("ecif:tprsnecifmailaddress:save")
    public Result save(@RequestBody TPrsnEcifMailAddressDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        tPrsnEcifMailAddressService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("ecif:tprsnecifmailaddress:update")
    public Result update(@RequestBody TPrsnEcifMailAddressDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        tPrsnEcifMailAddressService.updateByCstmNo(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("ecif:tprsnecifmailaddress:delete")
    public Result delete(@RequestBody String[] cstmNo) {
        //效验数据
        AssertUtils.isArrayEmpty(cstmNo, "cstmNo");

        tPrsnEcifMailAddressService.deleteByCstmNo(cstmNo);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("ecif:tprsnecifmailaddress:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TPrsnEcifMailAddressDTO> list = tPrsnEcifMailAddressService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "个人客户信息表-通讯地址段", list, TPrsnEcifMailAddressExcel.class);
    }

}