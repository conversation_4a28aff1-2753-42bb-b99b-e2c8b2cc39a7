package io.ibs.modules.irp.ecif.prsn.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.ecif.prsn.dto.TPrsnEcifOccupationDTO;
import io.ibs.modules.irp.ecif.prsn.excel.TPrsnEcifOccupationExcel;
import io.ibs.modules.irp.ecif.prsn.service.TPrsnEcifOccupationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 个人客户信息表-职业信息段
 *
 * <AUTHOR>
 * @since 3.0 2022-07-07
 */
@RestController
@RequestMapping("ecif/tprsnecifoccupation")
@Api(tags = "个人客户信息表-职业信息段")
public class TPrsnEcifOccupationController {
    @Autowired
    private TPrsnEcifOccupationService tPrsnEcifOccupationService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("ecif:tprsnecifoccupation:page")
    public Result<PageData<TPrsnEcifOccupationDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<TPrsnEcifOccupationDTO> page = tPrsnEcifOccupationService.page(params);

        return new Result<PageData<TPrsnEcifOccupationDTO>>().ok(page);
    }

    @GetMapping("{cstmNo}")
    @ApiOperation("信息")
    @RequiresPermissions("ecif:tprsnecifoccupation:info")
    public Result<TPrsnEcifOccupationDTO> get(@PathVariable("cstmNo") String cstmNo) {
        TPrsnEcifOccupationDTO data = tPrsnEcifOccupationService.getByCstmNo(cstmNo);
        if (data == null) {
            //查询为空时返回一条空数据
            data = new TPrsnEcifOccupationDTO();
            data.setCstmNo(cstmNo);
        }
        return new Result<TPrsnEcifOccupationDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("ecif:tprsnecifoccupation:save")
    public Result save(@RequestBody TPrsnEcifOccupationDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        tPrsnEcifOccupationService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("ecif:tprsnecifoccupation:update")
    public Result update(@RequestBody TPrsnEcifOccupationDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        tPrsnEcifOccupationService.updateByCstmNo(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("ecif:tprsnecifoccupation:delete")
    public Result delete(@RequestBody String[] cstmNo) {
        //效验数据
        AssertUtils.isArrayEmpty(cstmNo, "cstmNo");

        tPrsnEcifOccupationService.deleteByCstmNo(cstmNo);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("ecif:tprsnecifoccupation:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TPrsnEcifOccupationDTO> list = tPrsnEcifOccupationService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "个人客户信息表-职业信息段", list, TPrsnEcifOccupationExcel.class);
    }

}