package io.ibs.modules.irp.ecif.prsn.dto;

import io.ibs.modules.irp.ecif.common.dto.EcifBaseDTO;
import io.ibs.modules.irp.util.FieldUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 个人客户信息表-婚姻信息段
 *
 * <AUTHOR>
 * @since 3.0 2022-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "个人客户信息表-婚姻信息段")
public class TPrsnEcifMarriageDTO extends EcifBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "婚姻状况")
    private String mariStatus;
    @ApiModelProperty(value = "配偶姓名")
    private String spoName;
    public String getSpoName() {
        return FieldUtil.getSpoName(spoName);
    }

    public void setSpoName(String spoName) {
        this.spoName = FieldUtil.getSpoName(spoName);
    }
    @ApiModelProperty(value = "配偶证件类型")
    private String spoIdType;
    @ApiModelProperty(value = "配偶证件号码")
    private String spoIdNum;
    @ApiModelProperty(value = "配偶联系电话")
    private String spoTel;

    public String getSpsCmpyNm() {
        return FieldUtil.getAddress(spsCmpyNm);
    }

    public void setSpsCmpyNm(String spsCmpyNm) {
        this.spsCmpyNm = FieldUtil.getAddress(spsCmpyNm);
    }

    @ApiModelProperty(value = "配偶工作单位")
    private String spsCmpyNm;

}