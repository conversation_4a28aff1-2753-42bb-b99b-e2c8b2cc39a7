package io.ibs.modules.irp.ecif.prsn.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * 个人客户信息表-通讯地址段
 *
 * <AUTHOR> 
 * @since 3.0 2022-07-07
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_PRSN_ECIF_MAIL_ADDRESS")
public class TPrsnEcifMailAddressEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 客户号
	*/
	@TableId
	private String cstmNo;
	/**
	* 通讯地址
	*/
	private String mailAddr;
	/**
	* 通讯地邮编
	*/
	private String mailPc;
	/**
	* 通讯地行政区划
	*/
	private String mailDist;
	/**
	* 修改日期
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updator;
}