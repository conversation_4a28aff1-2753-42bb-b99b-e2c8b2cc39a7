package io.ibs.modules.irp.ecif.prsn.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * 个人客户信息表-职业信息段
 *
 * <AUTHOR> 
 * @since 3.0 2022-07-07
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("T_PRSN_ECIF_OCCUPATION")
public class TPrsnEcifOccupationEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 客户号
	*/
	@TableId
	private String cstmNo;
	/**
	* 就业状况 11-国家公务员13-专业技术人员17-职员21-企业管理人员24-工人27-农民31-学生37-现役军人51-自由职业者54-个体经营者70-无业人员8090-其他对于无法具体对应至上述编码的在职人员，填写“91-在职”；对于就业状况法获知的，填写“99-未知”
	*/
	private String empStatus;
	/**
	* 单位名称
	*/
	private String cpnname;
	/**
	* 单位性质 10-机关、事业单位20-国有企业3040-个体、私营企业50-其他（包括三资企业、民营企业、民间团体等）  对于单位性质无法获知的，填写“99-未知”。
	*/
	private String cpnType;
	/**
	* 单位所属行业 A-农、林、牧、渔业B-采矿业C-制造业D-E-建筑业G-交通运输、仓储和邮储业H-住宿和餐饮业I-信息传输、软件和信息技术服务J-金融业K-房地产业L-租赁和商务服务业M-科学研究和技术服务业N-水利、环境和公共设施管理业O-居民服务、修理和其他服务业P-教育Q-卫生和社会工作R-文化、体育和娱乐业S-公共>管理、社会保障和社会组织T-国际组织对于无法获知的情况，填写“9-未知”，不再采用一代的“Z-未知”。
	*/
	private String industry;
	/**
	* 单位详细地址
	*/
	private String cpnAddr;
	/**
	* 单位所在地邮编
	*/
	private String cpnPc;
	/**
	* 单位所在地行政区划
	*/
	private String cpnDist;
	/**
	* 单位电话
	*/
	private String cpnTel;
	/**
	* 职业
	*/
	private String occupation;
	/**
	* 职务
	*/
	private String title;
	/**
	* 职称
	*/
	private String techTitle;
	/**
	* 本单位工作起始年份
	*/
	private String workStartDate;
	/**
	* 修改日期
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updator;
}