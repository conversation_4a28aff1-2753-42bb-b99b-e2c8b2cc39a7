package io.ibs.modules.irp.ecif.prsn.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 个人客户信息表-婚姻信息段
 *
 * <AUTHOR> 
 * @since 3.0 2022-07-07
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class TPrsnEcifMarriageExcel {
    @ExcelProperty(value = "客户号", index = 0)
    private String cstmNo;
    @ExcelProperty(value = "婚姻状况", index = 1)
    private String mariStatus;
    @ExcelProperty(value = "配偶姓名", index = 2)
    private String spoName;
    @ExcelProperty(value = "配偶证件类型", index = 3)
    private String spoIdType;
    @ExcelProperty(value = "配偶证件号码", index = 4)
    private String spoIdNum;
    @ExcelProperty(value = "配偶联系电话", index = 5)
    private String spoTel;
    @ExcelProperty(value = "配偶工作单位", index = 6)
    private String spsCmpyNm;
    @ExcelProperty(value = "修改日期", index = 7)
    private Date updateTime;
    @ExcelProperty(value = "修改人", index = 8)
    private Integer updator;
}