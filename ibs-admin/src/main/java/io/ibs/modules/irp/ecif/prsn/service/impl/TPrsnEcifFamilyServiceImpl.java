package io.ibs.modules.irp.ecif.prsn.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.ecif.common.service.impl.TEcifCommonServiceImpl;
import io.ibs.modules.irp.ecif.prsn.dao.TPrsnEcifFamilyDao;
import io.ibs.modules.irp.ecif.prsn.dto.TPrsnEcifFamilyDTO;
import io.ibs.modules.irp.ecif.prsn.entity.TPrsnEcifFamilyEntity;
import io.ibs.modules.irp.ecif.prsn.service.TPrsnEcifFamilyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 个人客户信息表-家族关系信息记录
 *
 * <AUTHOR>
 * @since 3.0 2022-07-07
 */
@Service
public class TPrsnEcifFamilyServiceImpl extends TEcifCommonServiceImpl<TPrsnEcifFamilyDao, TPrsnEcifFamilyEntity, TPrsnEcifFamilyDTO> implements TPrsnEcifFamilyService {

    @Override
    public QueryWrapper<TPrsnEcifFamilyEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<TPrsnEcifFamilyEntity> wrapper = new QueryWrapper<>();
        String cstmNo = (String) params.get("cstmNo");
        wrapper.eq(StringUtils.isNotBlank(cstmNo), "CSTM_NO", cstmNo);
        String famMemCertNum = (String) params.get("famMemCertNum");
        wrapper.eq(StringUtils.isNotBlank(famMemCertNum), "FAM_MEM_CERT_NUM", famMemCertNum);

        return wrapper;
    }

    @Override
    public TPrsnEcifFamilyDTO getByPK(String cstmNo, String famMemCertNum) {
        TPrsnEcifFamilyEntity entity = baseDao.selectOne(new QueryWrapper<TPrsnEcifFamilyEntity>().lambda().
                eq(TPrsnEcifFamilyEntity::getCstmNo, cstmNo).
                eq(TPrsnEcifFamilyEntity::getFamMemCertNum, famMemCertNum));
        return ConvertUtils.sourceToTarget(entity, super.currentDtoClass());
    }

    @Override
    public void updateByPK(TPrsnEcifFamilyDTO dto) {
        TPrsnEcifFamilyEntity entity = ConvertUtils.sourceToTarget(dto, TPrsnEcifFamilyEntity.class);
        //更新人和更新日期为空时会由自动填充策略进行填充
        entity.setUpdator(null);
        entity.setUpdateTime(null);
        baseDao.update(entity, new UpdateWrapper<TPrsnEcifFamilyEntity>().lambda().
                eq(TPrsnEcifFamilyEntity::getCstmNo, dto.getCstmNo()).
                eq(TPrsnEcifFamilyEntity::getFamMemCertNum, dto.getFamMemCertNum()));
    }

    @Override
    public void deleteByPK(String cstmNo, String famMemCertNum) {
        baseDao.delete(new UpdateWrapper<TPrsnEcifFamilyEntity>().lambda().
                eq(TPrsnEcifFamilyEntity::getCstmNo, cstmNo).
                eq(TPrsnEcifFamilyEntity::getFamMemCertNum, famMemCertNum));
    }
}