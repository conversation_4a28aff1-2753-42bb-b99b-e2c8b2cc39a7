package io.ibs.modules.irp.ecif.prsn.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.modules.irp.ecif.common.service.impl.TEcifCommonServiceImpl;
import io.ibs.modules.irp.ecif.prsn.dao.TPrsnEcifIncomeDao;
import io.ibs.modules.irp.ecif.prsn.dto.TPrsnEcifIncomeDTO;
import io.ibs.modules.irp.ecif.prsn.entity.TPrsnEcifIncomeEntity;
import io.ibs.modules.irp.ecif.prsn.service.TPrsnEcifIncomeService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 个人客户信息表-收入信息段
 *
 * <AUTHOR> 
 * @since 3.0 2022-07-07
 */
@Service
public class TPrsnEcifIncomeServiceImpl extends TEcifCommonServiceImpl<TPrsnEcifIncomeDao, TPrsnEcifIncomeEntity, TPrsnEcifIncomeDTO> implements TPrsnEcifIncomeService {

    @Override
    public QueryWrapper<TPrsnEcifIncomeEntity> getWrapper(Map<String, Object> params){
        QueryWrapper<TPrsnEcifIncomeEntity> wrapper = new QueryWrapper<>();


        return wrapper;
    }

}