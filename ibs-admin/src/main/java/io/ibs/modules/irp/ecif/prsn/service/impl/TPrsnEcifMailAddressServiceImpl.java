package io.ibs.modules.irp.ecif.prsn.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.modules.irp.ecif.common.service.impl.TEcifCommonServiceImpl;
import io.ibs.modules.irp.ecif.prsn.dao.TPrsnEcifMailAddressDao;
import io.ibs.modules.irp.ecif.prsn.dto.TPrsnEcifMailAddressDTO;
import io.ibs.modules.irp.ecif.prsn.entity.TPrsnEcifMailAddressEntity;
import io.ibs.modules.irp.ecif.prsn.service.TPrsnEcifMailAddressService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 个人客户信息表-通讯地址段
 *
 * <AUTHOR> 
 * @since 3.0 2022-07-07
 */
@Service
public class TPrsnEcifMailAddressServiceImpl extends TEcifCommonServiceImpl<TPrsnEcifMailAddressDao, TPrsnEcifMailAddressEntity, TPrsnEcifMailAddressDTO> implements TPrsnEcifMailAddressService {

    @Override
    public QueryWrapper<TPrsnEcifMailAddressEntity> getWrapper(Map<String, Object> params){
        QueryWrapper<TPrsnEcifMailAddressEntity> wrapper = new QueryWrapper<>();


        return wrapper;
    }

}