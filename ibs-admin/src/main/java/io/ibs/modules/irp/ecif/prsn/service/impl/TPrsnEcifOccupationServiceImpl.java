package io.ibs.modules.irp.ecif.prsn.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.modules.irp.ecif.common.service.impl.TEcifCommonServiceImpl;
import io.ibs.modules.irp.ecif.prsn.dao.TPrsnEcifOccupationDao;
import io.ibs.modules.irp.ecif.prsn.dto.TPrsnEcifOccupationDTO;
import io.ibs.modules.irp.ecif.prsn.entity.TPrsnEcifOccupationEntity;
import io.ibs.modules.irp.ecif.prsn.service.TPrsnEcifOccupationService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 个人客户信息表-职业信息段
 *
 * <AUTHOR> 
 * @since 3.0 2022-07-07
 */
@Service
public class TPrsnEcifOccupationServiceImpl extends TEcifCommonServiceImpl<TPrsnEcifOccupationDao, TPrsnEcifOccupationEntity, TPrsnEcifOccupationDTO> implements TPrsnEcifOccupationService {

    @Override
    public QueryWrapper<TPrsnEcifOccupationEntity> getWrapper(Map<String, Object> params){
        QueryWrapper<TPrsnEcifOccupationEntity> wrapper = new QueryWrapper<>();


        return wrapper;
    }

}