package io.ibs.modules.irp.ecif.prsn.task;

import io.ibs.modules.irp.ecif.prsn.service.TPrsnInfoGetDataService;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ECIF
 * <p>
 * 核心系统 → ECIF系统
 * <p>
 * 从核心系统获取个人客户信息定时任务
 * <p>
 * Created by AileYoung on 2022/7/29.
 */
@Service
@Slf4j
public class PrsnInfoGetFromHostTask extends BaseTask {
    private TPrsnInfoGetDataService getDataSvc;

    @Autowired
    public PrsnInfoGetFromHostTask(TPrsnInfoGetDataService getDataSvc) {
        super("ECIF-从核心系统获取个人客户信息定时任务", PrsnInfoGetFromHostTask.class);
        this.getDataSvc = getDataSvc;
    }

    @Override
    public void execute(String params) {
        //参数为today时，tranDate为当前系统日期，否则为传入的日期
        String tranDate = super.checkDateParam(params, "yyyyMMdd");
        //当参数格式为yyyyMMdd-yyyyMMdd 某段时间内
        if (tranDate.contains("-")) {
            String bgnDate = tranDate.substring(0, tranDate.lastIndexOf("-"));
            String endDate = tranDate.substring(tranDate.lastIndexOf("-") + 1);
            getDataSvc.getDataBetweenTwoDate(bgnDate, endDate);
        } else {
            getDataSvc.getDataBetweenTwoDate(tranDate, tranDate);
        }
    }
}
