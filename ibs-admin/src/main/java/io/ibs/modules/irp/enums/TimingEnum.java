package io.ibs.modules.irp.enums;

import java.util.Arrays;

/**
 * 时点枚举
 */
public enum TimingEnum {
    OPEN("10"),//新开户/首次报送
    CLOSE("20"),//账户关闭
    AGREED_LOAN_REPAY("32"),//约定还款日结算（不含账户在约定还款日关闭）
    ACTUAL_LOAN_REPAY("33"),//33-实际还款（特指在非约定还款日还款）
    FIVE_LEVEL_ADJUST("41"),//41-五级分类调整
    EXTEND("42"),//42-展期发生
    OTHER("49");//49-其他情况

    private String number;

    TimingEnum(String number) {
        this.number = number;
    }

    public String getNumber() {
        return number;
    }

    public static boolean inTimings(TimingEnum[] timings, String timing) {
        return Arrays.stream(timings).anyMatch(item -> timing.equals(item.getNumber()));
    }
}
