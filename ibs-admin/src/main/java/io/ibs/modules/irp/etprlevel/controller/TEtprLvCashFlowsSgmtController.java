package io.ibs.modules.irp.etprlevel.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.etprlevel.dto.TEtprLvCashFlowsSgmtDTO;
import io.ibs.modules.irp.etprlevel.excel.TEtprLvCashFlowsSgmtExcel;
import io.ibs.modules.irp.etprlevel.service.TEtprLvCashFlowsSgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 企业评级-现金流量表段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@RestController
@RequestMapping("etprlevel/cashflowssgmt")
@Api(tags = "企业评级-现金流量表段")
public class TEtprLvCashFlowsSgmtController {
    @Autowired
    private TEtprLvCashFlowsSgmtService tEtprLvCashFlowsSgmtService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("etprlevel:cashflowssgmt:page")
    public Result<PageData<TEtprLvCashFlowsSgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<TEtprLvCashFlowsSgmtDTO> page = tEtprLvCashFlowsSgmtService.page(params);

        return new Result<PageData<TEtprLvCashFlowsSgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("etprlevel:cashflowssgmt:info")
    public Result<TEtprLvCashFlowsSgmtDTO> get(@PathVariable("id") Long id) {
        TEtprLvCashFlowsSgmtDTO data = tEtprLvCashFlowsSgmtService.get(id);

        return new Result<TEtprLvCashFlowsSgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("etprlevel:cashflowssgmt:save")
    public Result save(@RequestBody TEtprLvCashFlowsSgmtDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        tEtprLvCashFlowsSgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("etprlevel:cashflowssgmt:update")
    public Result update(@RequestBody TEtprLvCashFlowsSgmtDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        tEtprLvCashFlowsSgmtService.updateByCstmNo(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("etprlevel:cashflowssgmt:delete")
    public Result delete(@RequestBody String[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        tEtprLvCashFlowsSgmtService.deleteByCstmNo(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("etprlevel:cashflowssgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TEtprLvCashFlowsSgmtDTO> list = tEtprLvCashFlowsSgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "企业评级-现金流量表段", list, TEtprLvCashFlowsSgmtExcel.class);
    }

}