package io.ibs.modules.irp.etprlevel.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.etprlevel.dto.TEtprLvIncomeStatementSgmtDTO;
import io.ibs.modules.irp.etprlevel.excel.TEtprLvIncomeStatementSgmtExcel;
import io.ibs.modules.irp.etprlevel.service.TEtprLvIncomeStatementSgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 企业评级-利润表段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@RestController
@RequestMapping("etprlevel/incomestatementsgmt")
@Api(tags = "企业评级-利润表段")
public class TEtprLvIncomeStatementSgmtController {
    @Autowired
    private TEtprLvIncomeStatementSgmtService tEtprLvIncomeStatementSgmtService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("etprlevel:incomestatementsgmt:page")
    public Result<PageData<TEtprLvIncomeStatementSgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<TEtprLvIncomeStatementSgmtDTO> page = tEtprLvIncomeStatementSgmtService.page(params);

        return new Result<PageData<TEtprLvIncomeStatementSgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("etprlevel:incomestatementsgmt:info")
    public Result<TEtprLvIncomeStatementSgmtDTO> get(@PathVariable("id") Long id) {
        TEtprLvIncomeStatementSgmtDTO data = tEtprLvIncomeStatementSgmtService.get(id);

        return new Result<TEtprLvIncomeStatementSgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("etprlevel:incomestatementsgmt:save")
    public Result save(@RequestBody TEtprLvIncomeStatementSgmtDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        tEtprLvIncomeStatementSgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("etprlevel:incomestatementsgmt:update")
    public Result update(@RequestBody TEtprLvIncomeStatementSgmtDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        tEtprLvIncomeStatementSgmtService.updateByCstmNo(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("etprlevel:incomestatementsgmt:delete")
    public Result delete(@RequestBody String[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        tEtprLvIncomeStatementSgmtService.deleteByCstmNo(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("etprlevel:incomestatementsgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TEtprLvIncomeStatementSgmtDTO> list = tEtprLvIncomeStatementSgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "企业评级-利润表段", list, TEtprLvIncomeStatementSgmtExcel.class);
    }

}