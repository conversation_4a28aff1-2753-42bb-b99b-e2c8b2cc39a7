package io.ibs.modules.irp.etprlevel.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.etprlevel.dto.TEtprLvMnShaHodInfSgmtDTO;
import io.ibs.modules.irp.etprlevel.excel.TEtprLvMnShaHodInfSgmtExcel;
import io.ibs.modules.irp.etprlevel.service.TEtprLvMnShaHodInfSgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 企业评级-注册资本及主要出资人段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@RestController
@RequestMapping("etprlevel/mnshahodinfsgmt")
@Api(tags = "企业评级-注册资本及主要出资人段")
public class TEtprLvMnShaHodInfSgmtController {
    @Autowired
    private TEtprLvMnShaHodInfSgmtService tEtprLvMnShaHodInfSgmtService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("etprlevel:mnshahodinfsgmt:page")
    public Result<PageData<TEtprLvMnShaHodInfSgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<TEtprLvMnShaHodInfSgmtDTO> page = tEtprLvMnShaHodInfSgmtService.page(params);

        return new Result<PageData<TEtprLvMnShaHodInfSgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("etprlevel:mnshahodinfsgmt:info")
    public Result<TEtprLvMnShaHodInfSgmtDTO> get(@PathVariable("id") Long id) {
        TEtprLvMnShaHodInfSgmtDTO data = tEtprLvMnShaHodInfSgmtService.get(id);

        return new Result<TEtprLvMnShaHodInfSgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("etprlevel:mnshahodinfsgmt:save")
    public Result save(@RequestBody TEtprLvMnShaHodInfSgmtDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        tEtprLvMnShaHodInfSgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("etprlevel:mnshahodinfsgmt:update")
    public Result update(@RequestBody TEtprLvMnShaHodInfSgmtDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        tEtprLvMnShaHodInfSgmtService.updateByCstmNo(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("etprlevel:mnshahodinfsgmt:delete")
    public Result delete(@RequestBody String[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        tEtprLvMnShaHodInfSgmtService.deleteByCstmNo(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("etprlevel:mnshahodinfsgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<TEtprLvMnShaHodInfSgmtDTO> list = tEtprLvMnShaHodInfSgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "企业评级-注册资本及主要出资人段", list, TEtprLvMnShaHodInfSgmtExcel.class);
    }

}