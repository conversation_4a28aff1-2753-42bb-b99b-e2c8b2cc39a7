package io.ibs.modules.irp.etprlevel.dao;

import io.ibs.common.dao.BaseDao;
import io.ibs.modules.irp.etprlevel.dto.TEtprLvMnShaHodInfSgmtDetailDTO;
import io.ibs.modules.irp.etprlevel.entity.TEtprLvMnShaHodInfSgmtDetailEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 企业评级-注册资本及主要出资人段明细
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@Mapper
public interface TEtprLvMnShaHodInfSgmtDetailDao extends BaseDao<TEtprLvMnShaHodInfSgmtDetailEntity> {
    /**
     * 合并主要出资人明细段
     *
     * @param list
     */
    void mergeRecord(List<TEtprLvMnShaHodInfSgmtDetailDTO> list);
}