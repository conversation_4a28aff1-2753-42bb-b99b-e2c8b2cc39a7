package io.ibs.modules.irp.etprlevel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 企业评级-基本概况段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@ApiModel(value = "企业评级-基本概况段")
public class TEtprLvFcsInfSgmtDTO extends EtprLvBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "登记地址")
    private String regAdd;
    @ApiModelProperty(value = "登记地行政区划代码")
    private String admDivOfReg;
    @ApiModelProperty(value = "成立日期")
    private String establishDate;
    @ApiModelProperty(value = "业务范围")
    private String bizRange;
    @ApiModelProperty(value = "主营业务来源")
    private String mainBusSou;
    @ApiModelProperty(value = "行业分类代码")
    private String ecoIndusCate;
    @ApiModelProperty(value = "经济类型代码")
    private String ecoType;
    @ApiModelProperty(value = "金融机构内评体系描述")
    private String interRatingRes;
    @ApiModelProperty(value = "主营业务变更情况")
    private String mainBusChan;
    @ApiModelProperty(value = "法定代表人信贷资产支持")
    private String creAssSup;
    @ApiModelProperty(value = "近一年主要人员是否发生变动")
    private String perChaSit;
    @ApiModelProperty(value = "近三年的欠税条数")
    private Integer defaTaxAmount;
    @ApiModelProperty(value = "经营地产权情况")
    private String housEquities;
    @ApiModelProperty(value = "从业人员数量")
    private Integer numOfEmployees;
    @ApiModelProperty(value = "是否县域企业")
    private String isInCounty;
    @ApiModelProperty(value = "是否涉农企业")
    private String hasIncFromAgri;
    @ApiModelProperty(value = "董事会成员（执行董事）数量")
    private Integer direcortNum;
    @ApiModelProperty(value = "监事会成员（监事）数量")
    private Integer supervisorNum;

}