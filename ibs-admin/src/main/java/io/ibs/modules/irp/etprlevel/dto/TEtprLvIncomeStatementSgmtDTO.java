package io.ibs.modules.irp.etprlevel.dto;

import io.ibs.modules.irp.util.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业评级-利润表段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@ApiModel(value = "企业评级-利润表段")
public class TEtprLvIncomeStatementSgmtDTO extends EtprLvBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "一、营业收入")
    private BigDecimal revenueOfSales = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "减：营业成本")
    private BigDecimal costOfSales = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "税金及附加")
    private BigDecimal businessAndOtherTaxes = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "销售费用")
    private BigDecimal sellingExpenses = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "管理费用")
    private BigDecimal generalAndAdministrativeExpenses = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "研发费用")
    private BigDecimal rdExpenses = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "财务费用")
    private BigDecimal financialExpense = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "其中：利息费用")
    private BigDecimal interestFeeOfFinancialExpense = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "利息收入")
    private BigDecimal interestIncomeOfFinancialExpense = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "加：其他收益")
    private BigDecimal otherIncome = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "投资收益（损失以“-”号填列）")
    private BigDecimal investmentIncome = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "净敞口套期收益（损失以“-”号填列）")
    private BigDecimal netExposureHedgingIncome = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "公允价值变动收益（损失以“-”号填列）")
    private BigDecimal incomeFromChangesInFairValue = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "信用减值损失（损失以“-”号填列）")
    private BigDecimal creditImpairmentLoss = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "资产减值损失（损失以“-”号填列）")
    private BigDecimal impairmentLossOfAssets = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "资产处置收益（损失以“-”号填列）")
    private BigDecimal incomeFromAssetDisposal = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "二、营业利润")
    private BigDecimal operatingProfits = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "加：营业外收入")
    private BigDecimal nonOperatingIncome = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "减：营业外支出")
    private BigDecimal nonOperatingExpenses = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "三、利润总额")
    private BigDecimal profitBeforeTax = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "减：所得税费用")
    private BigDecimal incomeTaxExpense = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "四、净利润")
    private BigDecimal netProfit = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "五、其他综合收益的税后净额")
    private BigDecimal otherComprehensiveIncomeAfterTax = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "六、综合收益总额")
    private BigDecimal totalComprehensiveIncome = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "七、每股收益")
    private BigDecimal earningsPerShare = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "（一）基本每股收益")
    private BigDecimal basicEarningsPerShare = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "（二）稀释每股收益")
    private BigDecimal dilutedEarningsPerShare = Constants.DEFAULT_AMT;

}