package io.ibs.modules.irp.etprlevel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业评级-对外投资信息段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@ApiModel(value = "企业评级-对外投资信息段")
public class TEtprLvInvAbroInfSgmtDTO extends EtprLvBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "被投资企业名称")
    private String invEntName;
    @ApiModelProperty(value = "被投资企业身份标识类型")
    private String invEntIdType;
    @ApiModelProperty(value = "被投资企业身份号码")
    private String invEntIdNum;
    @ApiModelProperty(value = "被投资企业注册资本")
    private BigDecimal invEntRegCap;
    @ApiModelProperty(value = "被投资企业成立日期")
    private String invEntEstablishDate;
    @ApiModelProperty(value = "投资币种")
    private String invCurrency;
    @ApiModelProperty(value = "投资认缴金额")
    private BigDecimal invSubAmount;
    @ApiModelProperty(value = "投资实缴金额")
    private BigDecimal invPaidAmount;
    @ApiModelProperty(value = "持股比例")
    private BigDecimal shareHoldRatio;
    @ApiModelProperty(value = "投资日期")
    private String invDate;

}