package io.ibs.modules.irp.etprlevel.dto;

import io.ibs.modules.irp.util.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业评级-注册资本及主要出资人段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@ApiModel(value = "企业评级-注册资本及主要出资人段")
public class TEtprLvMnShaHodInfSgmtDTO extends EtprLvBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "企业客户号")
    private String cstmNo;
    @ApiModelProperty(value = "注册资本币种")
    private String regCapCurrency;
    @ApiModelProperty(value = "注册资本")
    private BigDecimal regCap = Constants.DEFAULT_AMT;
    @ApiModelProperty(value = "主要出资人个数")
    private Integer mnSharHodNm = 0;

}