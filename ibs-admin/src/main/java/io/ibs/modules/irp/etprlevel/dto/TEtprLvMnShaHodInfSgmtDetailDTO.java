package io.ibs.modules.irp.etprlevel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业评级-注册资本及主要出资人段明细
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@ApiModel(value = "企业评级-注册资本及主要出资人段明细")
public class TEtprLvMnShaHodInfSgmtDetailDTO extends EtprLvBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "出资人类型")
    private String sharHodType;
    @ApiModelProperty(value = "出资人身份类别")
    private String sharHodCertType;
    @ApiModelProperty(value = "出资人名称")
    private String sharHodName;
    @ApiModelProperty(value = "出资人身份标识类型")
    private String sharHodIdType;
    @ApiModelProperty(value = "出资人身份标识号码")
    private String sharHodIdNum;
    @ApiModelProperty(value = "出资比例")
    private BigDecimal invRatio;

}