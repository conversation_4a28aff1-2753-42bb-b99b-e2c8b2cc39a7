package io.ibs.modules.irp.etprlevel.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业评级-资产负债表段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_ETPR_LV_BALANCE_SHEET_SGMT")
public class TEtprLvBalanceSheetSgmtEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 企业客户号
     */
    private String cstmNo;
    /**
     * 货币资金
     */
    private BigDecimal currencyFunds;
    /**
     * 交易性金融资产
     */
    private BigDecimal financialAssetsHeldForTrading;
    /**
     * 衍生金融资产
     */
    private BigDecimal derivativeFinancialAssets;
    /**
     * 应收票据
     */
    private BigDecimal notesReceivable;
    /**
     * 应收账款
     */
    private BigDecimal accountsReceivable;
    /**
     * 应收款项融资
     */
    private BigDecimal accountsReceivableFinancing;
    /**
     * 预付款项
     */
    private BigDecimal prePayments;
    /**
     * 其他应收款
     */
    private BigDecimal otherReceivables;
    /**
     * 存货
     */
    private BigDecimal inventories;
    /**
     * 合同资产
     */
    private BigDecimal contractAssets;
    /**
     * 持有待售资产
     */
    private BigDecimal assetsAvailableForSale;
    /**
     * 一年内到期的非流动资产
     */
    private BigDecimal currentPortionOfNonCurrentAssets;
    /**
     * 其他流动资产
     */
    private BigDecimal otherCurrentAssets;
    /**
     * 流动资产合计
     * 流动资产合计 = 货币资金+交易性金融资产+衍生金融资产+应收票据+应收账款+应收款项融资+预付款项+其他应收款+存货+合同资产+持有待售资产+一年内到期的非流动资产+其他流动资产
     */
    private BigDecimal totalCurrentAssets;
    /**
     * 债权投资
     */
    private BigDecimal debtInvestment;
    /**
     * 其他债权投资
     */
    private BigDecimal otherDebtInvestment;
    /**
     * 长期应收款
     */
    private BigDecimal longTermReceivables;
    /**
     * 长期股权投资
     */
    private BigDecimal longTermEquityInvestment;
    /**
     * 其他权益工具投资
     */
    private BigDecimal otherEquityInstrumentsInvestment;
    /**
     * 其他非流动金融资产
     */
    private BigDecimal otherNonCurrentFinancialAssets;
    /**
     * 投资性房地产
     */
    private BigDecimal investmentProperties;
    /**
     * 固定资产
     */
    private BigDecimal fixedAssets;
    /**
     * 在建工程
     */
    private BigDecimal constructionInProgress;
    /**
     * 生产性生物资产
     */
    private BigDecimal nonCurrentBiologicalAssets;
    /**
     * 油气资产
     */
    private BigDecimal oilAndGasAssets;
    /**
     * 使用权资产
     */
    private BigDecimal useRightAssets;
    /**
     * 无形资产
     */
    private BigDecimal intangibleAssets;
    /**
     * 开发支出
     */
    private BigDecimal developmentDisbursements;
    /**
     * 商誉
     */
    private BigDecimal goodWill;
    /**
     * 长期待摊费用
     */
    private BigDecimal longTermDeferredExpenses;
    /**
     * 递延所得税资产
     */
    private BigDecimal deferredTaxAssets;
    /**
     * 其他非流动资产
     */
    private BigDecimal otherNonCurrentAssets;
    /**
     * 非流动资产合计
     * 非流动资产合计=债权投资+其他债权投资+长期应收款+长期股权投资+其他权益工具投资+其他非流动金融资产+投资性房地产+固定资产+在建工程+生产性生物资产+油气资产+使用权资产+无形资产+开发支出+商誉+长期待摊费用+递延所得税资产+其他非流动资产
     */
    private BigDecimal totalNonCurrentAssets;
    /**
     * 资产总计
     * 资产总计=流动资产合计+非流动资产合计
     */
    private BigDecimal totalAssets;
    /**
     * 短期借款
     */
    private BigDecimal shortTermBorrowings;
    /**
     * 交易性金融负债
     */
    private BigDecimal financialLiabilitiesHeldForTrading;
    /**
     * 衍生金融负债
     */
    private BigDecimal derivativeFinancialLiabilities;
    /**
     * 应付票据
     */
    private BigDecimal notesPayable;
    /**
     * 应付账款
     */
    private BigDecimal accountsPayable;
    /**
     * 预收款项
     */
    private BigDecimal receiptsInAdvance;
    /**
     * 合同负债
     */
    private BigDecimal contractualLiabilities;
    /**
     * 应付职工薪酬
     */
    private BigDecimal employeeBenefitsPayable;
    /**
     * 应交税费
     */
    private BigDecimal taxesPayable;
    /**
     * 其他应付款
     */
    private BigDecimal otherPayables;
    /**
     * 持有待售负债
     */
    private BigDecimal liabilitiesHeldForSale;
    /**
     * 一年内到期的非流动负债
     */
    private BigDecimal currentPortionOfLongTermLiabilities;
    /**
     * 其他流动负债
     */
    private BigDecimal otherCurrentLiabilities;
    /**
     * 流动负债合计
     * 流动负债合计=短期借款+交易性金融负债+衍生金融负债+应付票据+应付账款+预收款项 +合同负债+应付职工薪酬+应交税费+其他应付款+持有待售负债+一年内到期的非流动负债+其他流动负债
     */
    private BigDecimal totalCurrentLiabilities;
    /**
     * 长期借款
     */
    private BigDecimal longTermBorrowings;
    /**
     * 应付债券
     */
    private BigDecimal bondsPayables;
    /**
     * 其中：优先股
     */
    private BigDecimal preferredStockInBondsPayables;
    /**
     * 永续债
     */
    private BigDecimal perpetualBondsInBondsPayables;
    /**
     * 租赁负债
     */
    private BigDecimal leaseLiabilities;
    /**
     * 长期应付款
     */
    private BigDecimal longTermPayables;
    /**
     * 预计负债
     */
    private BigDecimal provisions;
    /**
     * 递延收益
     */
    private BigDecimal deferredIncome;
    /**
     * 递延所得税负债
     */
    private BigDecimal deferredTaxLiabilities;
    /**
     * 其他非流动负债
     */
    private BigDecimal otherNonCurrentLiabilities;
    /**
     * 非流动负债合计
     * 非流动负债合计=长期借款+应付债券+租赁负债+长期应付款+预计负债+递延收益+递延所得税负债+其他非流动负债
     */
    private BigDecimal totalNonCurrentLiabilities;
    /**
     * 负债合计
     * 负债合计=流动负债合计+非流动负债合计
     */
    private BigDecimal totalLiabilities;
    /**
     * 实收资本
     */
    private BigDecimal paidInCapitalOrShareCapital;
    /**
     * 其他权益工具
     */
    private BigDecimal otherEquityInstruments;
    /**
     * 其中：优先股
     */
    private BigDecimal preferredStockInOtherEquityInstruments;
    /**
     * 永续债
     */
    private BigDecimal perpetualBondsInOtherEquityInstruments;
    /**
     * 资本公积
     */
    private BigDecimal capitalrRserve;
    /**
     * 减：库存股
     */
    private BigDecimal lessTreasuryStocks;
    /**
     * 其他综合收益
     */
    private BigDecimal otherComprehensiveIncome;
    /**
     * 专项储备
     */
    private BigDecimal specialReserve;
    /**
     * 盈余公积
     */
    private BigDecimal surplusReserve;
    /**
     * 未分配利润
     */
    private BigDecimal unappropriatedProfit;
    /**
     * 所有者权益（或股东权益）合计
     * 所有者权益（或股东权益）合计=实收资本+其他权益工具+资本公积-减：库存股+其他综合收益+专项储备+盈余公积+未分配利润
     */
    private BigDecimal totalEquity;
    /**
     * 负债和所有者权益
     */
    private BigDecimal totalEquityAndLiabilities;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}