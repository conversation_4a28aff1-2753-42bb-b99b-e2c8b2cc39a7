package io.ibs.modules.irp.etprlevel.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 企业评级-基本概况段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_ETPR_LV_FCS_INF_SGMT")
public class TEtprLvFcsInfSgmtEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 企业客户号
     */
    private String cstmNo;
    /**
     * 登记地址
     */
    private String regAdd;
    /**
     * 登记地行政区划代码
     */
    private String admDivOfReg;
    /**
     * 成立日期 yyyy-MM-dd
     */
    private String establishDate;
    /**
     * 业务范围
     */
    private String bizRange;
    /**
     * 主营业务来源
     */
    private String mainBusSou;
    /**
     * 行业分类代码 采用国标《国民经济行业分类》（GB/T4754-2017）规定的代码表，细化到中类
     */
    private String ecoIndusCate;
    /**
     * 经济类型代码 采用国标《经济类型分类与代码》（GB/T12402-2000）中规定的代码表 100-内资；110-国有全资；120-集体全资；130-股份合作；140-联营；141-国有联营；142-集体联营；143-国有与集体联营；149-其他联营；150-有限责任（公司）；159-其他有限责任（公司）；160-股份有限（公司）；170-私有；171-私有独资；172-私有合伙；173-私营有限责任（公司）；174-私营股份有限（公司）；175-个体经营；179-其他私有；190-其他内资；200-港、澳、台投资；210-内地和港、澳或台合资；220-内地和港、澳或台合作；230-港、澳或台独资；240-港、澳或台投资股份有限（公司）；290-其他港澳台投资；300-国外投资；310-中外合资；320-中外合作；330-外资；340-国外投资股份有限（公司）；390-其他国外投资；900-其他；
     */
    private String ecoType;
    /**
     * 金融机构内部评级结果
     * 无-金融机构对该企业无内部评级结果。
     * AAA-短期债务的支付能力和长期债务的偿还能力具有最大保障；经营处于良性循环状态，不确定因素对经营与发展的影响最小。
     * AA-短期债务的支付能力和长期债务的偿还能力很强；经营处于良性循环状态，不确定因素对经营与发展的影响很小。
     * A-短期债务的支付能力和长期债务的偿还能力较强；企业经营处于良性循环状态，未来经营与发展易受企业内外部不确定因素的影响，盈利能力和偿债能力会产生波动。
     * BBB-短期债务的支付能力和长期债务的支付能力一般，目前对本息的保障尚属适当；企业经营处于良性循环状态，未来经营与发展受企业内外部不确定因素的影响，盈利能力和偿债能力会有较大波动，约定的条件可能不足以保障本息的安全。
     * BB-短期债务支付能力和长期债务偿还能力较弱；企业经营与发展状况不佳，支付能力不稳定，有一定风险。
     * B-短期债务支付能力和长期债务偿还能力较差；受内外不确定因素的影响，企业经营较困难，支付能力具有较大的不确定性，风险较大。
     * CCC-短期债务支付能力和长期债务偿还能力很差；受内外不确定因素的影响，企业经营 8 困难，支付能力很困难，风 9 险很大。
     * CC-短期债务的支付能力和长期债务的偿还能力严重不足；经营状况差，促使企业经营及发展走向良性循环状态的内外部因素很少，风险极大。
     * C-短期债务支付困难，长期债务偿还能力极差；企业经营状况一直不好，基本处于恶性循环状态，促使企业经营及发展走向良性循环状态的内外部因素极少，企业濒临破产。
     * RD-限制性违约。企业对某项债务违约，但尚未进入破产、清算等终止业务程序。
     * D-违约。企业进入破产、清算等终止业务程序。
     */
    private String interRatingRes;
    /**
     * 金融机构内评体系描述
     */
    private String interRatinDes;
    /**
     * 主营业务变更情况 1-未变更 2-变更
     */
    private String mainBusChan;
    /**
     * 法定代表人信贷资产支持 0-未知；1-以自有或共有资产为企业贷款担保、以自有资产为企业提供支持或关联方提供资金拆借；2-以其他形式为企业提供支持；3-未提供支持
     */
    private String creAssSup;
    /**
     * 近一年主要人员是否发生变动 1-未变动；2-变动
     */
    private String perChaSit;
    /**
     * 近三年的欠税条数
     */
    private Integer defaTaxAmount;
    /**
     * 经营地产权情况 0-未知；1-经营场地为自有；2-经营场地为租赁
     */
    private String housEquities;
    /**
     * 从业人员数量
     */
    private Integer numOfEmployees;
    /**
     * 是否县域企业 0-否 1-是
     */
    private String isInCounty;
    /**
     * 是否涉农企业 0-否 1-是
     */
    private String hasIncFromAgri;
    /**
     * 董事会成员（执行董事）数量
     */
    private Integer direcortNum;
    /**
     * 监事会成员（监事）数量
     */
    private Integer supervisorNum;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}