package io.ibs.modules.irp.etprlevel.entity.credit;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 资产负债表上期信息
 * <AUTHOR>
 * @since 2023/3/6 16:32
 */
@Data
@XmlRootElement(name = "BalanceSheetPPInf")
@XmlAccessorType(XmlAccessType.FIELD)
public class BalanceSheetPPInf {
    private String CurrencyFunds;   //货币资金
    private String FinancialAssetsHeldForTrading;   //交易性金融资产
    private String DerivativeFinancialAssets;   //衍生金融资产
    private String NotesReceivable;     //应收票据
    private String AccountsReceivable;  //应收账款
    private String AccountsReceivableFinancing;     //应收款项融资
    private String Prepayments;     //预付款项
    private String OtherReceivables;    //其他应收款
    private String Inventories;     //存货
    private String ContractAssets;  //合同资产
    private String AssetsAvailableForSale;  //持有待售资产
    private String CurrentPortionOfNonCurrentAssets;    //一年内到期的非流动资产
    private String OtherCurrentAssets;  //其他流动资产
    private String TotalCurrentAssets;  //流动资产合计
    private String DebtInvestment;  //债权投资
    private String OtherDebtInvestment;     //其他债权投资
    private String LongTermReceivables;     //长期应收款
    private String LongTermEquityInvestment;    //长期股权投资
    private String OtherEquityInstrumentsInvestment;    //其他权益工具投资
    private String OtherNonCurrentFinancialAssets;  //其他非流动金融资产
    private String InvestmentProperties;    //投资性房地产
    private String FixedAssets;     //固定资产
    private String ConstructionInProgress;  //在建工程
    private String NonCurrentBiologicalAssets;  //生产性生物资产
    private String OilAndGasAssets;     //油气资产
    private String UseRightAssets;  //使用权资产
    private String IntangibleAssets;    //无形资产
    private String DevelopmentDisbursements;    //开发支出
    private String Goodwill;    //商誉
    private String LongTermDeferredExpenses;    //长期待摊费用
    private String DeferredTaxAssets;   //递延所得税资产
    private String OtherNonCurrentAssets;   //其他非流动资产
    private String TotalNonCurrentAssets;   //非流动资产合计
    private String TotalAssets;     //资产总计
    private String ShortTermBorrowings;     //短期借款
    private String FinancialLiabilitiesHeldForTrading;  //交易性金融负债
    private String DerivativeFinancialLiabilities;  //衍生金融负债
    private String NotesPayable;    //应付票据
    private String AccountsPayable;     //应付账款
    private String ReceiptsInAdvance;   //预收款项
    private String ContractualLiabilities;  //合同负债
    private String EmployeeBenefitsPayable;     //应付职工薪酬
    private String TaxesPayable;    //应交税费
    private String OtherPayables;   //其他应付款
    private String LiabilitiesHeldForSale;  //持有待售负债
    private String CurrentPortionOfLongTermLiabilities;     //一年内到期的非流动负债
    private String OtherCurrentLiabilities;     //其他流动负债
    private String TotalCurrentLiabilities;     //流动负债合计
    private String LongTermBorrowings;  //长期借款
    private String BondsPayables;   //应付债券
    private String PreferredStockInBondsPayables;   //其中：优先股
    private String PerpetualBondsInBondsPayables;   //永续债
    private String LeaseLiabilities;    //租赁负债
    private String LongTermPayables;    //长期应付款
    private String Provisions;  //预计负债
    private String DeferredIncome;  //递延收益
    private String DeferredTaxLiabilities;  //递延所得税负债
    private String OtherNonCurrentLiabilities;  //其他非流动负债
    private String TotalNonCurrentLiabilities;  //非流动负债合计
    private String TotalLiabilities;    //负债合计
    private String PaidInCapitalOrShareCapital;     //实收资本（或股本）
    private String OtherEquityInstruments;  //其他权益工具
    private String PreferredStockInOtherEquityInstruments;  //其中：优先股
    private String PerpetualBondsInOtherEquityInstruments;  //永续债
    private String CapitalrRserve;  //资本公积
    private String LessTreasuryStocks;  //减：库存股
    private String OtherComprehensiveIncome;    //其他综合收益
    private String SpecialReserve;  //专项储备
    private String SurplusReserve;  //盈余公积
    private String UnappropriatedProfit;    //未分配利润
    private String TotalEquity;     //所有者权益（或股东权益）合计
    private String TotalEquityAndLiabilities;   //负债和所有者权益（或股东权益）总计
}
