package io.ibs.modules.irp.etprlevel.entity.credit;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 负面信息段
 * <AUTHOR>
 * @since 2023/3/6 16:20
 */
@Data
@XmlRootElement(name = "NegInfSgmt")
@XmlAccessorType(XmlAccessType.FIELD)
public class NegInfSgmt {
    private String NegInfNum; //负面信息数量
    private List<NegInf> NegInf; //负面信息
}
