package io.ibs.modules.irp.etprlevel.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业评级-注册资本及主要出资人段明细
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class TEtprLvMnShaHodInfSgmtDetailExcel {
    @ExcelProperty(value = "企业客户号", index = 0)
    private String cstmNo;
    @ExcelProperty(value = "出资人类型", index = 1)
    private String sharHodType;
    @ExcelProperty(value = "出资人身份类别", index = 2)
    private String sharHodCertType;
    @ExcelProperty(value = "出资人名称", index = 3)
    private String sharHodName;
    @ExcelProperty(value = "出资人身份标识类型", index = 4)
    private String sharHodIdType;
    @ExcelProperty(value = "出资人身份标识号码", index = 5)
    private String sharHodIdNum;
    @ExcelProperty(value = "出资比例", index = 6)
    private BigDecimal invRatio;
    @ExcelProperty(value = "修改人", index = 7)
    private Long updater;
    @ExcelProperty(value = "修改时间", index = 8)
    private Date updateDate;
}