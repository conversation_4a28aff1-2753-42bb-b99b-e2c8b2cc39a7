package io.ibs.modules.irp.etprlevel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.modules.irp.etprlevel.dao.TEtprLvBsSgmtDao;
import io.ibs.modules.irp.etprlevel.dto.TEtprLvBsSgmtDTO;
import io.ibs.modules.irp.etprlevel.entity.TEtprLvBsSgmtEntity;
import io.ibs.modules.irp.etprlevel.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 企业评级-基础段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@Service
public class TEtprLvBsSgmtServiceImpl extends EtprLvCommonServiceImpl<TEtprLvBsSgmtDao, TEtprLvBsSgmtEntity, TEtprLvBsSgmtDTO> implements TEtprLvBsSgmtService {
    private final TEtprLvBalanceSheetSgmtService balanceSheetSgmtService;
    private final TEtprLvCashFlowsSgmtService cashFlowsSgmtService;
    private final TEtprLvFcsInfSgmtService fcsInfSgmtService;
    private final TEtprLvIncomeStatementSgmtService incomeStatementSgmtService;
    private final TEtprLvInvAbroInfSgmtService invAbroInfSgmtService;
    private final TEtprLvLegRepInfSgmtService legRepInfSgmtService;
    private final TEtprLvMnShaHodInfSgmtDetailService mnShaHodInfSgmtDetailService;
    private final TEtprLvMnShaHodInfSgmtService mnShaHodInfSgmtService;
    private final TEtprLvNegInfSgmetService negInfSgmetService;

    @Autowired
    public TEtprLvBsSgmtServiceImpl(TEtprLvBalanceSheetSgmtService balanceSheetSgmtService,
                                    TEtprLvCashFlowsSgmtService cashFlowsSgmtService,
                                    TEtprLvFcsInfSgmtService fcsInfSgmtService,
                                    TEtprLvIncomeStatementSgmtService incomeStatementSgmtService,
                                    TEtprLvInvAbroInfSgmtService invAbroInfSgmtService,
                                    TEtprLvLegRepInfSgmtService legRepInfSgmtService,
                                    TEtprLvMnShaHodInfSgmtDetailService mnShaHodInfSgmtDetailService,
                                    TEtprLvMnShaHodInfSgmtService mnShaHodInfSgmtService,
                                    TEtprLvNegInfSgmetService negInfSgmetService) {
        this.balanceSheetSgmtService = balanceSheetSgmtService;
        this.cashFlowsSgmtService = cashFlowsSgmtService;
        this.fcsInfSgmtService = fcsInfSgmtService;
        this.incomeStatementSgmtService = incomeStatementSgmtService;
        this.invAbroInfSgmtService = invAbroInfSgmtService;
        this.legRepInfSgmtService = legRepInfSgmtService;
        this.mnShaHodInfSgmtDetailService = mnShaHodInfSgmtDetailService;
        this.mnShaHodInfSgmtService = mnShaHodInfSgmtService;
        this.negInfSgmetService = negInfSgmetService;
    }

    @Override
    public QueryWrapper<TEtprLvBsSgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<TEtprLvBsSgmtEntity> wrapper = new QueryWrapper<>();

        String cstmNo = (String) params.get("cstmNo");
        wrapper.likeRight(StringUtils.isNotBlank(cstmNo), "CSTM_NO", cstmNo);
        String entName = (String) params.get("entName");
        wrapper.like(StringUtils.isNotBlank(entName), "ENT_NAME", entName);
        String entCertNum = (String) params.get("entCertNum");
        wrapper.eq(StringUtils.isNotBlank(entCertNum), "ENT_CERT_NUM", entCertNum);

        return wrapper;
    }

    //删除基础段时同时删除其他所有段
    @Transactional(rollbackFor = Exception.class)
    public void deleteByCstmNo(String[] cstmNos) {
        UpdateWrapper<TEtprLvBsSgmtEntity> wrapper = new UpdateWrapper<>();
        wrapper.inSql("CSTM_NO", stringArray2StringSQL(cstmNos));
        baseDao.delete(wrapper);

        balanceSheetSgmtService.deleteByCstmNo(cstmNos);

        cashFlowsSgmtService.deleteByCstmNo(cstmNos);

        fcsInfSgmtService.deleteByCstmNo(cstmNos);

        incomeStatementSgmtService.deleteByCstmNo(cstmNos);

        invAbroInfSgmtService.deleteByCstmNo(cstmNos);

        legRepInfSgmtService.deleteByCstmNo(cstmNos);

        mnShaHodInfSgmtDetailService.deleteByCstmNo(cstmNos);

        mnShaHodInfSgmtService.deleteByCstmNo(cstmNos);

        negInfSgmetService.deleteByCstmNo(cstmNos);
    }

    /**
     * 合并基础段
     *
     * @param list
     */
    @Override
    public void mergeRecord(List<TEtprLvBsSgmtDTO> list) {
        baseDao.mergeRecord(list);
    }
}