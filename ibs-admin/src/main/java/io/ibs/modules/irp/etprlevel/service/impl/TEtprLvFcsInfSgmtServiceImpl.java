package io.ibs.modules.irp.etprlevel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.irp.etprlevel.dao.TEtprLvFcsInfSgmtDao;
import io.ibs.modules.irp.etprlevel.dto.TEtprLvFcsInfSgmtDTO;
import io.ibs.modules.irp.etprlevel.entity.TEtprLvFcsInfSgmtEntity;
import io.ibs.modules.irp.etprlevel.service.TEtprLvFcsInfSgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 企业评级-基本概况段
 *
 * <AUTHOR>
 * @since 1.0 2022-12-27
 */
@Service
public class TEtprLvFcsInfSgmtServiceImpl extends EtprLvCommonServiceImpl<TEtprLvFcsInfSgmtDao, TEtprLvFcsInfSgmtEntity, TEtprLvFcsInfSgmtDTO> implements TEtprLvFcsInfSgmtService {

    @Override
    public QueryWrapper<TEtprLvFcsInfSgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<TEtprLvFcsInfSgmtEntity> wrapper = new QueryWrapper<>();

        String cstmNo = (String) params.get("cstmNo");
        wrapper.eq(StringUtils.isNotBlank(cstmNo), "CSTM_NO", cstmNo);

        return wrapper;
    }

    /**
     * 合并基本概况
     * @param list
     */
    @Override
    public void mergeRecord(List<TEtprLvFcsInfSgmtDTO> list) {
        baseDao.mergeRecord(list);
    }
}