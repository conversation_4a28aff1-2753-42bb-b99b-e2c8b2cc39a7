package io.ibs.modules.irp.management.dao;

import io.ibs.common.dao.BaseDao;
import io.ibs.modules.irp.management.dto.SysUreportManagementDTO;
import io.ibs.modules.irp.management.domain.SysUreportManagementEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统一报表管理表
 *
 * <AUTHOR>
 * @since 3.0 2022-05-23
 */
@Mapper
public interface SysUreportManagementDao extends BaseDao<SysUreportManagementEntity> {

    SysUreportManagementDTO getByReportNum(@Param("reportNum") Long reportNum);

    List<SysUreportManagementDTO> getByReportType(@Param("reportType") String reportType);

}