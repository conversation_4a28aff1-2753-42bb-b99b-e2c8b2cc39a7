package io.ibs.modules.irp.management.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.ibs.common.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 统一报表管理表
 *
 * <AUTHOR> chen<PERSON>@gmail.com
 * @since 3.0 2022-05-23
 */
@Data
@ApiModel(value = "统一报表管理表")
public class SysUreportManagementDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @ApiModelProperty(value = "报表编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer reportNum;
    @ApiModelProperty(value = "报表名称")
    private String reportName;
    @ApiModelProperty(value = "报表类型")
    private String reportType;
    @ApiModelProperty(value = "设计编号")
    private String designNum;
    @ApiModelProperty(value = "文件名称")
    private String fileName;
    @ApiModelProperty(value = "状态1-启用 2-停用")
    private String status;
    @ApiModelProperty(value = "创建者")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date createDate;
    @ApiModelProperty(value = "更新者")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long updater;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)
    private Date updateDate;

}