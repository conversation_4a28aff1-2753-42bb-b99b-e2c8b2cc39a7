package io.ibs.modules.irp.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.irp.management.dao.SysUreportManagementDao;
import io.ibs.modules.irp.management.dto.SysUreportManagementDTO;
import io.ibs.modules.irp.management.domain.SysUreportManagementEntity;
import io.ibs.modules.irp.management.service.SysUreportManagementService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 统一报表管理表
 *
 * <AUTHOR> chenyon<PERSON>j<PERSON>@gmail.com
 * @since 3.0 2022-05-23
 */
@Service
public class SysUreportManagementServiceImpl extends CrudServiceImpl<SysUreportManagementDao, SysUreportManagementEntity, SysUreportManagementDTO> implements SysUreportManagementService {

    @Override
    public QueryWrapper<SysUreportManagementEntity> getWrapper(Map<String, Object> params) {
        // 查询字段
        String reportNum = (String) params.get("reportNum");
        String reportName = (String) params.get("reportName");
        String reportType = (String) params.get("reportType");

        // 排序类型、排序字段
        String order = (String) params.get("order");
        String orderFiled = (String) params.get("orderFiled");

        QueryWrapper<SysUreportManagementEntity> wrapper = new QueryWrapper<>();

        // 根据报表名称模糊查询
        wrapper.like(StringUtils.isNotBlank(reportName), "REPORT_NAME", reportName);
        // 根据报表编号查询
        wrapper.eq(StringUtils.isNotBlank(reportNum), "REPORT_NUM", reportNum);
        // 根据报表类型查询
        wrapper.eq(StringUtils.isNotBlank(reportType), "REPORT_TYPE", reportType);
        // 根据传入字段排序
        wrapper.orderBy(StringUtils.isNotBlank(order) && StringUtils.isNotBlank(orderFiled),
                "asc".equals(order), orderFiled);
        // 固定排序字段
        wrapper.orderByAsc("REPORT_NUM");
        return wrapper;
    }

    @Override
    public SysUreportManagementDTO getByReportNum(Long reportNum) {
        return baseDao.getByReportNum(reportNum);
    }

    @Override
    public List<SysUreportManagementDTO> getByReportType(String reportType) {
        return baseDao.getByReportType(reportType);
    }

    @Override
    public void save(SysUreportManagementDTO dto) {
        String reportType = dto.getReportType();
        // 查询当前报表类型的结果个数
        Long count = baseDao.selectCount(new QueryWrapper<SysUreportManagementEntity>()
                .lambda()
                .eq(StringUtils.isNotBlank(reportType), SysUreportManagementEntity::getReportType, reportType));

        // 拼接四位字符串序列
        StringBuilder sb = new StringBuilder();
        sb.append(count == 0 ? 1 : count + 1);
        for (int i = 0; i < 4; i++) {
            if (sb.length() < 4) {
                sb.insert(0, "0");
            }
        }
        // 设置6位报表编号 报表类型2位 + 自增序列4位
        dto.setReportNum(Integer.valueOf(reportType + sb));
        super.save(dto);
    }

}