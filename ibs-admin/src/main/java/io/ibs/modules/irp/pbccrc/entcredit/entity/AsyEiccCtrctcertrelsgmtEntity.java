package io.ibs.modules.irp.pbccrc.entcredit.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 授信-共同受信人信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-15
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("EICC_CTRCTCERTRELSGMT")
public class AsyEiccCtrctcertrelsgmtEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 共同受信人身份类别
	*/
	private String brerType;
	/**
	* 共同受信人名称
	*/
	private String certRelName;
	/**
	* 共同受信人身份标识类型
	*/
	private String certRelIdType;
	/**
	* 共同受信人身份标识号码
	*/
	private String certRelIdNum;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务发生日期
	*/
	private Date bussDate;
	/**
	* 业务号
	*/
	private String bussNum;
	/**
	* 原系统业务号
	*/
	private String nativeBussNum;
	/**
	* 业务发生日期
	*/
	private String itabGetDate;
}