package io.ibs.modules.irp.pbccrc.entcredit.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 授信-共同受信人信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-14
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EiccCtrctcertrelsgmtExcel {
    @ExcelProperty(value = "共同受信人身份类别", index = 0)
    private String brerType;
    @ExcelProperty(value = "共同受信人名称", index = 1)
    private String certRelName;
    @ExcelProperty(value = "共同受信人身份标识类型", index = 2)
    private String certRelIdType;
    @ExcelProperty(value = "共同受信人身份标识号码", index = 3)
    private String certRelIdNum;
    @ExcelProperty(value = "内部机构代码", index = 4)
    private String deptCode;
    @ExcelProperty(value = "业务发生日期", index = 5)
    private Date bussDate;
    @ExcelProperty(value = "业务号", index = 6)
    private String bussNum;
    @ExcelProperty(value = "原系统业务号", index = 7)
    private String nativeBussNum;
    @ExcelProperty(value = "业务发生日期", index = 8)
    private String itabGetDate;
}