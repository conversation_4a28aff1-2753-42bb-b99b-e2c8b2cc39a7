package io.ibs.modules.irp.pbccrc.entcredit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.irp.pbccrc.entcredit.dao.EicbCtrctbssgmtDao;
import io.ibs.modules.irp.pbccrc.entcredit.dto.EicbCtrctbssgmtDTO;
import io.ibs.modules.irp.pbccrc.entcredit.entity.EicbCtrctbssgmtEntity;
import io.ibs.modules.irp.pbccrc.entcredit.service.EicbCtrctbssgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 授信-基础段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-15
 */
@Service
public class EicbCtrctbssgmtServiceImpl extends CrudServiceImpl<EicbCtrctbssgmtDao, EicbCtrctbssgmtEntity, EicbCtrctbssgmtDTO> implements EicbCtrctbssgmtService {

    @Override
    public QueryWrapper<EicbCtrctbssgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EicbCtrctbssgmtEntity> wrapper = new QueryWrapper<>();

        String name = (String) params.get("name");
        wrapper.like(StringUtils.isNotBlank(name), "NAME", name);
        String idType = (String) params.get("idType");
        wrapper.eq(StringUtils.isNotBlank(idType), "ID_TYPE", idType);
        String idNum = (String) params.get("idNum");
        wrapper.eq(StringUtils.isNotBlank(idNum), "ID_NUM", idNum);
        String startDate = (String) params.get("startDate");
        String endDate = (String) params.get("endDate");
        wrapper.gt(StringUtils.isNotBlank(startDate), "BUSS_DATE", startDate);
        wrapper.lt(StringUtils.isNotBlank(endDate), "BUSS_DATE", endDate);
        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);

        return wrapper;
    }

}