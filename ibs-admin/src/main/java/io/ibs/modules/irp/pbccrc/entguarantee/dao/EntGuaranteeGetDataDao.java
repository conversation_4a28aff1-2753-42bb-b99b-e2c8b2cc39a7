package io.ibs.modules.irp.pbccrc.entguarantee.dao;

import io.ibs.common.dao.BaseDao;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EntGuaranteeGetDataEntity;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EntLoanUnclearEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 获取企业担保信息
 *
 * <AUTHOR>
 * @since 2022/8/1 15:33
 */
@Mapper
@Repository
public interface EntGuaranteeGetDataDao extends BaseDao<EntGuaranteeGetDataEntity> {

    /**
     * 获取指定日期开立的保证贷款
     *
     * @param tranDate
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getEntGuarOpenInCurrentDay(@Param("tranDate") String tranDate);

    /**
     * 获取指定日期结清的保证贷款
     *
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getEntGuarCloseInCurrentDay(@Param("tranDate") String tranDate);

    /**
     * 获取指定日期有五级分类或余额改变的保证贷款
     *
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getEntGuarOfBalUpdate(@Param("tranDate") String tranDate);

    /**
     * 获取指定借据号的担保信息
     *
     * @param list
     * @return
     */
    @DataSource("xdgl")
    List<EntGuaranteeGetDataEntity> getEntGuarantee(List<EntLoanUnclearEntity> list);

    /**
     * 获取存量担保贷款借据信息
     *
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getEntLoanOfUnclear();

    /**
     * 获取指定日期有五级分类调整的单位贷款
     *
     * @param tranDate
     * @return

     @DataSource("xdgl") List<String> getEntLoanOfFiveCate(@Param("tranDate") String tranDate);
     */

    /**
     * 获取指定借据的保证贷款在保信息
     *
     * @param loanNoList
     * @return
     */
     @DataSource("informix") List<EntLoanUnclearEntity> getEntLoanInfo(List<String> loanNoList);


    /**
     * 获取指定借据号的还款计划
     *
     * @param loanNoList
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getCorpGuarRetByLoanNo(List<String> loanNoList);

    /**
     * 获取指定日期前未还清本金的保证贷款
     *
     * @param tranDate
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getCorpGuarOfRetUnclear(@Param("tranDate") String tranDate);

    /**
     * 获取指定日期开立的保证贷款
     * @param bgnDate
     * @param endDate
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getEntGuarOpenInCurrentDayBetweenTwoDate(String bgnDate, String endDate);

    /**
     * 获取指定日期结清的保证贷款
     * @param bgnDate
     * @param endDate
     * @return
     */
    @DataSource("informix")
    List<EntLoanUnclearEntity> getEntGuarCloseInCurrentDayBetweenTwoDate(String bgnDate, String endDate);
}
