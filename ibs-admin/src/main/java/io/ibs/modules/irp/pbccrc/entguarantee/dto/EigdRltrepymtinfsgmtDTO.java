package io.ibs.modules.irp.pbccrc.entguarantee.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.ibs.common.utils.DateUtils;
import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.ibs.modules.security.user.SecurityUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

/**
 * 担保-在保责任信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Data
@ApiModel(value = "担保-在保责任信息段")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EigdRltrepymtinfsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "账户状态")
    private String acctStatus;
    @ApiModelProperty(value = "在保余额")
    private BigDecimal loanAmt;
    @ApiModelProperty(value = "余额变化日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date repayPrd;
    @ApiModelProperty(value = "五级分类")
    private String fiveCate;
    @ApiModelProperty(value = "五级分类认定日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date fiveCateAdjDate;
    @ApiModelProperty(value = "风险敞口")
    private BigDecimal riEx;
    @ApiModelProperty(value = "代偿(垫款)标识")
    private String compAdvFlag;
    @ApiModelProperty(value = "账户关闭日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date closeDate;
    @ApiModelProperty(value = "报告时点说明代码")
    private String rptDateCode;
    @ApiModelProperty(value = "内部机构代码")
    private String deptCode;
    @ApiModelProperty(value = "业务发生日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date bussDate;
    @ApiModelProperty(value = "业务号")
    private String bussNum;
    @ApiModelProperty(value = "原系统业务号")
    private String nativeBussNum;
    @ApiModelProperty(value = "业务发生日期")
    private String itabGetDate;

    public String getItabGetDate() {
        if (StringUtils.isBlank(itabGetDate)) {
            Calendar theTime = Calendar.getInstance();
            if (theTime.get(Calendar.HOUR_OF_DAY) >= 17)
                theTime.add(Calendar.DAY_OF_YEAR, 1);

            itabGetDate = DateUtils.format(theTime.getTime(), "yyyyMMdd");
        }
        return itabGetDate;
    }

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "更新人")
    private Long updator;
    private Long id;
    private Date createDate;

    public Date getCreateDate() {
        if (createDate == null) {
            createDate = new Date();
        }
        return createDate;
    }

    private Long creator;

    public Long getCreator() {
        if (creator == null && SecurityUser.getSubject().isAuthenticated())
            creator = SecurityUser.getUserId();
        return creator;
    }

    private String processDefinitionId;

    public void setInstanceId(String id) {
        // this.setProcInstId(id);
    }
}