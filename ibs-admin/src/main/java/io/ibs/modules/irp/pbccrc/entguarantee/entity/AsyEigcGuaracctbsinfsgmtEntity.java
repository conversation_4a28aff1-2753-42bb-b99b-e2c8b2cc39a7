package io.ibs.modules.irp.pbccrc.entguarantee.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 担保-基本信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("EIGC_GUARACCTBSINFSGMT")
public class AsyEigcGuaracctbsinfsgmtEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 担保业务大类
	*/
	private String busiLines;
	/**
	* 担保业务种类细分
	*/
	private String busiDtilLines;
	/**
	* 开户日期
	*/
	private Date openDate;
	/**
	* 担保金额
	*/
	private BigDecimal guarAmt;
	/**
	* 币种
	*/
	private String cy;
	/**
	* 到期日期
	*/
	private Date dueDate;
	/**
	* 反担保方式
	*/
	private String guarMode;
	/**
	* 其他还款保证方式
	*/
	private String othRepyGuarWay;
	/**
	* 保证金百分比
	*/
	private BigDecimal secDep;
	/**
	* 担保合同文本编号
	*/
	private String ctrctTxtCode;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务发生日期
	*/
	private Date bussDate;
	/**
	* 业务号
	*/
	private String bussNum;
	/**
	* 原系统业务号
	*/
	private String nativeBussNum;
	/**
	* 业务发生日期
	*/
	private String itabGetDate;

}