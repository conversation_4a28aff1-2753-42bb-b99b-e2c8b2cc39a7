package io.ibs.modules.irp.pbccrc.entguarantee.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 担保-基本信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("EIGC_GUARACCTBSINFSGMT")
public class EigcGuaracctbsinfsgmtEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 担保业务大类
     */
    private String busiLines;
    /**
     * 担保业务种类细分
     */
    private String busiDtilLines;
    /**
     * 开户日期
     */
    private Date openDate;
    /**
     * 担保金额
     */
    private BigDecimal guarAmt;
    /**
     * 币种
     */
    private String cy;
    /**
     * 到期日期
     */
    private Date dueDate;
    /**
     * 反担保方式
     */
    private String guarMode;
    /**
     * 其他还款保证方式
     */
    private String othRepyGuarWay;
    /**
     * 保证金百分比
     */
    private BigDecimal secDep;
    /**
     * 担保合同文本编号
     */
    private String ctrctTxtCode;
    /**
     * 业务号
     */
    private String bussNum;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;
}