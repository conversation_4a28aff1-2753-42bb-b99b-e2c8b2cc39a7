package io.ibs.modules.irp.pbccrc.entguarantee.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 担保-基础段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EigbGuaracctbssgmtExcel {
    @ExcelProperty(value = "账户类型", index = 0)
    private String acctType;
    @ExcelProperty(value = "债务人名称", index = 1)
    private String name;
    @ExcelProperty(value = "债务人身份标识类型", index = 2)
    private String idType;
    @ExcelProperty(value = "债务人身份标识号码", index = 3)
    private String idNum;
    @ExcelProperty(value = "内部机构代码", index = 4)
    private String deptCode;
    @ExcelProperty(value = "业务号", index = 5)
    private String bussNum;
    @ExcelProperty(value = "客户号", index = 6)
    private String custId;
}