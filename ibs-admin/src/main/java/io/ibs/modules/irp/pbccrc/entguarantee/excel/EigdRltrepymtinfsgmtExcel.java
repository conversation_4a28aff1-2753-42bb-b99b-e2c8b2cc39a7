package io.ibs.modules.irp.pbccrc.entguarantee.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 担保-在保责任信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EigdRltrepymtinfsgmtExcel {
    @ExcelProperty(value = "账户状态", index = 0)
    private String acctStatus;
    @ExcelProperty(value = "在保余额", index = 1)
    private Long loanAmt;
    @ExcelProperty(value = "余额变化日期", index = 2)
    private Date repayPrd;
    @ExcelProperty(value = "五级分类", index = 3)
    private String fiveCate;
    @ExcelProperty(value = "五级分类认定日期", index = 4)
    private Date fiveCateAdjDate;
    @ExcelProperty(value = "风险敞口", index = 5)
    private Long riEx;
    @ExcelProperty(value = "代偿(垫款)标识", index = 6)
    private String compAdvFlag;
    @ExcelProperty(value = "账户关闭日期", index = 7)
    private Date closeDate;
    @ExcelProperty(value = "内部机构代码", index = 8)
    private String deptCode;
    @ExcelProperty(value = "业务号", index = 9)
    private String bussNum;
}