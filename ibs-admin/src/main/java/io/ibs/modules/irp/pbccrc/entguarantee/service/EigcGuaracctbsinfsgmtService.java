package io.ibs.modules.irp.pbccrc.entguarantee.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.entguarantee.dto.EigcGuaracctbsinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EigcGuaracctbsinfsgmtEntity;

/**
 * 担保-基本信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
public interface EigcGuaracctbsinfsgmtService extends CrudService<EigcGuaracctbsinfsgmtEntity, EigcGuaracctbsinfsgmtDTO> {

    EigcGuaracctbsinfsgmtDTO getByBussNum(String bussNum);
}