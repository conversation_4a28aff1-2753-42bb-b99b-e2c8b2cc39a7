package io.ibs.modules.irp.pbccrc.entguarantee.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EntGuaranteeGetDataEntity;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EntLoanUnclearEntity;

import java.util.List;

/**
 * 、
 * 获取企业担保信息
 *
 * <AUTHOR>
 * @since 2022/8/1 15:34
 */

public interface EntGuaranteeGetDataService extends CrudService<EntGuaranteeGetDataEntity, EntGuaranteeGetDataEntity> {
    /**
     * 获取指定日期开立的保证贷款
     *
     * @param tranDate
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarOpenInCurrentDay(String tranDate);

    /**
     * 获取指定日期结清的保证贷款
     *
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarCloseInCurrentDay(String tranDate);

    /**
     * 获取指定日期有余额改变的保证贷款
     *
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarOfBalUpdate(String tranDate);

    /**
     * 获取指定借据号的担保信息
     *
     * @param list
     * @return
     */
    List<EntGuaranteeGetDataEntity> getEntGuarantee(List<EntLoanUnclearEntity> list);

    /**
     * 获取存量担保贷款借据号
     *
     * @return
     */
    List<EntLoanUnclearEntity> getEntLoanOfUnclear();

    /**
     * 获取五级分类调整的保证贷款在保信息
     *
     * @param tranDate
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarInfoOfFiveCate(String tranDate);

    /**
     * 获取指定担保借据的还款计划
     *
     * @param loanList
     * @return
     */
    List<EntLoanUnclearEntity> getCorpGuarRetByLoanNo(List<String> loanList);

    /**
     * 获取指定日期开立的保证贷款
     * @param bgnDate
     * @param endDate
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarOpenInCurrentDayBetweenTwoDate(String bgnDate, String endDate);

    /**
     * 获取指定日期结清的保证贷款
     * @param bgnDate
     * @param endDate
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarCloseInCurrentDayBetweenTwoDate(String bgnDate, String endDate);

    /**
     * 获取指定日期有余额改变的保证贷款
     * @param bgnDate
     * @param endDate
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarOfBalUpdateBetweenTwoDate(String bgnDate, String endDate);

    /**
     * 获取五级分类调整的保证贷款在保信息
     * @param bgnDate
     * @param endDate
     * @return
     */
    List<EntLoanUnclearEntity> getEntGuarInfoOfFiveCateBetweenTwoDate(String bgnDate, String endDate);
}
