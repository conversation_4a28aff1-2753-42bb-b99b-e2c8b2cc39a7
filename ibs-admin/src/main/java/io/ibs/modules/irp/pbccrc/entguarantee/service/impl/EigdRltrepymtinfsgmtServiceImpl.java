package io.ibs.modules.irp.pbccrc.entguarantee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.pbccrc.entguarantee.dao.EigdRltrepymtinfsgmtDao;
import io.ibs.modules.irp.pbccrc.entguarantee.dto.EigdRltrepymtinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EigdRltrepymtinfsgmtEntity;
import io.ibs.modules.irp.pbccrc.entguarantee.service.EigdRltrepymtinfsgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 担保-在保责任信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Service
public class EigdRltrepymtinfsgmtServiceImpl extends CrudServiceImpl<EigdRltrepymtinfsgmtDao, EigdRltrepymtinfsgmtEntity, EigdRltrepymtinfsgmtDTO> implements EigdRltrepymtinfsgmtService {

    @Override
    public QueryWrapper<EigdRltrepymtinfsgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EigdRltrepymtinfsgmtEntity> wrapper = new QueryWrapper<>();

        String acctStatus = (String) params.get("acctStatus");
        wrapper.eq(StringUtils.isNotBlank(acctStatus), "ACCT_STATUS", acctStatus);
        String fiveCate = (String) params.get("fiveCate");
        wrapper.eq(StringUtils.isNotBlank(fiveCate), "FIVE_CATE", fiveCate);
        String compAdvFlag = (String) params.get("compAdvFlag");
        wrapper.eq(StringUtils.isNotBlank(compAdvFlag), "COMP_ADV_FLAG", compAdvFlag);
        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);

        return wrapper;
    }

    @Override
    public EigdRltrepymtinfsgmtDTO getByBussNum(String bussNum) {
        QueryWrapper<EigdRltrepymtinfsgmtEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(String.valueOf(bussNum)), "BUSS_NUM", bussNum);
        EigdRltrepymtinfsgmtEntity rltrepymtinfsgmtEntity = baseDao.selectOne(wrapper);
        return ConvertUtils.sourceToTarget(rltrepymtinfsgmtEntity, EigdRltrepymtinfsgmtDTO.class);
    }
}