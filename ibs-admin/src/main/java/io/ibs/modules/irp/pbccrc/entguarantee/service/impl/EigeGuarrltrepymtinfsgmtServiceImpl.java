package io.ibs.modules.irp.pbccrc.entguarantee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.pbccrc.entguarantee.dao.EigeGuarrltrepymtinfsgmtDao;
import io.ibs.modules.irp.pbccrc.entguarantee.dto.EigeGuarrltrepymtinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EigeGuarrltrepymtinfsgmtEntity;
import io.ibs.modules.irp.pbccrc.entguarantee.service.EigeGuarrltrepymtinfsgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 担保-相关还款责任人段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Service
public class EigeGuarrltrepymtinfsgmtServiceImpl extends CrudServiceImpl<EigeGuarrltrepymtinfsgmtDao, EigeGuarrltrepymtinfsgmtEntity, EigeGuarrltrepymtinfsgmtDTO> implements EigeGuarrltrepymtinfsgmtService {

    @Override
    public QueryWrapper<EigeGuarrltrepymtinfsgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EigeGuarrltrepymtinfsgmtEntity> wrapper = new QueryWrapper<>();

        String arlpName = (String) params.get("arlpName");
        wrapper.eq(StringUtils.isNotBlank(arlpName), "ARLP_NAME", arlpName);
        String arlpCertNum = (String) params.get("arlpCertNum");
        wrapper.eq(StringUtils.isNotBlank(arlpCertNum), "ARLP_CERT_NUM", arlpCertNum);
        String arlpAmt = (String) params.get("arlpAmt");
        wrapper.eq(StringUtils.isNotBlank(arlpAmt), "ARLP_AMT", arlpAmt);
        String maxGuarBussNum = (String) params.get("maxGuarBussNum");
        wrapper.eq(StringUtils.isNotBlank(maxGuarBussNum), "MAX_GUAR_BUSS_NUM", maxGuarBussNum);
        String deptCode = (String) params.get("deptCode");
        wrapper.eq(StringUtils.isNotBlank(deptCode), "DEPT_CODE", deptCode);
        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);

        return wrapper;
    }

    @Override
    public List<EigeGuarrltrepymtinfsgmtDTO> getByBussNum(String bussNum) {
        Map<String, Object> map = new HashMap<>();
        map.put("BUSS_NUM", bussNum);
        List<EigeGuarrltrepymtinfsgmtEntity> eigeGuarrltrepymtinfsgmtEntity = baseDao.selectByMap(map);
        return ConvertUtils.sourceToTarget(eigeGuarrltrepymtinfsgmtEntity, EigeGuarrltrepymtinfsgmtDTO.class);
    }
}