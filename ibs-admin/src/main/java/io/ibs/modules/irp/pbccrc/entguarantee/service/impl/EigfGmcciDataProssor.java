package io.ibs.modules.irp.pbccrc.entguarantee.service.impl;

import io.ibs.modules.irp.pbccrc.entguarantee.dto.EigfGmcciDTO;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EigfGmcciEntity;
import io.ibs.common.aspect.IDataProcessor;
import org.springframework.stereotype.Service;
import java.util.Collection;

/**
*
* @<NAME_EMAIL>
* @since 3.0 2022-03-25
*/
@Service("EigfGmcciDataProssor")
public class EigfGmcciDataProssor implements IDataProcessor {
@Override
public void process(Collection<Object> data, stage stage) {
    for(Object d : data){
    if(stage == IDataProcessor.stage.delete){
        String id = (String) d;
        //System.out.println("担保信息"+id+"被删除了!");
    }
    else if(stage == IDataProcessor.stage.insert || stage == IDataProcessor.stage.update){
        if(d instanceof EigfGmcciDTO){
            EigfGmcciDTO dto = (EigfGmcciDTO) d;
            //System.out.println("担保信息"+dto.getBussNum()+"有更新");
        }else if(d instanceof EigfGmcciEntity){
            EigfGmcciEntity entity = (EigfGmcciEntity) d;
            //System.out.println("担保信息"+entity.getBussNum()+"有更新");
        }
    }
    }
    }
    }
