package io.ibs.modules.irp.pbccrc.entguarantee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.DateUtils;
import io.ibs.modules.irp.pbccrc.entguarantee.dao.EntGuaranteeGetDataDao;
import io.ibs.modules.irp.pbccrc.entguarantee.dto.EigdRltrepymtinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EigdRltrepymtinfsgmtEntity;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EntGuaranteeGetDataEntity;
import io.ibs.modules.irp.pbccrc.entguarantee.entity.EntLoanUnclearEntity;
import io.ibs.modules.irp.pbccrc.entguarantee.service.EigdRltrepymtinfsgmtService;
import io.ibs.modules.irp.pbccrc.entguarantee.service.EntGuaranteeGetDataService;
import io.ibs.modules.irp.util.CstmInfoExch;
import io.ibs.modules.sys.entity.DictData;
import io.ibs.modules.sys.service.SysDictDataService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 获取企业担保信息
 *
 * <AUTHOR>
 * @since 2022/8/1 15:34
 */
@Service
public class EntGuaranteeGetDataServiceImpl extends CrudServiceImpl<EntGuaranteeGetDataDao, EntGuaranteeGetDataEntity, EntGuaranteeGetDataEntity> implements EntGuaranteeGetDataService {

    @Autowired
    SysDictDataService sddSvc;
    @Autowired
    EigdRltrepymtinfsgmtService eigdSvc;

    @Override
    public QueryWrapper<EntGuaranteeGetDataEntity> getWrapper(Map<String, Object> params) {
        return null;
    }

    /**
     * 获取指定日期开立的保证贷款
     *
     * @param tranDate
     * @return
     */
    @Override
    public List<EntLoanUnclearEntity> getEntGuarOpenInCurrentDay(String tranDate) {
        return baseDao.getEntGuarOpenInCurrentDay(tranDate);
    }

    @Override
    public List<EntLoanUnclearEntity> getEntGuarCloseInCurrentDay(String tranDate) {
        return baseDao.getEntGuarCloseInCurrentDay(tranDate);
    }

    /**
     * 获取指定日期有余额改变的保证贷款
     *
     * @param tranDate
     * @return
     */
    @Override
    public List<EntLoanUnclearEntity> getEntGuarOfBalUpdate(String tranDate) {
        List<EntLoanUnclearEntity> balUpdate = baseDao.getEntGuarOfBalUpdate(tranDate);
        if (balUpdate == null || balUpdate.size() == 0) {
            return null;
        }

        //获取指定借据的未还清本金的还款计划
        List<EntLoanUnclearEntity> retList = baseDao.getCorpGuarRetByLoanNo(balUpdate.stream().map(EntLoanUnclearEntity::getLoanNo).collect(Collectors.toList()));
        Map<String, List<EntLoanUnclearEntity>> retMap = retList.stream().collect(Collectors.groupingBy(EntLoanUnclearEntity::getLoanNo));

        //获取借据的在保责任信息段
        QueryWrapper<EigdRltrepymtinfsgmtEntity> wrapper = new QueryWrapper<>();
        wrapper.in("BUSS_NUM", balUpdate.stream().map(EntLoanUnclearEntity::getLoanNo).collect(Collectors.toList()));
        List<EigdRltrepymtinfsgmtDTO> eigdList = eigdSvc.list(wrapper);

        Date date = DateUtils.parse(tranDate, "yyyy-MM-dd");
        List<DictData> dictList = sddSvc.getDataByType("FIVE_CATE_DAY");

        balUpdate.forEach(b -> {
            Date lastRetDate = retMap.get(b.getLoanNo()).stream().min((a, c) -> a.getDueDate().compareTo(c.getDueDate())).get().getDueDate();
            String newFive = CstmInfoExch.fiveCate(dictList, lastRetDate, date, b.getBgnintDate()).get("five").toString();
            b.setFiveFlag(newFive);

            EigdRltrepymtinfsgmtDTO dto = eigdList.stream().filter(p -> p.getBussNum().equals(b.getLoanNo())).findFirst().get();
            if (StringUtils.equals(newFive, dto.getFiveCate())) {
                b.setFiveCateDate(dto.getFiveCateAdjDate());
            } else {
                b.setFiveCateDate(date);
            }
        });
        return balUpdate;
    }

    @Override
    public List<EntGuaranteeGetDataEntity> getEntGuarantee(List<EntLoanUnclearEntity> list) {
        return baseDao.getEntGuarantee(list);
    }

    @Override
    public List<EntLoanUnclearEntity> getEntLoanOfUnclear() {
        return baseDao.getEntLoanOfUnclear();
    }

    /**
     * 获取五级分类调整的保证贷款在保信息
     *
     * @param tranDate
     * @return
     */
    @Override
    public List<EntLoanUnclearEntity> getEntGuarInfoOfFiveCate(String tranDate) {
        //获取未结清本金的担保贷款还款计划
        List<EntLoanUnclearEntity> retList = baseDao.getCorpGuarOfRetUnclear(tranDate);
        if (retList == null || retList.size() == 0) {
            return null;
        }
        Date date = DateUtils.parse(tranDate, "yyyy-MM-dd");
        List<DictData> dictList = sddSvc.getDataByType("FIVE_CATE_DAY");
        Map<String, List<EntLoanUnclearEntity>> retMap = retList.stream().collect(Collectors.groupingBy(EntLoanUnclearEntity::getLoanNo));

        List<String> loanList = retList.stream().map(EntLoanUnclearEntity::getLoanNo).collect(Collectors.toList());
        List<EntLoanUnclearEntity> fiveCateL = baseDao.getEntLoanInfo(loanList);

        List<EntLoanUnclearEntity> fiveCateList = new ArrayList<>();
        fiveCateL.forEach(f -> {
            Date lastDate = retMap.get(f.getLoanNo()).stream().min((a, b) -> a.getDueDate().compareTo(b.getDueDate())).get().getDueDate();

            //最小的到期日与当前日期的间隔天数等于五级分类认定的开始天数时,认为当天五级分类做了调整
            int dayBtwTwoDate = DateUtils.getDistanceOfTwoDay(lastDate, date);
            if (dictList.stream().filter(d -> Integer.parseInt(d.getDictValue().split("-")[0]) == dayBtwTwoDate).count() > 0) {
                f.setFiveFlag(CstmInfoExch.fiveCate(dictList, lastDate, date, f.getBgnintDate()).get("five").toString());
                f.setFiveCateDate(date);
                fiveCateList.add(f);
            }
        });
        return fiveCateList;
    }

    /**
     * 获取指定担保借据的还款计划
     *
     * @param loanList
     * @return
     */
    @Override
    public List<EntLoanUnclearEntity> getCorpGuarRetByLoanNo(List<String> loanList) {
        return baseDao.getCorpGuarRetByLoanNo(loanList);
    }

    @Override
    public List<EntLoanUnclearEntity> getEntGuarOpenInCurrentDayBetweenTwoDate(String bgnDate, String endDate) {
        return baseDao.getEntGuarOpenInCurrentDayBetweenTwoDate(bgnDate,endDate);
    }

    @Override
    public List<EntLoanUnclearEntity> getEntGuarCloseInCurrentDayBetweenTwoDate(String bgnDate, String endDate) {
        return baseDao.getEntGuarCloseInCurrentDayBetweenTwoDate(bgnDate,endDate);
    }

    @Override
    public List<EntLoanUnclearEntity> getEntGuarOfBalUpdateBetweenTwoDate(String bgnDate, String endDate) {
        return null;
    }

    @Override
    public List<EntLoanUnclearEntity> getEntGuarInfoOfFiveCateBetweenTwoDate(String bgnDate, String endDate) {
        return null;
    }
}
