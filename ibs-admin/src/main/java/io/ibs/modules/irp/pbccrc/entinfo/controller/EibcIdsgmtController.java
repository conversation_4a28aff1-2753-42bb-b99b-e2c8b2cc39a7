package io.ibs.modules.irp.pbccrc.entinfo.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.pbccrc.entinfo.dto.EibcIdsgmtDTO;
import io.ibs.modules.irp.pbccrc.entinfo.excel.EibcIdsgmtExcel;
import io.ibs.modules.irp.pbccrc.entinfo.service.EibcIdsgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 基本信息-其他标识段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@RestController
@RequestMapping("entinfo/eibcidsgmt")
@Api(tags = "基本信息-其他标识段")
public class EibcIdsgmtController {
    @Autowired
    private EibcIdsgmtService eibcIdsgmtService;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("entinfo:eibcidsgmt:page")
    public Result<PageData<EibcIdsgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<EibcIdsgmtDTO> page = eibcIdsgmtService.page(params);

        return new Result<PageData<EibcIdsgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("entinfo:eibcidsgmt:info")
    public Result<EibcIdsgmtDTO> get(@PathVariable("id") Long id) {
        EibcIdsgmtDTO data = eibcIdsgmtService.get(id);

        return new Result<EibcIdsgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("entinfo:eibcidsgmt:save")
    public Result save(@RequestBody EibcIdsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        eibcIdsgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("entinfo:eibcidsgmt:update")
    public Result update(@RequestBody EibcIdsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        eibcIdsgmtService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("entinfo:eibcidsgmt:delete")
    public Result delete(@RequestBody Long[] ids) {
        // 效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eibcIdsgmtService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("entinfo:eibcidsgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EibcIdsgmtDTO> list = eibcIdsgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "基本信息-其他标识段", list, EibcIdsgmtExcel.class);
    }

    @GetMapping("list")
    @ApiOperation("列表")
    @RequiresPermissions("entinfo:eibcidsgmt:list")
    public Result<List<EibcIdsgmtDTO>> list(@RequestParam Map<String, Object> params) {
        List<EibcIdsgmtDTO> list = eibcIdsgmtService.list(params);

        return new Result<List<EibcIdsgmtDTO>>().ok(list);
    }

}