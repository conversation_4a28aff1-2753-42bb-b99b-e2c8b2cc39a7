package io.ibs.modules.irp.pbccrc.entinfo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 基本信息-基本概况信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "基本信息-基本概况信息段")
public class EibdFcsinfsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "国别代码")
    private String nationality;
    @ApiModelProperty(value = "登记地址")
    private String regAdd;
    @ApiModelProperty(value = "登记地行政区划代码")
    private String admDivOfReg;
    @ApiModelProperty(value = "成立日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date establishDate;
    @ApiModelProperty(value = "营业许可证到期日")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bizEndDate;
    @ApiModelProperty(value = "业务范围")
    private String bizRange;
    @ApiModelProperty(value = "行业分类代码")
    private String ecoIndusCate;
    @ApiModelProperty(value = "经济类型代码")
    private String ecoType;
    @ApiModelProperty(value = "企业规模")
    private String entScale;
    @ApiModelProperty(value = "客户号")
    private String custId;

}