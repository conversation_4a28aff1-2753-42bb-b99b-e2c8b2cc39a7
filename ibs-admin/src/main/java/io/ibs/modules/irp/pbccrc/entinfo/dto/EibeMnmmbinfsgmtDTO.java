package io.ibs.modules.irp.pbccrc.entinfo.dto;

import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 基本信息-主要组成人员段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "基本信息-主要组成人员段")
public class EibeMnmmbinfsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组成人员姓名")
    private String mmbAlias;
    @ApiModelProperty(value = "组成人员证件类型")
    private String mmbIdType;
    @ApiModelProperty(value = "组成人员证件号码")
    private String mmbIdNum;
    @ApiModelProperty(value = "组成人员职位")
    private String mmbPstn;
    @ApiModelProperty(value = "客户号")
    private String custId;

}