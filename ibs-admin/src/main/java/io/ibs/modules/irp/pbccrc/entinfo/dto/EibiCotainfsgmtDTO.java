package io.ibs.modules.irp.pbccrc.entinfo.dto;

import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 基本信息-联系方式段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "基本信息-联系方式段")
public class EibiCotainfsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "联系地址行政区划代码")
    private String conAddDistrictCode;
    @ApiModelProperty(value = "联系地址")
    private String conAdd;
    @ApiModelProperty(value = "联系电话")
    private String conPhone;
    @ApiModelProperty(value = "财务部门联系电话")
    private String finConPhone;
    @ApiModelProperty(value = "客户号")
    private String custId;

}