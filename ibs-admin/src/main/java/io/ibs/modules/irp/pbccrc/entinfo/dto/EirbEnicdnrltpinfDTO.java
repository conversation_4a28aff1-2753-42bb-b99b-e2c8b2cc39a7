package io.ibs.modules.irp.pbccrc.entinfo.dto;

import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 基本信息-企业间关联关系记录
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel(value = "基本信息-企业间关联关系记录")
public class EirbEnicdnrltpinfDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "A 企业名称")
    private String entName;
    @ApiModelProperty(value = "A 企业身份标识类型")
    private String entCertType;
    @ApiModelProperty(value = "A 企业身份标识号码")
    private String entCertNum;
    @ApiModelProperty(value = "B 企业名称")
    private String assoEntName;
    @ApiModelProperty(value = "B 企业身份标识类型")
    private String assoEntCertType;
    @ApiModelProperty(value = "B 企业身份标识号码")
    private String assoEntCertNum;
    @ApiModelProperty(value = "关联关系类型")
    private String assoType;
    @ApiModelProperty(value = "关联有效标志")
    private String assoSign;
    @ApiModelProperty(value = "客户号")
    private String custId;

}