package io.ibs.modules.irp.pbccrc.entinfo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import io.ibs.common.entity.BaseEntity;

/**
 * 基本信息-基本概况信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("EIBD_FCSINFSGMT")
public class AsyEibdFcsinfsgmtEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 国别代码
	*/
	private String nationality;
	/**
	* 登记地址
	*/
	private String regAdd;
	/**
	* 登记地行政区划代码
	*/
	private String admDivOfReg;
	/**
	* 成立日期
	*/
	private Date establishDate;
	/**
	* 营业许可证到期日
	*/
	private Date bizEndDate;
	/**
	* 业务范围
	*/
	private String bizRange;
	/**
	* 行业分类代码
	*/
	private String ecoIndusCate;
	/**
	* 经济类型代码
	*/
	private String ecoType;
	/**
	* 企业规模
	*/
	private String entScale;
	/**
	* 客户号
	*/
	private String custId;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务在原系统发生日期
	*/
	private Date bussDate;
	/**
	* 业务发生日期（供数据导入使用）
	*/
	private String itabGetDate;
}