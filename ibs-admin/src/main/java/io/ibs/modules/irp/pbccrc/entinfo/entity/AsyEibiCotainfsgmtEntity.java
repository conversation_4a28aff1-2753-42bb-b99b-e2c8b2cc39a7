package io.ibs.modules.irp.pbccrc.entinfo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import io.ibs.common.entity.BaseEntity;

/**
 * 基本信息-联系方式段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("EIBI_COTAINFSGMT")
public class AsyEibiCotainfsgmtEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 联系地址行政区划代码
	*/
	private String conAddDistrictCode;
	/**
	* 联系地址
	*/
	private String conAdd;
	/**
	* 联系电话
	*/
	private String conPhone;
	/**
	* 财务部门联系电话
	*/
	private String finConPhone;
	/**
	* 客户号
	*/
	private String custId;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务在原系统发生日期
	*/
	private Date bussDate;
	/**
	* 业务发生日期
	*/
	private String itabGetDate;
}