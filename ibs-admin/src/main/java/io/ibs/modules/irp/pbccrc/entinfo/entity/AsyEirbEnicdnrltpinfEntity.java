package io.ibs.modules.irp.pbccrc.entinfo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import io.ibs.common.entity.BaseEntity;

/**
 * 基本信息-企业间关联关系记录
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("EIRB_ENICDNRLTPINF")
public class AsyEirbEnicdnrltpinfEntity {
	private static final long serialVersionUID = 1L;

	/**
	* A 企业名称
	*/
	private String entName;
	/**
	* A 企业身份标识类型
	*/
	private String entCertType;
	/**
	* A 企业身份标识号码
	*/
	private String entCertNum;
	/**
	* B 企业名称
	*/
	private String assoEntName;
	/**
	* B 企业身份标识类型
	*/
	private String assoEntCertType;
	/**
	* B 企业身份标识号码
	*/
	private String assoEntCertNum;
	/**
	* 关联关系类型
	*/
	private String assoType;
	/**
	* 关联有效标志
	*/
	private String assoSign;
	/**
	* 客户号
	*/
	private String custId;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务在原系统发生日期
	*/
	private Date bussDate;
	/**
	* 业务发生日期
	*/
	private String itabGetDate;
}