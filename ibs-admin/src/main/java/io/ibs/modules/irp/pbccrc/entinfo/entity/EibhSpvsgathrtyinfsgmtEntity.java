package io.ibs.modules.irp.pbccrc.entinfo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 基本信息-上级机构段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("EIBH_SPVSGATHRTYINFSGMT")
public class EibhSpvsgathrtyinfsgmtEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 上级机构类型
     */
    private String supOrgType;
    /**
     * 上级机构名称
     */
    private String supOrgName;
    /**
     * 上级机构身份标识类型
     */
    private String supOrgCertType;
    /**
     * 上级机构身份标识码
     */
    private String supOrgCertNum;
    /**
     * 客户号
     */
    private String custId;
    /**
     * 内部机构代码
     */
    private String deptCode;
    /**
     * 业务在原系统发生日期
     */
    private Date bussDate;
    /**
     * 业务发生日期
     */
    private String itabGetDate;
    @TableId
    private Long id;
    private Long creator;
    private Date createDate;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;
}