package io.ibs.modules.irp.pbccrc.entinfo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 基本信息-身份标识整合记录
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("EIIB_ENCTFITGINF")
public class EiibEnctfitginfEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 企业名称
     */
    private String entName;
    /**
     * 企业身份标识类型
     */
    private String entCertType;
    /**
     * 企业身份标识号码
     */
    private String entCertNum;
    /**
     * 企业其他身份标识类型
     */
    private String othEntCertType;
    /**
     * 企业其他身份标识号码
     */
    private String othEntCertNum;
    /**
     * 身份标识关系有效标志
     */
    private String certAssFlg;
    /**
     * 客户号
     */
    private String custId;
    /**
     * 内部机构代码
     */
    private String deptCode;
    /**
     * 业务在原系统发生日期
     */
    private Date bussDate;
    /**
     * 业务发生日期
     */
    private String itabGetDate;
    @TableId
    private Long id;
    private Long creator;
    private Date createDate;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;
}