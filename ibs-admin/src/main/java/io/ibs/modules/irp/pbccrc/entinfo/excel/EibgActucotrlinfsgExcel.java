package io.ibs.modules.irp.pbccrc.entinfo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 基本信息-实际控制人段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EibgActucotrlinfsgExcel {
    @ExcelProperty(value = "实际控制人身份类别", index = 0)
    private String actuCotrlIdType;
    @ExcelProperty(value = "实际控制人名称", index = 1)
    private String actuCotrlName;
    @ExcelProperty(value = "实际控制人身份标识类型", index = 2)
    private String actuCotrlCertIdType;
    @ExcelProperty(value = "实际控制人身份标识号码", index = 3)
    private String actuCotrlIdNum;
    @ExcelProperty(value = "客户号", index = 4)
    private String custId;
    @ExcelProperty(value = "内部机构代码", index = 5)
    private String deptCode;
    @ExcelProperty(value = "业务在原系统发生日期", index = 6)
    private Date bussDate;
}