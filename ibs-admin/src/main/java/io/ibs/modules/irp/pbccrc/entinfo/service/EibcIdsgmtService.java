package io.ibs.modules.irp.pbccrc.entinfo.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.entinfo.dto.EibcIdsgmtDTO;
import io.ibs.modules.irp.pbccrc.entinfo.entity.EibcIdsgmtEntity;

import java.util.List;

/**
 * 基本信息-其他标识段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
public interface EibcIdsgmtService extends CrudService<EibcIdsgmtEntity, EibcIdsgmtDTO> {

    void ays2Up(List<EibcIdsgmtDTO> ids);

    EibcIdsgmtDTO getByCustId(String id);
}