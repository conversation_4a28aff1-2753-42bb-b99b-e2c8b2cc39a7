package io.ibs.modules.irp.pbccrc.entinfo.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.entinfo.dto.EibfRegCapDTO;
import io.ibs.modules.irp.pbccrc.entinfo.entity.EibfRegCapEntity;

import java.util.List;

/**
 * 基本信息-注册资本段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-18
 */
public interface EibfRegCapService extends CrudService<EibfRegCapEntity, EibfRegCapDTO> {

    void ays2Up(List<EibfRegCapDTO> ids);

    EibfRegCapDTO getByCustId(String id);
}