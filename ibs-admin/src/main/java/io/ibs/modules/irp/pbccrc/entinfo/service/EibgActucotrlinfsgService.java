package io.ibs.modules.irp.pbccrc.entinfo.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.entinfo.dto.EibgActucotrlinfsgDTO;
import io.ibs.modules.irp.pbccrc.entinfo.entity.EibgActucotrlinfsgEntity;

import java.util.List;

/**
 * 基本信息-实际控制人段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
public interface EibgActucotrlinfsgService extends CrudService<EibgActucotrlinfsgEntity, EibgActucotrlinfsgDTO> {

    void ays2Up(List<EibgActucotrlinfsgDTO> ids);

    EibgActucotrlinfsgDTO getByCustId(String custId);
}