package io.ibs.modules.irp.pbccrc.entinfo.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.entinfo.dto.EibhSpvsgathrtyinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.entinfo.entity.EibhSpvsgathrtyinfsgmtEntity;

import java.util.List;

/**
 * 基本信息-上级机构段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-16
 */
public interface EibhSpvsgathrtyinfsgmtService extends CrudService<EibhSpvsgathrtyinfsgmtEntity, EibhSpvsgathrtyinfsgmtDTO> {

    void ays2Up(List<EibhSpvsgathrtyinfsgmtDTO> ids);

    EibhSpvsgathrtyinfsgmtDTO getByCustId(String custId);
}