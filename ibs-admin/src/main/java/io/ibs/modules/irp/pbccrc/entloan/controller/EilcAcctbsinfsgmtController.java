package io.ibs.modules.irp.pbccrc.entloan.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.pbccrc.entloan.dto.EilcAcctbsinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.entloan.excel.EilcAcctbsinfsgmtExcel;
import io.ibs.modules.irp.pbccrc.entloan.service.EilcAcctbsinfsgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 借贷-基本信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@RestController
@RequestMapping("entloan/eilcacctbsinfsgmt")
@Api(tags = "借贷-基本信息段")
public class EilcAcctbsinfsgmtController {
    @Autowired
    private EilcAcctbsinfsgmtService eilcAcctbsinfsgmtService;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("entloan:eilcacctbsinfsgmt:page")
    public Result<PageData<EilcAcctbsinfsgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<EilcAcctbsinfsgmtDTO> page = eilcAcctbsinfsgmtService.page(params);

        return new Result<PageData<EilcAcctbsinfsgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("entloan:eilcacctbsinfsgmt:info")
    public Result<EilcAcctbsinfsgmtDTO> get(@PathVariable("id") Long id) {
        EilcAcctbsinfsgmtDTO data = eilcAcctbsinfsgmtService.get(id);

        return new Result<EilcAcctbsinfsgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("entloan:eilcacctbsinfsgmt:save")
    public Result save(@RequestBody EilcAcctbsinfsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        eilcAcctbsinfsgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("entloan:eilcacctbsinfsgmt:update")
    public Result update(@RequestBody EilcAcctbsinfsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        eilcAcctbsinfsgmtService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("entloan:eilcacctbsinfsgmt:delete")
    public Result delete(@RequestBody Long[] ids) {
        // 效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        eilcAcctbsinfsgmtService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("entloan:eilcacctbsinfsgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<EilcAcctbsinfsgmtDTO> list = eilcAcctbsinfsgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "借贷-基本信息段", list, EilcAcctbsinfsgmtExcel.class);
    }

    @GetMapping("list")
    @ApiOperation("列表")
    @RequiresPermissions("entloan:eilcacctbsinfsgmt:list")
    public Result<List<EilcAcctbsinfsgmtDTO>> list(@RequestParam Map<String, Object> params) {
        List<EilcAcctbsinfsgmtDTO> list = eilcAcctbsinfsgmtService.list(params);

        return new Result<List<EilcAcctbsinfsgmtDTO>>().ok(list);
    }

}