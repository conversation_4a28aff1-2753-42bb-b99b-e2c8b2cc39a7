package io.ibs.modules.irp.pbccrc.entloan.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import io.ibs.common.entity.BaseEntity;

/**
 * 借贷-授信额度信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("EILF_ACCTCREDSGMT")
public class AsyEilfAcctcredsgmtEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 授信协议业务号
	*/
	private String mccBussNum;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务发生日期
	*/
	private Date bussDate;
	/**
	* 业务号
	*/
	private String bussNum;
	/**
	* 原系统业务号
	*/
	private String nativeBussNum;
	/**
	* 业务发生日期
	*/
	private String itabGetDate;
}