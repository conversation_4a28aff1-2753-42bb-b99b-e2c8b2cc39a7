package io.ibs.modules.irp.pbccrc.entloan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 借贷-初始债权说明段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-31
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("EILG_ORIGCREDITORINF")
public class AsyEilgOrigcreditorinfEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 业务号
	*/
	private String bussNum;
	/**
	* 初始债权人名称
	*/
	private String initCredName;
	/**
	* 初始债权人机构代码
	*/
	private Long initCedOrgCode;
	/**
	* 原债务种类
	*/
	private String origDbtCate;
	/**
	* 债权转移时的还款状态
	*/
	private String initRpySts;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务发生日期
	*/
	private Date bussDate;
	/**
	* 原系统业务号
	*/
	private String nativeBussNum;
	/**
	* 业务发生日期（供数据导入使用）
	*/
	private String itabGetDate;

}