package io.ibs.modules.irp.pbccrc.entloan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 借贷-抵质押物信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("EILE_MOTGACLTALCTRCTINF")
public class EileMotgacltalctrctinfEntity extends EILBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 抵(质)押合同号
     */
    private String ccBussNum;

}