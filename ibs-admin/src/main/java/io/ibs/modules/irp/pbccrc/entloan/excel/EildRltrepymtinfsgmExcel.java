package io.ibs.modules.irp.pbccrc.entloan.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 借贷-相关还款责任人信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-31
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EildRltrepymtinfsgmExcel {
    @ExcelProperty(value = "身份类别", index = 0)
    private String arlpIdType;
    @ExcelProperty(value = "责任人名称", index = 1)
    private String arlpName;
    @ExcelProperty(value = "责任人身份标识类型", index = 2)
    private String arlpCertType;
    @ExcelProperty(value = "责任人身份标识号码", index = 3)
    private String arlpCertNum;
    @ExcelProperty(value = "还款责任人类型", index = 4)
    private String arlpType;
    @ExcelProperty(value = "还款责任金额", index = 5)
    private BigDecimal arlpAmt;
    @ExcelProperty(value = "联保标志", index = 6)
    private String wartySign;
    @ExcelProperty(value = "保证合同业务号", index = 7)
    private String maxBussNum;
    @ExcelProperty(value = "内部机构代码", index = 8)
    private String deptCode;
    @ExcelProperty(value = "业务发生日期", index = 9)
    private Date bussDate;
    @ExcelProperty(value = "业务号", index = 10)
    private String bussNum;
}