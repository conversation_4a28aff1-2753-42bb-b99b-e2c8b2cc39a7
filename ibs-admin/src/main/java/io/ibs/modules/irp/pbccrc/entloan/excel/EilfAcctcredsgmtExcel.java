package io.ibs.modules.irp.pbccrc.entloan.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 借贷-授信额度信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class EilfAcctcredsgmtExcel {
    @ExcelProperty(value = "授信协议业务号", index = 0)
    private String mccBussNum;
    @ExcelProperty(value = "内部机构代码", index = 1)
    private String deptCode;
    @ExcelProperty(value = "业务发生日期", index = 2)
    private Date bussDate;
    @ExcelProperty(value = "业务号", index = 3)
    private String bussNum;
}