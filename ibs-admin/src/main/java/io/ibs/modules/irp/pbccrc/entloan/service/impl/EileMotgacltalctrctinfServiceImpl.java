package io.ibs.modules.irp.pbccrc.entloan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.pbccrc.entloan.dao.EileMotgacltalctrctinfDao;
import io.ibs.modules.irp.pbccrc.entloan.dto.EileMotgacltalctrctinfDTO;
import io.ibs.modules.irp.pbccrc.entloan.entity.EileMotgacltalctrctinfEntity;
import io.ibs.modules.irp.pbccrc.entloan.service.EileMotgacltalctrctinfService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 借贷-抵质押物信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Service
public class EileMotgacltalctrctinfServiceImpl extends CrudServiceImpl<EileMotgacltalctrctinfDao, EileMotgacltalctrctinfEntity, EileMotgacltalctrctinfDTO> implements EileMotgacltalctrctinfService {

    @Override
    public QueryWrapper<EileMotgacltalctrctinfEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<EileMotgacltalctrctinfEntity> wrapper = new QueryWrapper<>();

        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);

        return wrapper;
    }

    @Override
    public EileMotgacltalctrctinfDTO getByBussNum(String bussNum) {
        QueryWrapper<EileMotgacltalctrctinfEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(String.valueOf(bussNum)), "BUSS_NUM", bussNum);
        EileMotgacltalctrctinfEntity motgacltalctrctinfEntity = baseDao.selectOne(wrapper);
        return ConvertUtils.sourceToTarget(motgacltalctrctinfEntity, EileMotgacltalctrctinfDTO.class);
    }
}