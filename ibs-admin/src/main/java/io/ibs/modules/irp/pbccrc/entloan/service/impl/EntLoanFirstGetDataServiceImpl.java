package io.ibs.modules.irp.pbccrc.entloan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.irp.pbccrc.entloan.dao.EntLoanFirstGetDataDao;
import io.ibs.modules.irp.pbccrc.entloan.entity.*;
import io.ibs.modules.irp.pbccrc.entloan.service.EntLoanFirstGetDataService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 获取五年内结清的企业贷款
 *
 * <AUTHOR>
 * @since 20220908
 */
@Service
public class EntLoanFirstGetDataServiceImpl extends CrudServiceImpl<EntLoanFirstGetDataDao, EntLoanInfoEntity, EntLoanInfoEntity> implements EntLoanFirstGetDataService {

    @Override
    public QueryWrapper<EntLoanInfoEntity> getWrapper(Map<String, Object> params) {
        return null;
    }

    /**
     * 五年内结清的贷款
     *
     * @param fiveYearDate  五年前当月第一天
     * @param lastMontnDate 当月的上个月最后一天
     * @return 五年内结清贷款的开贷结清信息
     */
    @Override
    public List<EntLoanInfoEntity> getClearCorpLoanBeforeFiveYearsAgo(String fiveYearDate, String lastMontnDate) {
        return baseDao.getClearCorpLoanBeforeFiveYearsAgo(fiveYearDate, lastMontnDate);
    }

    /**
     * 指定日期前未结清的贷款
     *
     * @param tranDate 指定日期
     * @return 指定日期前未结清的贷款
     */
    @Override
    public List<EntLoanInfoEntity> getCorpLoanOfUnclearInFirst(String tranDate) {
        return baseDao.getCorpLoanOfUnclearInFirst(tranDate);
    }

    /**
     * 指定借据的全部分期计划
     *
     * @param list 借据列表
     * @return 借据的分期计划
     */
    @Override
    public List<EntLoanRetEntity> getClearCorpLoanOfRet(List<String> list) {
        return baseDao.getClearCorpLoanOfRet(list);
    }

    /**
     * 指定借据的利息清单
     *
     * @param list 借据列表
     * @return 借据的利息清单
     */
    @Override
    public List<EntLoanIntEntity> getClearCorpLoanOfIntr(List<String> list) {
        return baseDao.getClearCorpLoanOfIntr(list);
    }

    /**
     * 获取指定借据的还本明细
     *
     * @param list 借据列表
     * @return 还本明细
     */
    @Override
    public List<EntLoanDtlEntity> getClearCorpLoanOfDtl(List<String> list) {
        return baseDao.getClearCorpLoanOfDtl(list);
    }

    /**
     * 获取指定借据的还息明细
     *
     * @param list 借据列表
     * @return 还息明细
     */
    @Override
    public List<EntLoanDtlEntity> getClearCorpLoanOfIntDtl(List<String> list) {
        return baseDao.getClearCorpLoanOfIntDtl(list);
    }

    /**
     * 获取保证贷款的相关还款责任人
     *
     * @param list 借据列表
     * @return 相关还款责任人
     */
    @Override
    public List<EntLoanGuarEntity> getClearCorpLoanOfGuar(List<String> list) {
        return baseDao.getClearCorpLoanOfGuar(list);
    }

    /**
     * 发生过展期的借据
     *
     * @param list 借据列表
     * @return 发生过展期的借据及展期时间
     */
    @Override
    public List<Map<String, Object>> getClearCorpLoanNoOfExt(List<String> list) {
        return baseDao.getClearCorpLoanNoOfExt(list);
    }

    /**
     * 发生日期五级分类调整的借据
     *
     * @param list 借据列表
     * @return 发生日期五级分类调整的借据及时间
     */
    @Override
    public List<Map<String, Object>> getClearCorpLoanOfFiveCate(List<String> list) {
        return baseDao.getClearCorpLoanOfFiveCate(list);
    }

    /**
     * 贷款核销
     *
     * @param list 借据列表
     * @return 核销借据的信息
     */
    @Override
    public List<EntLoanGetDataEntity> getClearCorpLoanOfCav(List<String> list) {
        return baseDao.getClearCorpLoanOfCav(list);
    }
}
