package io.ibs.modules.irp.pbccrc.entrep.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业财务报表-企业资产负债表信息记录2007版-接口表
 *
 * <AUTHOR>
 * @since 3.0 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("EIOD_2007BALANCESHEET")
public class AsyEiod2007BalanceSheetEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 内部机构代码
     */
    private String deptCode;
    /**
     * 业务发生日期
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date bussDate;
    /**
     * 信息报告日期
     */
    private Date rptDate;
    /**
     * 客户号
     */
    private String custId;
    /**
     * 报表版本
     */
    private Integer sheetVersion;
    /**
     * 信息记录类型
     */
    private String infRecType;
    /**
     * 企业名称
     */
    private String entName;
    /**
     * 企业身份标识类型
     */
    private String entCertType;
    /**
     * 企业身份标识号码
     */
    private String entCertNum;
    /**
     * 报表年份
     */
    private Integer sheetYear;
    /**
     * 报表类型
     */
    private String sheetType;
    /**
     * 报表类型细分
     */
    private String sheetTypeDivide;
    /**
     * 审计事务所名称
     */
    private String auditFirmName;
    /**
     * 审计人员名称
     */
    private String auditorName;
    /**
     * 审计时间
     */
    private Date auditTime;
    /**
     * 报告时点说明代码
     */
    private String rptDateCode;
    /**
     * 货币资金
     */
    private BigDecimal curFun;
    /**
     * 交易性金融资产
     */
    private BigDecimal finAssHft;
    /**
     * 应收票据
     */
    private BigDecimal notRec;
    /**
     * 应收账款
     */
    private BigDecimal accRec;
    /**
     * 预付账款
     */
    private BigDecimal prepayments;
    /**
     * 应收利息
     */
    private BigDecimal intRec;
    /**
     * 应收股利
     */
    private BigDecimal divRec;
    /**
     * 其他应收款
     */
    private BigDecimal othRec;
    /**
     * 存货
     */
    private BigDecimal inventories;
    /**
     * 一年内到期的非流动资产
     */
    private BigDecimal curPorOnca;
    /**
     * 其他流动资产
     */
    @TableField(value = "OTH__CUR_ASS")
    private BigDecimal othCurAss;
    /**
     * 流动资产合计
     */
    private BigDecimal totCurAss;
    /**
     * 可供出售的金融资产
     */
    private BigDecimal finAssAfs;
    /**
     * 持有至到期投资
     */
    private BigDecimal helToMi;
    /**
     * 长期股权投资
     */
    private BigDecimal lonTerEi;
    /**
     * 长期应收款
     */
    private BigDecimal lonTerRec;
    /**
     * 投资性房地产
     */
    private BigDecimal invPro;
    /**
     * 固定资产
     */
    private BigDecimal fixAss;
    /**
     * 在建工程
     */
    private BigDecimal conInPro;
    /**
     * 工程物资
     */
    private BigDecimal conMat;
    /**
     * 固定资产清理
     */
    private BigDecimal fixAssPfd;
    /**
     * 生产性生物资产
     */
    private BigDecimal nonCurBa;
    /**
     * 油气资产
     */
    private BigDecimal oilAndGa;
    /**
     * 无形资产
     */
    private BigDecimal intAss;
    /**
     * 开发支出
     */
    private BigDecimal devDis;
    /**
     * 商誉
     */
    private BigDecimal goodwill;
    /**
     * 长期待摊费用
     */
    private BigDecimal lonTerDe;
    /**
     * 递延所得税资产
     */
    private BigDecimal defTaxAss;
    /**
     * 其他非流动资产
     */
    private BigDecimal othNonCa;
    /**
     * 非流动资产合计
     */
    private BigDecimal totNonCa;
    /**
     * 资产总计
     */
    private BigDecimal totAss;
    /**
     * 短期借款
     */
    private BigDecimal shoTerBor;
    /**
     * 交易性金融负债
     */
    private BigDecimal finLiaHft;
    /**
     * 应付票据
     */
    private BigDecimal notPay;
    /**
     * 应付账款
     */
    private BigDecimal accPay;
    /**
     * 预收账款
     */
    private BigDecimal recInAdv;
    /**
     * 应付利息
     */
    private BigDecimal intPay;
    /**
     * 应付职工薪酬
     */
    private BigDecimal empBenPay;
    /**
     * 应交税费
     */
    private BigDecimal taxPay;
    /**
     * 应付股利
     */
    private BigDecimal divPay;
    /**
     * 其他应付款
     */
    private BigDecimal othPay;
    /**
     * 一年内到期的非流动负债
     */
    private BigDecimal curPorOltl;
    /**
     * 其他流动负债
     */
    private BigDecimal othCurLia;
    /**
     * 流动负债合计
     */
    private BigDecimal totCurLia;
    /**
     * 长期借款
     */
    private BigDecimal lonTerBor;
    /**
     * 应付债券
     */
    private BigDecimal bonPay;
    /**
     * 长期应付款
     */
    private BigDecimal lonTerPay;
    /**
     * 专项应付款
     */
    private BigDecimal graPay;
    /**
     * 预计负债
     */
    private BigDecimal provisions;
    /**
     * 递延所得税负债
     */
    private BigDecimal defTaxLia;
    /**
     * 其他非流动负债
     */
    private BigDecimal othNonCl;
    /**
     * 非流动负债合计
     */
    private BigDecimal totNonCl;
    /**
     * 负债合计
     */
    private BigDecimal totLia;
    /**
     * 实收资本（或股本）
     */
    private BigDecimal paiInCosc;
    /**
     * 资本公积
     */
    private BigDecimal capRse;
    /**
     * 减：库存股
     */
    private BigDecimal lesTreSto;
    /**
     * 盈余公积
     */
    private BigDecimal surRes;
    /**
     * 未分配利润
     */
    private BigDecimal unaPro;
    /**
     * 所有者权益合计
     */
    private BigDecimal totEqu;
    /**
     * 负债和所有者权益合计
     */
    private BigDecimal totEquAl;
    /**
     * 业务发生日期（供数据导入使用），格式：年月日。如20181121
     */
    @TableField(fill = FieldFill.UPDATE)
    private String itabGetDate;
    /**
     * 扩展字段2
     */
    private String ex2;
    /**
     * 扩展字段3
     */
    private String ex3;
    /**
     * 扩展字段1
     */
    private String ex1;
}