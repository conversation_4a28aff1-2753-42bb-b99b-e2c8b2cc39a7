package io.ibs.modules.irp.pbccrc.entrep.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.entrep.dto.Eisd2007ispaDTO;
import io.ibs.modules.irp.pbccrc.entrep.dto.EntrepCommonDTO;
import io.ibs.modules.irp.pbccrc.entrep.entity.AsyEisd2007ispaEntity;
import io.ibs.modules.irp.pbccrc.entrep.entity.Eisd2007ispaEntity;

import java.util.Date;
import java.util.List;

/**
 * 企业财务报表-企业利润及利润分配表信息记录2007版-接口表
 *
 * <AUTHOR> <PERSON>@gmail.com
 * @since 3.0 2023-07-05
 */
public interface Eisd2007ispaService extends CrudService<Eisd2007ispaEntity, Eisd2007ispaDTO> {

    /**
     * 根据 客户号、报表年份、报表类型获取信息
     *
     * @param dto 查询参数
     * @return
     */
    Eisd2007ispaDTO getInfo(EntrepCommonDTO dto);

    /**
     * 查询待报送的记录
     *
     * @param between   时间范围
     * @return list
     */
    List<AsyEisd2007ispaEntity> getReportList(Date[] between);
}