package io.ibs.modules.irp.pbccrc.entrep.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.pbccrc.entrep.dao.Eiod2007BalanceSheetDao;
import io.ibs.modules.irp.pbccrc.entrep.dto.Eiod2007BalanceSheetDTO;
import io.ibs.modules.irp.pbccrc.entrep.dto.EntrepCommonDTO;
import io.ibs.modules.irp.pbccrc.entrep.entity.AsyEiod2007BalanceSheetEntity;
import io.ibs.modules.irp.pbccrc.entrep.entity.Eiod2007BalanceSheetEntity;
import io.ibs.modules.irp.pbccrc.entrep.service.Eiod2007BalanceSheetService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 企业财务报表-企业资产负债表信息记录2007版-接口表
 *
 * <AUTHOR>
 * @since 3.0 2023-07-05
 */
@Service
public class Eiod2007BalanceSheetServiceImpl extends CrudServiceImpl<Eiod2007BalanceSheetDao, Eiod2007BalanceSheetEntity, Eiod2007BalanceSheetDTO> implements Eiod2007BalanceSheetService {

    @Override
    public QueryWrapper<Eiod2007BalanceSheetEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<Eiod2007BalanceSheetEntity> wrapper = new QueryWrapper<>();

        String custId = (String) params.get("custId");
        wrapper.eq(StringUtils.isNotBlank(custId), "CUST_ID", custId);
        String sheetYear = (String) params.get("sheetYear");
        wrapper.eq(StringUtils.isNotBlank(sheetYear), "SHEET_YEAR", sheetYear);
        String sheetType = (String) params.get("sheetType");
        wrapper.eq(StringUtils.isNotBlank(sheetType), "SHEET_TYPE", sheetType);

        return wrapper;
    }

    @Override
    public Eiod2007BalanceSheetDTO getInfo(EntrepCommonDTO dto) {
        QueryWrapper<Eiod2007BalanceSheetEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Eiod2007BalanceSheetEntity::getCustId, dto.getCustId()).
                eq(Eiod2007BalanceSheetEntity::getSheetYear, dto.getSheetYear()).
                eq(Eiod2007BalanceSheetEntity::getSheetType, dto.getSheetType());
        Eiod2007BalanceSheetEntity entity = baseDao.selectOne(wrapper);
        Eiod2007BalanceSheetDTO balanceSheet = new Eiod2007BalanceSheetDTO();
        BeanUtils.copyProperties(entity, balanceSheet);
        return balanceSheet;
    }

    @Override
    public List<AsyEiod2007BalanceSheetEntity> getReportList(Date[] between) {
        QueryWrapper<Eiod2007BalanceSheetEntity> balanceSheetWrapper = new QueryWrapper<>();
        balanceSheetWrapper.
                between("BUSS_DATE", between[0], between[1]).
                eq("NO_RPT", "1");
        List<Eiod2007BalanceSheetEntity> list = baseDao.selectList(balanceSheetWrapper);
        return ConvertUtils.sourceToTarget(list, AsyEiod2007BalanceSheetEntity.class);
    }
}