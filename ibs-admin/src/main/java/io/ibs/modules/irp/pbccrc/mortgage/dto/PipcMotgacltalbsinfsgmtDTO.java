package io.ibs.modules.irp.pbccrc.mortgage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抵质押-基本信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@ApiModel(value = "抵质押-基本信息段")
public class PipcMotgacltalbsinfsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同类型")
    private String guarType;
    @ApiModelProperty(value = "担保金额")
    private BigDecimal ccAmt;
    @ApiModelProperty(value = "币种")
    private String cy;
    @ApiModelProperty(value = "抵(质)押合同生效日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date ccvalDate;
    @ApiModelProperty(value = "抵(质)押合同到期日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date ccexpDate;
    @ApiModelProperty(value = "最高担保标志")
    private String maxGuar;
    @ApiModelProperty(value = "抵(质)押合同状态")
    private String ccStatus;
    @ApiModelProperty(value = "抵质押标志区分个人和企业")
    private String ex1;
    @ApiModelProperty(value = "报告时点说明代码")
    private String rptDateCode;
}