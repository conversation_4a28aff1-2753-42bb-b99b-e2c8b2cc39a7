package io.ibs.modules.irp.pbccrc.mortgage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.ibs.modules.irp.util.FieldUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抵质押-抵押物信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@ApiModel(value = "抵质押-抵押物信息段")
public class PipeMotgaproptinfsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "抵押物种类")
    private String pleType;
    @ApiModelProperty(value = "抵押物识别号类型")
    private String motgaProptIdType;
    @ApiModelProperty(value = "抵押物唯一识别号")
    private String pleCertId;
    @ApiModelProperty(value = "抵押物位置所在地行政区划")
    private String pleDistr;
    @ApiModelProperty(value = "抵押物评估价值")
    private BigDecimal pleValue;
    @ApiModelProperty(value = "评估机构类型")
    private String valOrgType;
    @ApiModelProperty(value = "抵押物评估日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date valDate;
    @ApiModelProperty(value = "抵押人身份类别")
    private String pleDgorType;

    public String getPleDgorName() {
        return FieldUtil.getPleDgorName(pleDgorName);
    }

    public void setPleDgorName(String pleDgorName) {
        this.pleDgorName = FieldUtil.getPleDgorName(pleDgorName);
    }

    @ApiModelProperty(value = "抵押人名称")
    private String pleDgorName;
    @ApiModelProperty(value = "抵押人身份标识类型")
    private String pleorCertType;
    @ApiModelProperty(value = "抵押人身份标识号码")
    private String pleorCertNum;

    public String getPleDesc() {
        return FieldUtil.getPleDesc(pleDesc);
    }

    public void setPleDesc(String pleDesc) {
        this.pleDesc = FieldUtil.getPleDesc(pleDesc);
    }

    @ApiModelProperty(value = "抵押物说明")
    private String pleDesc;
    @ApiModelProperty(value = "币种")
    private String pleCy;
    @ApiModelProperty(value = "抵质押标志区分个人和企业")
    private String ex1;
    @ApiModelProperty(value = "报告时点说明代码")
    private String rptDateCode;
}