package io.ibs.modules.irp.pbccrc.mortgage.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.irp.util.FieldUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 抵质押-基础段信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PIPB_MOTGACLTALCTRCTBSSGMT")
public class AsyPipbMotgacltalctrctbssgmtEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 抵(质)押合同号
     */
    private String bussNum;
    /**
     * 抵质押标志区分个人和企业
     */
    private String ex1;
    /**
     * 债务人身份类别
     */
    private String infOldType;
    @TableId
    private Long id;
    /**
     * 债务人名称
     */
    private String guarName;

    public String getGuarName() {
        return FieldUtil.getGuarName(guarName);
    }

    public void setGuarName(String guarName) {
        this.guarName = FieldUtil.getGuarName(guarName);
    }
    /**
     * 债务人身份标识类型
     */
    private String guarCertType;
    /**
     * 债务人身份标识号码
     */
    private String guarCertNum;
    /**
     * 内部机构代码
     */
    private String deptCode;
    /**
     * 客户号
     */
    private String custId;
    /**
     * 业务发生日期
     */
    private Date bussDate;
    /**
     * 报告时点说明代码 10-合同生效 20-合同到期/失效 30-抵（质）押物变更
     */
    private String rptDateCode;
    /**
     * 业务发生日期（供数据导入使用）
     */
    private String itabGetDate;
}