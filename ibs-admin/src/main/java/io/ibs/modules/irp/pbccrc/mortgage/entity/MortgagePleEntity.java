package io.ibs.modules.irp.pbccrc.mortgage.entity;

import io.ibs.modules.irp.util.FieldUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抵押信息
 *
 * <AUTHOR>
 * @since 20220901
 */
@Data
public class MortgagePleEntity {
    /**
     * 抵(质)押合同号
     */
    private String bussNum;
    /**
     * 抵押物种类
     */
    private String pleType;
    /**
     * 抵押物唯一识别号
     */
    private String pleCertId;
    /**
     * 抵押物评估价值
     */
    private BigDecimal pleValue;
    /**
     * 评估机构类型
     */
    private String valOrgType;
    /**
     * 抵押物评估日期
     */
    private Date valDate;
    /**
     * 抵押人身份类别 1-自然人 2-组织机构
     */
    private String pleDgorType;
    /**
     * 抵押人名称
     */
    private String pleDgorName;
    public String getPleDgorName() {
        return FieldUtil.getPleDgorName(pleDgorName);
    }

    public void setPleDgorName(String pleDgorName) {
        this.pleDgorName = FieldUtil.getPleDgorName(pleDgorName);
    }
    /**
     * 抵押人身份标识号码
     */
    private String pleorCertNum;
    /**
     * 抵押人身份标识类型
     */
    private String pleorCertType;
    /**
     * 抵押物说明
     */
    private String pleDesc;
    public String getPleDesc() {
        return FieldUtil.getPleDesc(pleDesc);
    }

    public void setPleDesc(String pleDesc) {
        this.pleDesc = FieldUtil.getPleDesc(pleDesc);
    }
}
