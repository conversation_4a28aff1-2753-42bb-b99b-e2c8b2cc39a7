package io.ibs.modules.irp.pbccrc.prsncredit.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.pbccrc.prsncredit.dto.PicdCreditlimsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsncredit.excel.PicdCreditlimsgmtExcel;
import io.ibs.modules.irp.pbccrc.prsncredit.service.PicdCreditlimsgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 个人授信信息记录-额度信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@RestController
@RequestMapping("prsncredit/picdcreditlimsgmt")
@Api(tags = "个人授信信息记录-额度信息段")
public class PicdCreditlimsgmtController {
    @Autowired
    private PicdCreditlimsgmtService picdCreditlimsgmtService;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("prsncredit:picdcreditlimsgmt:page")
    public Result<PageData<PicdCreditlimsgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<PicdCreditlimsgmtDTO> page = picdCreditlimsgmtService.page(params);

        return new Result<PageData<PicdCreditlimsgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("prsncredit:picdcreditlimsgmt:info")
    public Result<PicdCreditlimsgmtDTO> get(@PathVariable("id") Long id) {
        PicdCreditlimsgmtDTO data = picdCreditlimsgmtService.get(id);

        return new Result<PicdCreditlimsgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("prsncredit:picdcreditlimsgmt:save")
    public Result save(@RequestBody PicdCreditlimsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        picdCreditlimsgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("prsncredit:picdcreditlimsgmt:update")
    public Result update(@RequestBody PicdCreditlimsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        picdCreditlimsgmtService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("prsncredit:picdcreditlimsgmt:delete")
    public Result delete(@RequestBody Long[] ids) {
        // 效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        picdCreditlimsgmtService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("prsncredit:picdcreditlimsgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<PicdCreditlimsgmtDTO> list = picdCreditlimsgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "个人授信信息记录-额度信息段", list, PicdCreditlimsgmtExcel.class);
    }

    @GetMapping("list")
    @ApiOperation("列表")
    @RequiresPermissions("prsncredit:picdcreditlimsgmt:list")
    public Result<List<PicdCreditlimsgmtDTO>> list(@RequestParam Map<String, Object> params) {
        List<PicdCreditlimsgmtDTO> list = picdCreditlimsgmtService.list(params);

        return new Result<List<PicdCreditlimsgmtDTO>>().ok(list);
    }
}