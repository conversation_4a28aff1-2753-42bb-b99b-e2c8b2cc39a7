package io.ibs.modules.irp.pbccrc.prsncredit.dao;

import io.ibs.common.dao.BaseDao;
import io.ibs.modules.irp.pbccrc.prsncredit.entity.PicbCtrctbssgmtEntity;
import io.ibs.modules.irp.pbccrc.prsncredit.entity.TLoanCredit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 个人授信信息记录
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Mapper
@Repository
public interface PicbCtrctbssgmtDao extends BaseDao<PicbCtrctbssgmtEntity> {
    void updateInstanceId(String instanceId, Long id);

    /**
     * 首次去核心同步个人授信数据
     *
     * @param lstChgDate 上一次修改日期
     * @param endDate    有效终止日期
     * @return 个人授信数据
     */
    List<TLoanCredit> getPersonalCreditInfoFromCBSFirst(@Param("lstChgDate") Date lstChgDate, @Param("endDate") Date endDate);

    /**
     * 去核心同步个人授信数据
     *
     * @param lstChgDate 上一次修改日期
     * @param endDate    有效终止日期
     * @return 个人授信数据
     */
    List<TLoanCredit> getPersonalCreditInfoFromCBS(@Param("lstChgDate") Date lstChgDate, @Param("endDate") Date endDate);
}