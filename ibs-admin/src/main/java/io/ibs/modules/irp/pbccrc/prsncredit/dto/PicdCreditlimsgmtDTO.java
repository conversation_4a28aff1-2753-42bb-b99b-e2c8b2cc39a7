package io.ibs.modules.irp.pbccrc.prsncredit.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.ibs.common.utils.DateUtils;
import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 个人授信信息记录-额度信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@ApiModel(value = "个人授信信息记录-额度信息段")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PicdCreditlimsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "授信额度类型")
    private String creditLimType;
    @ApiModelProperty(value = "额度循环标志")
    private String limLoopFlag;
    @ApiModelProperty(value = "授信额度")
    private BigDecimal creditLim;
    @ApiModelProperty(value = "币种")
    private String cy;
    @ApiModelProperty(value = "额度生效日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_PATTERN)
    private Date conEffDate;
    @ApiModelProperty(value = "额度到期日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateUtils.DATE_PATTERN)
    private Date conExpDate;
    @ApiModelProperty(value = "额度状态")
    private String conStatus;
    @ApiModelProperty(value = "授信限额")
    private BigDecimal creditRest;
    private Long id;
    @ApiModelProperty(value = "报告时点说明代码")
    private String rptDateCode;
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
}