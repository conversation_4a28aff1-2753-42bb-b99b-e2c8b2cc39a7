package io.ibs.modules.irp.pbccrc.prsncredit.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 个人授信信息记录-共同受信人段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PICC_CTRCTCERTREL")
public class PiccCtrctcertrelEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 业务号
     */
    private String bussNum;
    /**
     * 共同受信人身份类别
     */
    private String brerType;
    /**
     * 共同受信人名称
     */
    private String name;
    /**
     * 共同受信人身份标识类型
     */
    private String idType;
    /**
     * 共同受信人身份标识号码
     */
    private String idNum;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    @TableId
    private Long id;

    /**
     * 业务发生日期
     */
    private Date bussDate;
    /**
     * 内部机构代码
     */
    private String deptCode;
    /**
     * 报告时点说明代码
     */
    private String rptDateCode;
}