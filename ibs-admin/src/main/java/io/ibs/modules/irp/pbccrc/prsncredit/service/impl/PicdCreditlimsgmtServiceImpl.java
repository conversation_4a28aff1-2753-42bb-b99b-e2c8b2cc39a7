package io.ibs.modules.irp.pbccrc.prsncredit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.pbccrc.prsncredit.dao.PicdCreditlimsgmtDao;
import io.ibs.modules.irp.pbccrc.prsncredit.dto.PicdCreditlimsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsncredit.entity.PicdCreditlimsgmtEntity;
import io.ibs.modules.irp.pbccrc.prsncredit.service.PicdCreditlimsgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 个人授信信息记录-额度信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-30
 */
@Service
public class PicdCreditlimsgmtServiceImpl extends CrudServiceImpl<PicdCreditlimsgmtDao, PicdCreditlimsgmtEntity, PicdCreditlimsgmtDTO> implements PicdCreditlimsgmtService {

    @Override
    public QueryWrapper<PicdCreditlimsgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<PicdCreditlimsgmtEntity> wrapper = new QueryWrapper<>();

        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);
        String creditLimType = (String) params.get("creditLimType");
        wrapper.eq(StringUtils.isNotBlank(creditLimType), "CREDIT_LIM_TYPE", creditLimType);
        String limLoopFlag = (String) params.get("limLoopFlag");
        wrapper.eq(StringUtils.isNotBlank(limLoopFlag), "LIM_LOOP_FLAG", limLoopFlag);
        String creditLim = (String) params.get("creditLim");
        wrapper.eq(StringUtils.isNotBlank(creditLim), "CREDIT_LIM", creditLim);
        String cy = (String) params.get("cy");
        wrapper.eq(StringUtils.isNotBlank(cy), "CY", cy);
        String conEffDate = (String) params.get("conEffDate");
        wrapper.ge(StringUtils.isNotBlank(conEffDate), "CON_EFF_DATE", conEffDate);
        String conExpDate = (String) params.get("conExpDate");
        wrapper.le(StringUtils.isNotBlank(conExpDate), "CON_EXP_DATE", conExpDate);
        String conStatus = (String) params.get("conStatus");
        wrapper.eq(StringUtils.isNotBlank(conStatus), "CON_STATUS", conStatus);
        String creditRest = (String) params.get("creditRest");
        wrapper.eq(StringUtils.isNotBlank(creditRest), "CREDIT_REST", creditRest);

        return wrapper;
    }

    @Override
    public PicdCreditlimsgmtDTO getByBussNum(String bussNum) {
        QueryWrapper<PicdCreditlimsgmtEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(String.valueOf(bussNum)), "BUSS_NUM", bussNum);
        PicdCreditlimsgmtEntity picdCreditlimsgmtEntity = baseDao.selectOne(wrapper);
        return ConvertUtils.sourceToTarget(picdCreditlimsgmtEntity, PicdCreditlimsgmtDTO.class);
    }
}