package io.ibs.modules.irp.pbccrc.prsnguarantee.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 担保-基本信息段
 *
 * <AUTHOR>
 * @since 20220818
 */
@Data
@ApiModel(value = "担保-基本信息段")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PigcGuaracctbsinfsgDTO extends PbccrcBaseDTO implements Serializable {

    @ApiModelProperty(value = "担保业务大类")
    private String busiLines;

    @ApiModelProperty(value = "担保业务种类细分")
    private String busiDtiLines;

    @ApiModelProperty(value = "开户日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date openDate;

    @ApiModelProperty(value = "担保金额")
    private BigDecimal acctCredLine  = BigDecimal.ZERO;

    @ApiModelProperty(value = "币种")
    private String acctCy;

    @ApiModelProperty(value = "到期日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date dueDate;

    @ApiModelProperty(value = "反担保方式")
    private String guraMode;

    @ApiModelProperty(value = "其他还款保证方式")
    private String othRepyGuraWay;

    @ApiModelProperty(value = "保证金百分比")
    private BigDecimal secDep = BigDecimal.ZERO;

    @ApiModelProperty(value = "担保合同文本编号")
    private String ctrctTxtCd;
}
