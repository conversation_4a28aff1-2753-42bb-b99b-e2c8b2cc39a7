package io.ibs.modules.irp.pbccrc.prsnguarantee.dto;

import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 担保-还款责任人
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-18
 */
@Data
@ApiModel(value = "担保-还款责任人")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PigeRltrepymtinfDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "身份类别")
    private String infoIdType;

    @ApiModelProperty(value = "责任人名称")
    private String arlpName;

    @ApiModelProperty(value = "责任人身份标识类型")
    private String arlpCertType;

    @ApiModelProperty(value = "责任人身份标识号码")
    private String arlpCertNum;

    @ApiModelProperty(value = "还款责任人类型")
    private String arlpTp;

    @ApiModelProperty(value = "还款责任金额")
    private BigDecimal arlpAmt;

    @ApiModelProperty(value = "联保标志")
    private String wartySign;

    @ApiModelProperty(value = "保证合同业务号")
    private String maxGuarBussNum;
}