package io.ibs.modules.irp.pbccrc.prsnguarantee.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.irp.util.FieldUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 担保-基础段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("PIGB_GUARACCTBSSGMT")
public class AsyPigbGuaracctbssgmtEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 账户类型
	*/
	private String acctType;
	/**
	* 债务人名称
	*/
	private String name;
	public String getName() {
		return FieldUtil.getGuarName(name);
	}

	public void setName(String name) {
		this.name = FieldUtil.getGuarName(name);
	}
	/**
	* 债务人身份标识类型
	*/
	private String idType;
	/**
	* 债务人身份标识号码
	*/
	private String idNum;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务发生日期
	*/
	private Date bussDate;
	/**
	* 业务号
	*/
	private String bussNum;
	/**
	* 原系统业务号
	*/
	private String nativeBussNum;
	/**
	* 业务发生日期
	*/
	private String itabGetDate;
}