package io.ibs.modules.irp.pbccrc.prsnguarantee.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 担保-抵质押物信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("PIGF_GUARMOTGACLTALCTRCTINF")
public class AsyPigfGuarmotgacltalctrctinfEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 抵质押业务号
	*/
	private String acctNum;
	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	* 业务发生日期
	*/
	private Date bussDate;
	/**
	* 业务号
	*/
	private String bussNum;
	/**
	* 原系统业务号
	*/
	private String nativeBussNum;
	/**
	* 业务发生日期（供数据导入使用）
	*/
	private String itabGetDate;

}