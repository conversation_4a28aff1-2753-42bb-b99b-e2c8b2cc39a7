package io.ibs.modules.irp.pbccrc.prsnguarantee.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 担保-在保责任信息段
 *
 * <AUTHOR>
 * @since 20220817
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PIGD_GUARRLTPEPYMTINFSGMT")
public class PigdGuarrltpepymtinfsgmtEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 业务号
     */
    private String bussNum;

    /**
     * 账户状态
     */
    private String acctStatus;
    /**
     * 在保余额
     */
    private BigDecimal loanAmt;
    /**
     * 余额变化日期
     */
    private Date repayPrd;
    /**
     * 五级分类
     */
    private String fiveCate;
    /**
     * 五级分类认定日期
     */
    private Date fiveCateAdjDate;
    /**
     * 风险敞口
     */
    private String riEx;
    /**
     * 代偿(垫款)标识
     */
    private String compAdvFlag;
    /**
     * 账户关闭日期
     */
    private Date closeDate;
    /**
     * 报告时点说明代码
     */
    private String rptDateCode;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;

    /**
     * 业务发生日期
     */
    private Date bussDate;

    @TableId
    private Long id;
    /**
     * 内部机构代码
     */
    private String deptCode;
}
