package io.ibs.modules.irp.pbccrc.prsnguarantee.entity;

import lombok.Data;

/**
 * 从信贷系统抽取担保信息
 *
 * <AUTHOR>
 * @since 20220721
 */
@Data
public class PrsnGuaranteeGetDataEntity {
    /**
     * 贷款申请编号 作为担保业务号
     */
    private String sqbh;
    /**
     * 客户号
     */
    private String khh;
    /**
     * 借据号
     */
    private String jjh;
    /**
     * 担保方式 作为判断身份类别的标志
     */
    private String dbfs;
    /**
     * 担保方式为个人时责任人的名字
     */
    private String zrrDbrxm;
    /**
     * 担保方式为个人时责任人的证件类型
     */
    private String zrrZjlx;
    /**
     * 担保方式为个人时责任人的证件号码
     */
    private String zrrZjhm;
    /**
     * 担保方式为担保机构时项目名称
     */
    private String dbgsXmmc;
    /**
     * 担保方式为企业时企业的名称
     */
    private String qyQymc;
    /**
     * 担保方式为企业时企业的组织机构代码
     */
    private String qyZzjgdm;
    /**
     * 担保合同编号
     */
    private String htbh;
    /**
     * 保证金比例
     */
    private String dbgsBzjbl;
}
