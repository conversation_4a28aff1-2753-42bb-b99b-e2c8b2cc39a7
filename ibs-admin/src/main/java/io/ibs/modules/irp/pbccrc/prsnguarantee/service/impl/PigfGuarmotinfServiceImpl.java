package io.ibs.modules.irp.pbccrc.prsnguarantee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.irp.pbccrc.prsnguarantee.dao.PigfGuarmotinfDao;
import io.ibs.modules.irp.pbccrc.prsnguarantee.dto.PigfGuarmotinfDTO;
import io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PigfGuarmotinfEntity;
import io.ibs.modules.irp.pbccrc.prsnguarantee.service.PigfGuarmotinfService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 担保-抵质押物信息
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-21
 */
@Service
public class PigfGuarmotinfServiceImpl extends CrudServiceImpl<PigfGuarmotinfDao, PigfGuarmotinfEntity, PigfGuarmotinfDTO> implements PigfGuarmotinfService {

    @Override
    public QueryWrapper<PigfGuarmotinfEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<PigfGuarmotinfEntity> wrapper = new QueryWrapper<>();

        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);
        String acctNum = (String) params.get("acctNum");
        wrapper.eq(StringUtils.isNotBlank(acctNum), "ACCT_NUM", acctNum);

        return wrapper;
    }
}