package io.ibs.modules.irp.pbccrc.prsninfo.dto;

import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 基本信息记录-居住地址段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-24
 */
@Data
@ApiModel(value = "基本信息记录-居住地址段")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PibhRedncinfsgmtDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户号")
    private String custId;

    @ApiModelProperty(value = "居住状况")
    private String resiStatus;

    @ApiModelProperty(value = "居住地详细地址")
    private String resiAddr;

    @ApiModelProperty(value = "居住地邮编")
    private String resiPc;

    @ApiModelProperty(value = "居住地行政区划")
    private String resiDist;

    @ApiModelProperty(value = "住宅电话")
    private String homeTel;
}