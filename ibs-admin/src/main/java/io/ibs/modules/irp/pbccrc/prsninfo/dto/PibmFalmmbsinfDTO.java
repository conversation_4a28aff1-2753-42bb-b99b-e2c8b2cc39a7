package io.ibs.modules.irp.pbccrc.prsninfo.dto;

import io.ibs.modules.irp.pbccrc.common.dto.PbccrcBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * 基本信息记录-家族关系信息记录
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-24
 */
@Data
@ApiModel(value = "基本信息记录-家族关系信息记录")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PibmFalmmbsinfDTO extends PbccrcBaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户号")
    private String custId;

    @ApiModelProperty(value = "A姓名")
    private String name;

    @ApiModelProperty(value = "A证件类型")
    private String idType;

    @ApiModelProperty(value = "A证件号码")
    private String idNum;

    @ApiModelProperty(value = "B姓名")
    private String famMemName;

    @ApiModelProperty(value = "B证件类型")
    private String famMemCertType;

    @ApiModelProperty(value = "B证件号码")
    private String famMemCertNum;

    @ApiModelProperty(value = "家族关系")
    private String famRel;

    @ApiModelProperty(value = "家族关系有效标志")
    private String famRelaAssFlag;
}