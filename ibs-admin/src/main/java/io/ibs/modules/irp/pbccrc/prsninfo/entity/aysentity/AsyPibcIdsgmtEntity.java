package io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.irp.util.FieldUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基本信息记录-其他标识段(PIBC_IDSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("PIBC_IDSGMT")
public class AsyPibcIdsgmtEntity extends AsyCommEntity {

    /**
     * 客户号
     */
    private String custId;
    /**
     * 姓名
     */
    private String alias;
    public String getAlias() {
        return FieldUtil.getName(alias);
    }

    public void setAlias(String alias) {
        this.alias = FieldUtil.getName(alias);
    }
    /**
     * 证件类型
     */
    private String othidType;
    /**
     * 证件号码
     */
    private String othidNum;
}
