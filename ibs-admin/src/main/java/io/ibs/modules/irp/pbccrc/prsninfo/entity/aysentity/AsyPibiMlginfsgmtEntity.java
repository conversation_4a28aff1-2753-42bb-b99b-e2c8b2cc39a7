package io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基本信息记录-通讯地址信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("PIBI_MLGINFSGMT")
public class AsyPibiMlginfsgmtEntity extends AsyCommEntity {

    /**
     * 客户号
     */
    private String custId;
    /**
     * 通讯地址
     */
    private String mailAddr;
    /**
     * 通讯地邮编
     */
    private String mailPc;
    /**
     * 通讯地行政区划
     */
    private String mailDist;
}
