package io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基本信息记录-家族关系信息记录
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-11
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("PIBM_FALMMBSINF")
public class AsyPibmFalmmbsinfEntity extends AsyCommEntity {

    /**
     * 客户号
     */
    private String custId;
    /**
     * A姓名
     */
    private String name;
    /**
     * A证件类型
     */
    private String idType;
    /**
     * A证件号码
     */
    private String idNum;
    /**
     * B姓名
     */
    private String famMemName;
    /**
     * B证件类型
     */
    private String famMemCertType;
    /**
     * B证件号码
     */
    private String famMemCertNum;
    /**
     * 家族关系
     */
    private String famRel;
    /**
     * 家族关系有效标志
     */
    private String famRelaAssFlag;
}
