package io.ibs.modules.irp.pbccrc.prsninfo.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.prsninfo.dto.PibcIdsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity.AsyPibcIdsgmtEntity;

import java.util.List;

/**
 * 基本信息记录-其他标识段(PIBC_IDSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-23
 */
public interface PibcIdsgmtService extends CrudService<AsyPibcIdsgmtEntity, PibcIdsgmtDTO> {

    /**
     * 更新其他标识段到金电系统
     *
     * @param ids
     */
    void ays2Up(List<AsyPibcIdsgmtEntity> ids);
}