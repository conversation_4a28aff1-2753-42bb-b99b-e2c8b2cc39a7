package io.ibs.modules.irp.pbccrc.prsninfo.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.prsninfo.dto.PibgOctpninfsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity.AsyPibgOctpninfsgmtEntity;

import java.util.List;

/**
 * 基本信息记录-职业信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-24
 */
public interface PibgOctpninfsgmtService extends CrudService<AsyPibgOctpninfsgmtEntity, PibgOctpninfsgmtDTO> {

    /**
     * 同步职业信息段到金电系统
     *
     * @param list
     */
    void ays2Up(List<AsyPibgOctpninfsgmtEntity> list);
}