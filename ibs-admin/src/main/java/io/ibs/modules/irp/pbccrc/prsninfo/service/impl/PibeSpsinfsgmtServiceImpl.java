package io.ibs.modules.irp.pbccrc.prsninfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.irp.pbccrc.prsninfo.dao.aysdao.AsyPibeSpsinfsgmtDao;
import io.ibs.modules.irp.pbccrc.prsninfo.dto.PibeSpsinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity.AsyPibeSpsinfsgmtEntity;
import io.ibs.modules.irp.pbccrc.prsninfo.service.PibeSpsinfsgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 基本信息记录-婚姻信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-24
 */
@Service
public class PibeSpsinfsgmtServiceImpl extends CrudServiceImpl<AsyPibeSpsinfsgmtDao, AsyPibeSpsinfsgmtEntity, PibeSpsinfsgmtDTO> implements PibeSpsinfsgmtService {

    @Override
    public QueryWrapper<AsyPibeSpsinfsgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<AsyPibeSpsinfsgmtEntity> wrapper = new QueryWrapper<>();

        String custId = (String) params.get("custId");
        wrapper.eq(StringUtils.isNotBlank(custId), "CUST_ID", custId);

        return wrapper;
    }

    @Override
    @DataSource("credit")
    public void ays2Up(List<AsyPibeSpsinfsgmtEntity> list) {
        list.forEach(entity -> baseDao.insert(entity));
    }

}