package io.ibs.modules.irp.pbccrc.prsninfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.irp.pbccrc.prsninfo.dao.aysdao.AsyPibjIncinfsgmtDao;
import io.ibs.modules.irp.pbccrc.prsninfo.dto.PibjIncinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity.AsyPibjIncinfsgmtEntity;
import io.ibs.modules.irp.pbccrc.prsninfo.service.PibjIncinfsgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 基本信息记录-收入信息段
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-24
 */
@Service
public class PibjIncinfsgmtServiceImpl extends CrudServiceImpl<AsyPibjIncinfsgmtDao, AsyPibjIncinfsgmtEntity, PibjIncinfsgmtDTO> implements PibjIncinfsgmtService {

    @Override
    public QueryWrapper<AsyPibjIncinfsgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<AsyPibjIncinfsgmtEntity> wrapper = new QueryWrapper<>();

        String custId = (String) params.get("custId");
        wrapper.eq(StringUtils.isNotBlank(custId), "CUST_ID", custId);

        return wrapper;
    }

    @Override
    @DataSource("credit")
    public void ays2Up(List<AsyPibjIncinfsgmtEntity> list) {
        list.forEach(entity -> baseDao.insert(entity));
    }

}