package io.ibs.modules.irp.pbccrc.prsnloan.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.pbccrc.prsnloan.dto.PilfAcctcredsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsnloan.excel.PilfAcctcredsgmtExcel;
import io.ibs.modules.irp.pbccrc.prsnloan.service.PilfAcctcredsgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 个人借贷交易信息-授信额度信息段(PILF_ACCTCREDSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-06
 */
@RestController
@RequestMapping("prsnloan/pilfacctcredsgmt")
@Api(tags = "个人借贷交易信息-授信额度信息段(PILF_ACCTCREDSGMT)")
public class PilfAcctcredsgmtController {
    @Autowired
    private PilfAcctcredsgmtService pilfAcctcredsgmtService;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("prsnloan:pilfacctcredsgmt:page")
    public Result<PageData<PilfAcctcredsgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<PilfAcctcredsgmtDTO> page = pilfAcctcredsgmtService.page(params);

        return new Result<PageData<PilfAcctcredsgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("prsnloan:pilfacctcredsgmt:info")
    public Result<PilfAcctcredsgmtDTO> get(@PathVariable("id") Long id) {
        PilfAcctcredsgmtDTO data = pilfAcctcredsgmtService.get(id);

        return new Result<PilfAcctcredsgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("prsnloan:pilfacctcredsgmt:save")
    public Result save(@RequestBody PilfAcctcredsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        pilfAcctcredsgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("prsnloan:pilfacctcredsgmt:update")
    public Result update(@RequestBody PilfAcctcredsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        pilfAcctcredsgmtService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("prsnloan:pilfacctcredsgmt:delete")
    public Result delete(@RequestBody Long[] ids) {
        // 效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pilfAcctcredsgmtService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("prsnloan:pilfacctcredsgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<PilfAcctcredsgmtDTO> list = pilfAcctcredsgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "个人借贷交易信息-授信额度信息段(PILF_ACCTCREDSGMT)", list, PilfAcctcredsgmtExcel.class);
    }

    @GetMapping("list")
    @ApiOperation("列表")
    @RequiresPermissions("prsnloan:pilfacctcredsgmt:list")
    public Result<List<PilfAcctcredsgmtDTO>> list(@RequestParam Map<String, Object> params) {
        List<PilfAcctcredsgmtDTO> list = pilfAcctcredsgmtService.list(params);

        return new Result<List<PilfAcctcredsgmtDTO>>().ok(list);
    }

}