package io.ibs.modules.irp.pbccrc.prsnloan.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.pbccrc.prsnloan.dto.PilhAcctmthlyblginfsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsnloan.excel.PilhAcctmthlyblginfsgmtExcel;
import io.ibs.modules.irp.pbccrc.prsnloan.service.PilhAcctmthlyblginfsgmtService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 个人借贷交易信息-月度表现信息段(PILH_ACCTMTHLYBLGINFSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-08
 */
@RestController
@RequestMapping("prsnloan/pilhacctmthlyblginfsgmt")
@Api(tags = "个人借贷交易信息-月度表现信息段(PILH_ACCTMTHLYBLGINFSGMT)")
public class PilhAcctmthlyblginfsgmtController {
    @Autowired
    private PilhAcctmthlyblginfsgmtService pilhAcctmthlyblginfsgmtService;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("prsnloan:pilhacctmthlyblginfsgmt:page")
    public Result<PageData<PilhAcctmthlyblginfsgmtDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<PilhAcctmthlyblginfsgmtDTO> page = pilhAcctmthlyblginfsgmtService.page(params);

        return new Result<PageData<PilhAcctmthlyblginfsgmtDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("prsnloan:pilhacctmthlyblginfsgmt:info")
    public Result<PilhAcctmthlyblginfsgmtDTO> get(@PathVariable("id") Long id) {
        PilhAcctmthlyblginfsgmtDTO data = pilhAcctmthlyblginfsgmtService.get(id);

        return new Result<PilhAcctmthlyblginfsgmtDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("prsnloan:pilhacctmthlyblginfsgmt:save")
    public Result save(@RequestBody PilhAcctmthlyblginfsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        pilhAcctmthlyblginfsgmtService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("prsnloan:pilhacctmthlyblginfsgmt:update")
    public Result update(@RequestBody PilhAcctmthlyblginfsgmtDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        pilhAcctmthlyblginfsgmtService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("prsnloan:pilhacctmthlyblginfsgmt:delete")
    public Result delete(@RequestBody Long[] ids) {
        // 效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pilhAcctmthlyblginfsgmtService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("prsnloan:pilhacctmthlyblginfsgmt:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<PilhAcctmthlyblginfsgmtDTO> list = pilhAcctmthlyblginfsgmtService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "个人借贷交易信息-月度表现信息段(PILH_ACCTMTHLYBLGINFSGMT)", list, PilhAcctmthlyblginfsgmtExcel.class);
    }

    @GetMapping("list")
    @ApiOperation("列表")
    @RequiresPermissions("prsnloan:pilhacctmthlyblginfsgmt:list")
    public Result<List<PilhAcctmthlyblginfsgmtDTO>> list(@RequestParam Map<String, Object> params) {
        List<PilhAcctmthlyblginfsgmtDTO> list = pilhAcctmthlyblginfsgmtService.list(params);

        return new Result<List<PilhAcctmthlyblginfsgmtDTO>>().ok(list);
    }

}