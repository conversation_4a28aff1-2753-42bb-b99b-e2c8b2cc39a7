package io.ibs.modules.irp.pbccrc.prsnloan.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 个人借贷交易信息-基本信息段(PILC_ACCTBSINFSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("PILC_ACCTBSINFSGMT")
public class AsyPilcAcctbsinfsgmtEntity {
	private static final long serialVersionUID = 1L;

	/**
	* 业务号
	*/
	private String bussNum;
	/**
	* 借贷业务大类
	*/
	private String busiLines;
	/**
	* 借贷业务种类细分
	*/
	private String busiDtlLines;
	/**
	* 开户日期
	*/
	private Date openDate;
	/**
	* 币种
	*/
	private String acctcy;
	/**
	* 信用额度
	*/
	private BigDecimal acctCredLine;
	/**
	* 借款金额
	*/
	private BigDecimal loanAmt;
	/**
	* 分次放款标志
	*/
	private String flag;
	/**
	* 到期日期
	*/
	private Date dueDate;
	/**
	* 还款方式
	*/
	private String repayMode;
	/**
	* 还款频率
	*/
	private String repayFreqcy;
	/**
	* 还款期数
	*/
	private Long repayPrd;
	/**
	* 业务申请地行政区划代码
	*/
	private String applyBusiDist;
	/**
	* 担保方式
	*/
	private String guarMode;
	/**
	* 其他还款保证方式
	*/
	private String othRepyGuarWay;
	/**
	* 资产转让标志
	*/
	private String assetTrandFlag;
	/**
	* 业务经营类型
	*/
	private String fundSou;
	/**
	* 卡片标识号
	*/
	private String creditId;
	/**
	* 贷款发放形式
	*/
	private String loanForm;
	/**
	* 贷款合同编号
	*/
	private String loanConCode;
	/**
	* 是否为首套住房贷款
	*/
	private String firstHouLoanFlag;
	/**
	* 业务发生日期
	*/
	private Date bussDate;

	/**
	* 内部机构代码
	*/
	private String deptCode;
	/**
	 * 原系统业务号
	 */
	private String nativeBussNum;
	/**
	 * 业务发生日期（供数据导入使用）
	 */
	private String itabGetDate;
}