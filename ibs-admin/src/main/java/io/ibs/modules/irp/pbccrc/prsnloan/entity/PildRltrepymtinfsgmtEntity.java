package io.ibs.modules.irp.pbccrc.prsnloan.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 个人借贷交易信息-相关还款责任人段(PILD_RLTREPYMTINFSGMT)
 * 除借款人以外其他人的还款责任,包括但不限于共同借款人、保证人以及其他对本笔借款负有还款责任的个人或企业的信息
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PILD_RLTREPYMTINFSGMT")
public class PildRltrepymtinfsgmtEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 业务号
     */
    private String bussNum;
    /**
     * 保证合同业务号
     */
    private String maxGuarBussNum;
    /**
     * 身份类别
     */
    private String infoIdType;
    /**
     * 责任人名称
     */
    private String arlpName;
    /**
     * 责任人身份标识类型
     */
    private String arlpCertType;
    /**
     * 责任人身份标识号码
     */
    private String arlpCertNum;
    /**
     * 还款责任人类型
     */
    private String arlpType;
    /**
     * 还款责任金额
     */
    private BigDecimal arlpAmt;
    /**
     * 联保标志
     */
    private String wartySign;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 业务发生日期
     */
    private Date bussDate;
    @TableId
    private Long id;
    /**
     * 内部机构代码
     */
    private String deptCode;
}