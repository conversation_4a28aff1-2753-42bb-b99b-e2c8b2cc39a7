package io.ibs.modules.irp.pbccrc.prsnloan.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 个人借贷交易信息-账户特殊事件说明记录段(PILS_SPCEVTDSCIN)
 * 非D1账户报送
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PILS_SPCEVTDSCINF")
public class PilsSpcevtdscinfEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 业务号
     */
    private String bussNum;
    /**
     * 事件类型
     */
    private String opetnType;
    /**
     * 发生月份
     */
    private Date month;
    /**
     * 生效标志
     */
    private String flagTp;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 业务发生日期
     */
    private Date bussDate;
    @TableId
    private Long id;
    /**
     * 内部机构代码
     */
    private String deptCode;
}