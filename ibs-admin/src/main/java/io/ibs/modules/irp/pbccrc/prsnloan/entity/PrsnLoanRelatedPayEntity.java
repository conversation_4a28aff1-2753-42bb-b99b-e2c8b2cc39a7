package io.ibs.modules.irp.pbccrc.prsnloan.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 贷款类型为保证贷款的相关还款责任人
 *
 * <AUTHOR>
 * @since 20220818
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PrsnLoanRelatedPayEntity {

    /**
     * 借据号
     */
    private String jjh;
    /**
     * 担保方式 作为判断身份类别的标志
     */
    private String dbfs;
    /**
     * 担保方式为个人时责任人的名字
     */
    private String zrrDbrxm;
    /**
     * 担保方式为个人时责任人的证件类型
     */
    private String zrrZjlx;
    /**
     * 担保方式为个人时责任人的证件号码
     */
    private String zrrZjhm;
    /**
     * 担保方式为担保机构时项目名称
     */
    private String dbgsXmmc;
    /**
     * 担保方式为企业时企业的名称
     */
    private String qyQymc;
    /**
     * 担保方式为企业时企业的组织机构代码
     */
    private String qyZzjgdm;
    /**
     * 担保合同编号
     */
    private String htbh;
}
