package io.ibs.modules.irp.pbccrc.prsnloan.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import java.util.Date;

/**
 * 个人借贷交易信息-抵质押物信息段(PILE_MOTGACLTALCTRCTINFSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-06
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class PileMotgacltalctrctinfsgmtExcel {
    @ExcelProperty(value = "抵质押合同号", index = 0)
    private String ccc2;
    @ExcelProperty(value = "业务号", index = 1)
    private String bussNum;
    @ExcelProperty(value = "业务发生日期", index = 2)
    private Date bussDate;
    @ExcelProperty(value = "内部机构代码", index = 3)
    private String deptCode;
}