package io.ibs.modules.irp.pbccrc.prsnloan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.irp.pbccrc.prsnloan.dao.PileMotgacltalctrctinfsgmtDao;
import io.ibs.modules.irp.pbccrc.prsnloan.dto.PileMotgacltalctrctinfsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsnloan.entity.PileMotgacltalctrctinfsgmtEntity;
import io.ibs.modules.irp.pbccrc.prsnloan.service.PileMotgacltalctrctinfsgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 个人借贷交易信息-抵质押物信息段(PILE_MOTGACLTALCTRCTINFSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-06
 */
@Service
public class PileMotgacltalctrctinfsgmtServiceImpl extends CrudServiceImpl<PileMotgacltalctrctinfsgmtDao, PileMotgacltalctrctinfsgmtEntity, PileMotgacltalctrctinfsgmtDTO> implements PileMotgacltalctrctinfsgmtService {

    @Override
    public QueryWrapper<PileMotgacltalctrctinfsgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<PileMotgacltalctrctinfsgmtEntity> wrapper = new QueryWrapper<>();

        String ccc2 = (String) params.get("ccc2");
        wrapper.eq(StringUtils.isNotBlank(ccc2), "CCC2", ccc2);
        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);

        return wrapper;
    }
}