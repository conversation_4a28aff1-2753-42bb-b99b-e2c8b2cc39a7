package io.ibs.modules.irp.pbccrc.prsnloan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.irp.pbccrc.prsnloan.dao.PilkAcctspectrstdspnsgmtDao;
import io.ibs.modules.irp.pbccrc.prsnloan.dto.PilkAcctspectrstdspnsgmtDTO;
import io.ibs.modules.irp.pbccrc.prsnloan.entity.PilkAcctspectrstdspnsgmtEntity;
import io.ibs.modules.irp.pbccrc.prsnloan.service.PilkAcctspectrstdspnsgmtService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 个人借贷交易信息-特殊交易说明段(PILK_ACCTSPECTRSTDSPNSGMT)
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-07
 */
@Service
public class PilkAcctspectrstdspnsgmtServiceImpl extends CrudServiceImpl<PilkAcctspectrstdspnsgmtDao, PilkAcctspectrstdspnsgmtEntity, PilkAcctspectrstdspnsgmtDTO> implements PilkAcctspectrstdspnsgmtService {

    @Override
    public QueryWrapper<PilkAcctspectrstdspnsgmtEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<PilkAcctspectrstdspnsgmtEntity> wrapper = new QueryWrapper<>();

        String bussNum = (String) params.get("bussNum");
        wrapper.eq(StringUtils.isNotBlank(bussNum), "BUSS_NUM", bussNum);
        String chanTranType = (String) params.get("chanTranType");
        wrapper.eq(StringUtils.isNotBlank(chanTranType), "CHAN_TRAN_TYPE", chanTranType);
        String tranAmt = (String) params.get("tranAmt");
        wrapper.eq(StringUtils.isNotBlank(tranAmt), "TRAN_AMT", tranAmt);
        String deptCode = (String) params.get("deptCode");
        wrapper.eq(StringUtils.isNotBlank(deptCode), "DEPT_CODE", deptCode);

        return wrapper;
    }

    @Override
    public PilkAcctspectrstdspnsgmtDTO getByBussNumAndBussDate(String bussNum, Date bussDate) {
        PilkAcctspectrstdspnsgmtEntity acctspectrstdspnsgmtEntity = baseDao.selectOne(new QueryWrapper<PilkAcctspectrstdspnsgmtEntity>().lambda()
                .eq(PilkAcctspectrstdspnsgmtEntity::getBussNum, bussNum)
                .eq(PilkAcctspectrstdspnsgmtEntity::getBussDate, bussDate));
        return ConvertUtils.sourceToTarget(acctspectrstdspnsgmtEntity, PilkAcctspectrstdspnsgmtDTO.class);
    }
}