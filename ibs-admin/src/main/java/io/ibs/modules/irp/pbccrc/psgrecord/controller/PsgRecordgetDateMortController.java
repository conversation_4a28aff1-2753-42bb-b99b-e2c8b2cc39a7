package io.ibs.modules.irp.pbccrc.psgrecord.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.pbccrc.psgrecord.dto.PsgRecordgetDateMortDTO;
import io.ibs.modules.irp.pbccrc.psgrecord.service.PsgRecordgetDateMortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
* 抵质押报送
*
* <AUTHOR>  
* @since 1.0 2023-04-21
*/
@RestController
@RequestMapping("mort_invest/psgrecordgetdatemort")
@Api(tags="抵质押报送")
public class PsgRecordgetDateMortController {
    @Autowired
    private PsgRecordgetDateMortService psgRecordgetDateMortService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("mort_invest:psgrecordgetdatemort:page")
    public Result<PageData<PsgRecordgetDateMortDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<PsgRecordgetDateMortDTO> page = psgRecordgetDateMortService.page(params);

        return new Result<PageData<PsgRecordgetDateMortDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("mort_invest:psgrecordgetdatemort:info")
    public Result<PsgRecordgetDateMortDTO> get(@PathVariable("id") Long id){
        PsgRecordgetDateMortDTO data = psgRecordgetDateMortService.get(id);

        return new Result<PsgRecordgetDateMortDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("mort_invest:psgrecordgetdatemort:save")
    public Result save(@RequestBody PsgRecordgetDateMortDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        psgRecordgetDateMortService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("mort_invest:psgrecordgetdatemort:update")
    public Result update(@RequestBody PsgRecordgetDateMortDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        psgRecordgetDateMortService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("mort_invest:psgrecordgetdatemort:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        psgRecordgetDateMortService.delete(ids);

        return new Result();
    }

    @DeleteMapping("clearData")
    @ApiOperation("清除数据")
    @LogOperation("清除数据")
    @RequiresPermissions("mort_invest:psgrecordgetdatemort:delete")
    public Result clearData(@RequestBody Map<String, String> params){
        AssertUtils.isMapEmpty(params, "params");

        String fromDate = params.get("fromDate");
        String toDate = params.get("toDate");

        psgRecordgetDateMortService.clearData(fromDate, toDate);
        return new Result();
    }
}