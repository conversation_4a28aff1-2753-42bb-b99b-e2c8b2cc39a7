package io.ibs.modules.irp.pbccrc.psgrecord.controller;

import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.pbccrc.psgrecord.dto.PsgRecordgetDatePersonDTO;
import io.ibs.modules.irp.pbccrc.psgrecord.service.PsgRecordgetDatePersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;


/**
 * 个人上送接口数据记录表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-19
 */
@RestController
@RequestMapping("prsn_invest/psgrecordgetdate")
@Api(tags = "个人上送接口数据记录表")
public class PsgRecordgetDatePersonController {
    @Autowired
    private PsgRecordgetDatePersonService psgRecordgetDatePersonService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("prsn_invest:psgrecordgetdate:page")
    public Result<PageData<PsgRecordgetDatePersonDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<PsgRecordgetDatePersonDTO> page = psgRecordgetDatePersonService.page(params);

        return new Result<PageData<PsgRecordgetDatePersonDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("prsn_invest:psgrecordgetdate:info")
    public Result<PsgRecordgetDatePersonDTO> get(@PathVariable("id") Long id) {
        PsgRecordgetDatePersonDTO data = psgRecordgetDatePersonService.get(id);

        return new Result<PsgRecordgetDatePersonDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("prsn_invest:psgrecordgetdate:save")
    public Result save(@RequestBody PsgRecordgetDatePersonDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        psgRecordgetDatePersonService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("prsn_invest:psgrecordgetdate:update")
    public Result update(@RequestBody PsgRecordgetDatePersonDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        psgRecordgetDatePersonService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("prsn_invest:psgrecordgetdate:delete")
    public Result delete(@RequestBody Long[] ids) {
        // 效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        psgRecordgetDatePersonService.delete(ids);

        return new Result();
    }

    @GetMapping("list")
    @ApiOperation("列表")
    @RequiresPermissions("prsn_invest:psgrecordgetdate:list")
    public Result<List<PsgRecordgetDatePersonDTO>> list(@RequestParam Map<String, Object> params) {
        List<PsgRecordgetDatePersonDTO> list = psgRecordgetDatePersonService.list(params);

        return new Result<List<PsgRecordgetDatePersonDTO>>().ok(list);
    }

    @DeleteMapping("clearData")
    @ApiOperation("清除数据")
    @LogOperation("清除数据")
    @RequiresPermissions("prsn_invest:psgrecordgetdate:delete")
    public Result clearData(@RequestBody Map<String, String> params){
        AssertUtils.isMapEmpty(params, "params");

        String fromDate = params.get("fromDate");
        String toDate = params.get("toDate");

        psgRecordgetDatePersonService.clearData(fromDate, toDate);
        return new Result();
    }
}