package io.ibs.modules.irp.pbccrc.psgrecord.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 个人上送接口数据记录表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-19
 */
@Data
@ApiModel(value = "个人上送接口数据记录表")
public class AsyPsgRecordgetDatePersonDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    @ApiModelProperty(value = "上报机构的机构号")
    private String rptCode;
    @ApiModelProperty(value = "上报时间（用于同步上报表）")
    private String itabGetDate;
}