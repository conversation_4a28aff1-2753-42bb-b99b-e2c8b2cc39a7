package io.ibs.modules.irp.pbccrc.psgrecord.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业上送接口数据记录表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-03-15
 */
@Data
@ApiModel(value = "企业上送接口数据记录表")
public class PsgRecordgetDateEnterDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final String NAME_ENT_INFO = "企业信息";
    public static final String NAME_ENT_LOAN = "企业借贷";
    public static final String NAME_ENT_GUARANTEE = "企业担保";
    public static final String NAME_ENT_CREDIT = "企业授信";

    private Long id;
    @ApiModelProperty(value = "上报机构的机构号")
    private String rptCode;
    @ApiModelProperty(value = "上报时间（用于同步上报表）")
    private String itabGetDate;
    @ApiModelProperty(value = "上送时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date asyDate;
    @ApiModelProperty(value = "上报内容")
    private String content;
}