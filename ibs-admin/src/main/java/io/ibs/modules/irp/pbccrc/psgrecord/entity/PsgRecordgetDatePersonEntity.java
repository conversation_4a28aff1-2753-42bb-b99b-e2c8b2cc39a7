package io.ibs.modules.irp.pbccrc.psgrecord.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 个人上送接口数据记录表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PSG_RECORDGET_DATE_PERSON")
public class PsgRecordgetDatePersonEntity {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;
    /**
     * 上报机构的机构号
     */
    private String rptCode;
    /**
     * 上报时间（用于同步上报表）
     */
    private String itabGetDate;
    /**
     * 上送时间
     */
    private Date asyDate;
    /**
     * 上报内容
     */
    private String content;
}