package io.ibs.modules.irp.pbccrc.psgrecord.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.psgrecord.dto.PsgRecordgetDatePersonDTO;
import io.ibs.modules.irp.pbccrc.psgrecord.entity.PsgRecordgetDatePersonEntity;

/**
 * 个人上送接口数据记录表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-19
 */
public interface PsgRecordgetDatePersonService extends CrudService<PsgRecordgetDatePersonEntity, PsgRecordgetDatePersonDTO> {

    boolean checkAsy(String day, String sgmt);

    /**
     * 按日期清除数据
     *
     * @param fromDate 开始日期
     * @param toDate   结束日期
     */
    void clearData(String fromDate, String toDate);
}