package io.ibs.modules.irp.pbccrc.psgrecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.common.utils.DateUtils;
import io.ibs.modules.irp.pbccrc.psgrecord.dao.PsgRecordgetDatePersonDao;
import io.ibs.modules.irp.pbccrc.psgrecord.dto.PsgRecordgetDatePersonDTO;
import io.ibs.modules.irp.pbccrc.psgrecord.entity.PsgRecordgetDatePersonEntity;
import io.ibs.modules.irp.pbccrc.psgrecord.service.PsgRecordgetDatePersonService;
import io.ibs.modules.irp.record.entity.ReportRecordPrsnEntity;
import io.ibs.modules.irp.record.service.ReportRecordPrsnService;
import io.ibs.modules.irp.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;

/**
 * 个人上送接口数据记录表
 *
 * @<NAME_EMAIL>
 * @since 3.0 2022-04-19
 */
@Service
@Slf4j
public class PsgRecordgetDatePersonServiceImpl extends CrudServiceImpl<PsgRecordgetDatePersonDao, PsgRecordgetDatePersonEntity, PsgRecordgetDatePersonDTO> implements PsgRecordgetDatePersonService {
//    private static final String PRSN_ITEM_KEY = "prsnItem";
    @Autowired
    ReportRecordPrsnService reportRecordPrsnService;

    @Override
    public QueryWrapper<PsgRecordgetDatePersonEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<PsgRecordgetDatePersonEntity> wrapper = new QueryWrapper<>();
        String rptCode = (String) params.get("rptCode");
        wrapper.eq(StringUtils.isNotBlank(rptCode), "RPT_CODE", rptCode);
        String asyDate = (String) params.get("asyDate");
        wrapper.eq(StringUtils.isNotBlank(asyDate), "ASY_DATE", asyDate);
        String itabGetDate = (String) params.get("itabGetDate");
        wrapper.eq(StringUtils.isNotBlank(itabGetDate), "ITAB_GET_DATE", itabGetDate);
        return wrapper;
    }

    @Override
    public boolean checkAsy(String day, String sgmt) {
        if ("first".equals(day)) {
            day = DateUtils.format(new Date(), "yyyyMMdd");
        }
//        PsgRecordgetDatePersonEntity entity = baseDao.selectOne(new QueryWrapper<PsgRecordgetDatePersonEntity>().lambda().
//                eq(PsgRecordgetDatePersonEntity::getItabGetDate, day));
//        // 个人模块没有任何信息报送过，则新增报送记录
//        if (entity == null) {
//            log.info("尚未报送，新增报送记录");
//            entity = new PsgRecordgetDatePersonEntity();
//            entity.setItabGetDate(day);
//            entity.setRptCode(Constants.deptCode);
//            entity.setAsyDate(new Date());
//            JsonArray jsonArray = new JsonArray();
//            JsonObject jsonObject = new JsonObject();
//            jsonObject.addProperty(PRSN_ITEM_KEY, prsnItemName);
//            jsonArray.add(jsonObject);
//            entity.setContent(new Gson().toJson(jsonArray));
//            baseDao.insert(entity);
//        } else {
//            log.info("content:" + entity.getContent());
//            // 遍历content，检查是否存在prsnItem为prsnItemName的记录
//            JsonArray content = new Gson().fromJson(entity.getContent(), JsonArray.class);
//            for (JsonElement json : content) {
//                JsonObject jsonObject = (JsonObject) json;
//                String prsnItem = jsonObject.get(PRSN_ITEM_KEY).getAsString();
//                if (prsnItemName.equals(prsnItem)) {
//                    log.warn("[{}]已经报送过{}数据，请勿重复报送！", day, prsnItemName);
//                    return true;
//                }
//            }
//
//            // 不存在prsnItemName记录时，在content中添加记录
//            log.info(prsnItemName + "尚未报送，添加报送记录");
//            JsonObject jsonObject = new JsonObject();
//            jsonObject.addProperty(PRSN_ITEM_KEY, prsnItemName);
//            content.add(jsonObject);
//            entity.setContent(new Gson().toJson(content));
//            baseDao.updateById(entity);
//        }

        // 如果指定日期存在报送记录，则认为当天已经报送过
        boolean check = reportRecordPrsnService.exists(ReportRecordPrsnEntity.builder()
                .reportDate(day)
                .sgmt(sgmt)
                .reportFlag(Constants.REPORT_FLAG_1)
                .build());
        if (check) {
            log.warn("[{}]已经报送过[{}]数据，请勿重复报送！", day, sgmt);
        }
        return check;
    }

    @Override
    @Transactional
    public void clearData(String fromDate, String toDate) {
        baseDao.delete(new UpdateWrapper<PsgRecordgetDatePersonEntity>().lambda()
                .between(PsgRecordgetDatePersonEntity::getItabGetDate, fromDate, toDate));

        reportRecordPrsnService.clearData(fromDate, toDate);
    }
}