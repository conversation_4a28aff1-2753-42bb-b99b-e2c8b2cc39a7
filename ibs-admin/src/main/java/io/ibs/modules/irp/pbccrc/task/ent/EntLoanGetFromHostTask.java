package io.ibs.modules.irp.pbccrc.task.ent;

import io.ibs.common.constant.Constant;
import io.ibs.common.utils.DateUtils;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.modules.irp.pbccrc.entloan.entity.*;
import io.ibs.modules.irp.pbccrc.entloan.service.*;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.irp.util.CstmInfoExch;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 征信二代
 * 从核心系统以及其他系统抽取企业贷款信息到本地数据库
 * 参数为空表示报送当天的数据,参数为指定日期表示报送指定日期的数据
 *
 * <AUTHOR>
 * @since 2022/8/10
 */
@Deprecated
//@Service
@Slf4j
public class EntLoanGetFromHostTask extends BaseTask {

    private TransactionHelper transactionHelper;
    private EntLoanGetDataService entLoanSvc;
    private EilbAcctbssgmtService eilbSvc;// 基础段
    private EilcAcctbsinfsgmtService eilcSvc;// 基本信息段
    private EildRltrepymtinfsgmService eildSvc;// 相关还款责任人
    private EilhActlbltyinfsgmtService eilhSvc;// 还款表现信息段
    private EiliAcctspectrstdspnService eiliSvc;// 特定交易说明段
    private EileMotgacltalctrctinfService eileSvc;//抵质押信息

    @Autowired
    public EntLoanGetFromHostTask(TransactionHelper transactionHelper, EntLoanGetDataService entLoanSvc,
                                  EilbAcctbssgmtService eilbSvc,
                                  EilcAcctbsinfsgmtService eilcSvc,
                                  EildRltrepymtinfsgmService eildSvc,
                                  EilhActlbltyinfsgmtService eilhSvc,
                                  EiliAcctspectrstdspnService eiliSvc,
                                  EileMotgacltalctrctinfService eileSvc) {
        super("征信二代-从核心系统以及其他系统抽取企业贷款信息", EntLoanGetFromHostTask.class);
        this.transactionHelper = transactionHelper;
        this.entLoanSvc = entLoanSvc;
        this.eilbSvc = eilbSvc;
        this.eilcSvc = eilcSvc;
        this.eildSvc = eildSvc;
        this.eilhSvc = eilhSvc;
        this.eiliSvc = eiliSvc;
        this.eileSvc = eileSvc;
    }

    /**
     * 采集时点:账户开立、账户关闭、放款（不含首次放款）、约定还款、实际还款（特指在非约定还款日还款）、五级分类调整、展期发生、其他报送日期
     *
     * @param params
     * @throws Exception
     */
    @Override
    public void execute(String params) throws Exception {
        String tranDate = DateUtils.format(new Date(), "yyyy-MM-dd");
        if (!StringUtils.isBlank(params)) {
            tranDate = DateUtils.format(DateUtils.parse(params, "yyyyMMdd"), "yyyy-MM-dd");
        }
        Date today = new Date();
        final String tranDay = tranDate;

        List<EilbAcctbssgmtEntity> eilbList = new ArrayList<>();// 基础段
        List<EilcAcctbsinfsgmtEntity> eilcList = new ArrayList<>();// 基本信息段
        List<EildRltrepymtinfsgmEntity> eildList = new ArrayList<>();// 相关还款责任人
        List<EilhActlbltyinfsgmtEntity> eilhList = new ArrayList<>();// 还款表现信息段
        List<EiliAcctspectrstdspnEntity> eiliList = new ArrayList<>();// 特定交易说明段
        List<EileMotgacltalctrctinfEntity> eileList = new ArrayList();//抵质押信息

        // 获取指定日期开立的贷款
        List<EntLoanGetDataEntity> openList = entLoanSvc.getCorpLoanOfOpen(tranDate);
        log.info("获取指定日期[" + tranDate + "]开立的贷款,数量[{}]", CollectionUtils.isEmpty(openList) ? 0 : openList.size());
        if (openList != null && openList.size() > 0) {
            // 获取开立的保证贷款 查询保证贷款的相关还款责任人
            List<String> guarLoan = openList.stream().filter(x -> "1".equals(x.getGuarMode())).map(y -> y.getLoanNo()).collect(Collectors.toList());
            List<EntLoanGuarEntity> guarList = CollectionUtils.isEmpty(guarLoan) ? new ArrayList<>() : entLoanSvc.getCorpLoanOfGuar(guarLoan);
            List<EntLoanRetEntity> retList = entLoanSvc.getCorpLoanRet(openList.stream().map(x -> x.getLoanNo()).collect(Collectors.toList()), tranDate);
            openList.forEach(open -> {
                // 基础段
                EilbAcctbssgmtEntity eilb = new EilbAcctbssgmtEntity();
                eilb.setCustId(open.getCstmNo());
                eilb.setBussNum(open.getLoanNo());
                eilb.setName(open.getCstmName());
                eilb.setIdType("20");// 统一社会信用代码
                eilb.setIdNum(open.getPaperNo().substring(1).trim());
                eilb.setUpdateTime(today);
                eilb.setUpdator(Constants.UPDATER_FOR_TASK);
                eilbList.add(eilb);

                // 基本信息段
                EilcAcctbsinfsgmtEntity eilc = new EilcAcctbsinfsgmtEntity();
                eilc.setBussNum(open.getLoanNo());
                eilc.setBusiLines("11");// 借贷业务大类 11-贷款
                eilc.setBusiDtlLines("11");// 借贷业务种类细分 11-流动资金贷款
                eilc.setOpenDate(open.getOpenDate());// 开户日期
                eilc.setLoanAmt(open.getLoanAmt());// 贷款金额
                eilc.setFlag("0");// 分次放款标志
                eilc.setDueDate(open.getDueDate());
                eilc.setRepayMode(CstmInfoExch.entRepayModeExch(open.getRepayMode(), open.getTermNo()));// 还款方式
                eilc.setRepayFreqcy(CstmInfoExch.entRepayFreqExch(open.getRepayFreqcy(), open.getRepayMode()));// 还款频率
                eilc.setApplyBusiDist(Constants.distCode);
                eilc.setGuarMode(CstmInfoExch.guarModeExchEnt(open.getGuarMode()));// 担保方式
                eilc.setOthRepyGuarway("0");// 其他还款保证方式 0-无
                eilc.setLoanTimeLimCat(CstmInfoExch.loanLimitExch(open.getOpenDate(), open.getDueDate()));// 借款期限分类
                eilc.setLoaFrm("1");// 贷款发放形式 1-新增
                eilc.setActInvest(CstmInfoExch.actInvestExch(open.getActInvest()));// 贷款实际投向
                eilc.setFundSou(CstmInfoExch.entFoudSouExch(open.getFundSou()));// 业务经营类型
                eilc.setAssetTrandFlag("0");// 资产转让标志 0-未转让(代表该账户对应的债权人未发生转让)
                eilc.setUpdateTime(today);
                eilc.setUpdator(Constants.UPDATER_FOR_TASK);
                eilcList.add(eilc);

                // 还款表现段
                EilhActlbltyinfsgmtEntity eilh = new EilhActlbltyinfsgmtEntity();
                eilh.setBussNum(open.getLoanNo());
                eilh.setAcctStatus("10");// 账户状态 10-正常活动
                eilh.setAcctBal(open.getLoanAmt());// 余额
                eilh.setBalChgDate(open.getOpenDate());// 余额变化日期
                eilh.setFiveCate(open.getFiveCate());// 五级分类
                eilh.setFiveCateAdjDate(open.getOpenDate());// 五级分类认定日期
                eilh.setTotOverd(BigDecimal.ZERO);// 当前逾期总额
                eilh.setOverdPrinc(BigDecimal.ZERO);// 当前逾期本金
                eilh.setOverdDy(0L);// 当前逾期天数
                eilh.setLatRpyAmt(BigDecimal.ZERO);
                eilh.setLatRpyDate(open.getOpenDate());
                eilh.setLatRpyPrinAmt(BigDecimal.ZERO);
                eilh.setLatAgrrRpyAmt(BigDecimal.ZERO);
                eilh.setLatAgrrRpyDate(open.getOpenDate());
                // 按月还本
                if (open.getTermNo().intValue() >= 12) {
                    eilh.setNxtAgrrRpyDate(retList.stream().filter(x -> x.getLoanNo().equals(open.getLoanNo())).min((a, b) -> a.getDueDate().compareTo(b.getDueDate())).get().getDueDate());
                } else {
                    // 非按月还本时,认为企业贷款均为按年还本或者到期还本
                    // 不参与批量结息
                    if ("0".equals(open.getIntFlag())) {
                        eilh.setNxtAgrrRpyDate(retList.stream().filter(x -> x.getLoanNo().equals(open.getLoanNo())).min((a, b) -> a.getDueDate().compareTo(b.getDueDate())).get().getDueDate());
                    } else if ("1".equals(open.getIntFlag())) {
                        // 按月结息 开户日期在当月21号前,结息日为当月21号;开户日期在当月21号后,结息日为下个月21号
                        String month21 = DateUtils.format(open.getOpenDate(), "yyyy-MM").concat("-21");
                        eilh.setNxtAgrrRpyDate(DateUtils.compareTwoDate(open.getOpenDate(), DateUtils.stringToDate(month21, "yyyy-MM-dd")) < 0 ?
                                DateUtils.stringToDate(month21, "yyyy-MM-dd") : DateUtils.addDateMonths(DateUtils.stringToDate(month21, "yyyy-MM-dd"), 1));
                    } else if ("2".equals(open.getIntFlag())) {
                        // 按季结息
                        eilh.setNxtAgrrRpyDate(DateUtils.stringToDate(DateUtils.getSeasonDay(DateUtils.format(open.getOpenDate(), "yyyy-MM-dd")), "yyyy-MM-dd"));
                    } else if ("3".equals(open.getIntFlag())) {
                        // 按年结息(6月份)
                        String year0621 = DateUtils.format(open.getOpenDate(), "yyyy").concat("-06-21");
                        if (Integer.parseInt(DateUtils.format(open.getOpenDate(), "MMdd")) < 621) {
                            eilh.setNxtAgrrRpyDate(DateUtils.stringToDate(year0621, "yyyy-MM-dd"));// 6.21前开贷的,取当前年份的6.21
                        } else {
                            eilh.setNxtAgrrRpyDate(DateUtils.addDateYears(DateUtils.stringToDate(year0621, "yyyy-MM-dd"), 1));// 6.21后开贷的,取明年6.21
                        }
                    } else if ("4".equals(open.getIntFlag())) {
                        // 按年结息(9月份)
                        String year0921 = DateUtils.format(open.getOpenDate(), "yyyy").concat("-09-21");
                        if (Integer.parseInt(DateUtils.format(open.getOpenDate(), "MMdd")) < 921) {
                            eilh.setNxtAgrrRpyDate(DateUtils.stringToDate(year0921, "yyyy-MM-dd"));// 9.21前开贷的,取当前年份的9.21
                        } else {
                            eilh.setNxtAgrrRpyDate(DateUtils.addDateYears(DateUtils.stringToDate(year0921, "yyyy-MM-dd"), 1));// 9.21后开贷的,取明年9.21
                        }
                    } else {
                        // 按年结息(12月份)
                        String year1221 = DateUtils.format(open.getOpenDate(), "yyyy").concat("-12-21");
                        if (Integer.parseInt(DateUtils.format(open.getOpenDate(), "MMdd")) < 1221) {
                            eilh.setNxtAgrrRpyDate(DateUtils.stringToDate(year1221, "yyyy-MM-dd"));// 12.21前开贷的,取当前年份的12.21
                        } else {
                            eilh.setNxtAgrrRpyDate(DateUtils.addDateYears(DateUtils.stringToDate(year1221, "yyyy-MM-dd"), 1));// 12.21后开贷的,取明年12.21
                        }
                    }
                }
                eilh.setUpdateTime(today);
                eilh.setUpdator(Constants.UPDATER_FOR_TASK);
                eilh.setRptDateCode("10");// 报告时点说明代码 10-新开户/首次报送
                eilhList.add(eilh);

                if ("0".equals(open.getGuarMode()) || "2".equals(open.getGuarMode())) {
                    EileMotgacltalctrctinfEntity eileEntity = new EileMotgacltalctrctinfEntity();
                    eileEntity.setBussNum(open.getLoanNo());
                    eileEntity.setCcBussNum(open.getLoanNo());
                    eileEntity.setUpdateTime(today);
                    eileEntity.setUpdator(Constants.UPDATER_FOR_TASK);
                    eileList.add(eileEntity);
                }
            });

            // 相关还款责任人
            guarList.forEach(guar -> {
                EildRltrepymtinfsgmEntity eild = new EildRltrepymtinfsgmEntity();
                eild.setBussNum(guar.getJjh());
                // 个人
                if ("0".equals(guar.getDbfs())) {
                    eild.setArlpIdType("1");// 身份类别 1-自然人
                    eild.setArlpName(guar.getZrrDbrxm());// 责任人名称
                    eild.setArlpCertType(CstmInfoExch.pbccrcIDTypeExch(guar.getZrrZjlx()));// 责任人身份标识类型
                    eild.setArlpCertNum(guar.getZrrZjhm());// 责任人身份标识号码
                    eild.setArlpType("2");// 还款责任人类型 2-保证人
                    eild.setWartySign("0");// 联保标志 0-单人或多人联保
                } else if ("1".equals(guar.getDbfs())) {
                    // 企业担保
                    eild.setArlpIdType("2");// 身份类别 2-组织机构
                    eild.setArlpName(guar.getQyQymc());// 责任人名称
                    eild.setArlpCertType("30");// 责任人身份标识类型
                    eild.setArlpCertNum(guar.getQyZzjgdm());// 责任人身份标识号码
                    eild.setArlpType("9");// 还款责任人类型 9-其他
                } else {
                    // 担保公司
                    eild.setArlpIdType("2");// 身份类别 2-组织机构
                    eild.setArlpType("9");// 还款责任人类型 9-其他
                }
                eild.setArlpAmt(openList.stream().filter(x -> x.getLoanNo().equals(guar.getJjh())).findFirst().get().getLoanAmt());// 还款责任金额
                eild.setMaxBussNum(Constant.deptCode + guar.getHtbh());// 保证合同业务号
                eild.setUpdateTime(today);
                eild.setUpdator(Constants.UPDATER_FOR_TASK);
                eildList.add(eild);
            });
        }

        // 获取指定日期结清的贷款
        List<EntLoanGetDataEntity> closeList = entLoanSvc.getCorpLoanOfClose();
        log.info("获取指定日期[" + tranDate + "]结清的贷款,数量[{}]", CollectionUtils.isEmpty(closeList) ? 0 : closeList.size());
        closeList.forEach(close -> {
            // 还款表现段
            EilhActlbltyinfsgmtEntity eilh = new EilhActlbltyinfsgmtEntity();
            eilh.setBussNum(close.getLoanNo());
            eilh.setAcctStatus("21");// 账户状态 10-正常活动
            eilh.setAcctBal(BigDecimal.ZERO);// 余额
            eilh.setBalChgDate(close.getOpenDate());// 余额变化日期
            eilh.setFiveCate(close.getFiveCate());// 五级分类
            eilh.setFiveCateAdjDate(close.getOpenDate());// 五级分类认定日期
            eilh.setTotOverd(BigDecimal.ZERO);// 当前逾期总额
            eilh.setOverdPrinc(BigDecimal.ZERO);// 当前逾期本金
            eilh.setOverdDy(0L);// 当前逾期天数
            eilh.setLatRpyAmt(close.getAmt());// 最近一次实际还款金额(本+利)
            eilh.setLatRpyDate(DateUtils.parse(tranDay, "yyyy-MM-dd"));// 最近一次实际还款日期
            eilh.setLatRpyPrinAmt(close.getRetBal());// 最近一次实际归还本金
            eilh.setLatAgrrRpyDate(close.getLastAgreeDate());// 最近一次约定还款日(报告日期之前最晚的约定还款日)
            eilh.setLatAgrrRpyAmt(close.getLastAgreeAmt());// 最近一次应还款金额
            eilh.setCloseDate(DateUtils.parse(tranDay, "yyyy-MM-dd"));// 账户关闭日期
            eilh.setUpdateTime(today);
            eilh.setUpdator(Constants.UPDATER_FOR_TASK);
            eilh.setRptDateCode("20");// 报告时点说明代码 20-账户关闭
            eilhList.add(eilh);
        });

        // 获取指定日期是约定还款日的贷款还款情况
        List<EntLoanGetDataEntity> appointList = entLoanSvc.getCorpLoanOnAppointDate(tranDate);
        log.info("获取指定日期[" + tranDate + "]需要还款的贷款,数量[{}]", CollectionUtils.isEmpty(appointList) ? 0 : appointList.size());
        fillData(appointList, "32", tranDate, eilhList);// 报告时点说明代码 32-约定还款日结算（不含账户在约定还款日关闭）

        // 获取在非约定还款日有实际还款行为的贷款
        List<EntLoanGetDataEntity> repayList = entLoanSvc.getCorpLoanOfRepay(tranDate);
        log.info("获取指定日期[" + tranDate + "]是非约定日但有实际还款的贷款,数量[{}]", CollectionUtils.isEmpty(repayList) ? 0 : repayList.size());
        fillData(repayList, "33", tranDate, eilhList);// 33-实际还款（特指在非约定还款日还款）

        // 获取在指定日期发生五级分类调整的
        List<EntLoanGetDataEntity> fiveList = entLoanSvc.getCorpLoanOfFiveCate(tranDate);
        log.info("获取指定日期[" + tranDate + "]发生五级分类调整的贷款,数量[{}]", CollectionUtils.isEmpty(fiveList) ? 0 : fiveList.size());
        fillData(fiveList, "41", tranDate, eilhList);// 41-五级分类调整

        // 获取在指定日期发生展期的
        List<EntLoanGetDataEntity> extList = entLoanSvc.getCorpLoanOfExt(tranDate);
        log.info("获取指定日期[" + tranDate + "]发生展期的贷款,数量[{}]", CollectionUtils.isEmpty(extList) ? 0 : extList.size());

        if (!CollectionUtils.isEmpty(extList)) {
            fillData(extList, "42", tranDate, eilhList);// 42-展期发生

            List<EntLoanRetEntity> extRetList = entLoanSvc.getCorpLoanRet(extList.stream().map(x -> x.getLoanNo()).collect(Collectors.toList()), null);
            extList.forEach(spc -> {
                // 特定交易说明段
                EiliAcctspectrstdspnEntity eili = new EiliAcctspectrstdspnEntity();
                eili.setBussNum(spc.getLoanNo());
                eili.setTranDate(DateUtils.parse(tranDay, "yyyy-MM-dd"));
                eili.setChanTranType("11");// 交易类型 11-展期
                eili.setTranAmt(spc.getBal());// 展期时的贷款余额

                Date extDueDate = extRetList.stream().filter(e -> e.getLoanNo().equals(spc.getLoanNo()) && "1".equals(e.getExtFlag()) && e.getDistanceDay() > 0).
                        min((a, b) -> DateUtils.compareTwoDate(a.getDueDate(), b.getDueDate())).get().getDueDate();
                int year = Integer.parseInt(DateUtils.format(extDueDate, "yyyy")) - Integer.parseInt(tranDay.substring(0, 4));
                int month = Integer.parseInt(DateUtils.format(extDueDate, "MM")) - Integer.parseInt(tranDay.substring(5, 7));
                eili.setDueTranMon(Long.parseLong((year - 1) * 12 + 12 + month + ""));// 对于展期,则填写延长的月数

                eiliList.add(eili);
            });
        }

        // 获取指定日期发生提前还款
        List<EntLoanGetDataEntity> prepayList = entLoanSvc.getCorpLoanOfPrepay(tranDate);
        log.info("获取指定日期[" + tranDate + "]提前还款的贷款,数量[{}]", CollectionUtils.isEmpty(prepayList) ? 0 : prepayList.size());

        if(!CollectionUtils.isEmpty(prepayList)){
            fillData(prepayList, "49", tranDate, eilhList);// 49-其他情况

            List<EntLoanRetEntity> preRetList = entLoanSvc.getCorpLoanRet(prepayList.stream().map(x -> x.getLoanNo()).collect(Collectors.toList()), null);
            prepayList.forEach(spc -> {
                // 特定交易说明段
                EiliAcctspectrstdspnEntity eili = new EiliAcctspectrstdspnEntity();
                eili.setBussNum(spc.getLoanNo());
                eili.setTranDate(DateUtils.parse(tranDay, "yyyy-MM-dd"));
                eili.setChanTranType("12");// 交易类型 12-提前还款
                eili.setTranAmt(spc.getAmt());// 表示提前归还的本金
                Date preDate = preRetList.stream().filter(p -> p.getLoanNo().equals(spc.getLoanNo()) && p.getDistanceDay() > 0).
                        min((a, b) -> DateUtils.compareTwoDate(a.getDueDate(), b.getDueDate())).get().getDueDate();
                int year = Integer.parseInt(DateUtils.format(preDate, "yyyy")) - Integer.parseInt(tranDay.substring(0, 4));
                int month = Integer.parseInt(DateUtils.format(preDate, "MM")) - Integer.parseInt(tranDay.substring(5, 7));
                eili.setDueTranMon(Long.parseLong((year - 1) * 12 + 12 + month + ""));// 对于提前还款,则填写提前还款的月数

                eiliList.add(eili);
            });
        }

        // 获取指定日期发生贷款核销的
        List<EntLoanGetDataEntity> cavList = entLoanSvc.getCorpLoanOfCav(tranDate);
        log.info("获取指定日期[" + tranDate + "]核销的贷款,数量[{}]", CollectionUtils.isEmpty(cavList) ? 0 : cavList.size());
        fillData(cavList, "49", tranDate, eilhList);// 49-其他情况
        cavList.forEach(spc -> {
            // 特定交易说明段
            EiliAcctspectrstdspnEntity eili = new EiliAcctspectrstdspnEntity();
            eili.setBussNum(spc.getLoanNo());
            eili.setTranDate(DateUtils.parse(tranDay, "yyyy-MM-dd"));
            eili.setChanTranType("21");// 交易类型 21-核损核销
            eili.setTranAmt(spc.getBal());// 核损核销的金额
            eili.setDueTranMon(0L);// 对于非提前还款和非展期的情况,填写0
            eili.setUpdateTime(today);
            eili.setUpdator(Constants.UPDATER_FOR_TASK);
            eiliList.add(eili);
        });

        // 已逾期的企业贷款,如果当月没有需要报送的时点,则需要每月月底报送还款表现
        // 月底最后一天执行
        if (tranDate.equals(DateUtils.format(
                DateUtils.getMonthLastDay(tranDate.substring(0, 4).concat(tranDate.substring(5, 7))), "yyyy-MM-dd"))) {
            String firstDay = tranDate.substring(0, 4).concat(tranDate.substring(5, 8)).concat("01");
            List<EntLoanGetDataEntity> dueList = entLoanSvc.getCorpLoanOfDueAndUnReportInMonth(tranDate, firstDay, tranDate);
            fillData(dueList, "49", tranDate, eilhList);// 49-其他情况
        }

        //进行事务管理
        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();

        try {
            eilbSvc.insertBatch(eilbList.stream().distinct().collect(Collectors.toList()), 100);
            eilcSvc.insertBatch(eilcList.stream().distinct().collect(Collectors.toList()), 100);
            eildSvc.insertBatch(eildList.stream().distinct().collect(Collectors.toList()), 100);
            eilhSvc.insertBatch(eilhList.stream().distinct().collect(Collectors.toList()), 100);
            eiliSvc.insertBatch(eiliList.stream().distinct().collect(Collectors.toList()), 100);
            eileSvc.insertBatch(eileList.stream().distinct().collect(Collectors.toList()), 100);
            transactionHelper.commit(tm);
        } catch (Exception e) {
            log.error("企业借贷交易信息取数时捕获到异常", e);
            transactionHelper.rollback(tm);
            throw e;
        }
    }

    /**
     * 处理非开立结清日的贷款基础段数据和还款表现段数据
     *
     * @param entLoanList 基础段+还款信息段
     * @param rptDataCode 报告时点代码
     * @param tranDate    交易日期
     * @param eilhList    还款表现段列表
     */
    private void fillData(List<EntLoanGetDataEntity> entLoanList, String rptDataCode, String tranDate, List<EilhActlbltyinfsgmtEntity> eilhList) {
        Date today = new Date();
        entLoanList.forEach(ent -> {
            // 基础段
            /*EilbAcctbssgmtEntity eilb = new EilbAcctbssgmtEntity();
            eilb.setCustId(ent.getCstmNo());
            eilb.setBussNum(ent.getLoanNo());
            eilb.setName(ent.getCstmName());
            eilb.setIdType("20");// 统一社会信用代码
            eilb.setIdNum(ent.getPaperNo().substring(1).trim());
            eilb.setUpdateTime(today);
            eilb.setUpdator(Constants.UPDATER_FOR_TASK);
            eilbList.add(eilb);*/

            // 还款表现段
            EilhActlbltyinfsgmtEntity eilh = new EilhActlbltyinfsgmtEntity();
            eilh.setBussNum(ent.getLoanNo());
            eilh.setAcctStatus("10");// 账户状态 10-正常活动
            eilh.setAcctBal(ent.getBal());// 余额
            eilh.setBalChgDate(ent.getBalChgDate());// 余额变化日期
            eilh.setFiveCate(ent.getFiveCate());// 五级分类
            eilh.setFiveCateAdjDate(ent.getFiveCateDate());// 五级分类认定日期
            eilh.setTotOverd(ent.getTotalOver());// 当前逾期总额
            eilh.setOverdPrinc(ent.getOverBal());// 当前逾期本金
            eilh.setOverdDy(ent.getOverDay());// 当前逾期天数
            eilh.setLatRpyAmt(ent.getAmt());// 最近一次实际还款金额(本+利)
            eilh.setLatRpyDate(DateUtils.parse(tranDate, "yyyy-MM-dd"));// 最近一次实际还款日期
            eilh.setLatRpyPrinAmt(ent.getRetBal());// 最近一次实际归还本金
            eilh.setLatAgrrRpyDate(ent.getLastAgreeDate());// 最近一次约定还款日(报告日期之前最晚的约定还款日)
            eilh.setLatAgrrRpyAmt(ent.getLastAgreeAmt());// 最近一次应还款金额
            eilh.setUpdateTime(today);
            eilh.setUpdator(Constants.UPDATER_FOR_TASK);
            eilh.setRptDateCode(rptDataCode);// 报告时点说明代码
            eilhList.add(eilh);
        });
    }
}
