package io.ibs.modules.irp.pbccrc.task.ent;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.commons.dynamic.datasource.config.DynamicContextHolder;
import io.ibs.modules.irp.pbccrc.common.TaskUtil;
import io.ibs.modules.irp.pbccrc.entrep.entity.AsyEifd2007cashflowsEntity;
import io.ibs.modules.irp.pbccrc.entrep.entity.AsyEiod2007BalanceSheetEntity;
import io.ibs.modules.irp.pbccrc.entrep.entity.AsyEisd2007ispaEntity;
import io.ibs.modules.irp.pbccrc.entrep.service.Eifd2007cashflowsService;
import io.ibs.modules.irp.pbccrc.entrep.service.Eiod2007BalanceSheetService;
import io.ibs.modules.irp.pbccrc.entrep.service.Eisd2007ispaService;
import io.ibs.modules.irp.pbccrc.psgrecord.dto.AsyPsgRecordgetDateEnterDTO;
import io.ibs.modules.irp.pbccrc.psgrecord.entity.AsyPsgRecordgetDateEnterEntity;
import io.ibs.modules.irp.pbccrc.psgrecord.service.AsyPsgRecordgetDateEnterService;
import io.ibs.modules.irp.pbccrc.psgrecord.service.PsgRecordgetDateEnterService;
import io.ibs.modules.irp.pbccrc.whitelist.entity.TWhiteListEntity;
import io.ibs.modules.irp.pbccrc.whitelist.service.TWhiteListService;
import io.ibs.modules.irp.record.entity.ReportRecordEtprEntity;
import io.ibs.modules.irp.record.service.ReportRecordEtprService;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 征信二代
 * 从本地数据库抽取企业财务报表信息到金电数据库
 * 任务参数为first表示首次报送,任务参数为空表示日常报送抽取数据
 *
 * <AUTHOR>
 * @Date 2022/7/28 19:04
 */
@Slf4j
@Service
public class EntRepSendToJDTask extends BaseTask {

    private final TransactionHelper transactionHelper;
    private final ReportRecordEtprService reportRecordEtprService;
    private final PsgRecordgetDateEnterService psgRecordgetDateEnterService;
    private final AsyPsgRecordgetDateEnterService asyPsgRecordgetDateEnterService;
    private final Eifd2007cashflowsService cashflowsService;
    private final Eiod2007BalanceSheetService balanceSheetService;
    private final Eisd2007ispaService ispaService;
    private final TWhiteListService twhiteListSvc;

    @Autowired
    public EntRepSendToJDTask(TransactionHelper transactionHelper, ReportRecordEtprService reportRecordEtprService, PsgRecordgetDateEnterService psgRecordgetDateEnterService, AsyPsgRecordgetDateEnterService asyPsgRecordgetDateEnterService, Eifd2007cashflowsService cashflowsService, Eiod2007BalanceSheetService balanceSheetService, Eisd2007ispaService ispaService,
                              TWhiteListService twhiteListSvc) {
        super("征信二代-企业财务报表信息到金电数据库", EntRepSendToJDTask.class);
        this.transactionHelper = transactionHelper;
        this.reportRecordEtprService = reportRecordEtprService;
        this.psgRecordgetDateEnterService = psgRecordgetDateEnterService;
        this.asyPsgRecordgetDateEnterService = asyPsgRecordgetDateEnterService;
        this.cashflowsService = cashflowsService;
        this.balanceSheetService = balanceSheetService;
        this.ispaService = ispaService;
        this.twhiteListSvc = twhiteListSvc;
    }


    @Override
    public void execute(String params) throws Exception {
        String itabGetDate;
        params = super.checkDateParam(params, "yyyyMMdd");
        String prefix = "[企业财报]";
        // 检查当前日期是否已经报送过
        log.info(prefix + "检查当前日期是否已经报送过");
        if (psgRecordgetDateEnterService.checkAsy(params, ReportRecordEtprEntity.INFO_TYPE_EID)) {
            return;
        }

        // 获取一天的开始和结束 或 时间段
        Date[] between;
        if (params.contains("-")) {
            between = TaskUtil.getBeginEndOfPeriod(params.split("-")[0], params.split("-")[1]);
            itabGetDate = params.split("-")[1];
        } else {
            between = TaskUtil.getBeginEndOfDay(params);
            itabGetDate = params;
        }
        BigDecimal zero = BigDecimal.ZERO;
        // 现金流量表
        List<AsyEifd2007cashflowsEntity> cashflowsListTmp = cashflowsService.getReportList(between);
        cashflowsListTmp = cashflowsListTmp.stream().filter(e ->
                !NumberUtil.equals(e.getTotCasIfoa(), zero) ||
                        !NumberUtil.equals(e.getNetCasFfoa(), zero) ||
                        !NumberUtil.equals(e.getNetCasFfia(), zero) ||
                        !NumberUtil.equals(e.getNetCasFffa(), zero) ||
                        !NumberUtil.equals(e.getNetIncIcace(), zero) ||
                        !NumberUtil.equals(e.getTheFinCaceb(), zero)
        ).collect(Collectors.toList());
        // 资产负债表
        List<AsyEiod2007BalanceSheetEntity> balanceSheetListTmp = balanceSheetService.getReportList(between);
        balanceSheetListTmp = balanceSheetListTmp.stream().filter(e ->
                !NumberUtil.equals(e.getTotCurAss(), zero) ||
                        !NumberUtil.equals(e.getTotAss(), zero) ||
                        !NumberUtil.equals(e.getTotCurLia(), zero) ||
                        !NumberUtil.equals(e.getTotNonCl(), zero) ||
                        !NumberUtil.equals(e.getTotEqu(), zero) ||
                        !NumberUtil.equals(e.getTotEquAl(), zero)
        ).collect(Collectors.toList());
        // 利润分配表
        List<AsyEisd2007ispaEntity> ispaListTmp = ispaService.getReportList(between);
        ispaListTmp = ispaListTmp.stream().filter(e ->
                !NumberUtil.equals(e.getRevOfSal(), zero) ||
                        !NumberUtil.equals(e.getOpePro(), zero) ||
                        !NumberUtil.equals(e.getProBefTax(), zero) ||
                        !NumberUtil.equals(e.getNetPro(), zero)
        ).collect(Collectors.toList());

        // 过滤白名单
        List<String> cstmNoWhiteList = twhiteListSvc.getWhiteCstm(TWhiteListEntity.CSTM_TYPE_2, TWhiteListEntity.STATE_0);

        List<AsyEifd2007cashflowsEntity> cashflowsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(cashflowsListTmp)) {
            cashflowsList = cashflowsListTmp.stream().filter(entity -> !cstmNoWhiteList.contains(entity.getCustId())).collect(Collectors.toList());
        }
        List<AsyEiod2007BalanceSheetEntity> balanceSheetList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(balanceSheetListTmp)) {
            balanceSheetList = balanceSheetListTmp.stream().filter(entity -> !cstmNoWhiteList.contains(entity.getCustId())).collect(Collectors.toList());
        }
        List<AsyEisd2007ispaEntity> ispaList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ispaListTmp)) {
            ispaList = ispaListTmp.stream().filter(entity -> !cstmNoWhiteList.contains(entity.getCustId())).collect(Collectors.toList());
        }

        log.info(prefix + "现金流量表有{}条待报送", cashflowsList.size());
        log.info(prefix + "资产负债表有{}条待报送", balanceSheetList.size());
        log.info(prefix + "利润分配表有{}条待报送", ispaList.size());
        // 是否已经报过
        this.setRptDateCode(cashflowsList, balanceSheetList, ispaList);
        if (CollectionUtil.isEmpty(cashflowsList) && CollectionUtil.isEmpty(balanceSheetList) && CollectionUtil.isEmpty(ispaList)) {
            log.info(prefix + "无需要报送的财务报表信息：{}", params);
            return;
        }
        // 设置数据源
        DynamicContextHolder.push("credit");
        // 开启事务
        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();
        try {
            QueryWrapper<AsyPsgRecordgetDateEnterEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("ITAB_GET_DATE", itabGetDate);
            List<AsyPsgRecordgetDateEnterDTO> logs = asyPsgRecordgetDateEnterService.list(wrapper);
            if (CollectionUtils.isEmpty(logs)) {
                asyPsgRecordgetDateEnterService.createRecord(itabGetDate);
            }
            // 上报插入记录
            Db.saveBatch(cashflowsList);
            Db.saveBatch(balanceSheetList);
            Db.saveBatch(ispaList);
            // 提交事务
            transactionHelper.commit(tm);
            DynamicContextHolder.poll();
        } catch (Exception e) {
            log.error("捕获到异常", e);
            transactionHelper.rollback(tm);
            throw e;
        } finally {
            DynamicContextHolder.poll();
        }
        this.reportRecord(itabGetDate, cashflowsList, balanceSheetList, ispaList);
    }

    private void setRptDateCode(List<AsyEifd2007cashflowsEntity> cashflowsList,
                                List<AsyEiod2007BalanceSheetEntity> balanceSheetList,
                                List<AsyEisd2007ispaEntity> ispaList) {
        cashflowsList.forEach(flow -> {
            ReportRecordEtprEntity baseRecord = ReportRecordEtprEntity.builder()
                    .infoType(ReportRecordEtprEntity.INFO_TYPE_EID)
                    .sgmt(ReportRecordEtprEntity.SGMT_EIFD)
                    .infoKey1(flow.getCustId())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            if (reportRecordEtprService.exists(baseRecord)) {
                flow.setRptDateCode("20");
            }
        });
        balanceSheetList.forEach(balance -> {
            ReportRecordEtprEntity baseRecord = ReportRecordEtprEntity.builder()
                    .infoType(ReportRecordEtprEntity.INFO_TYPE_EID)
                    .sgmt(ReportRecordEtprEntity.SGMT_EIOD)
                    .infoKey1(balance.getCustId())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            if (reportRecordEtprService.exists(baseRecord)) {
                balance.setRptDateCode("20");
            }
        });
        ispaList.forEach(ispa -> {
            ReportRecordEtprEntity baseRecord = ReportRecordEtprEntity.builder()
                    .infoType(ReportRecordEtprEntity.INFO_TYPE_EID)
                    .sgmt(ReportRecordEtprEntity.SGMT_EISD)
                    .infoKey1(ispa.getCustId())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            if (reportRecordEtprService.exists(baseRecord)) {
                ispa.setRptDateCode("20");
            }
        });
    }

    private void reportRecord(String itabGetDate,
                              List<AsyEifd2007cashflowsEntity> cashflowsList,
                              List<AsyEiod2007BalanceSheetEntity> balanceSheetList,
                              List<AsyEisd2007ispaEntity> ispaList) {
        List<ReportRecordEtprEntity> entityList = new ArrayList<>();

        for (AsyEifd2007cashflowsEntity dto : cashflowsList) {
            // 基础段 报送记录
            ReportRecordEtprEntity entity = ReportRecordEtprEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordEtprEntity.INFO_TYPE_EID)
                    .sgmt(ReportRecordEtprEntity.SGMT_EIFD)
                    .infoKey1(dto.getCustId())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (AsyEiod2007BalanceSheetEntity dto : balanceSheetList) {
            // 基本信息段 报送记录
            ReportRecordEtprEntity entity = ReportRecordEtprEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordEtprEntity.INFO_TYPE_EID)
                    .sgmt(ReportRecordEtprEntity.SGMT_EIOD)
                    .infoKey1(dto.getCustId())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (AsyEisd2007ispaEntity dto : ispaList) {
            // 相关还款责任人信息段 报送记录
            ReportRecordEtprEntity entity = ReportRecordEtprEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordEtprEntity.INFO_TYPE_EID)
                    .sgmt(ReportRecordEtprEntity.SGMT_EISD)
                    .infoKey1(dto.getCustId())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        reportRecordEtprService.insertBatch(entityList);
    }

}
