package io.ibs.modules.irp.pbccrc.task.mortgage;

import cn.hutool.core.date.DateUtil;
import io.ibs.modules.irp.pbccrc.common.TaskUtil;
import io.ibs.modules.irp.pbccrc.psgrecord.service.PsgRecordgetDateMortService;
import io.ibs.modules.irp.pbccrc.whitelist.entity.TWhiteListEntity;
import io.ibs.modules.irp.pbccrc.whitelist.service.TWhiteListService;
import io.ibs.modules.irp.record.entity.ReportRecordMortEntity;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 征信二代
 * 从本地数据库同步抵质押数据到金电数据库
 *
 * <AUTHOR>
 * @since 20220826
 */
@Slf4j
@Service
public class MortgageEtprSendToJDTask extends BaseTask {
    private PsgRecordgetDateMortService psgRecordgetDateMortService;
    private TWhiteListService twhiteListSvc;
    private MortgageSendToJD mortgageSendToJD;

    public MortgageEtprSendToJDTask(PsgRecordgetDateMortService psgRecordgetDateMortService,
                                    TWhiteListService twhiteListSvc,
                                    MortgageSendToJD mortgageSendToJD) {
        super("征信二代 - 从本地数据库同步企业抵质押数据到金电数据库", MortgageEtprSendToJDTask.class);
        this.psgRecordgetDateMortService = psgRecordgetDateMortService;
        this.twhiteListSvc = twhiteListSvc;
        this.mortgageSendToJD = mortgageSendToJD;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void execute(String params) throws Exception {
        params = super.checkDateParam(params, "yyyyMMdd");
        // 获取一天的开始和结束 或 时间段
        Date[] between;
        if (params.contains("-")) {
            between = new Date[2];
            between[0] = DateUtil.parse(params.split("-")[0]);
            between[1] = DateUtil.parse(params.split("-")[1]);
        } else {
            // 检查当前日期是否已经报送过
            log.info("检查当前日期是否已经报送过");
            if (psgRecordgetDateMortService.checkAsy(params, ReportRecordMortEntity.INFO_TYPE_PIP)) {
                return;
            }
            between = TaskUtil.getBeginEndOfDay(params);
        }

        // 屏蔽白名单内的借据 updated by AileYoung on 2023/5/10
        List<String> whiteList = twhiteListSvc.getWhiteBussNum(TWhiteListEntity.CSTM_TYPE_2, TWhiteListEntity.STATE_0);

        mortgageSendToJD.execute(between, whiteList, "2");//1-个人 2-企业
    }

}
