package io.ibs.modules.irp.pbccrc.task.prsn;

import io.ibs.common.constant.Constant;
import io.ibs.common.utils.DateUtils;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.modules.irp.pbccrc.prsnguarantee.entity.*;
import io.ibs.modules.irp.pbccrc.prsnguarantee.service.*;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.irp.util.CstmInfoExch;
import io.ibs.modules.job.task.BaseTask;
import io.ibs.modules.sys.service.SysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 征信二代
 * 从核心及信贷系统抽取个人担保数据到本地数据库
 * 任务参数为空表示日常报送抽取数据
 *
 * <AUTHOR>
 * @since 20220719
 */
@Slf4j
//@Service
@Deprecated
public class PrsnGuaranteeGetFromHostTask extends BaseTask {

    private PrsnGuaranteeGetDataService pggdSvc;
    private TransactionHelper transactionHelper;
    private PigbGuaracctbssgmtService pigbSvc;
    private PigcGuaracctbsinfsgService pigcSvc;
    private PigdGuarrltpepymtinfsgmtService pigdSvc;
    private PigeRltrepymtinfService pigeSvc;
    private PigfGuarmotinfService pigfSvc;
    private SysDictDataService sddSvc;

    @Autowired
    public PrsnGuaranteeGetFromHostTask(TransactionHelper transactionHelper, PrsnGuaranteeGetDataService pggdSvc,
                                        PigbGuaracctbssgmtService pigbSvc,
                                        PigcGuaracctbsinfsgService pigcSvc,
                                        PigdGuarrltpepymtinfsgmtService pigdSvc,
                                        PigeRltrepymtinfService pigeSvc,
                                        PigfGuarmotinfService pigfSvc,
                                        SysDictDataService sddSvc) {
        super("征信二代 - 从核心及信贷系统抽取个人担保数据", PrsnGuaranteeGetFromHostTask.class);
        this.pggdSvc = pggdSvc;
        this.transactionHelper = transactionHelper;
        this.pigbSvc = pigbSvc;
        this.pigcSvc = pigcSvc;
        this.pigdSvc = pigdSvc;
        this.pigeSvc = pigeSvc;
        this.pigfSvc = pigfSvc;
        this.sddSvc = sddSvc;
    }

    @Override
    public void execute(String params) {
        String tranDate = super.checkDateParam(params, "yyyyMMdd");
        // 取当天开立的保证贷款 2022-03-24有数据
        List<PrsnLoanUnclearEntity> openList;
        // 获取指定日期结清的保证贷款 2022-02-17有数据
        List<PrsnLoanUnclearEntity> closeList;
        // 获取指定日期有五级分类调整的 在保责任信息段 认为未还清本金的最小一期到期日与当前日期的间隔天数等于五级分类认定的开始天数时就是五级分类做了调整
        List<PrsnLoanUnclearEntity> fiveCateList;
        // 获取指定日期有余额改变的保证贷款 在保责任信息段 多个报告时点重合时,以编码靠前的时点为主
        List<PrsnLoanUnclearEntity> updateList;
        // 当参数格式为yyyyMMdd-yyyyMMdd 某段时间内
        if (tranDate.contains("-")) {
            String bgnDate = tranDate.substring(0, tranDate.lastIndexOf("-"));
            String endDate = tranDate.substring(tranDate.lastIndexOf("-") + 1);
            // 取当天开立的保证贷款 2022-03-24有数据
            openList = pggdSvc.getPrsnGuarOpenBetweenTwoDate(bgnDate, endDate);
            if (openList != null && openList.size() > 0) {
                prsnGuarFillData(openList, "10");// 报送时点 10-新开户/首次上报
            }
            // 获取指定日期结清的保证贷款 2022-02-17有数据
            closeList = pggdSvc.getPrsnGuarCloseBetweenTwoDate(bgnDate, endDate);
            if (closeList != null && closeList.size() > 0) {
                closeList.parallelStream().forEach(c -> {
                    ArrayList<Date> closeDate = new ArrayList<>();
                    Optional.ofNullable(c.getFiveCateDate()).ifPresent(cl -> closeDate.add(c.getFiveCateDate()));
                    Optional.ofNullable(c.getTranDate()).ifPresent(cl -> closeDate.add(c.getTranDate()));
                    c.setCloseDate(Collections.max(closeDate));
                });
                prsnGuarFillData(closeList, "20");// 报送时点 20-账户关闭
            }
            // 获取指定日期有五级分类调整的
            // TODO: 写SQL
            fiveCateList = pggdSvc.getPrsnGuarOfFiveCateBetweenTwoDate(bgnDate, endDate);
            if (fiveCateList != null && fiveCateList.size() > 0) {
                prsnGuarFillData(fiveCateList, "40");
            }
            // 获取指定日期有余额改变的保证贷款
            // TODO: 写SQL
            updateList = pggdSvc.getPrsnGuarOfBalBetweenTwoDate(bgnDate, endDate);
            if (updateList != null && updateList.size() > 0) {
                prsnGuarFillData(updateList, "30");
            }
        } else {
            // 取当天开立的保证贷款 2022-03-24有数据
            openList = pggdSvc.getPrsnGuarOpenInCurrentDay(tranDate);
            if (openList != null && openList.size() > 0) {
                prsnGuarFillData(openList, "10");// 报送时点 10-新开户/首次上报
            }
            // 获取指定日期结清的保证贷款 2022-02-17有数据
            closeList = pggdSvc.getPrsnGuarCloseInCurrentDay(tranDate);
            if (closeList != null && closeList.size() > 0) {
                closeList.parallelStream().forEach(c -> {
                    ArrayList<Date> closeDate = new ArrayList<>();
                    Optional.ofNullable(c.getFiveCateDate()).ifPresent(cl -> closeDate.add(c.getFiveCateDate()));
                    Optional.ofNullable(c.getTranDate()).ifPresent(cl -> closeDate.add(c.getTranDate()));
                    c.setCloseDate(Collections.max(closeDate));
                });
                prsnGuarFillData(closeList, "20");// 报送时点 20-账户关闭
            }
            // 获取指定日期有五级分类调整的
            fiveCateList = pggdSvc.getPrsnGuarOfFiveCateUpdate(tranDate);
            if (fiveCateList != null && fiveCateList.size() > 0) {
                prsnGuarFillData(fiveCateList, "40");
            }
            // 获取指定日期有余额改变的保证贷款
            updateList = pggdSvc.getPrsnGuarOfBalUpdate(tranDate);
            if (updateList != null && updateList.size() > 0) {
                prsnGuarFillData(updateList, "30");
            }
        }
    }

    /**
     * 将担保贷款数据添加到基础段和在保责任信息段
     *
     * @param list
     * @param rptDataCode
     */
    private void prsnGuarFillData(List<PrsnLoanUnclearEntity> list, String rptDataCode) {
        int size = list.size() % 100 == 0 ? list.size() / 100 : list.size() / 100 + 1;
        List<PigbGuaracctbssgmtEntity> pigbList = new ArrayList<>();
        List<PigcGuaracctbsinfsgEntity> pigcList = new ArrayList<>();
        List<PigdGuarrltpepymtinfsgmtEntity> pigdList = new ArrayList<>();
        List<PigeRltrepymtinfEntity> pigeList = new ArrayList<>();
        Date today = new Date();

        for (int i = 0; i < size; i++) {
            List<PrsnLoanUnclearEntity> unclearList = list.subList(i * 100, (i + 1) * 100 > list.size() ? list.size() : (i + 1) * 100);
            List<PrsnGuaranteeGetDataEntity> guarList = pggdSvc.getPrsnGuarantee(unclearList);
            unclearList.forEach(loan -> {
                if ("10".equals(rptDataCode)) {
                    // 基础段
                    PigbGuaracctbssgmtEntity pigb = new PigbGuaracctbssgmtEntity();
                    pigb.setBussNum(loan.getLoanNo());
                    pigb.setAcctType("G1");// G1-融资担保账户;G2-非融资担保账户
                    pigb.setName(loan.getName());// 债务人姓名
                    pigb.setIdType(CstmInfoExch.pbccrcIDTypeExch(loan.getPaperNo().substring(0, 1)));// 证件类型
                    pigb.setIdNum(loan.getPaperNo().substring(1).trim());// 证件号码
                    pigb.setUpdateTime(today);
                    pigb.setUpdator(Constants.UPDATER_FOR_TASK);
                    pigbList.add(pigb);

                    List<PrsnGuaranteeGetDataEntity> guarEntity = guarList.stream().filter(x -> loan.getCstmNo().equals(x.getKhh()) && loan.getLoanNo().equals(x.getJjh())).collect(Collectors.toList());
                    // 基本信息段
                    PigcGuaracctbsinfsgEntity pigc = new PigcGuaracctbsinfsgEntity();
                    pigc.setBussNum(loan.getLoanNo());// 担保业务号
                    pigc.setBusiLines("1");// 担保业务大类
                    pigc.setBusiDtiLines("01");// 担保业务种类细分
                    pigc.setOpenDate(loan.getBgnintDate());// 开户日期
                    pigc.setAcctCredLine(loan.getLoanBal());// 担保金额
                    pigc.setAcctCy("CNY");// 币种
                    pigc.setDueDate(DateUtils.parse(loan.getDueDate(), "yyyy-MM-dd"));// 到期日期
                    pigc.setGuraMode("0");// 反担保方式 0-信用/免担保
                    pigc.setOthRepyGuraWay("0");// 其他还款保证方式 0-无
                    pigc.setCtrctTxtCd(ObjectUtils.isEmpty(guarEntity) ? null : guarEntity.get(0).getHtbh());// 担保合同文本编号
                    BigDecimal secDep;
                    if (ObjectUtils.isEmpty(guarEntity) || StringUtils.isBlank(guarEntity.get(0).getDbgsBzjbl())) {
                        secDep = BigDecimal.ZERO;
                    } else {
                        secDep = BigDecimal.valueOf(Double.parseDouble(guarEntity.get(0).getDbgsBzjbl()));
                    }
                    pigc.setSecDep(secDep);// 保证金比例
                    pigc.setUpdateTime(today);
                    pigc.setUpdator(Constants.UPDATER_FOR_TASK);
                    pigcList.add(pigc);

                    // 相关还款责任人
                    guarEntity.forEach(g -> {
                        PigeRltrepymtinfEntity pigeEntity = new PigeRltrepymtinfEntity();
                        pigeEntity.setBussNum(loan.getLoanNo());// 担保业务号
                        pigeEntity.setDeptCode(Constant.deptCode);// 所属机构
                        if ("1".equals(g.getDbfs())) {
                            pigeEntity.setInfoIdType("1");// 身份类别
                            pigeEntity.setArlpName(g.getZrrDbrxm());// 责任人名称
                            pigeEntity.setArlpCertType(CstmInfoExch.pbccrcIDTypeExch(g.getZrrZjlx()));// 责任人身份标识类型
                            pigeEntity.setArlpCertNum(g.getZrrZjhm());// 责任人身份标识号码

                        } else {
                            pigeEntity.setInfoIdType("2");// 身份类别
                            pigeEntity.setArlpName(g.getQyQymc());// 责任人名称
                            if (g.getQyZzjgdm().contains("-")) {
                                pigeEntity.setArlpCertType("30");// 责任人身份标识类型 30-组织机构代码
                                pigeEntity.setArlpCertNum(CstmInfoExch.pbccrcCertNumDeal(g.getQyZzjgdm()));// 责任人身份标识号码
                            } else {
                                pigeEntity.setArlpCertType("20");// 责任人身份标识类型 20-统一社会信用代码
                                pigeEntity.setArlpCertNum(g.getQyZzjgdm());// 责任人身份标识号码
                            }
                        }
                        pigeEntity.setArlpAmt(loan.getLoanBal());// 还款责任金额
                        pigeEntity.setArlpTp("2");// 还款责任人类型
                        pigeEntity.setWartySign("0");// 联保标志
                        pigeEntity.setMaxGuarBussNum(g.getHtbh());// 保证合同业务号
                        pigeList.add(pigeEntity);
                    });

                }

                // 在保责任信息段
                PigdGuarrltpepymtinfsgmtEntity pigd = new PigdGuarrltpepymtinfsgmtEntity();
                pigd.setBussNum(loan.getLoanNo());// 担保业务号
                pigd.setAcctStatus("1");// 账户状态 1-正常;2-关闭
                pigd.setCloseDate(null);// 账户关闭日期
                if ("20".equals(rptDataCode)) {
                    pigd.setAcctStatus("2");// 账户状态 1-正常;2-关闭
                    pigd.setCloseDate(loan.getCloseDate());// 账户关闭日期
                }
                pigd.setLoanAmt(loan.getBal());// 在保余额
                pigd.setRepayPrd(loan.getTranDate());// 余额变化日期
                pigd.setRptDateCode(rptDataCode);// 报告时点说明代码
                pigd.setFiveCate(loan.getFiveFlag());// 五级分类
                pigd.setFiveCateAdjDate(loan.getBgnintDate());// 五级分类调整日期
                if ("30".equals(rptDataCode) || "40".equals(rptDataCode)) {
                    pigd.setFiveCate(loan.getFiveFlag());// 五级分类
                    pigd.setFiveCateAdjDate(loan.getFiveCateDate());// 五级分类调整日期
                }
                pigd.setRiEx(null);// 风险敞口 填写债务人违约行为导致担保机构可能承受的风险金额
                pigd.setCompAdvFlag("0");// 代偿(垫款)标识 0-否(代表担保机构没有为债务人代偿/垫款)
                pigd.setBussDate(loan.getTranDate());
                pigd.setUpdateTime(today);
                pigd.setUpdator(Constants.UPDATER_FOR_TASK);
                pigdList.add(pigd);

                // 抵质押物信息段作为担保账户的预留信息段,暂时不需要报送
            });
        }

        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();

        try {
            pigbSvc.insertBatch(pigbList);
            pigcSvc.insertBatch(pigcList);
            pigdSvc.insertBatch(pigdList);
            pigeSvc.insertBatch(pigeList);
            transactionHelper.commit(tm);
        } catch (Exception e) {
            transactionHelper.rollback(tm);
            throw e;// 抛给父类捕获并发送短信
        }
    }
}
