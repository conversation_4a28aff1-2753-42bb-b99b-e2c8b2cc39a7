package io.ibs.modules.irp.pbccrc.task.prsn;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import io.ibs.common.utils.DateUtils;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.commons.dynamic.datasource.config.DynamicContextHolder;
import io.ibs.modules.irp.ecif.prsn.dto.*;
import io.ibs.modules.irp.ecif.prsn.service.*;
import io.ibs.modules.irp.pbccrc.common.service.AsySendService;
import io.ibs.modules.irp.pbccrc.prsninfo.dao.aysdao.*;
import io.ibs.modules.irp.pbccrc.prsninfo.dto.*;
import io.ibs.modules.irp.pbccrc.prsninfo.service.PrsnInfoSendQuartzService;
import io.ibs.modules.irp.pbccrc.prsninfo.util.PbccrcDictExch;
import io.ibs.modules.irp.pbccrc.psgrecord.dto.AsyPsgRecordgetDatePersonDTO;
import io.ibs.modules.irp.pbccrc.psgrecord.entity.AsyPsgRecordgetDatePersonEntity;
import io.ibs.modules.irp.pbccrc.psgrecord.service.AsyPsgRecordgetDatePersonService;
import io.ibs.modules.irp.pbccrc.psgrecord.service.PsgRecordgetDatePersonService;
import io.ibs.modules.irp.pbccrc.whitelist.entity.CstmAndLoanNo;
import io.ibs.modules.irp.pbccrc.whitelist.entity.TWhiteListEntity;
import io.ibs.modules.irp.pbccrc.whitelist.service.TWhiteListService;
import io.ibs.modules.irp.record.entity.ReportRecordPrsnEntity;
import io.ibs.modules.irp.record.service.ReportRecordPrsnService;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.irp.util.CstmInfoExch;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 征信二代
 * 从征信数据库抽取个人基本信息到金电数据库
 * 任务参数为first表示首次报送,参数为空表示报送当天的数据,参数为指定日期表示报送指定日期的数据
 *
 * <AUTHOR>
 * @since 20220411
 */
@Slf4j
//@Service
@Deprecated
public class PrsnInfoFirstSendToJDTask extends BaseTask {
    private TransactionHelper transactionHelper;

    private PrsnInfoSendQuartzService pifsqSvc;
    private TPrsnEcifBaseService tpeBaseSvc;
    private TPrsnEcifEducationService tpeEduSvc;
    private TPrsnEcifFamilyService tpeFamSvc;
    private TPrsnEcifIncomeService tpeIncSvc;
    private TPrsnEcifLiveAddressService tpeLiveSvc;
    private TPrsnEcifMailAddressService tpeMailSvc;
    private TPrsnEcifMarriageService tpeMarriageSvc;
    private TPrsnEcifOccupationService tpeOccupSvc;
    private TPrsnEcifOtherCredentialsService tpeOtherSvc;
    private TWhiteListService twhiteListSvc;
    private AsySendService asySendService;
    private PsgRecordgetDatePersonService psgRecordgetDatePersonService;
    private AsyPsgRecordgetDatePersonService asyPsgRecordgetDatePersonService;
    private ReportRecordPrsnService reportRecordPrsnService;

    @Autowired
    public PrsnInfoFirstSendToJDTask(TransactionHelper transactionHelper,
                                     PrsnInfoSendQuartzService pifsqSvc,
                                     TPrsnEcifBaseService tpeBaseSvc,
                                     TPrsnEcifEducationService tpeEduSvc,
                                     TPrsnEcifFamilyService tpeFamSvc,
                                     TPrsnEcifIncomeService tpeIncSvc,
                                     TPrsnEcifLiveAddressService tpeLiveSvc,
                                     TPrsnEcifMailAddressService tpeMailSvc,
                                     TPrsnEcifMarriageService tpeMarriageSvc,
                                     TPrsnEcifOccupationService tpeOccupSvc,
                                     TPrsnEcifOtherCredentialsService tpeOtherSvc,
                                     TWhiteListService twhiteListSvc,
                                     AsySendService asySendService,
                                     PsgRecordgetDatePersonService psgRecordgetDatePersonService,
                                     AsyPsgRecordgetDatePersonService asyPsgRecordgetDatePersonService,
                                     ReportRecordPrsnService reportRecordPrsnService) {
        super("从征信数据库抽取个人基本信息到金电数据库", PrsnInfoFirstSendToJDTask.class);
        this.transactionHelper = transactionHelper;
        this.pifsqSvc = pifsqSvc;
        this.tpeBaseSvc = tpeBaseSvc;
        this.tpeEduSvc = tpeEduSvc;
        this.tpeFamSvc = tpeFamSvc;
        this.tpeIncSvc = tpeIncSvc;
        this.tpeLiveSvc = tpeLiveSvc;
        this.tpeMailSvc = tpeMailSvc;
        this.tpeMarriageSvc = tpeMarriageSvc;
        this.tpeOccupSvc = tpeOccupSvc;
        this.tpeOtherSvc = tpeOtherSvc;
        this.twhiteListSvc = twhiteListSvc;
        this.asySendService = asySendService;
        this.psgRecordgetDatePersonService = psgRecordgetDatePersonService;
        this.asyPsgRecordgetDatePersonService = asyPsgRecordgetDatePersonService;
        this.reportRecordPrsnService = reportRecordPrsnService;
    }

    /**
     * 执行程序
     *
     * @param params 参数，多参数使用JSON数据
     */
    @Override
    public void execute(String params) throws Exception {
        // 首次报送时，需指定采集时间范围，如：20230101-20230331
        log.info("-----------------个人基本信息首次报送金电系统开始!------------------");

        String bgnDate = params.split("-")[0];
        String endDate = params.split("-")[1];
        Date from = DateUtil.parse(bgnDate, "yyyyMMdd");
        Date to = DateUtil.parse(endDate, "yyyyMMdd");

        // 检查当前日期是否已经报送过
        log.info("检查当前日期是否已经报送过>>>>>>>>");
        if (psgRecordgetDatePersonService.checkAsy(DateUtils.format(new Date(), "yyyyMMdd"), ReportRecordPrsnEntity.INFO_TYPE_PIB)) {
            return;
        }

        // 查询未结清贷款的客户号以及借据号
        log.info("查询未结清贷款的客户号以及借据号>>>>>>>>");
//        List<CstmAndLoanNo> cstm = pifsqSvc.getCstmAndLoanNoOfUnclear(endDate);

        // updated by AileYoung on 2023/6/13 首次上报时，客户信息改为从借据基础段中抽出，只有存在借据基础段的，才报送其客户信息
        List<CstmAndLoanNo> cstm = pifsqSvc.getFirstCstmInfo();
        Map<String, List<CstmAndLoanNo>> cstmMap = cstm.stream().collect(Collectors.groupingBy(CstmAndLoanNo::getCstmNo));

        log.info("检查白名单>>>>>>>>");
        //屏蔽白名单内的客户
        List<TWhiteListEntity> whiteList = twhiteListSvc.getWhiteCstmAndBussNum(TWhiteListEntity.CSTM_TYPE_1, TWhiteListEntity.STATE_0);
        Map<String, List<TWhiteListEntity>> whiteMap = whiteList.stream().collect(Collectors.groupingBy(TWhiteListEntity::getCstmNo));

        //将白名单内的客户号及借据号从未结清的客户号和借据号中屏蔽
        List<String> cstmLists = new ArrayList<>();
        cstmMap.forEach((cstmNo, cstmAndLoanNoLists) -> {
            if (whiteMap.containsKey(cstmNo)) {
                //只要看白名单中的借据是否包含未结清的借据,只要未结清的数据里有一个及以上的借据没在白名单里,则客户信息要报送
                List<String> wList = whiteMap.get(cstmNo).stream().map(TWhiteListEntity::getBussNum).collect(Collectors.toList());
                //从该客户未结清的借据中过滤该客户白名单里的借据号，如果过滤完后还有其他借据号，说明该客户未结清的数据里至少有一个没在白名单中，则仍要报送
                long count = cstmAndLoanNoLists.stream().filter(k -> wList.contains(k.getLoanNo())).count();
                if (count < cstmAndLoanNoLists.size()) {
                    log.info("客户号[{}]在白名单中但是未结清的数据里有一个及以上的借据没在白名单里，仍要报送", cstmNo);
                    cstmLists.add(cstmNo);//未在白名单中的客户号,需要报送
                } else {
                    log.info("客户号[{}]符合白名单条件，过滤", cstmNo);
                }
            } else {
                cstmLists.add(cstmNo);//未在白名单中的客户号,需要报送
            }
        });

        log.info("开始查询数据>>>>>>>>");
        List<String> baseIds = new ArrayList<>();
        List<TPrsnEcifBaseDTO> baseList = new ArrayList<>();
        List<TPrsnEcifEducationDTO> eduList = new ArrayList<>();
        List<TPrsnEcifOccupationDTO> occupList = new ArrayList<>();
        List<TPrsnEcifLiveAddressDTO> liveList = new ArrayList<>();
        List<TPrsnEcifMailAddressDTO> mailList = new ArrayList<>();
        List<TPrsnEcifMarriageDTO> marriageList = new ArrayList<>();
        List<TPrsnEcifIncomeDTO> incList = new ArrayList<>();
        List<TPrsnEcifOtherCredentialsDTO> otherList = new ArrayList<>();
        List<TPrsnEcifFamilyDTO> famList = new ArrayList<>();
        TreeSet<String> itabGetDates = new TreeSet<>();//收集itabGetDate
        for (List<String> list : Lists.partition(cstmLists.stream().distinct().collect(Collectors.toList()), 500)) {
            String[] cstmList = list.toArray(new String[list.size()]);
            // 基础段
            List<TPrsnEcifBaseDTO> baseListTmp = tpeBaseSvc.getListInfoByCstmNos(cstmList);

            for (TPrsnEcifBaseDTO base : baseListTmp) {
                Date openDate = DateUtil.parse(base.getOpenDate(), "yyyyMMdd");
                // 开户日期大于等于抽取区间开始日期，则取开户日期作为业务发生日期
                if (DateUtil.compare(openDate, from) > -1) {
                    base.setItabGetDate(base.getOpenDate());
                    base.setBussDate(openDate);
                }
                // 开户日期小于抽取区间开始日期的存量数据，业务发生日期统一取抽取区间开始日期
                else {
                    base.setItabGetDate(bgnDate);
                    base.setBussDate(from);
                }
                base.setRptDateCode("10");
                itabGetDates.add(base.getItabGetDate());
            }
            baseList.addAll(baseListTmp);

            baseIds.addAll(baseList.stream().map(TPrsnEcifBaseDTO::getCstmNo).collect(Collectors.toList()));

            // 教育信息段
            List<TPrsnEcifEducationDTO> eduListTmp = tpeEduSvc.getListInfoByCstmNos(cstmList);
            for (TPrsnEcifEducationDTO dto : eduListTmp) {
                TPrsnEcifBaseDTO base = tpeBaseSvc.getInfoByCstmNo(dto.getCstmNo());
                Date openDate = DateUtil.parse(base.getOpenDate(), "yyyyMMdd");
                if (DateUtil.isIn(openDate, from, to)) {
                    dto.setItabGetDate(base.getOpenDate());
                    dto.setBussDate(openDate);
                } else {
                    dto.setItabGetDate(bgnDate);
                    dto.setBussDate(from);
                }
                itabGetDates.add(dto.getItabGetDate());
            }
            eduList.addAll(eduListTmp);

            // 职业信息段
            List<TPrsnEcifOccupationDTO> occupListTmp = tpeOccupSvc.getListInfoByCstmNos(cstmList);
            for (TPrsnEcifOccupationDTO dto : occupListTmp) {
                TPrsnEcifBaseDTO base = tpeBaseSvc.getInfoByCstmNo(dto.getCstmNo());
                Date openDate = DateUtil.parse(base.getOpenDate(), "yyyyMMdd");
                if (DateUtil.isIn(openDate, from, to)) {
                    dto.setItabGetDate(base.getOpenDate());
                    dto.setBussDate(openDate);
                } else {
                    dto.setItabGetDate(bgnDate);
                    dto.setBussDate(from);
                }
                itabGetDates.add(dto.getItabGetDate());
            }
            occupList.addAll(occupListTmp);


            // 居住地址段
            List<TPrsnEcifLiveAddressDTO> liveListTmp = tpeLiveSvc.getListInfoByCstmNos(cstmList);
            for (TPrsnEcifLiveAddressDTO dto : liveListTmp) {
                TPrsnEcifBaseDTO base = tpeBaseSvc.getInfoByCstmNo(dto.getCstmNo());
                Date openDate = DateUtil.parse(base.getOpenDate(), "yyyyMMdd");
                if (DateUtil.isIn(openDate, from, to)) {
                    dto.setItabGetDate(base.getOpenDate());
                    dto.setBussDate(openDate);
                } else {
                    dto.setItabGetDate(bgnDate);
                    dto.setBussDate(from);
                }
                itabGetDates.add(dto.getItabGetDate());
            }
            liveList.addAll(liveListTmp);

            // 通讯地址
            List<TPrsnEcifMailAddressDTO> mailListTmp = tpeMailSvc.getListInfoByCstmNos(cstmList);
            for (TPrsnEcifMailAddressDTO dto : mailListTmp) {
                TPrsnEcifBaseDTO base = tpeBaseSvc.getInfoByCstmNo(dto.getCstmNo());
                Date openDate = DateUtil.parse(base.getOpenDate(), "yyyyMMdd");
                if (DateUtil.isIn(openDate, from, to)) {
                    dto.setItabGetDate(base.getOpenDate());
                    dto.setBussDate(openDate);
                } else {
                    dto.setItabGetDate(bgnDate);
                    dto.setBussDate(from);
                }
                itabGetDates.add(dto.getItabGetDate());
            }
            mailList.addAll(mailListTmp);

            // 婚姻信息
            List<TPrsnEcifMarriageDTO> marriageListTmp = tpeMarriageSvc.getListInfoByCstmNos(cstmList);
            for (TPrsnEcifMarriageDTO dto : marriageListTmp) {
                TPrsnEcifBaseDTO base = tpeBaseSvc.getInfoByCstmNo(dto.getCstmNo());
                Date openDate = DateUtil.parse(base.getOpenDate(), "yyyyMMdd");
                if (DateUtil.isIn(openDate, from, to)) {
                    dto.setItabGetDate(base.getOpenDate());
                    dto.setBussDate(openDate);
                } else {
                    dto.setItabGetDate(bgnDate);
                    dto.setBussDate(from);
                }
                itabGetDates.add(dto.getItabGetDate());
            }
            marriageList.addAll(marriageListTmp);

            // 收入信息
            List<TPrsnEcifIncomeDTO> incListTmp = tpeIncSvc.getListInfoByCstmNos(cstmList);
            for (TPrsnEcifIncomeDTO dto : incListTmp) {
                TPrsnEcifBaseDTO base = tpeBaseSvc.getInfoByCstmNo(dto.getCstmNo());
                Date openDate = DateUtil.parse(base.getOpenDate(), "yyyyMMdd");
                if (DateUtil.isIn(openDate, from, to)) {
                    dto.setItabGetDate(base.getOpenDate());
                    dto.setBussDate(openDate);
                } else {
                    dto.setItabGetDate(bgnDate);
                    dto.setBussDate(from);
                }
                itabGetDates.add(dto.getItabGetDate());
            }
            incList.addAll(incListTmp);

            //后面两个不会有数据
            // 证件整合信息
            otherList.addAll(tpeOtherSvc.getListInfoByCstmNos(cstmList));
            // 家庭关系
            famList.addAll(tpeFamSvc.getListInfoByCstmNos(cstmList));
        }

        log.info("开始报送数据>>>>>>>>");
        this.doSend(to, itabGetDates, baseIds, baseList, eduList, occupList, liveList, mailList, marriageList, incList, otherList, famList);

        log.info("-----------------个人基本信息首次报送金电系统结束!------------------");
    }

    @SuppressWarnings("unchecked")
    private void doSend(Date to,
                        TreeSet<String> itabGetDates,
                        List<String> baseIds,
                        List<TPrsnEcifBaseDTO> baseList,
                        List<TPrsnEcifEducationDTO> eduList,
                        List<TPrsnEcifOccupationDTO> occupList,
                        List<TPrsnEcifLiveAddressDTO> liveList,
                        List<TPrsnEcifMailAddressDTO> mailList,
                        List<TPrsnEcifMarriageDTO> marriageList,
                        List<TPrsnEcifIncomeDTO> incList,
                        List<TPrsnEcifOtherCredentialsDTO> otherList,
                        List<TPrsnEcifFamilyDTO> famList) throws Exception {
        // 根据未结清贷款客户号查询各个段的信息
        List<PibbBssgmtDTO> asyPibbList = new ArrayList<>();// 基础段
        List<PibcIdsgmtDTO> asyPibcList = new ArrayList<>();// 其他标识段
        List<PibdFcsinfsgmtDTO> asyPibdList = new ArrayList<>();// 基本信息概况
        List<PibkIdefctinfDTO> asyPibkList = new ArrayList<>();// 个人证件有效期信息


        if (CollectionUtils.isEmpty(baseIds)) {
            log.info("当前日期没有数据需要报送！");
            return;
        }

        // 设置数据源
        DynamicContextHolder.push("credit");
        // 开启事务
        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();
        try {
            for (String itabGetDate : itabGetDates) {
                // 查看当前日期是否有记录
                QueryWrapper<AsyPsgRecordgetDatePersonEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("ITAB_GET_DATE", itabGetDate);
                List<AsyPsgRecordgetDatePersonDTO> logs = asyPsgRecordgetDatePersonService.list(wrapper);
                if (CollectionUtils.isEmpty(logs)) {
                    asyPsgRecordgetDatePersonService.createRecord(itabGetDate);
                }
            }

            baseList.forEach(base -> {
                PibbBssgmtDTO pibbEntity = new PibbBssgmtDTO();
                pibbEntity.setCustId(base.getCstmNo());
                pibbEntity.setName(base.getName());
                pibbEntity.setIdType(PbccrcDictExch.EcifPrsnIdTypeExchange(base.getIdType()));
                pibbEntity.setIdNum(base.getIdNum().trim());
                pibbEntity.setCustomerType(base.getCustomerType());
                pibbEntity.setRptDateCode(base.getRptDateCode());
                pibbEntity.setUpdateTime(base.getUpdateTime());
                pibbEntity.setBussDate(base.getBussDate());
                pibbEntity.setItabGetDate(base.getItabGetDate());
                asyPibbList.add(pibbEntity);

                PibdFcsinfsgmtDTO pibdEntity = new PibdFcsinfsgmtDTO();
                pibdEntity.setCustId(base.getCstmNo());
                pibdEntity.setNation(base.getNation());
                pibdEntity.setSex(CstmInfoExch.sexExchangeOne(base.getSex()));
                pibdEntity.setCellPhone(base.getCellPhone());
                pibdEntity.setDob(base.getBirthday() == null ? null : new Timestamp(DateUtils.getDate(base.getBirthday()).getTime()));
                pibdEntity.setHouseAdd(base.getHouseAdd());
                pibdEntity.setEmail(base.getEmail());
                pibdEntity.setHhDist(base.getHhDist());
                pibdEntity.setUpdateTime(base.getUpdateTime());
                pibdEntity.setBussDate(base.getBussDate());
                pibdEntity.setItabGetDate(base.getItabGetDate());
                asyPibdList.add(pibdEntity);

                PibkIdefctinfDTO pibkEntity = new PibkIdefctinfDTO();
                pibkEntity.setCustId(base.getCstmNo());
                pibkEntity.setIdType(PbccrcDictExch.EcifPrsnIdTypeExchange(base.getIdType()));
                pibkEntity.setIdNum(base.getIdNum());
                pibkEntity.setName(base.getName());
                pibkEntity.setIdOrgName(base.getIdOrgName());
                pibkEntity.setIdDist(base.getHhDist());
                pibkEntity.setIdDueDate(base.getIdDueDate() == null ? null : new Timestamp(DateUtils.getDate(base.getIdDueDate()).getTime()));
                /* 之前说2023年1月1日之前开户的，存量未结清的个人客户信息的信息报告日期，统一取2023年1月1日。现在遇到这样的一个错误：
                 * 某客户的客户信息是在2017年开的户，他在行内有未结清的贷款，因此属于本轮测试的抽取范围。但是2023年4月24日，客户换了新的身份证，并到行内核心系统进行了身份信息变更，他的个人证件起始日期修改为2023-04-24，到期日期为2099-04-24。
                 * 本轮抽数时，抽取到该客户的信息时，信息报告日期bussDate取了2023-01-01，个人证件有效期信息记录的证件有效期起始日期取的是核心系统数据2023-04-24，所以预校验的时候报错：[I1301202--个人证件有效期信息记录的“信息报告日期”应不早于证件有效期起始日期]
                 *
                 * 经讨论修改为：
                 * 开户日期小于endDate的存量未结清的客户，如果他的证件开始日期大于endDate，那么证件开始日期就减20年
                 * */
                Date idEfctDate = base.getIdEfctDate();
                if (idEfctDate != null) {
                    if (DateUtil.compare(DateUtil.parse(base.getOpenDate(), "yyyyMMdd"), to) < 0 && DateUtil.compare(idEfctDate, to) > 0) {
                        idEfctDate = DateUtil.offsetMonth(idEfctDate, -20 * 12);
                    }
                }
                pibkEntity.setIdEfctDate(idEfctDate == null ? null : new Timestamp(idEfctDate.getTime()));
                pibkEntity.setUpdateTime(base.getUpdateTime());
                pibkEntity.setBussDate(base.getBussDate());
                pibkEntity.setItabGetDate(base.getItabGetDate());
                asyPibkList.add(pibkEntity);
            });

            // 教育信息段
            List<PibfEduinfsgmtDTO> asyPibfList = new ArrayList<>();
            eduList.forEach(edu -> {
                PibfEduinfsgmtDTO pibfEntity = new PibfEduinfsgmtDTO();
                pibfEntity.setCustId(edu.getCstmNo());
                pibfEntity.setEduLevel(edu.getEduLevel());
                pibfEntity.setAcaDegree(edu.getAcaDegree());
                pibfEntity.setUpdateTime(edu.getUpdateTime());
                pibfEntity.setBussDate(edu.getBussDate());
                pibfEntity.setItabGetDate(edu.getItabGetDate());
                asyPibfList.add(pibfEntity);
            });

            // 职业信息段
            List<PibgOctpninfsgmtDTO> asyPibgList = new ArrayList<>();
            occupList.forEach(occup -> {
                PibgOctpninfsgmtDTO pibgEntity = new PibgOctpninfsgmtDTO();
                pibgEntity.setCustId(occup.getCstmNo());
                String empStatus = occup.getEmpStatus();
                pibgEntity.setEmpStatus(empStatus);
                /* 若“就业状况”不为“11-国家公务员”、“13-专业技术人员”、“17-职员”、“21-企业管理人员”、“24-工人”或“91-在职”，
                 * 不能出现“单位名称”、“单位性质”、“单位所属行业”、“单位详细地址”、“单位所在地邮编”、“单位所在地行政区划”、“单位电话”、“职业”、“职务”、“职称”和“本单位工作起始年份”*/
                if ("11".equals(empStatus) || "13".equals(empStatus) || "17".equals(empStatus) || "21".equals(empStatus) || "24".equals(empStatus) || "91".equals(empStatus)) {
                    pibgEntity.setCpnname(occup.getCpnname());
                    pibgEntity.setCpnType(occup.getCpnType());
                    pibgEntity.setIndustry(occup.getIndustry());
                    pibgEntity.setCpnAddr(occup.getCpnAddr());
                    pibgEntity.setCpnPc(occup.getCpnPc());
                    pibgEntity.setCpnDist(occup.getCpnDist());
                    pibgEntity.setCpnTel(occup.getCpnTel());
                    pibgEntity.setOccupation(occup.getOccupation());
                    pibgEntity.setTitle(occup.getTitle());
                    pibgEntity.setTechTitle(occup.getTechTitle());
                    pibgEntity.setWorkStartDate(StringUtils.isNotBlank(occup.getWorkStartDate()) ?
                            new Timestamp(DateUtils.parse(occup.getWorkStartDate(), "yyyy").getTime()) : null);
                }
                pibgEntity.setUpdateTime(occup.getUpdateTime());
                pibgEntity.setBussDate(occup.getBussDate());
                pibgEntity.setItabGetDate(occup.getItabGetDate());
                asyPibgList.add(pibgEntity);
            });

            // 居住地址段
            List<PibhRedncinfsgmtDTO> asyPibhList = new ArrayList<>();
            liveList.forEach(live -> {
                PibhRedncinfsgmtDTO pibhEntity = new PibhRedncinfsgmtDTO();
                pibhEntity.setCustId(live.getCstmNo());
                pibhEntity.setResiStatus(live.getResiStatus());
                pibhEntity.setResiAddr(live.getResiAddr());
                pibhEntity.setHomeTel(live.getHomeTel());
                pibhEntity.setResiDist(live.getResiDist());
                pibhEntity.setResiPc(live.getResiPc());
                pibhEntity.setUpdateTime(live.getUpdateTime());
                pibhEntity.setBussDate(live.getBussDate());
                pibhEntity.setItabGetDate(live.getItabGetDate());
                asyPibhList.add(pibhEntity);
            });

            // 通讯地址
            List<PibiMlginfsgmtDTO> asyPibiList = new ArrayList<>();
            mailList.forEach(mail -> {
                PibiMlginfsgmtDTO pibiEntity = new PibiMlginfsgmtDTO();
                pibiEntity.setCustId(mail.getCstmNo());
                pibiEntity.setMailAddr(mail.getMailAddr());
                pibiEntity.setMailDist(mail.getMailDist());
                pibiEntity.setMailPc(mail.getMailPc());
                pibiEntity.setUpdateTime(mail.getUpdateTime());
                pibiEntity.setBussDate(mail.getBussDate());
                pibiEntity.setItabGetDate(mail.getItabGetDate());
                asyPibiList.add(pibiEntity);
            });

            // 婚姻信息
            List<PibeSpsinfsgmtDTO> asyPibeList = new ArrayList<>();
            marriageList.forEach(marriage -> {
                PibeSpsinfsgmtDTO pibeEntity = new PibeSpsinfsgmtDTO();
                pibeEntity.setCustId(marriage.getCstmNo());
                String mariStatus = marriage.getMariStatus();
                pibeEntity.setMariStatus(mariStatus);
                /* 若“婚姻状况”非“20-已婚”、“21-初婚”、“22-再婚”、“23-复婚”时，
                 * “配偶姓名”、“配偶证件类型”、“配偶证件号码”、“配偶联系电话”、“配偶工作单位”不能出现 */
                if ("20".equals(mariStatus) || "21".equals(mariStatus) || "22".equals(mariStatus) || "23".equals(mariStatus)) {
                    pibeEntity.setSpoName(marriage.getSpoName());
                    pibeEntity.setSpoIdType(PbccrcDictExch.EcifPrsnIdTypeExchange(marriage.getSpoIdType()));
                    pibeEntity.setSpoIdNum(marriage.getSpoIdNum());
                    pibeEntity.setSpoTel(marriage.getSpoTel());
                    pibeEntity.setSpsCmpyNm(marriage.getSpsCmpyNm());
                }
                pibeEntity.setUpdateTime(marriage.getUpdateTime());
                pibeEntity.setBussDate(marriage.getBussDate());
                pibeEntity.setItabGetDate(marriage.getItabGetDate());
                asyPibeList.add(pibeEntity);
            });

            // 收入信息
            List<PibjIncinfsgmtDTO> asyPibjList = new ArrayList<>();
            incList.forEach(inc -> {
                PibjIncinfsgmtDTO pibjEntity = new PibjIncinfsgmtDTO();
                pibjEntity.setCustId(inc.getCstmNo());
                pibjEntity.setAnnlInc(inc.getAnnlInc());
                pibjEntity.setTaxIncome(inc.getTaxIncome());
                pibjEntity.setUpdateTime(inc.getUpdateTime());
                pibjEntity.setBussDate(inc.getBussDate());
                pibjEntity.setItabGetDate(inc.getItabGetDate());
                asyPibjList.add(pibjEntity);
            });

            // 证件整合信息
            List<PiblCtfitginfDTO> asyPiblList = new ArrayList<>();
            otherList.forEach(other -> {
                PiblCtfitginfDTO piblEntity = new PiblCtfitginfDTO();
                piblEntity.setCustId(other.getCstmNo());
                // 现在没有出现客户有其他身份信息，如果有的话，报送时还要上送原name、原idType和原idNum，
                // 因为ecif的表里没有存，所以取不到值，如果要改的话要在ecif的身份信息整合段里加字段，加上这仨
//                piblEntity.setName();
//                piblEntity.setIdType();
//                piblEntity.setIdNum();
                piblEntity.setOthName(other.getOthName());
                piblEntity.setOthIdType(PbccrcDictExch.EcifPrsnIdTypeExchange(other.getOthIdType()));
                piblEntity.setOthIdNum(other.getOthIdNum());
                piblEntity.setOthAssFlg(other.getOthAssFlg());
                piblEntity.setUpdateTime(other.getUpdateTime());
                piblEntity.setBussDate(other.getBussDate());
                piblEntity.setItabGetDate(other.getItabGetDate());
                asyPiblList.add(piblEntity);

                PibcIdsgmtDTO pibcEntity = new PibcIdsgmtDTO();
                pibcEntity.setAlias(other.getOthName());
                pibcEntity.setOthidType(PbccrcDictExch.EcifPrsnIdTypeExchange(other.getOthIdType()));
                pibcEntity.setOthidNum(other.getOthIdNum());
                pibcEntity.setCustId(other.getCstmNo());
                pibcEntity.setUpdateTime(other.getUpdateTime());
                pibcEntity.setBussDate(other.getBussDate());
                pibcEntity.setItabGetDate(other.getItabGetDate());
                asyPibcList.add(pibcEntity);
            });

            // 家庭关系
            List<PibmFalmmbsinfDTO> asyPibmList = new ArrayList<>();
            famList.forEach(fam -> {
                PibmFalmmbsinfDTO pibmEntity = new PibmFalmmbsinfDTO();
                pibmEntity.setCustId(fam.getCstmNo());
                pibmEntity.setName(fam.getName());
                pibmEntity.setIdType(PbccrcDictExch.EcifPrsnIdTypeExchange(fam.getIdType()));
                pibmEntity.setIdNum(fam.getIdNum());
                pibmEntity.setFamMemName(fam.getFamMemName());
                pibmEntity.setFamMemCertType(PbccrcDictExch.EcifPrsnIdTypeExchange(fam.getFamMemCertType()));
                pibmEntity.setFamMemCertNum(fam.getFamMemCertNum());
                pibmEntity.setFamRel(fam.getFamRel());
                pibmEntity.setFamRelaAssFlag(fam.getFamRelaAssFlag());
                pibmEntity.setUpdateTime(fam.getUpdateTime());
                pibmEntity.setBussDate(fam.getBussDate());
                pibmEntity.setItabGetDate(fam.getItabGetDate());
                asyPibmList.add(pibmEntity);
            });

            asySendService.ays2Up(asyPibbList, AsyPibbBssgmtDao.class);
            asySendService.ays2Up(asyPibcList, AsyPibcIdsgmtDao.class);
            asySendService.ays2Up(asyPibdList, AsyPibdFcsinfsgmtDao.class);
            asySendService.ays2Up(asyPibeList, AsyPibeSpsinfsgmtDao.class);
            asySendService.ays2Up(asyPibfList, AsyPibfEduinfsgmtDao.class);
            asySendService.ays2Up(asyPibgList, AsyPibgOctpninfsgmtDao.class);
            asySendService.ays2Up(asyPibhList, AsyPibhRedncinfsgmtDao.class);
            asySendService.ays2Up(asyPibiList, AsyPibiMlginfsgmtDao.class);
            asySendService.ays2Up(asyPibjList, AsyPibjIncinfsgmtDao.class);
            asySendService.ays2Up(asyPibkList, AsyPibkIdefctinfDao.class);
            asySendService.ays2Up(asyPiblList, AsyPiblCtfitginfDao.class);
            asySendService.ays2Up(asyPibmList, AsyPibmFalmmbsinfDao.class);
            // 提交事务
            transactionHelper.commit(tm);

        } catch (Exception e) {
            log.error("捕获到异常", e);
            transactionHelper.rollback(tm);
            throw e;
        } finally {
            DynamicContextHolder.poll();
        }

        //登记报送记录
        this.reportRecord(DateUtils.format(new Date(), "yyyyMMdd"), baseList, eduList, occupList, liveList, mailList, marriageList, incList, otherList, famList);
    }

    private void reportRecord(String itabGetDate,
                              List<TPrsnEcifBaseDTO> baseList,
                              List<TPrsnEcifEducationDTO> eduList,
                              List<TPrsnEcifOccupationDTO> occupList,
                              List<TPrsnEcifLiveAddressDTO> liveList,
                              List<TPrsnEcifMailAddressDTO> mailList,
                              List<TPrsnEcifMarriageDTO> marriageList,
                              List<TPrsnEcifIncomeDTO> incList,
                              List<TPrsnEcifOtherCredentialsDTO> otherList,
                              List<TPrsnEcifFamilyDTO> famList) {

        List<ReportRecordPrsnEntity> entityList = new ArrayList<>();

        for (TPrsnEcifBaseDTO dto : baseList) {
            //基础段报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBB)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);

            //基本概况信息段报送记录
            ReportRecordPrsnEntity entityD = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBD)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entityD);

            //人证件有效期信息段报送记录
            ReportRecordPrsnEntity entityK = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBK)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entityK);
        }

        for (TPrsnEcifEducationDTO dto : eduList) {
            //教育信息段报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBF)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (TPrsnEcifOccupationDTO dto : occupList) {
            //职业信息段报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBG)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (TPrsnEcifLiveAddressDTO dto : liveList) {
            //居住地址段报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBH)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (TPrsnEcifMailAddressDTO dto : mailList) {
            //通讯地址报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBI)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (TPrsnEcifMarriageDTO dto : marriageList) {
            //婚姻信息报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBE)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (TPrsnEcifIncomeDTO dto : incList) {
            //收入信息报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBJ)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        for (TPrsnEcifOtherCredentialsDTO dto : otherList) {
            //个人证件整合信息记录报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBL)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);

            //其他标识段报送记录
            ReportRecordPrsnEntity entityC = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBC)
                    .infoKey1(dto.getCstmNo())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entityC);
        }

        for (TPrsnEcifFamilyDTO dto : famList) {
            //家庭关系报送记录
            ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                    .reportDate(itabGetDate)
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBM)
                    .infoKey1(dto.getCstmNo())
                    .infoKey2(dto.getFamMemCertNum())
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            entityList.add(entity);
        }

        reportRecordPrsnService.insertBatch(entityList);
    }
}
