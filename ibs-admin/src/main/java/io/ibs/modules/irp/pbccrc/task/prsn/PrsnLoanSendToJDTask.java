package io.ibs.modules.irp.pbccrc.task.prsn;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import io.ibs.common.utils.DateUtils;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.commons.dynamic.datasource.config.DynamicContextHolder;
import io.ibs.modules.irp.ecif.prsn.dto.TPrsnEcifBaseDTO;
import io.ibs.modules.irp.ecif.prsn.service.TPrsnEcifBaseService;
import io.ibs.modules.irp.pbccrc.common.TaskUtil;
import io.ibs.modules.irp.pbccrc.common.service.AsySendService;
import io.ibs.modules.irp.pbccrc.prsninfo.dao.aysdao.*;
import io.ibs.modules.irp.pbccrc.prsninfo.dto.*;
import io.ibs.modules.irp.pbccrc.prsninfo.entity.aysentity.AsyPrsnInfoEntity;
import io.ibs.modules.irp.pbccrc.prsnloan.dao.*;
import io.ibs.modules.irp.pbccrc.prsnloan.dto.*;
import io.ibs.modules.irp.pbccrc.prsnloan.entity.*;
import io.ibs.modules.irp.pbccrc.prsnloan.service.*;
import io.ibs.modules.irp.pbccrc.psgrecord.dto.AsyPsgRecordgetDatePersonDTO;
import io.ibs.modules.irp.pbccrc.psgrecord.entity.AsyPsgRecordgetDatePersonEntity;
import io.ibs.modules.irp.pbccrc.psgrecord.service.AsyPsgRecordgetDatePersonService;
import io.ibs.modules.irp.pbccrc.psgrecord.service.PsgRecordgetDatePersonService;
import io.ibs.modules.irp.pbccrc.whitelist.entity.TWhiteListEntity;
import io.ibs.modules.irp.pbccrc.whitelist.service.TWhiteListService;
import io.ibs.modules.irp.pbccrc2.common.informix.entity.TLoanIou;
import io.ibs.modules.irp.pbccrc2.common.informix.service.TLoanIouService;
import io.ibs.modules.irp.record.entity.ReportRecordPrsnEntity;
import io.ibs.modules.irp.record.service.ReportRecordPrsnService;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.irp.util.ListUtils;
import io.ibs.modules.irp.util.UserUtil;
import io.ibs.modules.job.task.BaseTask;
import io.ibs.modules.sendMsg.service.AgtMsgDtlManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 征信二代
 * 从本地数据库抽取个人借贷信息到金电数据库
 *
 * <AUTHOR>
 * @since 20220728
 */
@Slf4j
@Service
public class PrsnLoanSendToJDTask extends BaseTask {

    private AsySendService asySendService;
    private TransactionHelper transactionHelper;
    private PilbAcctbssgmtService pilbAcctbssgmtService;
    private PilcAcctbsinfsgmtService pilcAcctbsinfsgmtService;
    private PildRltrepymtinfsgmtService pildRltrepymtinfsgmtService;
    private PileMotgacltalctrctinfsgmtService pileMotgacltalctrctinfsgmtService;
    private PilfAcctcredsgmtService pilfAcctcredsgmtService;
    private PilgOrigcreditorinfsgmtService pilgOrigcreditorinfsgmtService;
    private PilhAcctmthlyblginfsgmtService pilhAcctmthlyblginfsgmtService;
    private PiliSpecprdsgmtService piliSpecprdsgmtService;
    private PiljAcctdbtinfsgmtService piljAcctdbtinfsgmtService;
    private PilkAcctspectrstdspnsgmtService pilkAcctspectrstdspnsgmtService;
    private PilsSpcevtdscinfService pilsSpcevtdscinfService;
    private PsgRecordgetDatePersonService psgRecordgetDatePersonService;
    private AsyPsgRecordgetDatePersonService asyPsgRecordgetDatePersonService;
    private TWhiteListService twhiteListSvc;
    private ReportRecordPrsnService reportRecordPrsnService;
    private PrsnInfoSendToJD prsnInfoSendToJD;
    private TPrsnEcifBaseService tPrsnEcifBaseService;
    private TLoanIouService tLoanIouService;

    @Autowired
    public PrsnLoanSendToJDTask(TransactionHelper transactionHelper,
                                AsySendService asySendService,
                                PilbAcctbssgmtService pilbAcctbssgmtService,
                                PilcAcctbsinfsgmtService pilcAcctbsinfsgmtService,
                                PildRltrepymtinfsgmtService pildRltrepymtinfsgmtService,
                                PileMotgacltalctrctinfsgmtService pileMotgacltalctrctinfsgmtService,
                                PilfAcctcredsgmtService pilfAcctcredsgmtService,
                                PilgOrigcreditorinfsgmtService pilgOrigcreditorinfsgmtService,
                                PilhAcctmthlyblginfsgmtService pilhAcctmthlyblginfsgmtService,
                                PiliSpecprdsgmtService piliSpecprdsgmtService,
                                PiljAcctdbtinfsgmtService piljAcctdbtinfsgmtService,
                                PilkAcctspectrstdspnsgmtService pilkAcctspectrstdspnsgmtService,
                                PilsSpcevtdscinfService pilsSpcevtdscinfService,
                                PsgRecordgetDatePersonService psgRecordgetDatePersonService,
                                AsyPsgRecordgetDatePersonService asyPsgRecordgetDatePersonService,
                                TWhiteListService twhiteListSvc,
                                ReportRecordPrsnService reportRecordPrsnService,
                                PrsnInfoSendToJD prsnInfoSendToJD,
                                AgtMsgDtlManagementService agtMsgDtlManagementService,
                                TPrsnEcifBaseService tPrsnEcifBaseService,
                                TLoanIouService tLoanIouService) {
        super("征信二代-抽取个人借贷信息到金电数据库", PrsnLoanSendToJDTask.class);
        this.transactionHelper = transactionHelper;
        this.asySendService = asySendService;
        this.pilbAcctbssgmtService = pilbAcctbssgmtService;
        this.pilcAcctbsinfsgmtService = pilcAcctbsinfsgmtService;
        this.pildRltrepymtinfsgmtService = pildRltrepymtinfsgmtService;
        this.pileMotgacltalctrctinfsgmtService = pileMotgacltalctrctinfsgmtService;
        this.pilfAcctcredsgmtService = pilfAcctcredsgmtService;
        this.pilgOrigcreditorinfsgmtService = pilgOrigcreditorinfsgmtService;
        this.pilhAcctmthlyblginfsgmtService = pilhAcctmthlyblginfsgmtService;
        this.piliSpecprdsgmtService = piliSpecprdsgmtService;
        this.piljAcctdbtinfsgmtService = piljAcctdbtinfsgmtService;
        this.pilkAcctspectrstdspnsgmtService = pilkAcctspectrstdspnsgmtService;
        this.pilsSpcevtdscinfService = pilsSpcevtdscinfService;
        this.psgRecordgetDatePersonService = psgRecordgetDatePersonService;
        this.asyPsgRecordgetDatePersonService = asyPsgRecordgetDatePersonService;
        this.twhiteListSvc = twhiteListSvc;
        this.reportRecordPrsnService = reportRecordPrsnService;
        this.prsnInfoSendToJD = prsnInfoSendToJD;
        this.tPrsnEcifBaseService= tPrsnEcifBaseService;
        this.tLoanIouService=tLoanIouService;
    }

    @Override
    public void execute(String params) throws Exception {
        params = super.checkDateParam(params, "yyyyMMdd");
        if (params.contains("-")) {
            String bgnDate = params.split("-")[0];
            String endDate = params.split("-")[1];
            // 获取开始日期到结束日期之间的所有日期
            List<String> dateList = io.ibs.modules.irp.util.DateUtils.getDateListBetweenTwoDays(bgnDate, endDate, Constants.SUBMIT_DATE_FORMAT);
            for (String bussDate : dateList) {
                this.doSend(bussDate, params);
                log.info("[{}]报送结束\n", bussDate);
            }
        } else {
            this.doSend(params, params);
            log.info("[{}]报送结束\n", params);
        }
    }

    @SuppressWarnings("unchecked")
    private void doSend(String bussDate, String params) throws Exception {
        // 因为任意段报送时，基础段都要一起跟着报，所以前面会收集所有报送段的业务号baseIds，然后基础段根据这些业务号查询后上报
        List<String> baseIds;

        // 检查当前日期是否已经报送过
        log.info("检查当前日期[{}]是否已经报送过", bussDate);
        if (psgRecordgetDatePersonService.checkAsy(bussDate, ReportRecordPrsnEntity.INFO_TYPE_PIL)) {
            return;
        }

        // 屏蔽白名单内的借据 20221229 add
        List<String> whiteList = twhiteListSvc.getWhiteBussNum(TWhiteListEntity.CSTM_TYPE_1, TWhiteListEntity.STATE_0);

        // 获取一天的开始和结束 或 时间段
        Date[] between = TaskUtil.getBeginEndOfDay(bussDate);

        // 查询基础段更新的记录
        QueryWrapper<PilbAcctbssgmtEntity> pilblsWrapper = new QueryWrapper<>();
        pilblsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PilbAcctbssgmtDTO> acctbssgmtDTOS = pilbAcctbssgmtService.list(pilblsWrapper);


        // 查询 借贷-基本信息段 需要更新的记录
        QueryWrapper<PilcAcctbsinfsgmtEntity> pilclsWrapper = new QueryWrapper<>();
        pilclsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PilcAcctbsinfsgmtDTO> pilcls = pilcAcctbsinfsgmtService.list(pilclsWrapper);

        // 查询  借贷-相关还款责任人信息段，需要更新的记录
        QueryWrapper<PildRltrepymtinfsgmtEntity> pildlsWrapper = new QueryWrapper<>();
        pildlsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PildRltrepymtinfsgmtDTOPbccrc> pildls = pildRltrepymtinfsgmtService.list(pildlsWrapper);

        // 查询  借贷-抵质押物信息段，需要更新的记录
        QueryWrapper<PileMotgacltalctrctinfsgmtEntity> pilelsWrapper = new QueryWrapper<>();
        pilelsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PileMotgacltalctrctinfsgmtDTO> pilels = pileMotgacltalctrctinfsgmtService.list(pilelsWrapper);

        // 查询  借贷-授信额度信息段，需要更新的记录
        QueryWrapper<PilfAcctcredsgmtEntity> pilflsWrapper = new QueryWrapper<>();
        pilflsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PilfAcctcredsgmtDTO> pilfls = pilfAcctcredsgmtService.list(pilflsWrapper);

        // 查询  借贷-初始债权说明段，需要更新的记录
        QueryWrapper<PilgOrigcreditorinfsgmtEntity> pilglsWrapper = new QueryWrapper<>();
        pilglsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PilgOrigcreditorinfsgmtDTO> pilgdls = pilgOrigcreditorinfsgmtService.list(pilglsWrapper);

        // 查询  借贷-还款表现信息段，需要更新的记录
        QueryWrapper<PilhAcctmthlyblginfsgmtEntity> pilhlsWrapper = new QueryWrapper<>();
        pilhlsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PilhAcctmthlyblginfsgmtDTO> pilhls = pilhAcctmthlyblginfsgmtService.list(pilhlsWrapper);

        // 查询  借贷-特定交易说明段，需要更新的记录
        QueryWrapper<PiliSpecprdsgmtEntity> pililsWrapper = new QueryWrapper<>();
        pililsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PiliSpecprdsgmtDTO> pilils = piliSpecprdsgmtService.list(pililsWrapper);

        // 查询  借贷-非月度表现信息段，需要更新的记录
        QueryWrapper<PiljAcctdbtinfsgmtEntity> piljlsWrapper = new QueryWrapper<>();
        piljlsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PiljAcctdbtinfsgmtDTO> piljls = piljAcctdbtinfsgmtService.list(piljlsWrapper);

        // 查询  借贷-特殊交易说明段，需要更新的记录
        QueryWrapper<PilkAcctspectrstdspnsgmtEntity> pilklsWrapper = new QueryWrapper<>();
        pilklsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PilkAcctspectrstdspnsgmtDTO> pilkls = pilkAcctspectrstdspnsgmtService.list(pilklsWrapper);

        // 查询  借贷-账户特殊事件说明记录段，需要更新的记录
        QueryWrapper<PilsSpcevtdscinfEntity> pilslsWrapper = new QueryWrapper<>();
        pilslsWrapper.between("BUSS_DATE", between[0], between[1]);
        List<PilsSpcevtdscinfDTO> pilsls = pilsSpcevtdscinfService.list(pilslsWrapper);

        //如果借据号在白名单内，则排除 之前用not in 语句会导致sql超长，改为下面的形式移除
        if (!CollectionUtils.isEmpty(whiteList)) {
            for (String whiteBussNum : whiteList) {
                // 借贷-基础段
                for (int i = 0; i < acctbssgmtDTOS.size(); i++) {
                    if (acctbssgmtDTOS.get(i).getBussNum().equals(whiteBussNum)){
                        acctbssgmtDTOS.remove(i);
                        i--;
                    }
                }
                // 借贷-基本信息段
                for (int i = 0; i < pilcls.size(); i++) {
                    if (pilcls.get(i).getBussNum().equals(whiteBussNum)){
                        pilcls.remove(i);
                        i--;
                    }
                }
                // 借贷-相关还款责任人信息段
                for (int i = 0; i < pildls.size(); i++) {
                    if (pildls.get(i).getBussNum().equals(whiteBussNum)){
                        pildls.remove(i);
                        i--;
                    }
                }
                // 借贷-抵质押物信息段
                for (int i = 0; i < pilels.size(); i++) {
                    if (pilels.get(i).getBussNum().equals(whiteBussNum)){
                        pilels.remove(i);
                        i--;
                    }
                }
                // 借贷-授信额度信息段
                for (int i = 0; i < pilfls.size(); i++) {
                    if (pilfls.get(i).getBussNum().equals(whiteBussNum)){
                        pilfls.remove(i);
                        i--;
                    }
                }
                // 借贷-初始债权说明段
                for (int i = 0; i < pilgdls.size(); i++) {
                    if (pilgdls.get(i).getBussNum().equals(whiteBussNum)) {
                        pilgdls.remove(i);
                        i--;
                    }
                }
                // 借贷-还款表现信息段
                for (int i = 0; i < pilhls.size(); i++) {
                    if (pilhls.get(i).getBussNum().equals(whiteBussNum)){
                        pilhls.remove(i);
                        i--;
                    }
                }
                // 借贷-特定交易说明段
                for (int i = 0; i < pilils.size(); i++) {
                    if (pilils.get(i).getBussNum().equals(whiteBussNum)){
                        pilils.remove(i);
                        i--;
                    }
                }
                // 借贷-非月度表现信息段
                for (int i = 0; i < piljls.size(); i++) {
                    if (piljls.get(i).getBussNum().equals(whiteBussNum)){
                        piljls.remove(i);
                        i--;
                    }
                }
                // 借贷-特殊交易说明段
                for (int i = 0; i < pilkls.size(); i++) {
                    if (pilkls.get(i).getBussNum().equals(whiteBussNum)){
                        pilkls.remove(i);
                        i--;
                    }
                }
                // 借贷-账户特殊事件说明记录段
                for (int i = 0; i < pilsls.size(); i++) {
                    if (pilsls.get(i).getBussNum().equals(whiteBussNum)){
                        pilsls.remove(i);
                        i--;
                    }
                }
            }
        }

        //20240116
        String endDate="";
        String bgnDate="";
        if (params.contains("-")) {
            bgnDate = params.split("-")[0];
            endDate = params.split("-")[1];
        }

        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
        if(StringUtils.isNoneBlank(endDate)){
            QueryWrapper<PilhAcctmthlyblginfsgmtEntity> pilhlsWrapper1 = new QueryWrapper<>();
            try {
                pilhlsWrapper1.ge("BUSS_DATE",sdf.parse(bgnDate));
                pilhlsWrapper1.le("BUSS_DATE",sdf.parse(endDate));
                pilhlsWrapper1.eq("acct_status","2");
            } catch (ParseException e) {
                e.printStackTrace();
            }
            List<PilhAcctmthlyblginfsgmtDTO> pilhls1 = pilhAcctmthlyblginfsgmtService.list(pilhlsWrapper1);
            Map<String, Optional<Date>> pilhls2 = pilhls1.stream().collect(Collectors.groupingBy(PilhAcctmthlyblginfsgmtDTO::getBussNum, Collectors.mapping(PilhAcctmthlyblginfsgmtDTO::getMonth, Collectors.maxBy(Comparator.naturalOrder()))));
            pilhls.forEach(s->{
                if(pilhls2.containsKey(s.getBussNum())){//存量最后一笔逾期发送短信处理
                    if(pilhls2.get(s.getBussNum()).isPresent()){
                        log.info("bussNum:" + s.getBussNum());
                        String curdate=sdf.format(s.getMonth());

                        String msgdate=sdf.format(pilhls2.get(s.getBussNum()).get());
                        log.info("存量 bussDate={},curdate={},msgdate={}",bussDate,curdate,msgdate);
                        if(curdate.equals(msgdate)){
                            String overDuteDate=sdf.format(s.getLastOverdueDate());
                            //短信
                            QueryWrapper<PilbAcctbssgmtEntity> pilblsWrapper1 = new QueryWrapper<>();
                            try {
                                pilblsWrapper1.eq("BUSS_DATE",sdf.parse(bussDate));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            pilblsWrapper1.eq("buss_Num",s.getBussNum());
                            List<PilbAcctbssgmtDTO> pilcls1 = pilbAcctbssgmtService.list(pilblsWrapper1);
                            if(null != pilcls1 && pilcls1.size()==1){
                                String openDate="";
                                try{
                                    PilcAcctbsinfsgmtDTO pilcAcctbsinfsgmtDTO = pilcAcctbsinfsgmtService.getByBussNumAndBussDate(s.getBussNum(), sdf.parse(bussDate));
                                    if(null != pilcAcctbsinfsgmtDTO){
                                        openDate =sdf.format(pilcAcctbsinfsgmtDTO.getOpenDate());
                                        String custId= pilcls1.get(0).getCustId();
                                        if(StringUtils.isNoneBlank(custId)){
                                            TPrsnEcifBaseDTO tPrsnEcifBaseDTO = tPrsnEcifBaseService.getByCstmNo(custId);
                                            String phone=tPrsnEcifBaseDTO.getCellPhone();
                                            if(StringUtils.isNoneBlank(phone)){
                                                String cstmName=tPrsnEcifBaseDTO.getName();
                                                QueryWrapper<TLoanIou> pilblsWrapper2 = new QueryWrapper<>();
                                                pilblsWrapper2.eq("LOAN_NO",s.getBussNum());
                                                List<TLoanIou> pilcls2 = tLoanIouService.list(pilblsWrapper2);
                                                String loanInst="";
                                                if(null != pilcls2 && pilcls2.size()==1){
                                                    loanInst= pilcls2.get(0).getLoanInst();
                                                }
                                                this.sendMsgWhenOverDate(phone,cstmName,s.getBussNum(),openDate,overDuteDate,custId,loanInst);
                                            }
                                           }
                                    }
                                }catch (ParseException e){
                                    e.printStackTrace();
                                }
                            }
                        }

                    }

                }
            });
        }else{//增量
            pilhls.forEach(s->{
                String acctStatus=s.getAcctStatus();
                if(StringUtils.isNoneBlank(acctStatus) && "2".equals(acctStatus)){
                    log.info("增量");
                    QueryWrapper<PilbAcctbssgmtEntity> pilblsWrapper1 = new QueryWrapper<>();
                    try {
                        pilblsWrapper1.eq("BUSS_DATE",sdf.parse(bussDate));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    pilblsWrapper1.eq("buss_Num",s.getBussNum());
                    List<PilbAcctbssgmtDTO> pilcls1 = pilbAcctbssgmtService.list(pilblsWrapper1);
                    if(null != pilcls1 && pilcls1.size()==1){
                        String openDate="";
                        try{
                            PilcAcctbsinfsgmtDTO pilcAcctbsinfsgmtDTO = pilcAcctbsinfsgmtService.getByBussNumAndBussDate(s.getBussNum(), sdf.parse(bussDate));
                            if(null != pilcAcctbsinfsgmtDTO){
                            openDate =sdf.format(pilcAcctbsinfsgmtDTO.getOpenDate());
                            String custId= pilcls1.get(0).getCustId();
                            String overDuteDate=sdf.format(s.getLastOverdueDate());
                            if(StringUtils.isNoneBlank(custId)){
                                TPrsnEcifBaseDTO tPrsnEcifBaseDTO = tPrsnEcifBaseService.getByCstmNo(custId);
                                String phone=tPrsnEcifBaseDTO.getCellPhone();

                                if(StringUtils.isNoneBlank(phone)){
                                    String cstmName=tPrsnEcifBaseDTO.getName();
                                    QueryWrapper<TLoanIou> pilblsWrapper2 = new QueryWrapper<>();
                                    pilblsWrapper2.eq("LOAN_NO",s.getBussNum());
                                    List<TLoanIou> pilcls2 = tLoanIouService.list(pilblsWrapper2);
                                    String loanInst="";
                                    if(null != pilcls2 && pilcls2.size()==1){
                                        loanInst= pilcls2.get(0).getLoanInst();
                                    }
                                    this.sendMsgWhenOverDate(phone,cstmName,s.getBussNum(),openDate,overDuteDate,custId,loanInst);
                                }
                            }
                            }
                        }catch (ParseException e){
                            e.printStackTrace();
                        }

                    }
                }
            });
        }
        //20240116
        // 收集借据号
        baseIds = acctbssgmtDTOS.stream().map(PilbAcctbssgmtDTO::getBussNum).collect(Collectors.toList());
        baseIds.addAll(pilcls.stream().map(PilcAcctbsinfsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pildls.stream().map(PildRltrepymtinfsgmtDTOPbccrc::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pilels.stream().map(PileMotgacltalctrctinfsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pilfls.stream().map(PilfAcctcredsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pilgdls.stream().map(PilgOrigcreditorinfsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pilhls.stream().map(PilhAcctmthlyblginfsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pilils.stream().map(PiliSpecprdsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(piljls.stream().map(PiljAcctdbtinfsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pilkls.stream().map(PilkAcctspectrstdspnsgmtDTO::getBussNum).collect(Collectors.toList()));
        baseIds.addAll(pilsls.stream().map(PilsSpcevtdscinfDTO::getBussNum).collect(Collectors.toList()));

        // 最后 上报  借贷-基础段
        if (CollectionUtils.isEmpty(baseIds)) {
            log.info("当前日期没有数据需要报送！");
            return;
        }
        baseIds = baseIds.stream().distinct().collect(Collectors.toList());
        List<PilbAcctbssgmtDTO> pilbls2 = new ArrayList<>();
        Lists.partition(baseIds, 200).forEach(base -> {
            QueryWrapper<PilbAcctbssgmtEntity> pilblsWrapper2 = new QueryWrapper<>();
            pilblsWrapper2.between("BUSS_DATE", between[0], between[1]);
            pilblsWrapper2.in("BUSS_NUM", base);
            pilbls2.addAll(pilbAcctbssgmtService.list(pilblsWrapper2));
        });

        List<AsyPrsnInfoEntity> asyPrsnInfoEntityList = new ArrayList<>();
        //报送借据的同时，如果该借据的客户信息没有报送过，则同时报送其客户信息
        for (PilbAcctbssgmtDTO dto : pilbls2) {
            String cstmNo = dto.getCustId();
            // 检查该客户的客户信息是否报送过
            ReportRecordPrsnEntity baseRecord = ReportRecordPrsnEntity.builder()
                    .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIB)
                    .sgmt(ReportRecordPrsnEntity.SGMT_PIBB)
                    .infoKey1(cstmNo)
                    .reportFlag(Constants.REPORT_FLAG_1)
                    .build();
            boolean baseRecordIsExists = reportRecordPrsnService.exists(baseRecord);
            if (!baseRecordIsExists) {
                AsyPrsnInfoEntity info = prsnInfoSendToJD.execute(cstmNo, params, bussDate);
                if (info != null) {
                    asyPrsnInfoEntityList.add(info);
                }
            }
        }

        // 设置数据源
        DynamicContextHolder.push("credit");
        // 开启事务
        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();
        try {
            // 设置 非月度表现信息段中的“最近一次实际还款日期”必须和“信息报告日期”保持一致
            piljls.parallelStream().forEach(dto -> {
                dto.setItabGetDate(DateUtils.format(dto.getLatRpyDate(), "yyyyMMdd"));
                dto.setBussDate(dto.getLatRpyDate());
            });

            QueryWrapper<AsyPsgRecordgetDatePersonEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("ITAB_GET_DATE", bussDate);
            List<AsyPsgRecordgetDatePersonDTO> logs = asyPsgRecordgetDatePersonService.list(wrapper);
            if (logs == null || logs.isEmpty()) {
                asyPsgRecordgetDatePersonService.createRecord(bussDate);
            }
            // 上报插入记录
            asySendService.ays2Up(pilcls, AsyPilcAcctbsinfsgmtDao.class);
            asySendService.ays2Up(pildls, AsyPildRltrepymtinfsgmtDao.class);
            asySendService.ays2Up(pilels, AsyPileMotgacltalctrctinfsgmtDao.class);
            asySendService.ays2Up(pilfls, AsyPilfAcctcredsgmtDao.class);
            asySendService.ays2Up(pilgdls, AsyPilgOrigcreditorinfsgmtDao.class);
            asySendService.ays2Up(pilhls, AsyPilhAcctmthlyblginfsgmtDao.class);
            asySendService.ays2Up(pilils, AsyPiliSpecprdsgmtDao.class);
            asySendService.ays2Up(piljls, AsyPiljAcctdbtinfsgmtDao.class);
            asySendService.ays2Up(pilkls, AsyPilkAcctspectrstdspnsgmtDao.class);
            asySendService.ays2Up(pilsls, AsyPilsSpcevtdscinfDao.class);
            asySendService.ays2Up(pilbls2, AsyPilbAcctbssgmtDao.class);

            if (asyPrsnInfoEntityList.size() > 0) {
                List<PibbBssgmtDTO> asyPibbList = new ArrayList<>();// 基础段
                List<PibcIdsgmtDTO> asyPibcList = new ArrayList<>();// 其他标识段
                List<PibdFcsinfsgmtDTO> asyPibdList = new ArrayList<>();// 基本信息概况
                List<PibeSpsinfsgmtDTO> asyPibeList = new ArrayList<>();// 婚姻信息段
                List<PibfEduinfsgmtDTO> asyPibfList = new ArrayList<>();// 教育信息段
                List<PibgOctpninfsgmtDTO> asyPibgList = new ArrayList<>();// 职业信息段
                List<PibhRedncinfsgmtDTO> asyPibhList = new ArrayList<>();// 居住地址段
                List<PibiMlginfsgmtDTO> asyPibiList = new ArrayList<>();// 通讯地址信息段
                List<PibjIncinfsgmtDTO> asyPibjList = new ArrayList<>();// 收入信息段
                List<PibkIdefctinfDTO> asyPibkList = new ArrayList<>();// 个人证件有效期信息
                List<PiblCtfitginfDTO> asyPiblList = new ArrayList<>();// 个人证件整合信息记录
                List<PibmFalmmbsinfDTO> asyPibmList = new ArrayList<>();// 家族关系信息记录
                for (AsyPrsnInfoEntity asyPrsnInfoEntity : asyPrsnInfoEntityList) {
                    asyPibbList.addAll(asyPrsnInfoEntity.getAsyPibbList());
                    asyPibcList.addAll(asyPrsnInfoEntity.getAsyPibcList());
                    asyPibdList.addAll(asyPrsnInfoEntity.getAsyPibdList());
                    asyPibeList.addAll(asyPrsnInfoEntity.getAsyPibeList());
                    asyPibfList.addAll(asyPrsnInfoEntity.getAsyPibfList());
                    asyPibgList.addAll(asyPrsnInfoEntity.getAsyPibgList());
                    asyPibhList.addAll(asyPrsnInfoEntity.getAsyPibhList());
                    asyPibiList.addAll(asyPrsnInfoEntity.getAsyPibiList());
                    asyPibjList.addAll(asyPrsnInfoEntity.getAsyPibjList());
                    asyPibkList.addAll(asyPrsnInfoEntity.getAsyPibkList());
                    asyPiblList.addAll(asyPrsnInfoEntity.getAsyPiblList());
                    asyPibmList.addAll(asyPrsnInfoEntity.getAsyPibmList());
                }

                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibbList, PibbBssgmtDTO::getCustId), AsyPibbBssgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibcList, PibcIdsgmtDTO::getCustId), AsyPibcIdsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibdList, PibdFcsinfsgmtDTO::getCustId), AsyPibdFcsinfsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibeList, PibeSpsinfsgmtDTO::getCustId), AsyPibeSpsinfsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibfList, PibfEduinfsgmtDTO::getCustId), AsyPibfEduinfsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibgList, PibgOctpninfsgmtDTO::getCustId), AsyPibgOctpninfsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibhList, PibhRedncinfsgmtDTO::getCustId), AsyPibhRedncinfsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibiList, PibiMlginfsgmtDTO::getCustId), AsyPibiMlginfsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibjList, PibjIncinfsgmtDTO::getCustId), AsyPibjIncinfsgmtDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibkList, PibkIdefctinfDTO::getCustId), AsyPibkIdefctinfDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPiblList, PiblCtfitginfDTO::getCustId), AsyPiblCtfitginfDao.class);
                asySendService.ays2Up(ListUtils.listDistinctWithObject(asyPibmList, PibmFalmmbsinfDTO::getCustId, PibmFalmmbsinfDTO::getFamMemCertNum), AsyPibmFalmmbsinfDao.class);
            }

            // 提交事务
            transactionHelper.commit(tm);
        } catch (Exception e) {
            log.error("捕获到异常", e);
            transactionHelper.rollback(tm);
            throw e;// 抛给父类捕获并发送短信
        } finally {
//            // 恢复数据源
            DynamicContextHolder.poll();
        }

        // 登记报送记录
        this.reportRecord(pilbls2, pilcls, pildls, pilels, pilfls, pilgdls, pilhls, pilils, piljls, pilkls, pilsls, asyPrsnInfoEntityList);
    }

    private void reportRecord(List<PilbAcctbssgmtDTO> pilbls,
                              List<PilcAcctbsinfsgmtDTO> pilcls,
                              List<PildRltrepymtinfsgmtDTOPbccrc> pildls,
                              List<PileMotgacltalctrctinfsgmtDTO> pilels,
                              List<PilfAcctcredsgmtDTO> pilfls,
                              List<PilgOrigcreditorinfsgmtDTO> pilgdls,
                              List<PilhAcctmthlyblginfsgmtDTO> pilhls,
                              List<PiliSpecprdsgmtDTO> pilils,
                              List<PiljAcctdbtinfsgmtDTO> piljls,
                              List<PilkAcctspectrstdspnsgmtDTO> pilkls,
                              List<PilsSpcevtdscinfDTO> pilsls,
                              List<AsyPrsnInfoEntity> asyPrsnInfoEntityList) {
        // 开启事务
        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();
        try {
            if (asyPrsnInfoEntityList.size() > 0) {
                prsnInfoSendToJD.reportRecord(asyPrsnInfoEntityList);
            }

            for (PilbAcctbssgmtDTO dto : pilbls) {
                //基础段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILB)
                        .infoKey1(dto.getBussNum())
                        .infoKey2(dto.getName())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .note1(dto.getCustId().substring(0, 4))
                        .note2(UserUtil.getRealNameByUserId(dto.getUpdator()))
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PilcAcctbsinfsgmtDTO dto : pilcls) {
                //基本信息段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILC)
                        .infoKey1(dto.getBussNum())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PildRltrepymtinfsgmtDTOPbccrc dto : pildls) {
                //相关还款责任人段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILD)
                        .infoKey1(dto.getBussNum())
                        .infoKey2(dto.getArlpCertNum())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PileMotgacltalctrctinfsgmtDTO dto : pilels) {
                //抵质押物信息段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILE)
                        .infoKey1(dto.getBussNum())
                        .infoKey2(dto.getCcc2())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PilfAcctcredsgmtDTO dto : pilfls) {
                //授信额度信息段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILF)
                        .infoKey1(dto.getBussNum())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PilgOrigcreditorinfsgmtDTO dto : pilgdls) {
                //初始债权说明段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILG)
                        .infoKey1(dto.getBussNum())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PilhAcctmthlyblginfsgmtDTO dto : pilhls) {
                //月度表现信息段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILH)
                        .infoKey1(dto.getBussNum())
                        .infoKey2(DateUtils.format(dto.getFiveCateAdjDate()))
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PiliSpecprdsgmtDTO dto : pilils) {
                //大额专项分期信息段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILI)
                        .infoKey1(dto.getBussNum())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PiljAcctdbtinfsgmtDTO dto : piljls) {
                //非月度表现信息段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILJ)
                        .infoKey1(dto.getBussNum())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PilkAcctspectrstdspnsgmtDTO dto : pilkls) {
                //特殊交易说明段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILK)
                        .infoKey1(dto.getBussNum())
                        .infoKey2(DateUtils.format(dto.getTranDate(), "yyyyMMdd"))
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            for (PilsSpcevtdscinfDTO dto : pilsls) {
                //账户特殊事件说明记录段报送记录
                ReportRecordPrsnEntity entity = ReportRecordPrsnEntity.builder()
                        .reportDate(DateUtil.format(dto.getBussDate(), "yyyyMMdd"))
                        .infoType(ReportRecordPrsnEntity.INFO_TYPE_PIL)
                        .sgmt(ReportRecordPrsnEntity.SGMT_PILS)
                        .infoKey1(dto.getBussNum())
                        .reportFlag(Constants.REPORT_FLAG_1)
                        .build();
                reportRecordPrsnService.updateByIndex(entity);
            }

            // 提交事务
            transactionHelper.commit(tm);
        } catch (Exception e) {
            log.error("捕获到异常", e);
            transactionHelper.rollback(tm);
            throw e;// 抛给父类捕获并发送短信
        }
    }
}
