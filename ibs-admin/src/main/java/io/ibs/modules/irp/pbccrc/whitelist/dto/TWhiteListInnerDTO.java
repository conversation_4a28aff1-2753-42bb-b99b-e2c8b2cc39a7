package io.ibs.modules.irp.pbccrc.whitelist.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 白名单表 系统内部
 *
 * <AUTHOR> mail
 * @since 3.0 2022-11-28
 */
@Data
@ApiModel(value = "白名单表")
public class TWhiteListInnerDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "客户号")
    private String cstmNo;
    @ApiModelProperty(value = "客户名称")
    private String cstmName;
    @ApiModelProperty(value = "业务号")
    private String bussNum;
    @ApiModelProperty(value = "白名单类型")
    private String cstmType;
    @ApiModelProperty(value = "状态")
    private String state;
    @ApiModelProperty(value = "备注")
    private String remake;
    private Long creator;
    private String creatorName;
    private Date createDate;
    private Long updater;
    private String updaterName;
    private Date updateDate;

}