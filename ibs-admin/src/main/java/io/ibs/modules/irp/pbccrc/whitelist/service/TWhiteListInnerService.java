package io.ibs.modules.irp.pbccrc.whitelist.service;

import io.ibs.common.service.CrudService;
import io.ibs.modules.irp.pbccrc.whitelist.dto.TWhiteListInnerDTO;
import io.ibs.modules.irp.pbccrc.whitelist.entity.TWhiteListInnerEntity;

import java.util.List;

/**
 * 白名单表 系统内部
 *
 * <AUTHOR> mail
 * @since 3.0 2022-11-28
 */
public interface TWhiteListInnerService extends CrudService<TWhiteListInnerEntity, TWhiteListInnerDTO> {
    /**
     * 根据客户类型和状态获取白名单客户号
     *
     * @param cstmType 1-个人;2-企业
     * @param state    0-启用;1-停用
     * @return 白名单数据
     */
    List<String> getWhiteCstm(String cstmType, String state);

    /**
     * 根据客户类型和状态获取白名单借据号
     *
     * @param cstmType 1-个人;2-企业
     * @param state    0-启用;1-停用
     * @return 白名单数据
     */
    List<String> getWhiteBussNum(String cstmType, String state);

    /**
     * 根据客户类型和状态获取白名单数据
     *
     * @param cstmType 1-个人;2-企业
     * @param state    0-启用;1-停用
     * @return 白名单数据
     */
    List<TWhiteListInnerEntity> getWhiteCstmAndBussNum(String cstmType, String state);
}