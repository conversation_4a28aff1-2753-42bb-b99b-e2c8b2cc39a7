package io.ibs.modules.irp.pbccrc2.common.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.irp.util.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 五级分类调整历史记录表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "五级分类调整记录", description = "")
@TableName(value = "t_loan_five_cate_adj")
public class FiveCateAdjustEntity implements Serializable, Cloneable {
    /**
     * id
     */
    @ApiModelProperty(name = "id", notes = "")
    @TableId
    private Long id;
    /**
     * 借据号
     */
    @ApiModelProperty(name = "借据号", notes = "")
    private String loanNo;
    /**
     * 五级分类
     */
    @ApiModelProperty(name = "五级分类", notes = "")
    private String cate;
    /**
     * 五级分类调整日期
     */
    @ApiModelProperty(name = "五级分类调整日期", notes = "")
    private Date adjDate;
    /**
     * 上一次五级分类信息
     */
    @ApiModelProperty(name = "上一次五级分类信息", notes = "")
    private Long lastId;
    /**
     * 计算日期
     */
    @ApiModelProperty(name = "计算日期", notes = "")
    private Date bussDate;
    /**
     * 修改日期
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updator;

    public FiveCateAdjustEntity(String loanNo, String fiveCate, Date adjDate, Date bussDate) {
        this.loanNo = loanNo;
        this.cate = fiveCate;
        this.adjDate = adjDate;
        this.bussDate = bussDate;
        this.updator = Constants.UPDATER_FOR_TASK;
        this.updateTime = new Date();
    }

    /**
     * 五级分类是否首次发生了调整
     *
     * @return
     */
    public boolean isAdjust() {
        return id == null;
    }

    /**
     * 五级分类相较于上次是否首次发生了调整
     *
     * @return
     */
    public boolean isAdjust(FiveCateEntity fiveCateEntity) {

        return cate != null && !cate.equals(fiveCateEntity.getFiveCate());
    }
}
