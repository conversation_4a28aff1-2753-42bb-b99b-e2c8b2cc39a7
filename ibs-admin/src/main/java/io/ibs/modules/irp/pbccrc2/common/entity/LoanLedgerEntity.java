package io.ibs.modules.irp.pbccrc2.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 贷款主账户实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoanLedgerEntity {
    /**
     * 标志
     */
    private String flag;

    /**
     * 起息日
     */
    private String bgnDate;

    /**
     * 余额
     */
    private BigDecimal bal;

    /**
     * 基数
     */
    private BigDecimal accu;
}
