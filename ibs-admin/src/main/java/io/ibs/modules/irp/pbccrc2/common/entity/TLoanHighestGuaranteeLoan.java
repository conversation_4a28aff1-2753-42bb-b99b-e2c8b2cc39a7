package io.ibs.modules.irp.pbccrc2.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 借贷-最高额担保借据信息表实体类
 * Created by AileYoung on 2023-09-19 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_LOAN_HIGHEST_GUARANTEE_LOAN")
@Builder
@AllArgsConstructor
public class TLoanHighestGuaranteeLoan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 借据号
     */
    @TableField("LOAN_NO")
    private String loanNo;

    /**
     * 最高额担保合同号
     */
    @TableField("CONTRACT_NO")
    private String contractNo;

    /**
     * 借据状态 0-正常 1-结清
     */
    @TableField("LOAN_STATUS")
    private String loanStatus;

    /**
     * 创建时间
     */
    @TableField("CREATED_TIME")
    private Date createdTime;

}
