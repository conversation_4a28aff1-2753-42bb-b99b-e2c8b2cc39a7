package io.ibs.modules.irp.pbccrc2.common.informix.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体类
 * Created by AileYoung on 2024-05-07 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_CDM_LEDGER")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TCdmLedger implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("INST_NO")
    private String instNo;

    @TableField("ACC")
    private String acc;

    @TableField("CSTM_NAME")
    private String cstmName;

    @TableField("CSTM_NO")
    private String cstmNo;

    @TableField("CURR_TYPE")
    private String currType;

    @TableField("PRD_NO")
    private String prdNo;

    @TableField("PRD_AGT")
    private String prdAgt;

    @TableField("BAL_DIR")
    private String balDir;

    @TableField("BAL")
    private BigDecimal bal;

    @TableField("AVAL_BAL")
    private BigDecimal avalBal;

    @TableField("NORM_FRATIO")
    private BigDecimal normFratio;

    @TableField("OVFD_FRATIO")
    private BigDecimal ovfdFratio;

    @TableField("OVFD_DATE")
    private Date ovfdDate;

    @TableField("OVFD_LMT")
    private BigDecimal ovfdLmt;

    @TableField("OVFD_DEADLINE")
    private BigDecimal ovfdDeadline;

    @TableField("DR_ACCU")
    private BigDecimal drAccu;

    @TableField("CR_ACCU")
    private BigDecimal crAccu;

    @TableField("MON_ACCU")
    private BigDecimal monAccu;

    @TableField("YEAR_ACCU")
    private BigDecimal yearAccu;

    @TableField("LAST_TRAN_DATE")
    private Date lastTranDate;

    @TableField("LAST_TRAN_TIME")
    private Date lastTranTime;

    @TableField("PRT_LINE_NUM")
    private BigDecimal prtLineNum;

    @TableField("NOREG_PK_REC_NUM")
    private BigDecimal noregPkRecNum;

    @TableField("PK_NO")
    private BigDecimal pkNo;

    @TableField("MAX_ACC_PG_NO")
    private BigDecimal maxAccPgNo;

    @TableField("MAX_NUM")
    private BigDecimal maxNum;

    @TableField("PWD")
    private String pwd;

    @TableField("FLAG")
    private String flag;

    @TableField("BGN_INT_DATE")
    private Date bgnIntDate;

    @TableField("OPEN_DATE")
    private Date openDate;

    @TableField("CLS_DATE")
    private Date clsDate;

    @TableField("OPEN_TLR")
    private String openTlr;

    @TableField("CLS_TLR")
    private String clsTlr;

    @TableField("CLS_INT")
    private BigDecimal clsInt;

    @TableField("DAC")
    private String dac;

}
