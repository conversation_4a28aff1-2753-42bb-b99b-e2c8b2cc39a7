package io.ibs.modules.irp.pbccrc2.common.informix.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 反洗钱历史表实体类
 * Created by <PERSON><PERSON><PERSON><PERSON>ng on 2024-06-20 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_FXJ_HIST")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TFxjHist implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("INST_NO")
    private String instNo;

    @TableField("INST_NAME")
    private String instName;

    @TableField("OPEN_INST")
    private String openInst;

    @TableField("TLR_NO")
    private String tlrNo;

    @TableField("TRAN_NO")
    private String tranNo;

    @TableField("CLT_SEQNO")
    private String cltSeqno;

    @TableField("HOST_SEQNO")
    private long hostSeqno;

    @TableField("SUB_SEQNO")
    private long subSeqno;

    @TableField("TRAN_DATE")
    private Date tranDate;

    @TableField("TRAN_STAMP")
    private Date tranStamp;

    @TableField("AMT")
    private BigDecimal amt;

    @TableField("CSH_TSF_FLAG")
    private String cshTsfFlag;

    @TableField("DR_CR_FLAG")
    private String drCrFlag;

    @TableField("SUMMARY")
    private String summary;

    @TableField("ACC_TYPE")
    private String accType;

    @TableField("ACC")
    private String acc;

    @TableField("OPEN_TIME")
    private String openTime;

    @TableField("CLS_TIME")
    private String clsTime;

    @TableField("CSTM_NO")
    private String cstmNo;

    @TableField("CSTM_TYPE")
    private String cstmType;

    @TableField("CSTM_PHONE")
    private String cstmPhone;

    @TableField("CSTM_ADDR")
    private String cstmAddr;

    @TableField("CSTM_OTHER_INFO")
    private String cstmOtherInfo;

    @TableField("CSTM_NAME")
    private String cstmName;

    @TableField("PAPER_TYPE")
    private String paperType;

    @TableField("PAPER_NO")
    private String paperNo;

    @TableField("PEER_INSTNO")
    private String peerInstno;

    @TableField("PEER_INST_NAME")
    private String peerInstName;

    @TableField("PEER_ACC_TYPE")
    private String peerAccType;

    @TableField("PEER_ACC")
    private String peerAcc;

    @TableField("PEER_CSTM_NO")
    private String peerCstmNo;

    @TableField("PEER_NAME")
    private String peerName;

    @TableField("PEER_PAPER_TYPE")
    private String peerPaperType;

    @TableField("PEER_PAPER_NO")
    private String peerPaperNo;

    @TableField("FLAG")
    private String flag;

}
