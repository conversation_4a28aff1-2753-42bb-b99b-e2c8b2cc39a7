package io.ibs.modules.irp.pbccrc2.common.informix.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体类
 * Created by AileYoung on 2023-06-15 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_LOAN_IOU")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TLoanIou implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("LOAN_NO")
    private String loanNo;

    @TableField("LOAN_INST")
    private String loanInst;

    @TableField("MCONTRACT_NO")
    private long mcontractNo;

    @TableField("SCONTRACT_NO")
    private long scontractNo;

    @TableField("MORTGAGE_PRO")
    private String mortgagePro;

    @TableField("CSTM_NO")
    private String cstmNo;

    @TableField("CSTM_NAME")
    private String cstmName;

    @TableField("CURR_TYPE")
    private String currType;

    @TableField("PRD_NO")
    private String prdNo;

    @TableField("DLV_GUIDELINE")
    private BigDecimal dlvGuideline;

    @TableField("CSH_TSF_FLAG")
    private String cshTsfFlag;

    @TableField("PAYINT_ACC")
    private String payintAcc;

    @TableField("RATE_LEV")
    private String rateLev;

    @TableField("FRATIO")
    private BigDecimal fratio;

    @TableField("RATE_NO")
    private long rateNo;

    @TableField("OVERDUE_FRATIO")
    private BigDecimal overdueFratio;

    @TableField("OUT_RB")
    private BigDecimal outRb;

    @TableField("INN_RB")
    private BigDecimal innRb;

    @TableField("CPND_RB")
    private BigDecimal cpndRb;

    @TableField("UNDO_RB")
    private BigDecimal undoRb;

    @TableField("LOAN_PURP")
    private String loanPurp;

    @TableField("SERNO")
    private long serno;

    @TableField("LOAN_TYPE")
    private String loanType;

    @TableField("CSTM_ATTR")
    private String cstmAttr;

    @TableField("CSTM_INDUS")
    private String cstmIndus;

    @TableField("FLAG")
    private String flag;

    @TableField("INT_CAL_FLAG")
    private String intCalFlag;

    @TableField("MNG_FLAG")
    private String mngFlag;

    @TableField("TERM_NUM")
    private long termNum;

    @TableField("BGNINT_DATE")
    private Date bgnintDate;

    @TableField("DUE_DATE")
    private Date dueDate;

    @TableField("CLR_DATE")
    private Date clrDate;

    @TableField("CLR_FLAG")
    private String clrFlag;

    @TableField("EXT_NUM")
    private long extNum;

    @TableField("LOANER")
    private String loaner;

    @TableField("OP_TLR")
    private String opTlr;

    @TableField("CL_TLR")
    private String clTlr;

    @TableField("LAST_INT_DATE")
    private Date lastIntDate;

    @TableField("LAST_TRAN_DATE")
    private Date lastTranDate;

    @TableField("ACCU")
    private BigDecimal accu;

    @TableField("ORI_DUE_DATE")
    private Date oriDueDate;

    @TableField("DAC")
    private String dac;

}
