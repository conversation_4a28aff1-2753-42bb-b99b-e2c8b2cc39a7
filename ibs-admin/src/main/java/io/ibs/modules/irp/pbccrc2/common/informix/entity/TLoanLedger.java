package io.ibs.modules.irp.pbccrc2.common.informix.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体类
 * Created by AileYoung on 2023-06-15 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_LOAN_LEDGER")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TLoanLedger implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("INST_NO")
    private String instNo;

    @TableField("LOAN_ACC")
    private String loanAcc;

    @TableField("LOAN_NO")
    private String loanNo;

    @TableField("CSTM_NO")
    private String cstmNo;

    @TableField("CSTM_NAME")
    private String cstmName;

    @TableField("PRD_NO")
    private String prdNo;

    @TableField("DITM_NO")
    private String ditmNo;

    @TableField("PRD_AGT")
    private String prdAgt;

    @TableField("CURR_TYPE")
    private String currType;

    @TableField("LEDGER_GUIDELINE")
    private BigDecimal ledgerGuideline;

    @TableField("DLV_TOTL")
    private BigDecimal dlvTotl;

    @TableField("RECV_TOTL")
    private BigDecimal recvTotl;

    @TableField("BAL_DIR")
    private String balDir;

    @TableField("BAL")
    private BigDecimal bal;

    @TableField("ACCU")
    private BigDecimal accu;

    @TableField("MON_ACCU")
    private BigDecimal monAccu;

    @TableField("YEAR_ACCU")
    private BigDecimal yearAccu;

    @TableField("MAX_ACCPG_NO")
    private long maxAccpgNo;

    @TableField("MAX_NUM")
    private long maxNum;

    @TableField("FLAG")
    private String flag;

    @TableField("INT_CAL_FLAG")
    private String intCalFlag;

    @TableField("HOLDINT_WAY")
    private String holdintWay;

    @TableField("LAST_TRAN_DATE")
    private Date lastTranDate;

    @TableField("LAST_TRAN_TIME")
    private Date lastTranTime;

    @TableField("BGNINT_DATE")
    private Date bgnintDate;

    @TableField("DUE_DATE")
    private Date dueDate;

    @TableField("BGN_TLR")
    private String bgnTlr;

    @TableField("END_TLR")
    private String endTlr;

    @TableField("BGN_DATE")
    private Date bgnDate;

    @TableField("END_DATE")
    private Date endDate;

    @TableField("DAC")
    private String dac;

}
