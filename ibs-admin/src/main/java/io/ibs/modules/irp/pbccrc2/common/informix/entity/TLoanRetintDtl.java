package io.ibs.modules.irp.pbccrc2.common.informix.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 实体类
 * Created by AileYoung on 2023-06-15 using generator
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("T_LOAN_RETINT_DTL")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TLoanRetintDtl implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("INST_NO")
    private String instNo;

    @TableField("CLT_SEQNO")
    private String cltSeqno;

    @TableField("LOAN_NO")
    private String loanNo;

    @TableField("LOAN_ACC")
    private String loanAcc;

    @TableField("ACC_DATE")
    private Date accDate;

    @TableField("HOST_SEQNO")
    private long hostSeqno;

    @TableField("RPT_INN_NO")
    private String rptInnNo;

    @TableField("DITM_NO")
    private String ditmNo;

    @TableField("PEER_DITM_NO")
    private String peerDitmNo;

    @TableField("PAYINT_ACC")
    private String payintAcc;

    @TableField("HOLDINT_ACC")
    private String holdintAcc;

    @TableField("RECVINT_ACC")
    private String recvintAcc;

    @TableField("UNRECVINT_ACC")
    private String unrecvintAcc;

    @TableField("RET_INT")
    private BigDecimal retInt;

    @TableField("RET_HOST_SEQNO")
    private long retHostSeqno;

    @TableField("RET_DATE")
    private Date retDate;

    @TableField("TRAN_CODE")
    private String tranCode;

    @TableField("CERT_TYPE")
    private long certType;

    @TableField("CERT_NO")
    private long certNo;

    @TableField("PRT_NUM")
    private long prtNum;

    @TableField("FLAG")
    private String flag;

}
