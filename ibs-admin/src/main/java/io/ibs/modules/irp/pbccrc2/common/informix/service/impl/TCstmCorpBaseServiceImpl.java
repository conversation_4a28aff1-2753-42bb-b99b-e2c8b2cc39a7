package io.ibs.modules.irp.pbccrc2.common.informix.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.irp.pbccrc2.common.informix.dao.TCstmCorpBaseMapper;
import io.ibs.modules.irp.pbccrc2.common.informix.entity.TCstmCorpBase;
import io.ibs.modules.irp.pbccrc2.common.informix.service.TCstmCorpBaseService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *  资源服务实现类
 * Created by AileYoung on 2023-08-01 using generator
 */
@Service
public class TCstmCorpBaseServiceImpl extends ServiceImpl<TCstmCorpBaseMapper, TCstmCorpBase> implements TCstmCorpBaseService {

    /**
     * 根据 Wrapper，查询一条记录 <br/>
     * <p>结果集，如果是多个会抛出异常，随机取一条加上限制条件 wrapper.last("LIMIT 1")</p>
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    @Override
    @DataSource("informix")
    public TCstmCorpBase getOne(Wrapper<TCstmCorpBase> queryWrapper) {
        return getOne(queryWrapper, true);
    }

    /**
     * 查询列表
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    @Override
    @DataSource("informix")
    public List<TCstmCorpBase> list(Wrapper<TCstmCorpBase> queryWrapper) {
        return getBaseMapper().selectList(queryWrapper);
    }
}
