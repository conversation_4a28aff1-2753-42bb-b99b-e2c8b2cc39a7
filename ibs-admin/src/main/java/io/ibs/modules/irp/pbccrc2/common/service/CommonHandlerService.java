package io.ibs.modules.irp.pbccrc2.common.service;


import io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifBaseEntity;
import io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifBasicProfileEntity;
import io.ibs.modules.irp.pbccrc2.common.entity.*;

import java.util.Date;
import java.util.List;

/**
 * 公共服务
 * 个人和企业都可以调用
 */
public interface CommonHandlerService {

    /**
     * 获取未逾期借据的 余额、应还本金、实还本金、应还利息、实还利息 <p>
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @return 余额、应还本金、实还本金、应还利息、实还利息
     */
    AmQryEntity getLoanNoAmQry(String bussNum, String bussDate);

    /**
     * 获取逾期借据的 余额、应还本金、实还本金、应还利息、实还利息
     * 注：包括整个借据到期，整个借据中间几期逾期 <p>
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @param dueDate  整个合同到期日期
     * @return 余额、应还本金、实还本金、应还利息、实还利息
     */
    AmQryEntity getLoanNoAmQryOved(String bussNum, String bussDate, Date dueDate);

    /**
     * 获取企业借据的 余额、应还本金、实还本金、应还利息、实还利息
     * 查询的是当天实际发生的 <p>
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @return 余额、应还本金、实还本金、应还利息、实还利息
     */
    OverInfoEnt getLoanEntAmQry(String bussNum, String bussDate);

    /**
     * 获取指定借据的 余额变化日期、最近一次实际还款日期
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @return 余额变化日期、最近一次实际还款日期
     */
    DateQryEntity getLoanDateQry(String bussNum, String bussDate);

    /**
     * 获取指定逾期借据的五级分类信息
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @param openDate 开户日期
     * @return 五级分类信息
     */
    FiveCateEntity getFiveCate(String bussNum, String bussDate, Date openDate);

    /**
     * 判断借据是否逾期
     * false 没有逾期
     * true 逾期
     *
     * @param loanNo   借据号
     * @param bussDate 业务日期
     * @return
     */
    boolean isOverdue(String loanNo, String bussDate);

    /**
     * 根据借据号和当前业务日期获取本期还款计划
     *
     * @param bussNum  借据号
     * @param bussDate 业务发生日期
     * @return 本期还款计划
     */
    LoanRetTermInfo getLoanRetTermInfo(String bussNum, String bussDate);

    /**
     * 最近一次约定还款日 下一次约定还款日期
     *
     * @param bussNum  借据号
     * @param bussDate 业务发生日期
     * @return
     */
    AgrrRpyDate getAgrrRpyDate(String bussNum, String bussDate);

    TEtprEcifBasicProfileEntity getEtprEcifBaseProfileByCstmNo(String cstmNo);

    /**
     * 查询借据截止到bussDate逾期的本金和利息，
     * 可以判断是否逾期，查询为空表示截止当前日期没有逾期
     * 适用于个人和企业
     * 注意：由于历史数据的原因，所以getLoanOverd可能在现在查属于没有逾期（在抽数之前结清了） ，所以加判断是否大于最后到期日期
     *
     * @param bussNum  借据号
     * @param bussDate 业务发生日期
     * @return
     */
    LoanOverd getLoanOverd(String bussNum, String bussDate);

    /**
     * 通过客户号获取企业的信息 基本段信息
     *
     * @param cstmNo
     * @return
     */
    TEtprEcifBaseEntity getEtprEcifBaseByCstmNo(String cstmNo);

    /**
     * 根据借据号和业务日期计算还款表现
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @return 借据还款表现信息
     */
    LoanRetInfoEntity getLoanRetInfoEntity(String bussNum, String bussDate);

    /**
     * 获取截止到bussDate逾期的借据号
     * 利息明细要取时间还款时间，不用acc_date，就是截止到目前为止还了多少钱，不用管哪一期
     *
     * @param bussNumList 借据号
     * @param bussDate    业务日期
     * @return 逾期借据号
     */
    List<String> getOverLoanNoList(List<String> bussNumList, String bussDate);

    /**
     * 借据在指定时间范围结清的
     *
     * @param cstmType 客户类型 0-个人 1-企业 ""-全部
     * @param bgnDate  开始日期 （不传默认：20230101）
     * @param endDate  结束日期（不传默认：当前系统时间）
     * @return 结清的借据号
     */
    List<String> getCloseLoanNoList(String cstmType, String bgnDate, String endDate);

    /**
     * 查询截止bgnDate未结清的
     *
     * @param cstmType 客户类型 0-个人 1-企业
     * @param tranDate 开始日期
     * @return 未结清的借据号
     */
    List<String> getUnCloseLoanNoList(String cstmType, String tranDate);

    List<String> getEntUnclearedLoanNoList(String bgnDate);

    /**
     * 查出需要循环的date
     *
     * @param loanNoList 未结清的借据号
     * @param bgnDate    开始日期
     * @return
     */
    List<String> getEntUnCloseDateList(List<String> loanNoList, String bgnDate);

    /**
     * 企业获取还款表现信息
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @return 还款表现信息
     */
    EntLoanMonthEntity getEntLoanMonthEntity(String bussNum, String bussDate);

    /**
     * 获取最小的约定还款日
     *
     * @param bussNum
     * @param bussDate
     * @return
     */
    LoanEntOver getLoanEntOver(String bussNum, String bussDate);

    /**
     * 根据借据号查询最后一次交易日期
     *
     * @param loanNo 借据号
     * @return 后一次交易日期
     */
    Date getLastTranDateByLoanNo(String loanNo);

    /**
     * 获取BadList
     *
     * @return 借据号
     */
    List<String> getBadList();

    /**
     * 计算借据的原到期日
     * 如果发生展期，借据表中的到期日会被更新为新的到期日，对于存量数据而言，上报到期日时要上报原来的到日期，因此如果借据发生了展期，可以通过本方法计算出本次展期的原到期日
     *
     * @param bussNum  借据号
     * @param bussDate 业务日期
     * @return 原到期日
     */
    Date getOrgDueDate(String bussNum, String bussDate);
}