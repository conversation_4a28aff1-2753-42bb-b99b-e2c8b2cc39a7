package io.ibs.modules.irp.pbccrc2.common.xdgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.irp.pbccrc2.common.xdgl.service.DhbgService;
import io.ibs.modules.irp.pbccrc2.common.xdgl.dao.DhbgDao;
import io.ibs.modules.irp.pbccrc2.common.xdgl.entity.Dhbg;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
public class DhbgServiceImpl extends ServiceImpl<DhbgDao, Dhbg> implements DhbgService {

    /**
     * 根据 Wrapper，查询一条记录 <br/>
     * <p>结果集，如果是多个会抛出异常，随机取一条加上限制条件 wrapper.last("LIMIT 1")</p>
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    @Override
    @DataSource("xdgl")
    public Dhbg getOne(Wrapper<Dhbg> queryWrapper) {
        return getOne(queryWrapper, true);
    }

    /**
     * 查询列表
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    @Override
    @DataSource("xdgl")
    public List<Dhbg> list(Wrapper<Dhbg> queryWrapper) {
        return getBaseMapper().selectList(queryWrapper);
    }
}
