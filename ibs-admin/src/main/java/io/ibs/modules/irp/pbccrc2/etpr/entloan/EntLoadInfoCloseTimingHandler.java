package io.ibs.modules.irp.pbccrc2.etpr.entloan;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.common.utils.DateUtils;
import io.ibs.modules.irp.enums.TimingEnum;
import io.ibs.modules.irp.pbccrc.entloan.entity.EilhActlbltyinfsgmtEntity;
import io.ibs.modules.irp.pbccrc.entloan.entity.EntLoanGetDataEntity;
import io.ibs.modules.irp.pbccrc.entloan.service.EntLoanGetDataService;
import io.ibs.modules.irp.pbccrc2.common.entity.TLoanHighestGuaranteeLoan;
import io.ibs.modules.irp.pbccrc2.common.service.CommonHandlerService;
import io.ibs.modules.irp.pbccrc2.common.service.TLoanHighestGuaranteeLoanService;
import io.ibs.modules.irp.pbccrc2.mortgage.MortInfo;
import io.ibs.modules.irp.pbccrc2.mortgage.MortInfoStopTimingHandler;
import io.ibs.modules.irp.util.CacheUtil;
import io.ibs.modules.irp.util.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 账户关闭时点
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EntLoadInfoCloseTimingHandler implements EntLoanInfoTimingHandler {
    private final static String CACHE_NAME = "EntLoadInfoCloseTimingCache";
    private final static TimingEnum RPT_DATE_CODE = TimingEnum.CLOSE;

    private final EntLoanGetDataService entLoanSvc;
    private final EntLoanInfoPartService entLoanInfoPartService;
    private final MortInfoStopTimingHandler mortInfoStopTimingHandler;
    private final CommonHandlerService commonHandlerService;
    private final TLoanHighestGuaranteeLoanService loanHighestGuaranteeLoanService;

    @Override
    public List<EntLoanInfo> handle(QueryParams params) {
        List<EntLoanInfo> entLoanInfoList = new ArrayList<>();
        String tranDate = params.getBussDate();
        params.setNowDate(new Date());
        params.setRptDateCode(RPT_DATE_CODE);

        // 获取指定日期结清的贷款
        List<EntLoanGetDataEntity> closeList;
        // 在缓存中查找已关闭借据列表，缓存中无已关闭借据列表，查询并放入缓存
        closeList = CacheUtil.getList(CACHE_NAME, "closeList", entLoanSvc::getCorpLoanOfClose);

        if (!CollectionUtils.isEmpty(closeList)) {
            for (EntLoanGetDataEntity entLoanInfoEntity : closeList) {
                String loanNo = entLoanInfoEntity.getLoanNo();
                // 从缓存中获取借据的最后一次交易日期，缓存中为空，则去核心查找该借据最大的还本日期和还息日期，再取其中最大的一个作为最后一次交易日期
                Date lastTranTime = CacheUtil.get(CACHE_NAME, "lastTranTime-" + loanNo, Date.class, () ->
                        commonHandlerService.getLastTranDateByLoanNo(loanNo));

                // 如果最后一次交易日期等于当前业务日期，则属于当天账户关闭时点
                if (tranDate.equals(DateUtil.format(lastTranTime, Constants.SUBMIT_DATE_FORMAT))) {
                    log.info("[20-账户关闭],日期:{}，借据号:{}", tranDate, entLoanInfoEntity.getLoanNo());
                    EntLoanInfo entLoanInfo = new EntLoanInfo();
                    entLoanInfo.setEilb(entLoanInfoPartService.handleEilb(entLoanInfoEntity, params)); // 基础段
                    entLoanInfo.setEilh(handleEilhFields(entLoanInfoPartService.handleEilh(entLoanInfoEntity, params), params)); // 还款表现信息段

                    // 账户关闭时，如果存在抵质押物信息，则抵质押物要上报“合同失效/到期”
                    if ("0".equals(entLoanInfoEntity.getGuarMode()) || "2".equals(entLoanInfoEntity.getGuarMode())) {
                        //尝试更新最高额担保合同借据表的状态为结清，后续如果它是最高额担保合同下的借据，会用来判断合同是否失效
                        TLoanHighestGuaranteeLoan loan = TLoanHighestGuaranteeLoan.builder()
                                .loanStatus("1")// 1-结清
                                .build();
                        loanHighestGuaranteeLoanService.update(loan, new UpdateWrapper<TLoanHighestGuaranteeLoan>().lambda()
                                .eq(TLoanHighestGuaranteeLoan::getLoanNo, loanNo));

                        Map<String, Object> paramMap = new HashMap<>();
                        // 如果贷款是抵质押贷款，需要同步抽取抵质押物信息
                        paramMap.put("bussNum", entLoanInfoEntity.getLoanNo());
                        paramMap.put("bussDate", params.getBussDate());
                        paramMap.put("mortRptDateCode", "20");// 20-合同失效/到期
                        List<MortInfo> mortInfos = mortInfoStopTimingHandler.handle(paramMap);
                        entLoanInfo.setMortInfos(mortInfos);

                        // 由于需要判断最高额担保的情况，必须在采集抵质押设置E段
                        entLoanInfo.addEile(entLoanInfoPartService.handleEileList(entLoanInfoEntity, params));
                    }
                    entLoanInfoList.add(entLoanInfo);
                }
            }
        }
        return entLoanInfoList;
    }

    /**
     * 账户关闭特殊字段处理
     */
    private EilhActlbltyinfsgmtEntity handleEilhFields(EilhActlbltyinfsgmtEntity eilh, QueryParams params) {
        Date date = DateUtils.parse(params.getBussDate(), Constants.SUBMIT_DATE_FORMAT);
        eilh.setAcctBal(BigDecimal.ZERO);// 余额
        eilh.setFiveCateAdjDate(date);// 五级分类认定日期
        eilh.setFiveCate("1");// 五级分类
        eilh.setTotOverd(BigDecimal.ZERO);// 当前逾期总额
        eilh.setOverdPrinc(BigDecimal.ZERO);// 当前逾期本金
        eilh.setOverdDy(0L);// 当前逾期天数
        eilh.setCloseDate(date);// 账户关闭日期
        eilh.setNxtAgrrRpyDate(null); // 下一次约定还款日期
        return eilh;
    }
}