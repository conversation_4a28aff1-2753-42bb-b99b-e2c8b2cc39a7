package io.ibs.modules.irp.pbccrc2.etpr.entloan;

import cn.hutool.core.date.DateUtil;
import io.ibs.modules.irp.enums.TimingEnum;
import io.ibs.modules.irp.pbccrc.entloan.entity.*;
import io.ibs.modules.irp.pbccrc.entloan.service.EntLoanGetDataService;
import io.ibs.modules.irp.pbccrc2.common.service.CommonHandlerService;
import io.ibs.modules.irp.pbccrc2.common.entity.EntLoanMonthEntity;
import io.ibs.modules.irp.pbccrc2.mortgage.MortInfo;
import io.ibs.modules.irp.pbccrc2.mortgage.MortInfoStartTimingHandler;
import io.ibs.modules.irp.util.Constants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 账户开立时点
 */
@Slf4j
@Service
@AllArgsConstructor
public class EntLoadInfoOpenTimingHandler implements EntLoanInfoTimingHandler {
    private final static TimingEnum RPT_DATE_CODE = TimingEnum.OPEN;

    private EntLoanGetDataService entLoanSvc;
    private EntLoanInfoPartService entLoanInfoPartService;
    private MortInfoStartTimingHandler mortInfoStartTimingHandler;
    private CommonHandlerService commonHandlerService;

    List<EilbAcctbssgmtEntity> eilbList;// 基础段
    List<EilcAcctbsinfsgmtEntity> eilcList;// 基本信息段
    List<EildRltrepymtinfsgmEntity> eildList;// 相关还款责任人
    List<EilhActlbltyinfsgmtEntity> eilhList;// 还款表现信息段
    List<EiliAcctspectrstdspnEntity> eiliList;// 特定交易说明段
    List<EileMotgacltalctrctinfEntity> eileList;// 抵质押信息

    @Override
    public List<EntLoanInfo> handle(QueryParams params) {

        List<EntLoanInfo> entLoanInfoList = new ArrayList<>();
        String tranDate = params.getBussDate();
        params.setRptDateCode(RPT_DATE_CODE);
        params.setNowDate(new Date());
        // 查询新开立账户的数据
        List<EntLoanGetDataEntity> entLoanInfoTmpList = entLoanSvc.getCorpLoanOfOpen(tranDate);
        log.info("[10-账户开立]获取指定日期[" + tranDate + "]开立的贷款,数量[{}]", CollectionUtils.isEmpty(entLoanInfoTmpList) ? 0 : entLoanInfoTmpList.size());

        if (entLoanInfoTmpList != null && entLoanInfoTmpList.size() > 0) {
            if (params.isFirstFlag()) {
                // 如果是true，则只用包括需要抽取的5年未结清的，其他得不要
                List<String> loanNos = params.getLoanNos();
                entLoanInfoTmpList = entLoanInfoTmpList.stream().filter(e -> loanNos.contains(e.getLoanNo())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(entLoanInfoTmpList)) {
                return entLoanInfoList;
            }
            List<String> guarLoan = entLoanInfoTmpList.stream().filter(x -> "1".equals(x.getGuarMode())).map(EntLoanGetDataEntity::getLoanNo).collect(Collectors.toList());
            List<EntLoanGuarEntity> guarList = CollectionUtils.isEmpty(guarLoan) ? new ArrayList<>() : entLoanSvc.getCorpLoanOfGuar(guarLoan);

            entLoanInfoTmpList.forEach(entLoanInfoEntity -> {
                log.info("当前开户借据号[{}]", entLoanInfoEntity.getLoanNo());
                EntLoanInfo entLoanInfo = EntLoanInfo.builder().
                        eilb(entLoanInfoPartService.handleEilb(entLoanInfoEntity, params)).// 基础段
                                eilc(entLoanInfoPartService.handleEilc(entLoanInfoEntity, params)).// 基本信息段
                                eilh(handleEilhFields(entLoanInfoPartService.handleEilh(entLoanInfoEntity, params), entLoanInfoEntity)).// 还款表现信息段
                                eileList(new ArrayList<>()).
                        eildList(new ArrayList<>()).
                        build();

                // 如果贷款是抵质押贷款，需要同步抽取抵质押物信息
                if ("0".equals(entLoanInfoEntity.getGuarMode()) || "2".equals(entLoanInfoEntity.getGuarMode())) {
                    log.info("借据是抵质押贷款，需要同步抽取抵质押物信息");
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("bussNum", entLoanInfoEntity.getLoanNo());
                    paramMap.put("bussDate", params.getBussDate());
                    paramMap.put("mortRptDateCode", "10");//10-合同生效
                    List<MortInfo> mortInfos = mortInfoStartTimingHandler.handle(paramMap);
                    entLoanInfo.setMortInfos(mortInfos);

                    // 由于需要判断最高额担保的情况，必须在采集抵质押后设置E段
                    entLoanInfo.addEile(entLoanInfoPartService.handleEileList(entLoanInfoEntity, params));
                }

                // 相关还款责任人
                entLoanInfo.setEildList(entLoanInfoPartService.handleEildList(guarList.stream().filter(guar -> guar.getJjh().equals(entLoanInfoEntity.getLoanNo())).collect(Collectors.toList()), entLoanInfoEntity, params));
                entLoanInfoList.add(entLoanInfo);
            });
        }
        // 获取开立的保证贷款 查询保证贷款的相关还款责任人

        return entLoanInfoList;
    }

    /**
     * 处理还款变现信息段，各时点，不同字段的值
     */
    private EilhActlbltyinfsgmtEntity handleEilhFields(EilhActlbltyinfsgmtEntity eilh, EntLoanGetDataEntity entLoanInfo) {
        EntLoanMonthEntity loanMonthEntity = commonHandlerService.getEntLoanMonthEntity(eilh.getBussNum(), DateUtil.format(entLoanInfo.getOpenDate(), Constants.SUBMIT_DATE_FORMAT));
        eilh.setNxtAgrrRpyDate(loanMonthEntity.getNxtAgrrRpyDate());
        eilh.setLatAgrrRpyDate(entLoanInfo.getOpenDate()); // 近一次约定还款日
        // eilh.setCloseDate(entLoanInfo.getDueDate()); //账户关闭日期
        eilh.setBussDate(entLoanInfo.getOpenDate());// 业务发生日期
        return eilh;
    }


}