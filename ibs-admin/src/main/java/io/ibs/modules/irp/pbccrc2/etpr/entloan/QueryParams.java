package io.ibs.modules.irp.pbccrc2.etpr.entloan;

import io.ibs.modules.irp.enums.TimingEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class QueryParams {
    /**
     * 是否是首次，是否抽5年前的
     */
    private boolean firstFlag = false;
    /**
     * 业务时间
     */
    private String bussDate;

    /**
     * 当前时间
     */
    private Date nowDate;

    /**
     * 报告时点
     */
    private TimingEnum rptDateCode;

    /**
     * 基础段-账户状态 10-正常活动 21-关闭
     */
    private String acctStatus = "10";

    /**
     * 特定交易段-交易类型 11-展期;12-提前还款;21-核损核销
     */
    private String chanTranType;

    /**
     * 需要处理的借据号清单
     */
    private List<String> loanNos = new ArrayList<>();


    /**
     * 五级分类需要排除的数据,只有五级分类时点需要
     */
    private List<EntLoanInfo> entLoanInfoList = new ArrayList<>();
}
