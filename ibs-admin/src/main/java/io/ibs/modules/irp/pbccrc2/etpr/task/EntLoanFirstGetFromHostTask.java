package io.ibs.modules.irp.pbccrc2.etpr.task;

import io.ibs.modules.irp.pbccrc2.common.service.CommonHandlerService;
import io.ibs.modules.irp.pbccrc2.etpr.entloan.EntLoanInfo;
import io.ibs.modules.irp.pbccrc2.etpr.entloan.EntLoanInfoTimingService;
import io.ibs.modules.irp.pbccrc2.etpr.entloan.QueryParams;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 征信二代
 * 从核心及信贷信息首次收取存量的企业贷款信息到本地数据库
 * 执行时间:只执行一次
 */
@Service
@Slf4j
public class EntLoanFirstGetFromHostTask extends BaseTask {

    private final EntLoanInfoTimingService entLoanInfoTimingService;
    private final CommonHandlerService commonHandlerService;

    @Autowired
    public EntLoanFirstGetFromHostTask(EntLoanInfoTimingService entLoanInfoTimingService, CommonHandlerService commonHandlerService) {
        super("征信二代 - 首次从核心及信贷信息抽取存量的企业贷款信息", EntLoanFirstGetFromHostTask.class);
        this.entLoanInfoTimingService = entLoanInfoTimingService;
        this.commonHandlerService = commonHandlerService;
    }

    /**
     * 首次报送时获取数据,在接入后的第一个月结日报送存量数据
     * 基础段,基本信息段,月度表现信息段 必须报送
     * 相关还款责任人段,抵质押物信息段,授信额度信息段,特殊交易说明段 存在数据才报送
     * 20180101-20180102,true  为true表示抽取5年前的
     * 20180101-20180102,false 或 20180101-20180102  只抽取20180101-20180102
     *
     * @param param 20180101-20180102,true  为true表示抽取5年前的
     * @throws Exception
     */
    @Override
    public void execute(String param) throws Exception {
        String prefix = "[企业借贷首次抽取]";
        if (!param.contains("-") && StringUtils.isBlank(param)) {
            log.error(prefix + "参数为空或不合法!" + param);
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start(prefix + "从核心系统以及其他系统首次抽取个人贷款信息");
        String[] strings = param.split(",");
        if (strings.length > 1 && Objects.equals(strings[1], "true")) {
            String bgnDate = strings[0].split("-")[0];
            fiveYearAgoHandle(bgnDate);
        }

        // 通过时间段获取每一天的时间,并加上5年前的
        List<String> dates = getDatesByInterval(strings[0]);

        if (dates.size() > 0) {
            QueryParams params = new QueryParams();
            params.setFirstFlag(false);
            for (String date : dates) {
                params.setBussDate(LocalDate.parse(date, DateTimeFormatter.ofPattern(Constants.SUBMIT_DATE_FORMAT)).format(DateTimeFormatter.ofPattern(Constants.SUBMIT_DATE_FORMAT)));
                // 收集各时点数据
                List<EntLoanInfo> entLoanInfos = entLoanInfoTimingService.collect(params);
                log.info(prefix + "抽取数据结束,共{}条", entLoanInfos.size());
                entLoanInfoTimingService.saveLocal(entLoanInfos);
            }
        }
        // 计时停止
        stopWatch.stop();
        // 输出显示计时器各种
        log.info("******--[{}]数据统计--******", stopWatch.getLastTaskName());
        log.info("----总耗时: " + stopWatch.getTotalTimeSeconds() / 60 + "(分钟)");
        log.info("----总耗时: " + stopWatch.getTotalTimeSeconds() + "(秒)");
    }

    /**
     * 抽取5年前的数据，可单独运行
     *
     * @param bgnDate
     */
    public void fiveYearAgoHandle(String bgnDate) {
        // 查询截止bgnDate未结清的
        List<String> loanNoList = commonHandlerService.getUnCloseLoanNoList("1", bgnDate);
        // 查出需要循环的date
        List<String> dates = commonHandlerService.getEntUnCloseDateList(loanNoList, bgnDate);
        QueryParams params = new QueryParams();
        params.setFirstFlag(true);
        params.setLoanNos(loanNoList);
        for (String date : dates) {
            params.setBussDate(LocalDate.parse(date, DateTimeFormatter.ofPattern(Constants.SUBMIT_DATE_FORMAT)).format(DateTimeFormatter.ofPattern(Constants.SUBMIT_DATE_FORMAT)));
            // 收集各时点数据
            List<EntLoanInfo> entLoanInfos = entLoanInfoTimingService.collect(params);
            log.info("[5年前]抽取数据结束,共{}条", entLoanInfos.size());
            entLoanInfoTimingService.saveLocal(entLoanInfos);
        }
    }
}
