package io.ibs.modules.irp.pbccrc2.etpr.task;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.ibs.modules.irp.pbccrc2.common.service.CommonHandlerService;
import io.ibs.modules.irp.pbccrc2.etpr.entloan.EntLoadInfoOtherDateTimingHandler;
import io.ibs.modules.irp.pbccrc2.etpr.entloan.EntLoanInfo;
import io.ibs.modules.irp.pbccrc2.etpr.entloan.EntLoanInfoTimingService;
import io.ibs.modules.irp.pbccrc2.etpr.entloan.QueryParams;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 征信二代 - 首次抽取其它报送日数据  逾期
 * 执行时间:只执行一次
 */
@Service
@Slf4j
public class EntLoanFirstGetOtherTask extends BaseTask {

    private final EntLoanInfoTimingService entLoanInfoTimingService;
    private final EntLoadInfoOtherDateTimingHandler otherDateTimingHandler;
    private final CommonHandlerService commonHandlerService;

    @Autowired
    public EntLoanFirstGetOtherTask(EntLoanInfoTimingService entLoanInfoTimingService, EntLoadInfoOtherDateTimingHandler otherDateTimingHandler, CommonHandlerService commonHandlerService) {
        super("征信二代 - 首次抽取其它报送日数据", EntLoanFirstGetOtherTask.class);
        this.entLoanInfoTimingService = entLoanInfoTimingService;
        this.otherDateTimingHandler = otherDateTimingHandler;
        this.commonHandlerService = commonHandlerService;
    }

    /**
     * 首次报送时获取数据
     * 注：参数填写时，除非当前已是月底最后最一天做完日终，截止日期可以写到当前月，否则一律写到上个月
     * 比如现在是20230728，则是抽数时间20180701-20230630，月必须对，天可以随意，默认会取最后一天
     * 默认5年前开始20121101，所以只需传5年内的起止时间 execute("20180701-20230630")
     *
     * @param param 20180701-20230630
     * @throws Exception
     */
    @Override
    public void execute(String param) throws Exception {
        String prefix = "[企业借贷其它报送日]";
        if (!param.contains("-") && StringUtils.isBlank(param)) {
            log.error(prefix + "参数为空或不合法!" + param);
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start(prefix + "首次抽取其它报送日数据");

        // 5年前
        CompletableFuture.runAsync(() -> fiveYearAgoHandle(param.split("-")[0]));
        // 通过时间段获取每一天的时间
        List<String> dates = getDatesByInterval(param);
        dates = dates.stream().map(d -> d.substring(0, 6)).distinct().collect(Collectors.toList());
        List<String> endOfMonths = new ArrayList<>();
        for (String date : dates) {
            DateTime endOfMonth = DateUtil.endOfMonth(DateUtil.parse(date, "yyyyMM"));
            if (DateUtil.compare(endOfMonth, new Date()) < 1) {
                endOfMonths.add(DateUtil.format(endOfMonth, Constants.SUBMIT_DATE_FORMAT));
            }
        }
        if (endOfMonths.size() > 0) {
            QueryParams params = new QueryParams();
            params.setFirstFlag(false);
            for (String date : endOfMonths) {
                params.setBussDate(date);
                List<EntLoanInfo> entLoanInfoList = otherDateTimingHandler.handle(params);
                log.info(prefix + "抽取数据结束,共{}条", entLoanInfoList.size());
                entLoanInfoTimingService.saveLocal(entLoanInfoList);
            }
        }
        // 计时停止
        stopWatch.stop();
        // 输出显示计时器各种
        log.info("******--[{}]数据统计--******", stopWatch.getLastTaskName());
        log.info("----总耗时: " + stopWatch.getTotalTimeSeconds() / 60 + "(分钟)");
        log.info("----总耗时: " + stopWatch.getTotalTimeSeconds() + "(秒)");
    }

    /**
     * 抽取5年前的数据，可单独运行
     *
     * @param bgnDate
     */
    public void fiveYearAgoHandle(String bgnDate) {
        // 查询截止bgnDate未结清的
        List<String> loanNoList = commonHandlerService.getUnCloseLoanNoList("1", bgnDate);
        // 查出需要循环的date 20121101-20180701
        DateTime endDateTime = DateUtil.offsetMonth(DateUtil.parse(bgnDate, Constants.SUBMIT_DATE_FORMAT), -1);
        List<String> dates = getDatesByInterval("20121101-" + DateUtil.format(endDateTime, Constants.SUBMIT_DATE_FORMAT));
        dates = dates.stream().map(d -> d.substring(0, 6)).distinct().collect(Collectors.toList());
        List<String> endOfMonths = new ArrayList<>();
        dates.forEach(d -> {
            DateTime endOfMonth = DateUtil.endOfMonth(DateUtil.parse(d, "yyyyMM"));
            endOfMonths.add(DateUtil.format(endOfMonth, Constants.SUBMIT_DATE_FORMAT));
        });
        QueryParams params = new QueryParams();
        params.setFirstFlag(true);
        params.setLoanNos(loanNoList);
        for (String date : endOfMonths) {
            params.setBussDate(date);
            List<EntLoanInfo> entLoanInfos = otherDateTimingHandler.handle(params);
            log.info("[5年前]抽取数据结束,共{}条", entLoanInfos.size());
            entLoanInfoTimingService.saveLocal(entLoanInfos);
        }
    }
}
