package io.ibs.modules.irp.pbccrc2.mortgage;

import io.ibs.modules.irp.pbccrc2.common.base.CollectionTimingAbstractHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 抵（质）押物变更时点采集器
 * Created by AileYoung on 2023/5/30.
 */
@Service
@RequiredArgsConstructor
public class MortInfoChangeTimingHandler extends CollectionTimingAbstractHandler<MortInfo> {

    /**
     * 抽象方法，采集时点处理器
     *
     * @param params 处理参数
     */
    @Override
    public List<MortInfo> handle(Map<String, Object> params) {

        return new ArrayList<>();
    }
}
