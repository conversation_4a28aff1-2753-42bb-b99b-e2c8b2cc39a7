package io.ibs.modules.irp.pbccrc2.mortgage;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifBaseEntity;
import io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageBaseEntity;
import io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageImpEntity;
import io.ibs.modules.irp.pbccrc.mortgage.entity.MortgagePleEntity;
import io.ibs.modules.irp.pbccrc.mortgage.service.MortgageGetDataService;
import io.ibs.modules.irp.pbccrc2.common.base.CollectionTimingAbstractHandler;
import io.ibs.modules.irp.pbccrc2.common.entity.TLoanHighestGuaranteeContract;
import io.ibs.modules.irp.pbccrc2.common.entity.TLoanHighestGuaranteeLoan;
import io.ibs.modules.irp.pbccrc2.common.service.CommonHandlerService;
import io.ibs.modules.irp.pbccrc2.common.service.TLoanHighestGuaranteeContractService;
import io.ibs.modules.irp.pbccrc2.common.service.TLoanHighestGuaranteeLoanService;
import io.ibs.modules.irp.pbccrc2.common.xdgl.entity.Dkzht;
import io.ibs.modules.irp.pbccrc2.common.xdgl.service.DkzhtService;
import io.ibs.modules.irp.util.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 抵（质）押物合同生效时点采集器
 * Created by AileYoung on 2023/5/30.
 */
@Service
@RequiredArgsConstructor
public class MortInfoStartTimingHandler extends CollectionTimingAbstractHandler<MortInfo> {

    private final MortgageGetDataService mortgageGetDataSvc;
    private final CommonHandlerService commonHandlerService;
    private final DkzhtService dkzhtService;
    private final TLoanHighestGuaranteeLoanService highestGuaranteeLoanService;
    private final TLoanHighestGuaranteeContractService highestGuaranteeContractService;

    /**
     * 抽象方法，采集时点处理器
     *
     * @param params 处理参数
     */
    @Override
    public List<MortInfo> handle(Map<String, Object> params) {
        String bussNum = MapUtil.getStr(params, "bussNum");
        String bussDate = MapUtil.getStr(params, "bussDate");
        Date bussDateDate = DateUtil.parse(bussDate, Constants.SUBMIT_DATE_FORMAT);
        // 报告时点说明代码 10-合同生效 20-合同到期/失效 30-抵（质）押物变更
        String rptDateCode = MapUtil.getStr(params, "mortRptDateCode");

        String msg = "[抵质押合同生效时点]";
        log.info(msg + "开始抽取{}数据 {}", bussNum, bussDate);

        // 抵质押信息处理结果集
        List<MortInfo> result = new ArrayList<>();
        MortInfo mortInfo = new MortInfo();
        MortgageBaseEntity mort = mortgageGetDataSvc.getMortgageByBussNum(bussNum);
        if (mort == null) {
            log.warn("没有查询到该借据的抵质押信息");
            return result;
        }

        TEtprEcifBaseEntity etprEcifBase = new TEtprEcifBaseEntity();
        if (StrUtil.equalsCharAt(mort.getCstmNo(), 4, '1')) {
            etprEcifBase = commonHandlerService.getEtprEcifBaseByCstmNo(mort.getCstmNo());
            mort.setPaperNo(etprEcifBase.getEntCertNum());
            mort.setPaperType(etprEcifBase.getEntCertType());
        }

        // 抵质押合同生效时，如果该抵质押物合同是最高额担保合同，则登记最高额担保合同借据表和最高额担保合同信息表 created by AileYoung on 2023/9/20
        if ("1".equals(mort.getHighestFlag())) {
            log.info("当前为最高额担保合同！");
            // 查询主合同表，获取最高额信息
            Dkzht dkzht = dkzhtService.getOne(new QueryWrapper<Dkzht>().lambda()
                    .eq(Dkzht::getSqbh, mort.getSqbh()));
            if (dkzht != null) {
                String highestContractNo = dkzht.getZhsxhth();// 取主合同的综合授信合同号作为最高额担保合同号
                TLoanHighestGuaranteeLoan highestGuaranteeLoan = TLoanHighestGuaranteeLoan.builder()
                        .contractNo(highestContractNo)
                        .loanNo(mort.getLoanNo())
                        .loanStatus("0")
                        .createdTime(new Date())
                        .build();
                /* 查询最高额担保合同借据表，看该最高额担保合同之前是否登记过，
                 * 如果登记过，说明本借据与之前的借据同属一个最高额担保合同，则本借据不再上报抵质押信息
                 * 同一最高额担保合同下的抵质押信息只上报一次
                 */
                if (highestGuaranteeLoanService.count(new QueryWrapper<TLoanHighestGuaranteeLoan>().lambda()
                        .eq(TLoanHighestGuaranteeLoan::getContractNo, highestContractNo)) > 0) {
                    log.info("该最高额担保合同已登记过，不再上报抵质押信息");
                    // 登记最高额担保合同借据表 判断合同失效时要用
                    highestGuaranteeLoanService.save(highestGuaranteeLoan);
                    return result;
                }

                // 登记最高额担保合同借据表 判断合同失效时要用
                highestGuaranteeLoanService.save(highestGuaranteeLoan);
                // 登记最高额担保合同信息表 判断合同失效时要用
                highestGuaranteeContractService.save(TLoanHighestGuaranteeContract.builder()
                        .contractNo(highestContractNo)
                        .ccc2(mort.getLoanNo())
                        .cstmNo(mort.getCstmNo())
                        .cstmName(mort.getCstmName())
                        .contractAmt(dkzht.getDzyhtje())
                        .contractBeginDate(dkzht.getDzyhtsxrq())
                        .contractEndDate(dkzht.getDzyhtdqrq())
                        .status("0")//0-未上报
                        .build());

                // 将最高额担保合同的最高债权额作为抵质押金额
                mort.setAmt(dkzht.getDzyhtje());
            }
        }

        // 设置抵质押-基础段信息段（PIPB_MOTGACLTALCTRCTBSSGMT）
        mortInfo.setPipbInfo(mort, bussDateDate, rptDateCode);

        // 抵质押-基本信息段（PIPC_MOTGACLTALBSINFSGMT）
        mortInfo.setPipcInfo(mort, bussDateDate, rptDateCode);

        String loanType = mort.getLoanType();
        log.info("LoanType:[{}](0-抵押、2-质押)", loanType);

        // 如果是抵押贷款，获取抵押物信息
        if ("0".equals(loanType)) {
            log.info(">>>>>>>>获取抵押贷款的抵质押信息");
            List<MortgagePleEntity> pleList = mortgageGetDataSvc.getMortgageOfPle(Collections.singletonList(bussNum));
            for (MortgagePleEntity ple : pleList) {
                if ("2".equals(ple.getPleDgorType())) {
                    ple.setPleorCertNum(etprEcifBase.getEntCertNum());
                    ple.setPleorCertType(etprEcifBase.getEntCertType());
                }
            }
            // 抵质押-抵押物信息段（PIPE_MOTGAPROPTINFSGMT）
            mortInfo.setPipeInfos(pleList, bussDateDate);
        }

        // 如果是质押贷款，获取质物信息
        if ("2".equals(loanType)) {
            log.info(">>>>>>>>获取抵押贷款的质物信息");
            List<MortgageImpEntity> impList = mortgageGetDataSvc.getMortgageOfImp(Collections.singletonList(bussNum));
            // 抵质押-质物信息段（PIPF_CLTALINFSGMT）
            mortInfo.setPipfInfos(impList, bussDateDate);
        }

        result.add(mortInfo);
        return result;
    }
}
