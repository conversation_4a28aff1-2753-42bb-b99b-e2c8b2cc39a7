package io.ibs.modules.irp.pbccrc2.prsn.prsncredit;

import io.ibs.common.utils.DateUtils;
import io.ibs.modules.irp.pbccrc.prsncredit.entity.PrsnCreditGetDataEntity;
import io.ibs.modules.irp.pbccrc.prsncredit.service.PrsnCreditGetDataService;
import io.ibs.modules.irp.pbccrc2.common.base.CollectionTimingAbstractHandler;
import io.ibs.modules.irp.util.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 个人授信授信到期/失效时点采集器
 * Created by AileYoung on 2023/5/30.
 */
@Service
@RequiredArgsConstructor
public class PrsnCreditStopTimingHandler extends CollectionTimingAbstractHandler<PrsnCreditInfo> {

    private final PrsnCreditGetDataService pcgdSvc;

    /**
     * 抽象方法，采集时点处理器
     *
     * @param params 处理参数
     */
    @Override
    public List<PrsnCreditInfo> handle(Map<String, Object> params) {
        //取业务日期
        String bussDate = (String) params.get("bussDate");
        //业务日期格式转换
        Date bussDateD = DateUtils.parse(bussDate, Constants.SUBMIT_DATE_FORMAT);
        //借据对应的授信信息
        List<PrsnCreditGetDataEntity> personalCreditInfoList = pcgdSvc.getPrsnCreditInfoInFirst();
        List<PrsnCreditInfo> prsnCreditInfoList = new ArrayList<>();
        personalCreditInfoList.forEach(prsncredit -> {
            String bussDateCrdit;
            PrsnCreditInfo prsnCreditInfo = new PrsnCreditInfo();
            //额度到期日期比业务日期小视为失效
            if (DateUtils.compareTwoDate(prsncredit.getEndDate(), bussDateD) < 0) {
                //授信信息结束日期与业务日期比较
                if (DateUtils.compareTwoDate(prsncredit.getEndDate(), bussDateD) < 0) {
                    bussDateCrdit = DateUtils.format(bussDateD, Constants.SUBMIT_DATE_FORMAT);
                } else {
                    bussDateCrdit = DateUtils.format(prsncredit.getEndDate(), Constants.SUBMIT_DATE_FORMAT);
                }
                // 个人授信信息记录-基础段赋值
                prsnCreditInfo.setPicbInfo(prsncredit, bussDateCrdit, "20");
                // //个人授信信息记录-额度信息段
                prsnCreditInfo.setPicdInfo(prsncredit, bussDateCrdit);
                prsnCreditInfoList.add(prsnCreditInfo);
            }
        });
        return prsnCreditInfoList;
    }
}
