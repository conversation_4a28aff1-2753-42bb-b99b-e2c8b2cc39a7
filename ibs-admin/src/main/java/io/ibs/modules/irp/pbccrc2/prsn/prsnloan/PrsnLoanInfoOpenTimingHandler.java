package io.ibs.modules.irp.pbccrc2.prsn.prsnloan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import io.ibs.modules.irp.pbccrc.prsnloan.entity.PersonLoanInfoEntity;
import io.ibs.modules.irp.pbccrc.prsnloan.entity.PilhAcctmthlyblginfsgmtEntity;
import io.ibs.modules.irp.pbccrc.prsnloan.entity.PrsnLoanRelatedPayEntity;
import io.ibs.modules.irp.pbccrc.prsnloan.service.PrsnLoanGetDataService;
import io.ibs.modules.irp.pbccrc2.common.base.CollectionTimingAbstractHandler;
import io.ibs.modules.irp.pbccrc2.common.service.CommonHandlerService;
import io.ibs.modules.irp.pbccrc2.mortgage.MortInfo;
import io.ibs.modules.irp.pbccrc2.mortgage.MortInfoStartTimingHandler;
import io.ibs.modules.irp.util.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 个人借贷账户开立处理器
 * 条件：开户日期=bussDate
 * Created by AileYoung on 2023/5/17.
 */
@Service
@RequiredArgsConstructor
public class PrsnLoanInfoOpenTimingHandler extends CollectionTimingAbstractHandler<PrsnLoanInfo> {

    private final PrsnLoanGetDataService prsnLoanGetDataSvc;
    private final MortInfoStartTimingHandler mortInfoCollection;
    private final CommonHandlerService commonHandlerService;

    /**
     * 采集时点处理器
     *
     * @param params 处理参数
     */
    @Override
    public List<PrsnLoanInfo> handle(Map<String, Object> params) {
        String bussDate = (String) params.get("bussDate");
        String msg = "[账户开立]";
        log.info(msg + "开始抽取数据 {}", bussDate);
        // 获取当天开立的贷款账户基础信息
        List<PersonLoanInfoEntity> openInCurrentDayList = prsnLoanGetDataSvc.getPrsnLoanOfOpenInCurrentDay(bussDate);
        if (CollectionUtil.isEmpty(openInCurrentDayList)) {
            log.info(msg + "当前日期没有开立的账户：{}", bussDate);
            return new ArrayList<>();
        }
        log.info(msg + "核心查询到{}，开立的账户共有[{}]条", bussDate, openInCurrentDayList.size());
        List<PrsnLoanInfo> prsnLoanInfoList = new ArrayList<>();

        // 查询保证贷款的相关还款责任人
        List<String> guarBussNumList = openInCurrentDayList.stream().filter(p -> "1".equals(p.getGuarMode())).map(PersonLoanInfoEntity::getBussNum).collect(Collectors.toList());
        List<PrsnLoanRelatedPayEntity> relateList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(guarBussNumList)) {
            relateList = prsnLoanGetDataSvc.getPrsnLoanOfRelatedPay(guarBussNumList);
        }

        // 设置各个段
        for (PersonLoanInfoEntity personLoanInfoEntity : openInCurrentDayList) {
            String loanNo = personLoanInfoEntity.getBussNum();
            PrsnLoanInfo prsnLoanInfo = new PrsnLoanInfo();
            // 借贷-基础段(PILB_ACCTBSSGMT)
            prsnLoanInfo.setPilbInfo(personLoanInfoEntity, bussDate, "10");
            // 借贷-基本信息段(PILC_ACCTBSINFSGMT)
            prsnLoanInfo.setPilcInfo(personLoanInfoEntity, bussDate, commonHandlerService.getOrgDueDate(loanNo, bussDate));
            // 借贷-相关还款责任人段(PILD_RLTREPYMTINFSGMT)
            prsnLoanInfo.setPildInfos(relateList, personLoanInfoEntity, bussDate);
            // 借贷-抵质押物信息段(PILE_MOTGACLTALCTRCTINFSGMT)
            if ("0".equals(personLoanInfoEntity.getGuarMode()) || "2".equals(personLoanInfoEntity.getGuarMode())) {
                // 当报送时点是10-新开户时，如果贷款是抵质押贷款时，需要同步抽取抵质押物信息
                params.put("bussNum", loanNo);
                params.put("mortRptDateCode", "10");//10-合同生效
                List<MortInfo> mortInfos = mortInfoCollection.handle(params);
                prsnLoanInfo.setMortInfos(mortInfos);

                // 由于需要判断最高额担保的情况，必须在采集抵质押设置E段
                prsnLoanInfo.setPileInfo(personLoanInfoEntity, bussDate);
            }
            //  借贷-  授信额度信息段(PILF_ACCTCREDSGMT)
            //  借贷- 月度表现信息段(PILH_ACCTMTHLYBLGINFSGMT)
            prsnLoanInfo.setPilh(getPilh(personLoanInfoEntity, bussDate));
            prsnLoanInfoList.add(prsnLoanInfo);
        }
        log.info(msg + "数据抽取完毕 {}", bussDate);
        return prsnLoanInfoList;
    }


    /**
     * 月度表现信息
     *
     * @param personLoanInfoEntity 核心查出的数据
     * @param bussDate             日期
     * @return 月度表现信息
     */
    private PilhAcctmthlyblginfsgmtEntity getPilh(PersonLoanInfoEntity personLoanInfoEntity, String bussDate) {
        // 个人借贷交易信息-月度表现信息段(PILH_ACCTMTHLYBLGINFSGMT)
        PilhAcctmthlyblginfsgmtEntity pilhEntity = new PilhAcctmthlyblginfsgmtEntity();
        pilhEntity.setBussNum(personLoanInfoEntity.getBussNum());
        pilhEntity.setAcctStatus("1");// 账户状态 1-正常
        pilhEntity.setAcctBal(personLoanInfoEntity.getLoanAmt());// 本金余额
        pilhEntity.setActRpyAmt(BigDecimal.ZERO);// 本月实际还款金额 已到期或非按月还款
        pilhEntity.setCurRpyAmt(BigDecimal.ZERO);// 本月应还款金额
        pilhEntity.setRemRepPrd(personLoanInfoEntity.getRepayPrd());// 剩余还款期数,指按照还款计划表仍需归还的期数,不包含以前逾期期数
        pilhEntity.setFiveCate("1");// 五级分类 1-正常
        pilhEntity.setFiveCateAdjDate(DateUtil.parse(bussDate));
        pilhEntity.setRpyStatus("*");// 当前还款状态 *-当月不需要还款且之前没有拖欠
        pilhEntity.setOverdPrd(Long.valueOf("0"));// 当前逾期期数
        pilhEntity.setTotOverd(BigDecimal.ZERO);// 当前逾期总额
        pilhEntity.setOverdPrinc(BigDecimal.ZERO);// 当前逾期本金
        pilhEntity.setOved3160princ(BigDecimal.ZERO);// 截至信息报告日期，当前逾期 31-60 天未归还的欠款本金
        pilhEntity.setOved6190princ(BigDecimal.ZERO);// 截至信息报告日期，当前逾期 61-90 天未归还的欠款本金
        pilhEntity.setOved91180princ(BigDecimal.ZERO);// 截至信息报告日期，当前逾期 91-180 天未归还的欠款本金
        pilhEntity.setOvedPrinc180(BigDecimal.ZERO);// 截至信息报告日期，当前逾期 180 天以上未归还的欠款本金
        pilhEntity.setMonth(DateUtil.parse(bussDate));// 月份
        pilhEntity.setLatRpyDate(personLoanInfoEntity.getOpenDate());// 最近一次实际还款日期 开户时取开户日期
        pilhEntity.setBussDate(DateUtil.parse(bussDate));
        pilhEntity.setUpdator(Constants.UPDATER_FOR_TASK);// 修改人
        pilhEntity.setDeptCode(Constants.deptCode);
        pilhEntity.setUpdateTime(new Date());// 修改时间
        pilhEntity.setRptDateCode("10");// 报送时点说明 10-新开户
        // 结算/应还款日
        // 本期账单余额
        // 已使用额度
        // 未出单的大额专项分期余额
        // 实际还款百分比
        return pilhEntity;
    }
}
