package io.ibs.modules.irp.pbccrc2.prsn.task;

import io.ibs.modules.irp.pbccrc2.prsn.prsnloan.PrsnLoanInfo;
import io.ibs.modules.irp.pbccrc2.prsn.prsnloan.PrsnLoanInfoCollection;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.irp.util.DateUtils;
import io.ibs.modules.job.task.BaseTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 征信二代
 * 从核心系统以及其他系统抽取个人贷款信息到本地数据库
 * <p>
 * Created by AileYoung on 2023/5/17
 */
@Slf4j
@Service
public class PrsnLoanFirstGetFromHostTask extends BaseTask {

    private PrsnLoanInfoCollection prsnLoanInfoCollection;

    @Autowired
    public PrsnLoanFirstGetFromHostTask(PrsnLoanInfoCollection prsnLoanInfoCollection) {
        super("征信二代-从核心系统以及其他系统首次抽取个人贷款信息", PrsnLoanFirstGetFromHostTask.class);
        this.prsnLoanInfoCollection = prsnLoanInfoCollection;
    }

    @Override
    public void execute(String params) {
        if (StringUtils.isBlank(params)) {
            log.error("首次抽数时参数不能为空！");
            return;
        }
        //声明计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("从核心系统以及其他系统首次抽取个人贷款信息");
        String bgnDate = params.split("-")[0];
        String endDate = params.split("-")[1];
        log.info("开始时间{}，结束时间{}", bgnDate, endDate);

        // 获取开始日期到结束日期之间的所有日期
        List<String> dateList = DateUtils.getDateListBetweenTwoDays(bgnDate, endDate, Constants.SUBMIT_DATE_FORMAT);

        // 处理器参数 bussDate-业务发生日期 firstFlag-首次报送标志
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("firstFlag", true);
        paramsMap.put("bgnDate", bgnDate);
        paramsMap.put("endDate", endDate);
        for (String bussDate : dateList) {
            log.info("准备抽取{}数据>>>>>>>>", bussDate);
            paramsMap.put("bussDate", bussDate);

            // 收集个人借贷所有时点交易信息，后续按天一次性入库
            List<PrsnLoanInfo> prsnLoanInfoList = prsnLoanInfoCollection.collect(paramsMap);

            // 数据拦截器 在数据入库前，可以通过拦截器对数据进行预处理，之后再入库
            prsnLoanInfoList = prsnLoanInfoCollection.dataIntercept(prsnLoanInfoList);

            // 数据按天入库
            prsnLoanInfoCollection.saveInfo(prsnLoanInfoList);

            log.info("抽取{}数据结束<<<<<<<<\n", bussDate);
        }
        // 计时停止
        stopWatch.stop();
        // 输出显示计时器各种
        log.info("******--[{}]数据统计--******", stopWatch.getLastTaskName());
        log.info("----总耗时: " + stopWatch.getTotalTimeSeconds() / 60 + "(分钟)");
        log.info("----总耗时: " + stopWatch.getTotalTimeSeconds() + "(秒)");
    }
}

