package io.ibs.modules.irp.record.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.utils.TransactionHelper;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifBaseEntity;
import io.ibs.modules.irp.ecif.prsn.entity.TPrsnEcifBaseEntity;
import io.ibs.modules.irp.pbccrc.entloan.entity.EilbAcctbssgmtEntity;
import io.ibs.modules.irp.pbccrc.entrep.entity.Eiod2007BalanceSheetEntity;
import io.ibs.modules.irp.pbccrc.mortgage.entity.PipbMotgacltalctrctbssgmtEntity;
import io.ibs.modules.irp.pbccrc.prsnloan.entity.PilbAcctbssgmtEntity;
import io.ibs.modules.irp.record.dto.PSGExamineRecordDTO;
import io.ibs.modules.irp.record.entity.PSGExamineRecordEntity;
import io.ibs.modules.irp.record.excel.PSGExamineRecordExcel;
import io.ibs.modules.irp.record.service.PSGExamineRecordService;
import io.ibs.modules.irp.util.Constants;
import io.ibs.modules.security.user.SecurityUser;
import io.ibs.modules.security.user.UserDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 审核记录
 * Created by AileYoung on 2023/12/27.
 */
@RestController
@RequestMapping("record/examine")
@Api(tags = "审核记录")
@RequiredArgsConstructor
@Slf4j
public class PSGExamineRecordController {
    private final PSGExamineRecordService examineRecordService;
    private final TransactionHelper transactionHelper;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("record:psgexaminerecord:page")
    public Result<PageData<PSGExamineRecordDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        UserDetail user = SecurityUser.getUser();
        params.put("creator", user.getId());
        PageData<PSGExamineRecordDTO> page = examineRecordService.page(params);

        return new Result<PageData<PSGExamineRecordDTO>>().ok(page);
    }

    @GetMapping("allPage")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("record:psgexaminerecord:allpage")
    public Result<PageData<PSGExamineRecordDTO>> allPage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<PSGExamineRecordDTO> page = examineRecordService.page(params);

        return new Result<PageData<PSGExamineRecordDTO>>().ok(page);
    }


    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("record:psgexaminerecord:info")
    public Result<PSGExamineRecordDTO> get(@PathVariable("id") Long id) {
        PSGExamineRecordDTO data = examineRecordService.get(id);

        return new Result<PSGExamineRecordDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("record:psgexaminerecord:save")
    public Result save(@RequestBody PSGExamineRecordDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        if (PSGExamineRecordEntity.EXAMINE_TYPE_PB.equals(dto.getType())) {
            log.info("处理个人基本信息");
            String cstmNo = dto.getCstmNo();

            TPrsnEcifBaseEntity baseEntity = Db.getOne(new LambdaQueryWrapper<>(TPrsnEcifBaseEntity.class)
                    .eq(TPrsnEcifBaseEntity::getCstmNo, cstmNo));
            if (baseEntity == null) {
                return new Result().error("无此客户号[" + cstmNo + "]信息，请核对！");
            }
        } else if (PSGExamineRecordEntity.EXAMINE_TYPE_PL.equals(dto.getType())) {
            log.info("处理个人借贷信息");
            String bussNum = dto.getBussNum();
            Date bussDate = dto.getBussDate();
            List<PilbAcctbssgmtEntity> baseEntityList = Db.list(new LambdaQueryWrapper<>(PilbAcctbssgmtEntity.class)
                    .eq(PilbAcctbssgmtEntity::getBussNum, bussNum)
                    .eq(PilbAcctbssgmtEntity::getBussDate, bussDate));
            if (CollectionUtils.isEmpty(baseEntityList)) {
                return new Result().error("无此借据[" + bussNum + "]，业务日期[" + DateUtil.format(bussDate, Constants.SUBMIT_DATE_FORMAT) + "]信息，请核对！");
            }
        } else if (PSGExamineRecordEntity.EXAMINE_TYPE_EB.equals(dto.getType())) {
            log.info("处理企业基本信息");
            String cstmNo = dto.getCstmNo();
            TEtprEcifBaseEntity baseEntity = Db.getOne(new LambdaQueryWrapper<>(TEtprEcifBaseEntity.class)
                    .eq(TEtprEcifBaseEntity::getCstmNo, cstmNo));
            if (baseEntity == null) {
                return new Result().error("无此客户号[" + cstmNo + "]信息，请核对！");
            }
        } else if (PSGExamineRecordEntity.EXAMINE_TYPE_EL.equals(dto.getType())) {
            log.info("处理企业借贷信息");
            String bussNum = dto.getBussNum();
            Date bussDate = dto.getBussDate();
            List<EilbAcctbssgmtEntity> baseEntityList = Db.list(new LambdaQueryWrapper<>(EilbAcctbssgmtEntity.class)
                    .eq(EilbAcctbssgmtEntity::getBussNum, bussNum)
                    .eq(EilbAcctbssgmtEntity::getBussDate, bussDate));
            if (CollectionUtils.isEmpty(baseEntityList)) {
                return new Result().error("无此借据[" + bussNum + "]，业务日期[" + DateUtil.format(bussDate, Constants.SUBMIT_DATE_FORMAT) + "]信息，请核对！");
            }
        } else if (PSGExamineRecordEntity.EXAMINE_TYPE_ER.equals(dto.getType())) {
            log.info("处理企业财报信息");
            String cstmNo = dto.getCstmNo();
            TEtprEcifBaseEntity baseEntity = Db.getOne(new LambdaQueryWrapper<>(TEtprEcifBaseEntity.class)
                    .eq(TEtprEcifBaseEntity::getCstmNo, cstmNo));
            if (baseEntity == null) {
                return new Result().error("无此客户号[" + cstmNo + "]信息，请核对！");
            }
            String bussNum = dto.getBussNum();
            String sheetYear = bussNum.substring(0, 4);//报表年份
            String sheetType = bussNum.substring(4, 6);//报表类型
            List<Eiod2007BalanceSheetEntity> baseEntityList = Db.list(new LambdaQueryWrapper<>(Eiod2007BalanceSheetEntity.class)
                    .eq(Eiod2007BalanceSheetEntity::getEntCertNum, baseEntity.getEntCertNum())
                    .eq(Eiod2007BalanceSheetEntity::getSheetYear, sheetYear)
                    .eq(Eiod2007BalanceSheetEntity::getSheetType, sheetType));
            if (CollectionUtils.isEmpty(baseEntityList)) {
                return new Result().error("企业财报信息有误，客户号[" + cstmNo + "]，报表年份[" + sheetYear + "]，报表类型[" + sheetType + "]，请核对！");
            }
        } else if (PSGExamineRecordEntity.EXAMINE_TYPE_PP.equals(dto.getType())) {
            log.info("处理抵质押信息");
            String bussNum = dto.getBussNum();
            Date bussDate = dto.getBussDate();
            List<PipbMotgacltalctrctbssgmtEntity> baseEntityList = Db.list(new LambdaQueryWrapper<>(PipbMotgacltalctrctbssgmtEntity.class)
                    .eq(PipbMotgacltalctrctbssgmtEntity::getBussNum, bussNum)
                    .eq(PipbMotgacltalctrctbssgmtEntity::getBussDate, bussDate));
            if (CollectionUtils.isEmpty(baseEntityList)) {
                return new Result().error("无此抵质押[" + bussNum + "]，业务日期[" + DateUtil.format(bussDate, Constants.SUBMIT_DATE_FORMAT) + "]信息，请核对！");
            }
        } else {
            return new Result().error("暂不支持的审核类型[" + dto.getType() + "]");
        }

        examineRecordService.save(dto);
        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("record:psgexaminerecord:update")
    public Result update(@RequestBody PSGExamineRecordDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        examineRecordService.update(dto);

        return new Result();
    }

//    @DeleteMapping
//    @ApiOperation("删除")
//    @LogOperation("删除")
//    @RequiresPermissions("record:psgexaminerecord:delete")
//    public Result delete(@RequestBody Long[] ids){
//        //效验数据
//        AssertUtils.isArrayEmpty(ids, "id");
//
//        examineRecordService.delete(ids);
//
//        return new Result();
//    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("record:psgexaminerecord:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<PSGExamineRecordDTO> list = examineRecordService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "审核记录", list, PSGExamineRecordExcel.class);
    }

//    @PostMapping("pass")
//    @ApiOperation("通过审核")
//    @LogOperation("通过审核")
//    @RequiresPermissions("record:psgexaminerecord:pass")
//    public Result pass(@RequestBody Long id) {
//        PSGExamineRecordDTO dto = examineRecordService.get(id);
//        if (dto == null) {
//            return new Result().error("审核记录不存在！");
//        }
//        if (!PSGExamineRecordEntity.EXAMINE_STATUS_0.equals(dto.getStatus())) {
//            log.info("当前审核状态[{}]", dto.getStatus());
//            return new Result().error("请勿重复提交审核！");
//        }
//
//        // 开启事务
//        TransactionHelper.TransactionManager tm = transactionHelper.startTransaction();
//        try {
//            if (PSGExamineRecordEntity.EXAMINE_TYPE_PB.equals(dto.getType())) {
//                log.info("处理个人基本信息");
//                String cstmNo = dto.getCstmNo();
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifBaseEntity.class)
//                        .set(TPrsnEcifBaseEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifBaseEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifOtherCredentialsEntity.class)
//                        .set(TPrsnEcifOtherCredentialsEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifOtherCredentialsEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifMarriageEntity.class)
//                        .set(TPrsnEcifMarriageEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifMarriageEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifEducationEntity.class)
//                        .set(TPrsnEcifEducationEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifEducationEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifOccupationEntity.class)
//                        .set(TPrsnEcifOccupationEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifOccupationEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifLiveAddressEntity.class)
//                        .set(TPrsnEcifLiveAddressEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifLiveAddressEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifMailAddressEntity.class)
//                        .set(TPrsnEcifMailAddressEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifMailAddressEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TPrsnEcifIncomeEntity.class)
//                        .set(TPrsnEcifIncomeEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TPrsnEcifIncomeEntity::getCstmNo, cstmNo));
//            } else if (PSGExamineRecordEntity.EXAMINE_TYPE_PL.equals(dto.getType())) {
//                log.info("处理个人借贷信息");
//                String bussNum = dto.getBussNum();
//                Date bussDate = dto.getBussDate();
//
//                Db.update(new LambdaUpdateWrapper<>(PilbAcctbssgmtEntity.class)
//                        .set(PilbAcctbssgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PilbAcctbssgmtEntity::getBussNum, bussNum)
//                        .eq(PilbAcctbssgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PilcAcctbsinfsgmtEntity.class)
//                        .set(PilcAcctbsinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PilcAcctbsinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PilcAcctbsinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PildRltrepymtinfsgmtEntity.class)
//                        .set(PildRltrepymtinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PildRltrepymtinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PildRltrepymtinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PileMotgacltalctrctinfsgmtEntity.class)
//                        .set(PileMotgacltalctrctinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PileMotgacltalctrctinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PileMotgacltalctrctinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PilhAcctmthlyblginfsgmtEntity.class)
//                        .set(PilhAcctmthlyblginfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PilhAcctmthlyblginfsgmtEntity::getBussNum, bussNum)
//                        .eq(PilhAcctmthlyblginfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PiljAcctdbtinfsgmtEntity.class)
//                        .set(PiljAcctdbtinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PiljAcctdbtinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PiljAcctdbtinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PilkAcctspectrstdspnsgmtEntity.class)
//                        .set(PilkAcctspectrstdspnsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PilkAcctspectrstdspnsgmtEntity::getBussNum, bussNum)
//                        .eq(PilkAcctspectrstdspnsgmtEntity::getBussDate, bussDate));
//            } else if (PSGExamineRecordEntity.EXAMINE_TYPE_EB.equals(dto.getType())) {
//                log.info("处理企业基本信息");
//                String cstmNo = dto.getCstmNo();
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifBaseEntity.class)
//                        .set(TEtprEcifBaseEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifBaseEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifOtherCertEntity.class)
//                        .set(TEtprEcifOtherCertEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifOtherCertEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifMainMemberEntity.class)
//                        .set(TEtprEcifMainMemberEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifMainMemberEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifRegisterCapitalEntity.class)
//                        .set(TEtprEcifRegisterCapitalEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifRegisterCapitalEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifLeadInvestorEntity.class)
//                        .set(TEtprEcifLeadInvestorEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifLeadInvestorEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifActualControllerEntity.class)
//                        .set(TEtprEcifActualControllerEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifActualControllerEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifSuperiorOrganizationEntity.class)
//                        .set(TEtprEcifSuperiorOrganizationEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifSuperiorOrganizationEntity::getCstmNo, cstmNo));
//
//                Db.update(new LambdaUpdateWrapper<>(TEtprEcifContactWayEntity.class)
//                        .set(TEtprEcifContactWayEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(TEtprEcifContactWayEntity::getCstmNo, cstmNo));
//            } else if (PSGExamineRecordEntity.EXAMINE_TYPE_EL.equals(dto.getType())) {
//                log.info("处理企业借贷信息");
//                String bussNum = dto.getBussNum();
//                Date bussDate = dto.getBussDate();
//
//                Db.update(new LambdaUpdateWrapper<>(EilbAcctbssgmtEntity.class)
//                        .set(EilbAcctbssgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(EilbAcctbssgmtEntity::getBussNum, bussNum)
//                        .eq(EilbAcctbssgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(EilcAcctbsinfsgmtEntity.class)
//                        .set(EilcAcctbsinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(EilcAcctbsinfsgmtEntity::getBussNum, bussNum)
//                        .eq(EilcAcctbsinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(EildRltrepymtinfsgmEntity.class)
//                        .set(EildRltrepymtinfsgmEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(EildRltrepymtinfsgmEntity::getBussNum, bussNum)
//                        .eq(EildRltrepymtinfsgmEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(EileMotgacltalctrctinfEntity.class)
//                        .set(EileMotgacltalctrctinfEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(EileMotgacltalctrctinfEntity::getBussNum, bussNum)
//                        .eq(EileMotgacltalctrctinfEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(EilhActlbltyinfsgmtEntity.class)
//                        .set(EilhActlbltyinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(EilhActlbltyinfsgmtEntity::getBussNum, bussNum)
//                        .eq(EilhActlbltyinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(EiliAcctspectrstdspnEntity.class)
//                        .set(EiliAcctspectrstdspnEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(EiliAcctspectrstdspnEntity::getBussNum, bussNum)
//                        .eq(EiliAcctspectrstdspnEntity::getBussDate, bussDate));
//            } else if (PSGExamineRecordEntity.EXAMINE_TYPE_ER.equals(dto.getType())) {
//                log.info("处理企业财报信息");
//                String cstmNo = dto.getCstmNo();
//                TEtprEcifBaseEntity baseEntity = Db.getOne(new LambdaQueryWrapper<>(TEtprEcifBaseEntity.class)
//                        .eq(TEtprEcifBaseEntity::getCstmNo, cstmNo));
//                String bussNum = dto.getBussNum();
//                String sheetYear = bussNum.substring(0, 4);//报表年份
//                String sheetType = bussNum.substring(4, 6);//报表类型
//                Db.update(new LambdaUpdateWrapper<>(Eifd2007cashflowsEntity.class)
//                        .set(Eifd2007cashflowsEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(Eifd2007cashflowsEntity::getEntCertNum, baseEntity.getEntCertNum())
//                        .eq(Eifd2007cashflowsEntity::getSheetYear, sheetYear)
//                        .eq(Eifd2007cashflowsEntity::getSheetType, sheetType));
//
//                Db.update(new LambdaUpdateWrapper<>(Eiod2007BalanceSheetEntity.class)
//                        .set(Eiod2007BalanceSheetEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(Eiod2007BalanceSheetEntity::getEntCertNum, baseEntity.getEntCertNum())
//                        .eq(Eiod2007BalanceSheetEntity::getSheetYear, sheetYear)
//                        .eq(Eiod2007BalanceSheetEntity::getSheetType, sheetType));
//
//                Db.update(new LambdaUpdateWrapper<>(Eisd2007ispaEntity.class)
//                        .set(Eisd2007ispaEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(Eisd2007ispaEntity::getEntCertNum, baseEntity.getEntCertNum())
//                        .eq(Eisd2007ispaEntity::getSheetYear, sheetYear)
//                        .eq(Eisd2007ispaEntity::getSheetType, sheetType));
//            } else if (PSGExamineRecordEntity.EXAMINE_TYPE_PP.equals(dto.getType())) {
//                log.info("处理抵质押信息");
//                String bussNum = dto.getBussNum();
//                Date bussDate = dto.getBussDate();
//                Db.update(new LambdaUpdateWrapper<>(PipbMotgacltalctrctbssgmtEntity.class)
//                        .set(PipbMotgacltalctrctbssgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PipbMotgacltalctrctbssgmtEntity::getBussNum, bussNum)
//                        .eq(PipbMotgacltalctrctbssgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PipcMotgacltalbsinfsgmtEntity.class)
//                        .set(PipcMotgacltalbsinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PipcMotgacltalbsinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PipcMotgacltalbsinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PipdComrecinfsgmtEntity.class)
//                        .set(PipdComrecinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PipdComrecinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PipdComrecinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PipeMotgaproptinfsgmtEntity.class)
//                        .set(PipeMotgaproptinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PipeMotgaproptinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PipeMotgaproptinfsgmtEntity::getBussDate, bussDate));
//
//                Db.update(new LambdaUpdateWrapper<>(PipfCltalinfsgmtEntity.class)
//                        .set(PipfCltalinfsgmtEntity::getStat, Constants.REPORT_FLAG_3)
//                        .eq(PipfCltalinfsgmtEntity::getBussNum, bussNum)
//                        .eq(PipfCltalinfsgmtEntity::getBussDate, bussDate));
//            }
//
//            log.info("更新审核记录状态");
//            Db.update(new LambdaUpdateWrapper<>(PSGExamineRecordEntity.class)
//                    .set(PSGExamineRecordEntity::getStatus, PSGExamineRecordEntity.EXAMINE_STATUS_1)
//                    .eq(PSGExamineRecordEntity::getId, id));
//            transactionHelper.commit(tm);
//        } catch (Exception e) {
//            transactionHelper.rollback(tm);
//        }
//        return new Result();
//    }
}