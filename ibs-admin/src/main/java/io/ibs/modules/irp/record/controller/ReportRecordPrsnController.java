package io.ibs.modules.irp.record.controller;

import cn.hutool.core.date.DateUtil;
import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.ExcelUtils;
import io.ibs.common.utils.Result;
import io.ibs.common.validator.AssertUtils;
import io.ibs.common.validator.ValidatorUtils;
import io.ibs.common.validator.group.AddGroup;
import io.ibs.common.validator.group.DefaultGroup;
import io.ibs.common.validator.group.UpdateGroup;
import io.ibs.modules.irp.record.dto.ReportRecordPrsnDTO;
import io.ibs.modules.irp.record.excel.ReportRecordPrsnExcel;
import io.ibs.modules.irp.record.service.ReportRecordPrsnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 个人报送记录
 *
 * <AUTHOR>
 * @since 1.0 2023-04-12
 */
@RestController
@RequestMapping("record/reportRecordPrsn")
@Api(tags = "个人报送记录")
public class ReportRecordPrsnController {
    @Autowired
    private ReportRecordPrsnService reportRecordPrsnService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("record:reportrecordprsn:page")
    public Result<PageData<ReportRecordPrsnDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ReportRecordPrsnDTO> page = reportRecordPrsnService.page(params);

        return new Result<PageData<ReportRecordPrsnDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("record:reportrecordprsn:info")
    public Result<ReportRecordPrsnDTO> get(@PathVariable("id") Long id) {
        ReportRecordPrsnDTO data = reportRecordPrsnService.get(id);

        return new Result<ReportRecordPrsnDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("record:reportrecordprsn:save")
    public Result save(@RequestBody ReportRecordPrsnDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        reportRecordPrsnService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("record:reportrecordprsn:update")
    public Result update(@RequestBody ReportRecordPrsnDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        reportRecordPrsnService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("record:reportrecordprsn:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        reportRecordPrsnService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("record:reportrecordprsn:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<ReportRecordPrsnDTO> list = reportRecordPrsnService.list(params);
        ExcelUtils.exportExcelToTarget(response, "个人报送记录-" + DateUtil.format(new Date(), "yyyyMMddHHmmss"), "个人报送记录", list, ReportRecordPrsnExcel.class);
    }

    @DeleteMapping("clearData")
    @ApiOperation("清除数据")
    @LogOperation("清除数据")
    @RequiresPermissions("record:reportrecordprsn:clear")
    public Result clearData(@RequestBody Map<String, String> params) {
        AssertUtils.isMapEmpty(params, "params");

        String fromDate = params.get("fromDate");
        String toDate = params.get("toDate");

        reportRecordPrsnService.clearData(fromDate, toDate);
        return new Result();
    }
}