package io.ibs.modules.irp.record.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* 企业报送记录
*
* <AUTHOR>  
* @since 1.0 2023-04-12
*/
@Data
@ApiModel(value = "企业报送记录")
public class ReportRecordEtprDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "报送日期")
    private String reportDate;
    @ApiModelProperty(value = "信息类型")
    private String infoType;
    @ApiModelProperty(value = "段名")
    private String sgmt;
    @ApiModelProperty(value = "信息标识1")
    private String infoKey1;
    @ApiModelProperty(value = "信息标识2")
    private String infoKey2;
    @ApiModelProperty(value = "报送标志")
    private String reportFlag;
    @ApiModelProperty(value = "备注1")
    private String note1;
    @ApiModelProperty(value = "备注2")
    private String note2;
    @ApiModelProperty(value = "备注3")
    private String note3;
    @ApiModelProperty(value = "备注4")
    private String note4;

}