package io.ibs.modules.irp.record.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.util.Date;

/**
 * 操作记录
 * Created by AileYoung on 2023/12/27.
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(25)
public class PSGOperateRecordExcel {
    @ExcelProperty(value = "ID", index = 0)
    private Long id;
    @ExcelProperty(value = "类型", index = 1)
    private String type;
    @ExcelProperty(value = "操作", index = 2)
    private String operate;
    @ExcelProperty(value = "操作前原始数据", index = 3)
    private String oriData;
    @ExcelProperty(value = "操作后新数据", index = 4)
    private String newData;
    @ExcelProperty(value = "操作人", index = 5)
    private Long creator;
    @ExcelProperty(value = "操作时间", index = 6)
    private Date createDate;
    @ExcelProperty(value = "更新人", index = 7)
    private Long updater;
    @ExcelProperty(value = "更新时间", index = 8)
    private Date updateDate;
}