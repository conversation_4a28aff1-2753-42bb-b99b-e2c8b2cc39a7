package io.ibs.modules.irp.record.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.ibs.common.service.impl.CrudServiceImpl;
import io.ibs.modules.irp.record.dao.ReportRecordEtprDao;
import io.ibs.modules.irp.record.dto.ReportRecordEtprDTO;
import io.ibs.modules.irp.record.entity.ReportRecordEtprEntity;
import io.ibs.modules.irp.record.service.ReportRecordEtprService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 企业报送记录
 *
 * <AUTHOR>
 * @since 1.0 2023-04-12
 */
@Service
public class ReportRecordEtprServiceImpl extends CrudServiceImpl<ReportRecordEtprDao, ReportRecordEtprEntity, ReportRecordEtprDTO> implements ReportRecordEtprService {

    @Override
    public QueryWrapper<ReportRecordEtprEntity> getWrapper(Map<String, Object> params) {
        QueryWrapper<ReportRecordEtprEntity> wrapper = new QueryWrapper<>();
        String from =  MapUtil.getStr(params,"from");
        from = StringUtils.isBlank(from) ? "20000101" : from;
        String to = MapUtil.getStr(params,"to");
        to = StringUtils.isBlank(to) ? DateUtil.format(new Date(), "yyyyMMdd") : to;
        wrapper.between( "REPORT_DATE", from, to);
        String infoType = (String) params.get("infoType");
        wrapper.eq(StringUtils.isNotBlank(infoType), "INFO_TYPE", infoType);
        String sgmt = (String) params.get("sgmt");
        wrapper.eq(StringUtils.isNotBlank(sgmt), "SGMT", sgmt);
        String infoKey1 = (String) params.get("infoKey1");
        wrapper.eq(StringUtils.isNotBlank(infoKey1), "INFO_KEY1", infoKey1);
        String infoKey2 = (String) params.get("infoKey2");
        wrapper.eq(StringUtils.isNotBlank(infoKey2), "INFO_KEY2", infoKey2);
        String reportFlag = (String) params.get("reportFlag");
        wrapper.eq(StringUtils.isNotBlank(reportFlag), "REPORT_FLAG", reportFlag);
        wrapper.orderByAsc("REPORT_DATE", "INFO_KEY1");
        return wrapper;
    }

    @Override
    public void clearData(String fromDate, String toDate) {
        baseDao.delete(new UpdateWrapper<ReportRecordEtprEntity>().lambda()
                .between(ReportRecordEtprEntity::getReportDate, fromDate, toDate));
    }
}