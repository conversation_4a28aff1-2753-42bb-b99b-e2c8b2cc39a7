package io.ibs.modules.irp.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by AileYoung on 2023/5/17.
 */
public class DateUtils extends io.ibs.common.utils.DateUtils {

    public static List<String> getDateListBetweenTwoDays(String startDate, String endDate, String format) {
        if (Integer.valueOf(startDate) > Integer.valueOf(endDate)) {
            return new ArrayList<>();
        }
        List<String> dateList = new ArrayList<String>();
        while (!startDate.equals(endDate)) {
            dateList.add(startDate);
            DateTime day = DateUtil.offsetDay(DateUtil.parse(startDate, format), 1);
            startDate = DateUtil.format(day, format);
        }
        dateList.add(endDate);

        return dateList;
    }
}
