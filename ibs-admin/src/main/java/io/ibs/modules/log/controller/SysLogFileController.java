package io.ibs.modules.log.controller;

import com.alibaba.fastjson.JSON;
import io.ibs.common.annotation.LogOperation;
import io.ibs.common.constant.Constant;
import io.ibs.common.page.PageData;
import io.ibs.common.utils.Result;
import io.ibs.modules.log.dto.SysLogFileDTO;
import io.ibs.modules.log.service.SysLogFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 功能描述
 * 作者 zhaobei
 * 日期 2021-06-21 22:12
 */
@RestController
@RequestMapping("sys/log/file")
@Api(tags = "日志文件")
@Slf4j
public class SysLogFileController {
    @Autowired
    SysLogFileService sysLogFileService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    public Result<PageData<SysLogFileDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        log.info("查询日志文件列表");
        params.put("irpFileDir", System.getenv("irpLogFileDir"));
        PageData<SysLogFileDTO> page = sysLogFileService.page(params);
        log.info("返回结果：\n[{}]", JSON.toJSONString(page));
        return new Result<PageData<SysLogFileDTO>>().ok(page);
    }

    @GetMapping("download")
    @ApiOperation("下载")
    @LogOperation("下载")
    public void download(@RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        log.info("下载日志文件");
        sysLogFileService.download(params, response);
    }

    @PostMapping("loadView")
    @ApiOperation("日志内容")
    @LogOperation("日志内容")
    public Result<PageData<Map<String, Object>>> getFileContext(@RequestBody Map<String, Object> params) throws Exception {
        log.info("浏览日志内容");
        return sysLogFileService.getFileContext(params);
    }
}
