package io.ibs.modules.log.service;


import io.ibs.common.page.PageData;
import io.ibs.common.service.BaseService;
import io.ibs.modules.log.dto.SysLogErrorDTO;
import io.ibs.modules.log.entity.SysLogErrorEntity;

import java.util.List;
import java.util.Map;

/**
 * 异常日志
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysLogErrorService extends BaseService<SysLogErrorEntity> {

    PageData<SysLogErrorDTO> page(Map<String, Object> params);

    List<SysLogErrorDTO> list(Map<String, Object> params);

    void save(SysLogErrorEntity entity);

}