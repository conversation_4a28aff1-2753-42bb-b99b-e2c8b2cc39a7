package io.ibs.modules.log.service;

import io.ibs.common.page.PageData;
import io.ibs.common.utils.Result;
import io.ibs.modules.log.dto.SysLogFileDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface SysLogFileService {
    PageData<SysLogFileDTO> page(Map<String, Object> params);

    void download(Map<String, Object> params, HttpServletResponse response) throws Exception;

    Result<PageData<Map<String, Object>>> getFileContext(Map<String, Object> params) throws Exception;
}
