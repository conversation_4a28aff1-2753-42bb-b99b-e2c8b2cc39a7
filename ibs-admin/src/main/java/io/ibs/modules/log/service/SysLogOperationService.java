package io.ibs.modules.log.service;

import io.ibs.common.page.PageData;
import io.ibs.common.service.BaseService;
import io.ibs.modules.log.dto.SysLogOperationDTO;
import io.ibs.modules.log.entity.SysLogOperationEntity;

import java.util.List;
import java.util.Map;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysLogOperationService extends BaseService<SysLogOperationEntity> {

    PageData<SysLogOperationDTO> page(Map<String, Object> params);

    List<SysLogOperationDTO> list(Map<String, Object> params);

    void save(SysLogOperationEntity entity);
}