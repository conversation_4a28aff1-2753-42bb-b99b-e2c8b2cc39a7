package io.ibs.modules.security.service;

import io.ibs.common.page.PageData;
import io.ibs.common.service.BaseService;
import io.ibs.common.utils.Result;
import io.ibs.modules.security.entity.SysUserTokenEntity;
import io.ibs.modules.sys.entity.SysOnlineEntity;

import java.util.Map;

/**
 * 用户Token
 * 
 * <AUTHOR> <PERSON>@gmail.com
 */
public interface SysUserTokenService extends BaseService<SysUserTokenEntity> {

	/**
	 * 生成token
	 * @param userId  用户ID
	 */
	Result createToken(Long userId);

	/**
	 * 退出
	 * @param userId  用户ID
	 */
	void logout(Long userId);

	/**
	 * 在线用户分页
	 */
	PageData<SysOnlineEntity> onlinePage(Map<String, Object> params);

}