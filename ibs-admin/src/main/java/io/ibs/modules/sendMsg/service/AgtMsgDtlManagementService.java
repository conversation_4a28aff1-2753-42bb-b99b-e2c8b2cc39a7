package io.ibs.modules.sendMsg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.ibs.common.service.CrudService;

import io.ibs.modules.sendMsg.domain.AgtMsgDtl;
import io.ibs.modules.sendMsg.dto.AgtMsgDtlDTO;

import java.util.List;

/**
 * 短信内容
 *
 * <AUTHOR>
 * @since 3.0 2022-07-08
 */
public interface AgtMsgDtlManagementService extends IService<AgtMsgDtl> {
    void insertAgtDtl(AgtMsgDtl agtMsgDtl);
}