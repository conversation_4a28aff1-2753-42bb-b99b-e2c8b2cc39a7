package io.ibs.modules.sendMsg.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.ibs.common.service.impl.CrudServiceImpl;

import io.ibs.commons.dynamic.datasource.annotation.DataSource;
import io.ibs.modules.sendMsg.dao.AgtMsgDtlMapperDao;
import io.ibs.modules.sendMsg.domain.AgtMsgDtl;
import io.ibs.modules.sendMsg.dto.AgtMsgDtlDTO;
import io.ibs.modules.sendMsg.service.AgtMsgDtlManagementService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 短信管理
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Service
public class AgtMsgDtlManagementServiceImpl extends ServiceImpl<AgtMsgDtlMapperDao, AgtMsgDtl> implements AgtMsgDtlManagementService {

    @Override
    public void insertAgtDtl(AgtMsgDtl agtMsgDtl) {
        baseMapper.insertAgtDtl(agtMsgDtl);
    }
}