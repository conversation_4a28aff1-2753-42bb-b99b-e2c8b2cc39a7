package io.ibs.modules.sys.service;

import io.ibs.common.service.BaseService;
import io.ibs.modules.sys.dto.SysRegionDTO;
import io.ibs.modules.sys.dto.region.RegionProvince;
import io.ibs.modules.sys.entity.SysRegionEntity;

import java.util.List;
import java.util.Map;

/**
 * 行政区域
 * 
 * <AUTHOR> <PERSON>@gmail.com
 */
public interface SysRegionService extends BaseService<SysRegionEntity> {

	List<SysRegionDTO> list(Map<String, Object> params);

	List<SysRegionEntity> getTreeList();

	SysRegionDTO get(Long id);

	void save(SysRegionDTO dto);

	void update(SysRegionDTO dto);

	void delete(Long id);

	int getCountByPid(Long pid);

	List<RegionProvince> getRegion(boolean threeLevel);
}