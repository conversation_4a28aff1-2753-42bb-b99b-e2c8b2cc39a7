package io.ibs.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.ibs.common.page.PageData;
import io.ibs.common.service.impl.BaseServiceImpl;
import io.ibs.common.utils.ConvertUtils;
import io.ibs.modules.security.password.PasswordUtils;
import io.ibs.modules.security.user.SecurityUser;
import io.ibs.modules.security.user.UserDetail;
import io.ibs.modules.sys.dao.SysUserDao;
import io.ibs.modules.sys.dto.SysUserDTO;
import io.ibs.modules.sys.dto.SysUsersDTO;
import io.ibs.modules.sys.entity.SysUserEntity;
import io.ibs.modules.sys.entity.SysUserPassRecordEntity;
import io.ibs.modules.sys.enums.SuperAdminEnum;
import io.ibs.modules.sys.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 系统用户
 *
 * @<NAME_EMAIL>
 */
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
    @Autowired
    private SysRoleUserService sysRoleUserService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private SysUserPostService sysUserPostService;
    @Autowired
    private SysUserPassRecordService userPassRecordService;

    @Override
    public PageData<SysUserDTO> page(Map<String, Object> params) {
        // 转换成like
        paramsToLike(params, "username");

        // 分页
        IPage<SysUserEntity> page = getPage(params, "t1.create_date", false);

        // 普通管理员，只能查询所属部门及子部门的数据
        UserDetail user = SecurityUser.getUser();
        if (user.getSuperAdmin() == SuperAdminEnum.NO.value()) {
            params.put("deptIdList", sysDeptService.getSubDeptIdList(user.getDeptId()));
        }

        // 查询
        List<SysUserEntity> list = baseDao.getList(params);
        PageData<SysUserDTO> pageData = getPageData(list, page.getTotal(), SysUserDTO.class);
        for (SysUserDTO sysUserDTO : pageData.getList()) {
            sysUserDTO.setRoleList(sysRoleUserService.getRoleList(sysUserDTO.getId()));
            sysUserDTO.setPostList(sysUserPostService.getPostList(sysUserDTO.getId()));
        }
        return pageData;
    }

    @Override
    public List<SysUserDTO> list(Map<String, Object> params) {
        // 普通管理员，只能查询所属部门及子部门的数据
        UserDetail user = SecurityUser.getUser();
        if (user.getSuperAdmin() == SuperAdminEnum.NO.value()) {
            params.put("deptIdList", sysDeptService.getSubDeptIdList(user.getDeptId()));
        }

        List<SysUserEntity> entityList = baseDao.getList(params);

        return ConvertUtils.sourceToTarget(entityList, SysUserDTO.class);
    }

    @Override
    public List<SysUsersDTO> getUsers() {
        List<SysUserEntity> entityList = baseDao.selectList(new LambdaQueryWrapper<SysUserEntity>()
                .select(SysUserEntity::getId, SysUserEntity::getRealName, SysUserEntity::getUsername));
        return ConvertUtils.sourceToTarget(entityList, SysUsersDTO.class);
    }

    @Override
    public SysUserDTO get(Long id) {
        SysUserEntity entity = baseDao.getById(id);

        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    public SysUserDTO getByUsername(String username) {
        SysUserEntity entity = baseDao.getByUsername(username);
        return ConvertUtils.sourceToTarget(entity, SysUserDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysUserDTO dto) {
        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

        // 密码加密
        String password = PasswordUtils.encode(entity.getPassword());
        entity.setPassword(password);

        // 保存用户
        entity.setSuperAdmin(SuperAdminEnum.NO.value());
        insert(entity);

        // 保存角色用户关系
        sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());

        // 保存用户岗位关系
        sysUserPostService.saveOrUpdate(entity.getId(), dto.getPostIdList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SysUserDTO dto) {
        SysUserEntity entity = ConvertUtils.sourceToTarget(dto, SysUserEntity.class);

        // 密码加密
        if (StringUtils.isBlank(dto.getPassword())) {
            entity.setPassword(null);
        } else {
            String password = PasswordUtils.encode(entity.getPassword());
            entity.setPassword(password);
            // 新增密码更新记录
            SysUserPassRecordEntity passRecord = new SysUserPassRecordEntity();
            passRecord.setId(dto.getId());
            passRecord.setUserName(dto.getUsername());
            userPassRecordService.save(passRecord);
        }

        // 更新用户
        updateById(entity);

        // 更新角色用户关系
        sysRoleUserService.saveOrUpdate(entity.getId(), dto.getRoleIdList());

        // 保存用户岗位关系
        sysUserPostService.saveOrUpdate(entity.getId(), dto.getPostIdList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long[] ids) {
        // 删除用户
        baseDao.deleteBatchIds(Arrays.asList(ids));

        // 删除角色用户关系
        sysRoleUserService.deleteByUserIds(ids);

        // 删除用户岗位关系
        sysUserPostService.deleteByUserIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String newPassword) {
        newPassword = PasswordUtils.encode(newPassword);
        SysUserEntity user = new SysUserEntity();
        user.setId(id);
        user.setPassword(newPassword);
        baseDao.updateById(user);
    }

    @Override
    public int getCountByDeptId(Long deptId) {
        return baseDao.getCountByDeptId(deptId);
    }

    @Override
    public List<Long> getUserIdListByDeptId(List<Long> deptIdList) {
        return baseDao.getUserIdListByDeptId(deptIdList);
    }

}
