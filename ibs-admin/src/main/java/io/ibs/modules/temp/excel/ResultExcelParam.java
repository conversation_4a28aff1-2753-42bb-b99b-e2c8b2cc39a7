package io.ibs.modules.temp.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ResultExcelParam {
    @ExcelProperty("客户号")
    private String cstmNo;

    @ExcelProperty("授信类型")
    private String credType;

    @ExcelProperty("授信序号")
    private String serno;

    @ExcelProperty("原授信额度")
    private BigDecimal oriCredAmt;

    @ExcelProperty("原已使用额度")
    private BigDecimal oriUsedCred;

    @ExcelProperty("修改后授信额度")
    private BigDecimal credAmt;

    @ExcelProperty("修改后已使用额度")
    private BigDecimal usedCred;

    @ExcelProperty("对应借据号")
    private String loanNo;

    @ExcelProperty("借据放款金额")
    private String loanAmt;

    @ExcelProperty("借据本金余额")
    private String bal;

    @ExcelProperty("处理结果")
    private String result;

    @ExcelProperty("备注")
    private String remark;
}
