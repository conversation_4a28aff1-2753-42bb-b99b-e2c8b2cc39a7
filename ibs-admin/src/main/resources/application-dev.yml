spring:
  datasource:
    druid:
      driver-class-name: oracle.jdbc.OracleDriver
      url: *******************************************
      username: IRP
      password: IRP
      name: ORA-IRP
      #      url: *******************************************
      #      username: irp
      #      password: irp
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: false
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

      ##多数据源的配置，需要引用ibs-dynamic-datasource
      #dynamic:
      #  datasource:
      #    informix:
      #      #  url: jdbc:informix-sqli://192.168.102.25:1528/cbsdb:informixserver=cs_dbs_tcp;NEWLOACLE=en_us,zh_cn,zh_tw;NEWCODESET=GB2312,885901,819,GB,Big5;IFX_USE_STRENC=true;
      #      #核心informix数据库中字符集为GBK，从中获取数据时需要进行转码，否则中文生僻字会乱码
      #      url: jdbc:informix-sqli://192.168.102.25:1528/cbsdb:NEWCODESET=GB18030,8859-1,819;IFX_USE_STRENC=true;
      #      #      url: jdbc:informix-sqli://192.168.102.25:1528/cbsdb:NEWCODESET=GB18030-2000,8859-1,819;IFX_USE_STRENC=true;
      #      username: cbs20
      #      password: cbs200
      #      name: informix
      # url: jdbc:informix-sqli://132.147.234.112:1528/cbsdb:NEWCODESET=GB2312,885901,819,GB,Big5;INFORMIXSERVER=;IFX_USE_STRENC=true;
      # url: jdbc:informix-sqli://132.147.234.112:1528/cbsdb:NEWCODESET=GB2312,885901,819,GB,Big5;INFORMIXSERVER=;IFX_USE_STRENC=true;
      # username: cbs30
      # password: cbs300
      # url: jdbc:informix-sqli://172.18.18.94:50000/cbsdb:NEWCODESET=GB2312,885901,819,GB,Big5;INFORMIXSERVER=lc_dbs_tcp;IFX_USE_STRENC=true;
      # username: informix
      # password: informix
#    credit:
#      name: ORA-credit
#      driver-class-name: oracle.jdbc.OracleDriver
#      url: ********************************************
#      username: C##UNIDCPDB
#      password: unidcpdb
#    xdgl:
#      name: mysql-xdgl
#      driver-class-name: com.mysql.cj.jdbc.Driver
#      url: *********************************************************************************************************
#      username: root
#      password: root
#    unidcpdb:
#      name: ORA-unidcpdb
#      driver-class-name: oracle.jdbc.OracleDriver
#      url: ********************************************
#      username: c##unidcpdb
#      password: unidcpdb
#    sqlserver:
#      name: sqlserver
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      url: ***************************************************************************************************;
#      username: sa
#      password: Sa123456
# 日志相关配置
logging:
  config: classpath:logback-spring.xml # 日志配置文件路径
#  path: ./logs #日志存放路径
