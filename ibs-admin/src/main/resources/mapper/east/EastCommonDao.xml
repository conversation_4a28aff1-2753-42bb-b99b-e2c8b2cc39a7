<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.common.EastCommonDao">
    <select id="getAuthTlrNoList" resultType="java.util.Map">
        select trim(entry.acc) ||
               to_char(entry.tran_date, '%Y%m%d') ||
               entry.host_seqno tlr_index,
               tlrlog.auth_tlr  auth_tlr
        from t_bak_entry_bak entry,
             t_srm_tlrlog_bak tlrlog
        where entry.clt_seqno = tlrlog.clt_seqno
          and entry.tran_date = tlrlog.date
          and entry.acc = tlrlog.acc
          and entry.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        and entry.tran_date >= #{startDate}
        and entry.tran_date <![CDATA[ <= ]]> #{endDate}
    </select>

    <select id="getFixPeerAcc" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select trim(fix_inn_acc) acc, trim(peer_acc) dfzh, to_char(tran_date, '%Y%m%d') tranDate
        from t_fix_dtl where summ_no = 86
                         and tran_date >= #{startDate}
                         and tran_date <![CDATA[ <= ]]> #{endDate}
                         and peer_acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
    </select>

    <select id="getPeerAccByInstNo" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select trim(mnacc_acc) || '0000' acc, trim(mnacc_acc_name) name
        from t_gnl_mnacc
        where itm_no = #{itmNo}
          and inst_no = #{instNo}
          and substr(trim(mnacc_acc), 14, 2) = '01'
    </select>

    <select id="getLedgerNoList" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select inst_no instNo, trim(acc) acc, trim(cstm_name) name, 'cdm' type
        from t_cdm_ledger
        union
        select inst_no instNo, trim(acc) acc, trim(cstm_name) name, 'sdm' type
        from t_sdm_ledger
        union
        select inst_no instNo, trim(acc) acc, trim(acc_name) name, 'fix' type
        from t_fix_ledger
        union
        select inst_no instNo, trim(acc) acc, trim(acc_name) name, 'inn' type
        from t_inn_ledger
        union
        select inst_no instNo, trim(loan_acc) acc, trim(cstm_name) name, 'loa' type
        from t_loan_ledger
    </select>

    <select id="getPeerAccByTlrEntry" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select trim(tlrB.acc)                     acc,
               trim(tlrA.acc)                     dfzh,
               entry.host_seqno                   hostSeqno,
               to_char(entry.tran_date, '%Y%m%d') tranDate
        from t_pub_tlr_bal tlrA,
             t_pub_tlr_bal tlrB,
        (
        select bak.acc, bak.clt_seqno, bak.tran_date, bak.host_seqno
        from t_bak_entry_bak bak
        where bak.tran_date >= #{startDate}
          and bak.tran_date <![CDATA[ <= ]]> #{endDate}
          and  bak.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        ) entry
        where tlrB.tran_date = entry.tran_date
          and tlrB.clt_seqno = entry.clt_seqno
          and tlrB.acc = entry.acc
          and tlrA.tran_date = tlrB.tran_date
          and tlrA.clt_seqno = tlrB.peer_seqno
    </select>

    <select id="getBakEntryByPeerAcc" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select distinct trim(a.peer_acc)               acc,
                        trim(a.acc)                    dfzh,
                        trim(a.acc_name)               name,
                        b.host_seqno                   hostSeqno,
                        to_char(a.tran_date, '%Y%m%d') tranDate,
                        a.ditm_no                      dfkmbh
        from t_bak_entry_bak a,
             t_bak_entry_bak b
        where a.clt_seqno = b.clt_seqno
          and a.tran_date = b.tran_date
          and a.peer_acc = b.acc
          and a.equal_flag = '1'
          and b.equal_flag = '1'
          and a.agt_inst != '9996'
          and (trim(b.peer_acc) = '' or trim(b.peer_acc) is null)
          and b.tran_date >= #{startDate}
          and b.tran_date <![CDATA[ <= ]]> #{endDate}
          and a.peer_acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
    </select>

    <select id="getBakEntryDitmByPeerAcc" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select dict.ditm_no dfkmbh, trim(dict.ditm_name) dfkmmc, trim(led.acc) dfzh
        from t_cdm_ledger led,
             t_pub_svt svt,
             t_srm_ditm_dict dict
        where led.prd_no = svt.prd_no
          and svt.ditm_no = dict.ditm_no
          and led.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        union
        select dict.ditm_no dfkmbh, trim(dict.ditm_name) dfkmmc, trim(led.acc) dfzh
        from t_sdm_ledger led,
             t_pub_svt svt,
             t_srm_ditm_dict dict
        where led.prd_no = svt.prd_no
          and svt.ditm_no = dict.ditm_no
          and led.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        union
        select dict.ditm_no dfkmbh, trim(dict.ditm_name) dfkmmc, trim(led.acc) dfzh
        from t_fix_ledger led,
             t_pub_svt svt,
             t_srm_ditm_dict dict
        where led.prd_no = svt.prd_no
          and svt.ditm_no = dict.ditm_no
          and led.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        union
        select dict.ditm_no dfkmbh, trim(dict.ditm_name) dfkmmc, trim(led.loan_acc) dfzh
        from t_loan_para para,
             t_loan_ledger led,
             t_srm_ditm_dict dict
        where led.prd_no = para.prd_no
          and para.ditm_no = dict.ditm_no
          and led.loan_acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
    </select>

    <select id="getBakEntryIntOut" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select distinct trim(b.acc)                    acc,
                        trim(a.acc)                    dfzh,
                        trim(a.acc_name)               name,
                        b.host_seqno                   hostSeqno,
                        to_char(a.tran_date, '%Y%m%d') tranDate,
                        a.ditm_no                      dfkmbh
        from t_bak_entry_bak a,
             t_bak_entry_bak b
        where a.tran_date >= #{startDate}
          and a.tran_date <![CDATA[ <= ]]> #{endDate}
          and a.clt_seqno = b.clt_seqno
          and a.tran_date = b.tran_date
          and a.amt <![CDATA[ < ]]> 0
          and b.amt > 0
          and (a.peer_acc is not null or trim(a.peer_acc) != '')
          and (b.peer_acc is null or trim(b.peer_acc) = '')
          and b.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
    </select>

    <select id="getBakEntryInt6188" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select distinct trim(a.acc)                    acc,
                        trim(b.acc)                    dfzh,
                        trim(b.acc_name)               name,
                        to_char(b.tran_date, '%Y%m%d') tranDate,
                        a.host_seqno                   hostSeqno
        from (select
        trim(nvl((
        select
        case
        when para.prd_name like '%农户%'
        then '农户贷款'
        when (para.prd_name like '%单位%')
        then
        (select
        distinct nvl(acc_name[1,8], '其他贷款')
        from
        t_bak_entry_bak t
        where
        t.tran_date = x.tran_date
        and t.clt_seqno = x.clt_seqno
        and t.dr_cr_flag = '2'
        and t.equal_flag = '1'
        and t.acc_name like '%农村工商%')
        else '其他贷款'
        end
        from
        t_loan_para para
        where
        para.prd_no = x.prd_no),
        '其他贷款')) loantype,
                     acc,
                     tran_date,
                     clt_seqno,
                     inst_no,
                     host_seqno
              from t_bak_entry_bak x
        where x.equal_flag = '1'
          and x.dr_cr_flag = '1'
          and x.tran_date >= #{startDate}
          and x.tran_date <![CDATA[ <= ]]> #{endDate}
          and x.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        )a
            left join
        t_bak_entry_bak b
        on a.tran_date = b.tran_date
            and a.clt_seqno = b.clt_seqno
            and a.inst_no = b.inst_no
            and a.acc != b.acc
            and b.acc_name like concat(a.loantype, '%')
            and b.dr_cr_flag = '2'
            and b.equal_flag = '1'
    </select>

    <select id="getBakEntryIntCr6188" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select distinct trim(a.acc)                    acc,
                        trim(b.acc)                    dfzh,
                        trim(b.acc_name)               name,
                        to_char(b.tran_date, '%Y%m%d') tranDate,
                        a.host_seqno                   hostSeqno
        from
        (
        select acc,
               tran_date,
               clt_seqno,
               inst_no,
               host_seqno
        from t_bak_entry_bak x
        where x.equal_flag = '1'
          and x.dr_cr_flag = '2'
          and x.tran_date >= #{startDate}
          and x.tran_date <![CDATA[ <= ]]> #{endDate}
          and tran_no = '6188'
          and x.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        ) a
            left join
        t_bak_entry_bak b
        on
            a.tran_date = b.tran_date
                and a.clt_seqno = b.clt_seqno
                and a.inst_no = b.inst_no
                and b.dr_cr_flag = '1'
                and b.equal_flag = '1'
        order by acc
    </select>

    <select id="getBakEntryNotSeq" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select distinct trim(b.acc)                    acc,
                        trim(a.acc)                    dfzh,
                        trim(a.acc_name)               name,
                        b.host_seqno                   hostSeqno,
                        to_char(a.tran_date, '%Y%m%d') tranDate,
                        a.ditm_no                      dfkmbh
        FROM t_bak_entry_bak a
                 join t_bak_entry_bak b
                      on a.clt_seqno = b.clt_seqno
                          and a.inst_no = b.inst_no
                          and a.tran_date = b.tran_date
                          and a.amt = b.amt
                          and a.dr_cr_flag != b.dr_cr_flag
                          and (b.peer_acc is null or trim(b.peer_acc) = '')
        where a.equal_flag = '1'
          and b.equal_flag = '1'
          and a.tran_date >= #{startDate}
          and a.tran_date <![CDATA[ <= ]]> #{endDate}
          and b.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
    </select>

    <select id="getBakEntryAcc" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select distinct trim(b.acc)                    acc,
                        trim(a.acc)                    dfzh,
                        trim(a.acc_name)               name,
                        b.host_seqno                   hostSeqno,
                        to_char(a.tran_date, '%Y%m%d') tranDate,
                        a.ditm_no                      dfkmbh
        FROM t_bak_entry_bak a
                 join t_bak_entry_bak b
                      on a.clt_seqno = b.clt_seqno
                          and a.host_seqno = b.host_seqno
                          and a.tran_date = b.tran_date
                          and a.amt = b.amt
                          and a.dr_cr_flag != b.dr_cr_flag
                          and (b.peer_acc is null or trim(b.peer_acc) = '')
        where a.equal_flag = '1'
          and b.equal_flag = '1'
          and a.tran_date >= #{startDate}
          and a.tran_date <![CDATA[ <= ]]> #{endDate}
          and b.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
    </select>

    <select id="getDitmDict" resultType="io.ibs.modules.east.entity.dkjjzxx.EastPeerInfo">
        select trim(ditm_no) dfkmbh,trim(ditm_name) dfkmmc from t_srm_ditm_dict
        union
        select trim(itm_no) dfkmbh,trim(itm_name) dfkmmc from t_srm_itm_dict
    </select>

    <select id="getMnaccDitmNoList" resultType="java.lang.String">
        select distinct ditm_no from t_gnl_mnacc_dtl where amt_date >= #{startDate} and amt_date <![CDATA[ <= ]]> #{endDate}
    </select>

    <select id="getMonthDitm" resultType="io.ibs.modules.east.entity.other.DitmNoEntity">
        select mn.inst_no instNo,mn.ditm_no ditmNo from t_gnl_mnacc_dtl mn where mn.amt_date<![CDATA[ >= ]]>#{bgnDate} and mn.amt_date<![CDATA[ <= ]]>#{endDate}
        group by mn.inst_no,mn.ditm_no
        union
        (
        select mn.inst_no instNo,trim(mn.ditm_no) ditmNo from t_gnl_mnacc_dtl mn,
        (select d.inst_no,d.ditm_no,max(d.amt_date) amt_date from t_gnl_mnacc_dtl d,t_srm_inst_mgmt m
        where d.amt_date<![CDATA[ <= ]]>#{endDate} and d.inst_no=m.inst_no and m.inst_type in ('4','7') and d.ditm_no in (select distinct mnacc_acc[8,15] from t_gnl_mnacc)
        group by d.inst_no,d.ditm_no) dtl
        where mn.amt_date<![CDATA[ < ]]>#{bgnDate} and dtl.amt_date=mn.amt_date and dtl.inst_no=mn.inst_no and dtl.ditm_no=mn.ditm_no
        )
        union
        (
        select mn.inst_no instNo,mn.mnacc_acc[8,15] ditmNo from t_gnl_mnacc mn,t_srm_inst_mgmt mgmt
        where mn.lst_tran_date<![CDATA[ < ]]>'2013-12-27' and mn.inst_no=mgmt.inst_no and mgmt.inst_type in ('4','7') and (mn.dr_crt_bal>0 or mn.cr_crt_bal>0)
        )
    </select>
    <select id="getDitmNoInMnacc" resultType="io.ibs.modules.east.entity.other.DitmNoEntity">
        select trim(ditm_no) ditmNo,trim(itm_no) itmNo,trim(sub_itm) subItm from t_srm_ditm_dict where ditm_no not in (select mnacc_acc[8,15] from t_gnl_mnacc)
    </select>
</mapper>