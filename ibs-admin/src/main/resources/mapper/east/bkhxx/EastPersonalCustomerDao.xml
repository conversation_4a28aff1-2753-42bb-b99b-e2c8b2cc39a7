<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.bkhxx.EastPersonalCustomerDao">

    <resultMap type="io.ibs.modules.east.entity.bkhxx.EastPersonalCustomerEntity" id="eastPersonalCustomerMap">
        <result property="id" column="ID"/>
        <result property="jrxkzh" column="JRXKZH"/>
        <result property="nbjgh" column="NBJGH"/>
        <result property="khtybh" column="KHTYBH"/>
        <result property="khxm" column="KHXM"/>
        <result property="zjlb" column="ZJLB"/>
        <result property="zjhm" column="ZJHM"/>
        <result property="gxlx" column="GXLX"/>
        <result property="gxrkhtybh" column="GXRKHTYBH"/>
        <result property="gxrmc" column="GXRMC"/>
        <result property="gxrzjlb" column="GXRZJLB"/>
        <result property="gxrzjhm" column="GXRZJHM"/>
        <result property="gxzt" column="GXZT"/>
        <result property="bbz" column="BBZ"/>
        <result property="cjrq" column="CJRQ"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="updater" column="UPDATER"/>
    </resultMap>

    <select id="getDataFormCredit" resultMap="eastPersonalCustomerMap">
        select CSTM_NO khtybh,
        NAME khxm,
        ID_TYPE zjlb,
        ID_NUM zjhm,
        FAM_REL gxlx,
        FAM_MEM_NAME gxrmc,
        FAM_MEM_CERT_TYPE gxrzjlb,
        FAM_MEM_CERT_NUM gxrzjhm,
        FAM_RELA_ASS_FLAG gxzt
        from T_PRSN_ECIF_FAMILY
        <where>
            <if test="startDate != null and startDate != ''">
                UPDATE_TIME &gt;= TO_DATE(#{startDate}, 'yyyyMMdd')
            </if>
            <if test="endDate != null and endDate != ''">
                and UPDATE_TIME &lt;= TO_DATE(#{endDate}, 'yyyyMMdd')
            </if>
        </where>
    </select>
</mapper>