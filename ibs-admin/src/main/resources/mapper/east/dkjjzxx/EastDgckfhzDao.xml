<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.dkjjzxx.EastDgckfhzDao">
    <resultMap type="io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzEntity" id="eastDgckfhzMap">
        <result property="id" column="ID"/>
        <result property="jrxkzh" column="JRXKZH"/>
        <result property="nbjgh" column="NBJGH"/>
        <result property="yhjgmc" column="YHJGMC"/>
        <result property="mxkmbh" column="MXKMBH"/>
        <result property="mxkmmc" column="MXKMMC"/>
        <result property="khtybh" column="KHTYBH"/>
        <result property="zhmc" column="ZHMC"/>
        <result property="dgckzh" column="DGCKZH"/>
        <result property="dgckzhlx" column="DGCKZHLX"/>
        <result property="bzjzhbz" column="BZJZHBZ"/>
        <result property="ll" column="LL"/>
        <result property="bz" column="BZ"/>
        <result property="ckye" column="CKYE"/>
        <result property="khrq" column="KHRQ"/>
        <result property="khgyh" column="KHGYH"/>
        <result property="xhrq" column="XHRQ"/>
        <result property="scdhrq" column="SCDHRQ"/>
        <result property="chlb" column="CHLB"/>
        <result property="zhzt" column="ZHZT"/>
        <result property="bbz" column="BBZ"/>
        <result property="cjrq" column="CJRQ"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="getGrckfhzOfSavingDeposite" resultType="io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzEntity">
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.cstm_name) ZHMC,trim(led.acc) DGCKZH,svt.cal_flag DGCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.open_date KHRQ,led.open_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_sdm_dtl dtl where dtl.acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} and dtl.tran_code not in ('6149', '0207')) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[1]||led.flag[3] ZHZT
        from t_sdm_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.open_date <![CDATA[ <= ]]>#{endDate} and led.flag[1]!='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>#{endDate})
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.cstm_name) ZHMC,trim(led.acc) DGCKZH,svt.cal_flag DGCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.open_date KHRQ,led.open_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_sdm_dtl dtl where dtl.acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} and dtl.tran_code not in ('6149', '0207')) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[1]||led.flag[3] ZHZT
        from t_sdm_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.cls_date <![CDATA[ >= ]]>#{bgnDate} and led.cls_date <![CDATA[ <= ]]>#{endDate} and led.flag[1]='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>#{endDate})
        )
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.cstm_name) ZHMC,trim(led.acc) DGCKZH,svt.cal_flag DGCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.open_date KHRQ,led.open_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_sdm_dtl dtl where dtl.acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} and dtl.tran_code not in ('6149', '0207')) SCDHRQ,
        '' XHRQ,'0'||led.flag[1]||led.flag[3] ZHZT
        from t_sdm_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.open_date <![CDATA[ <= ]]>#{endDate} and led.cls_date <![CDATA[ > ]]>#{endDate} and led.flag[1]='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>#{endDate})
        )
    </select>

    <select id="getGrckfhzOfTimeDeposite" resultType="io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzEntity">
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.acc_name) ZHMC,trim(led.acc) DGCKZH,svt.cal_flag DGCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[3]||led.flag[4] ZHZT
        from t_fix_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]!='1' and led.cstm_no[5]='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.acc_name) ZHMC,trim(led.acc) DGCKZH,svt.cal_flag DGCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[3]||led.flag[4] ZHZT
        from t_fix_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.cls_date <![CDATA[ >= ]]>#{bgnDate} and led.cls_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]='1' and led.cstm_no[5]='1'
        and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
        )
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.acc_name) ZHMC,trim(led.acc) DGCKZH,svt.cal_flag DGCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        '' XHRQ,led.flag[3]||led.flag[4] ZHZT
        from t_fix_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.cls_date <![CDATA[ > ]]>#{endDate} and led.flag[3]='1' and led.cstm_no[5]='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
        )
    </select>

    <!-- 查询单位保证金存款账户-->
    <select id="getGrckfhzOfEarnestMoney" resultType="io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzEntity">
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,led.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,trim(led.acc) KHTYBH,
               trim(led.acc_name) ZHMC,trim(led.acc) DGCKZH,'单位保证金存款' DGCKZHLX,'0' LL,'CNY' BZ,led.aval_bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
               (select to_char(max(dtl.date),'%Y%m%d') from t_inn_dtl dtl where dtl.acc = led.acc and dtl.date <![CDATA[ <= ]]>#{endDate} and dtl.tran_no not in ('6149', '0207')) SCDHRQ,
               to_char(led.cls_date,'%Y%m%d') XHRQ,led.acc_stat_fld[1]||led.acc_stat_fld[3] ZHZT
        from t_inn_ledger led,t_srm_inst_mgmt mgmt,t_srm_ditm_dict dict
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.acc_stat_fld[1]!='1' and led.inst_no=mgmt.inst_no and led.ditm_no=dict.ditm_no and led.ditm_no  like '2511%'  and led.aval_bal>0
        union
        (select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,led.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,trim(led.acc) KHTYBH,
               trim(led.acc_name) ZHMC,trim(led.acc) DGCKZH,'单位保证金存款' DGCKZHLX,'0' LL,'CNY' BZ,led.aval_bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
                (select to_char(max(dtl.date),'%Y%m%d') from t_inn_dtl dtl where dtl.acc = led.acc and dtl.date <![CDATA[ <= ]]>#{endDate} and dtl.tran_no not in ('6149', '0207')) SCDHRQ,
                to_char(led.cls_date,'%Y%m%d') XHRQ,led.acc_stat_fld[1]||led.acc_stat_fld[3] ZHZT
        from t_inn_ledger led,t_srm_inst_mgmt mgmt,t_srm_ditm_dict dict
        where led.cls_date <![CDATA[ >= ]]>#{bgnDate} and led.cls_date <![CDATA[ <= ]]>#{endDate} and led.acc_stat_fld[1]='1'
          and led.inst_no=mgmt.inst_no and led.ditm_no=dict.ditm_no and led.ditm_no  like '2511%')
        union
        (select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,led.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,trim(led.acc) KHTYBH,
               trim(led.acc_name) ZHMC,trim(led.acc) DGCKZH,'单位保证金存款' DGCKZHLX,'0' LL,'CNY' BZ,led.aval_bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
                (select to_char(max(dtl.date),'%Y%m%d') from t_inn_dtl dtl where dtl.acc = led.acc and dtl.date <![CDATA[ <= ]]>#{endDate} and dtl.tran_no not in ('6149', '0207')) SCDHRQ,
                to_char(led.cls_date,'%Y%m%d') XHRQ,led.acc_stat_fld[1]||led.acc_stat_fld[3] ZHZT
        from t_inn_ledger led,t_srm_inst_mgmt mgmt,t_srm_ditm_dict dict
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.cls_date <![CDATA[ > ]]>#{endDate} and led.acc_stat_fld[1]='1'
          and led.inst_no=mgmt.inst_no and led.ditm_no=dict.ditm_no and led.ditm_no  like '2511%')
    </select>

    <select id="getBalUntilDay" resultType="io.ibs.modules.east.entity.other.AccBalEntity">
        select trim(sdtl.acc) acc,sdtl.bal,sdtl.amt,sdtl.tran_code trancode from t_sdm_dtl sdtl,
        table(multiset(select sdmdtl.acc,max(sdmdtl.tran_date) tran_date,max(sdmdtl.host_seqno) host_seqno from t_sdm_dtl sdmdtl,
        table(multiset(select led.acc,max(dtl.tran_date) trandate from t_sdm_ledger led,t_sdm_dtl dtl
        where led.flag[1]!='1' and led.acc=dtl.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc)) d
        where sdmdtl.acc=d.acc and sdmdtl.tran_date=d.trandate group by sdmdtl.acc)) dl
        where sdtl.acc=dl.acc and sdtl.tran_date=dl.tran_date and sdtl.host_seqno=dl.host_seqno
        UNION
        (
        select trim(sdtl.acc) acc,sdtl.bal,sdtl.amt,sdtl.tran_code trancode from t_sdm_dtl sdtl,
        table(multiset(select sdmdtl.acc,max(sdmdtl.tran_date) tran_date,max(sdmdtl.host_seqno) host_seqno from t_sdm_dtl sdmdtl,
        table(multiset(select led.acc,max(dtl.tran_date) trandate from t_sdm_ledger led,t_sdm_dtl dtl
        where led.flag[1]='1' and led.cls_date <![CDATA[ > ]]>#{endDate} and led.acc=dtl.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc)) d
        where sdmdtl.acc=d.acc and sdmdtl.tran_date=d.trandate group by sdmdtl.acc)) dl
        where sdtl.acc=dl.acc and sdtl.tran_date=dl.tran_date and sdtl.host_seqno=dl.host_seqno
        )
        UNION
        (
        select trim(fdtl.fix_inn_acc) acc,fdtl.bal,fdtl.amt,fdtl.tran_no trancode from t_fix_dtl fdtl,
        table(multiset(select fixdtl.fix_inn_acc,max(fixdtl.tran_date) tran_date,max(fixdtl.tran_time) trantime from t_fix_dtl fixdtl,
        table(multiset(select led.acc,max(dtl.tran_date) trandate from t_fix_ledger led,t_fix_dtl dtl
        where led.flag[3]='0' and led.op_date <![CDATA[ <= ]]>#{endDate} and led.cstm_no[5]='1' and led.acc=dtl.fix_inn_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc)) d
        where fixdtl.fix_inn_acc=d.acc and fixdtl.tran_date=d.trandate group by fixdtl.fix_inn_acc)) dl
        where fdtl.fix_inn_acc=dl.fix_inn_acc and fdtl.tran_date=dl.tran_date and fdtl.tran_time=dl.trantime
        )
        UNION
        (
        select trim(fdtl.fix_inn_acc) acc,fdtl.bal,fdtl.amt,fdtl.tran_no trancode from t_fix_dtl fdtl,
        table(multiset(select fixdtl.fix_inn_acc,max(fixdtl.tran_date) tran_date,max(fixdtl.tran_time) trantime from t_fix_dtl fixdtl,
        table(multiset(select led.acc,max(dtl.tran_date) trandate from t_fix_ledger led,t_fix_dtl dtl
        where led.flag[3]!='0' and led.cstm_no[5]='1' and led.op_date <![CDATA[ <= ]]>#{endDate} and led.cls_date <![CDATA[ > ]]>#{endDate} and led.acc=dtl.fix_inn_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc)) d
        where fixdtl.fix_inn_acc=d.acc and fixdtl.tran_date=d.trandate group by fixdtl.fix_inn_acc)) dl
        where fdtl.fix_inn_acc=dl.fix_inn_acc and fdtl.tran_date=dl.tran_date and fdtl.tran_time=dl.trantime
        )
        UNION
        (
        select trim(ind.acc) acc,ind.crt_bal bal,ind.amt,ind.tran_no trancode from t_inn_dtl ind,
              table(multiset(select inndtl.acc,max(inndtl.time) mtime,max(host_seqno) maxhostseqno from t_inn_dtl inndtl,
                  table(multiset(select dtl.acc,max(dtl.time) maxtime from t_inn_dtl dtl,t_inn_ledger led
                  where dtl.date <![CDATA[ <= ]]>#{endDate} and dtl.ditm_no like '2511%' and led.aval_bal>0 and dtl.acc=led.acc and dtl.inst_no in ('0001','0002','0003') and led.acc_stat_fld[1]='0' and led.op_date <![CDATA[ <= ]]>#{endDate} group by dtl.acc)) d
              where inndtl.acc=d.acc and inndtl.time=d.maxtime group by inndtl.acc))dt
        where ind.acc=dt.acc and ind.time=dt.mtime and ind.host_seqno=dt.maxhostseqno
        )
        UNION
        (
        select trim(ind.acc) acc,ind.crt_bal bal,ind.amt,ind.tran_no trancode from t_inn_dtl ind,
              table(multiset(select inndtl.acc,max(inndtl.time) mtime,max(host_seqno) maxhostseqno from t_inn_dtl inndtl,
                  table(multiset(select dtl.acc,max(dtl.time) maxtime from t_inn_dtl dtl,t_inn_ledger led
                  where dtl.date <![CDATA[ <= ]]>#{endDate} and dtl.ditm_no like '2511%' and led.aval_bal=0 and dtl.acc=led.acc and dtl.inst_no in ('0001','0002','0003') and led.acc_stat_fld[1]='1' and led.op_date <![CDATA[ <= ]]> #{endDate} and led.cls_date <![CDATA[ > ]]> #{endDate} group by dtl.acc)) d
              where inndtl.acc=d.acc and inndtl.time=d.maxtime group by inndtl.acc))dt
        where ind.acc=dt.acc and ind.time=dt.mtime and ind.host_seqno=dt.maxhostseqno
        )
    </select>

    <select id="getTempTimeDeposite" resultType="io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzEntity">
        select led.inst_no NBJGH,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
               trim(led.acc_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,ddtl.bal CKYE,ddtl.op_date KHRQ,led.op_tlr KHGYH,
               (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
               '' XHRQ,led.flag[3]||led.flag[4] ZHZT
        from t_fix_ledger led,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate,
             table(multiset((
                 select dt.fix_inn_acc acc,dt.tran_date op_date,dt.amt bal from t_fix_dtl dt,
             table(multiset(select dtl.fix_inn_acc,min(tran_date) tran_date,min(dtl.tran_time) trantime from t_fix_ledger led,t_fix_dtl dtl
        where led.acc in ('88880130003016834','88880130007398691') and led.acc=dtl.fix_inn_acc and led.op_date>dtl.tran_date group by dtl.fix_inn_acc)) d
                 where dt.fix_inn_acc=d.fix_inn_acc and dt.tran_time=d.trantime
                 union
                 (
                     select led.acc,led.op_date,tmpdtl.amt bal from t_fix_ledger led,
                     table(multiset(select fix_inn_acc,amt from t_fix_dtl dtl where dtl.peer_acc='88880130004191157' and tran_no='0362')) tmpdtl
                     where led.acc=tmpdtl.fix_inn_acc
                 )
             ))) ddtl
        where led.acc=ddtl.acc and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
          and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
          and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>ddtl.op_date)
    </select>

    <!-- 获取因自动转存开户日期改变的账户-->
    <select id="getTDOfAutoTransfer" resultType="io.ibs.modules.east.entity.dkjjzxx.EastDgckfhzEntity">
        select led.inst_no NBJGH,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,trim(led.acc_name) ZHMC,
        trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.op_tlr KHGYH,led.last_tran_date SCDHRQ,'' XHRQ,led.flag[3]||led.flag[4] ZHZT
        from t_fix_ledger led,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.op_date <![CDATA[ > ]]>#{endDate} and led.op_date <![CDATA[ > ]]>(select min(tran_date) from t_fix_dtl d where d.fix_inn_acc=led.acc and d.tran_date <![CDATA[ <= ]]>#{endDate})
        and led.cstm_no[5]='1' and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
    </select>

    <!-- 获取因自动转存开户日期改变的账户既定日期前的余额-->
    <select id="getTDATBal" resultType="io.ibs.modules.east.entity.other.AccBalEntity">
        select trim(dt.fix_inn_acc) acc,dt.tran_date op_date,dt.amt bal from t_fix_dtl dt,
        table(multiset(select dtl.fix_inn_acc,max(tran_date) tran_date,max(dtl.tran_time) trantime from t_fix_ledger led,t_fix_dtl dtl
        where led.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
        and led.acc=dtl.fix_inn_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by dtl.fix_inn_acc)) d
        where dt.fix_inn_acc=d.fix_inn_acc and dt.tran_time=d.trantime
    </select>

    <!-- 获取发生在endDate后的部支，部支后旧帐户被销户余额保持不变，新账户的开户日期是旧帐户的开户日期-->
    <select id="getTDPartDraw" resultType="java.lang.String">
        select trim(led.acc) acc from t_fix_ledger led
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]='0' and led.cstm_no[5]='1'
        and led.op_date <![CDATA[ < ]]>(select tran_date from t_fix_dtl d where d.fix_inn_acc=led.acc and d.tran_no='0343' and d.tran_date <![CDATA[ > ]]>#{endDate})
    </select>
</mapper>