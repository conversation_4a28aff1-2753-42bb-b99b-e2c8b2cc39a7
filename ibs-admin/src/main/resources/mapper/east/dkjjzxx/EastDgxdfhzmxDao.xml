<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.dkjjzxx.EastDgxdfhzmxDao">
    <resultMap type="io.ibs.modules.east.entity.dkjjzxx.EastDgxdfhzmxEntity" id="eastDgxdfhzmxMap">
        <result property="id" column="ID"/>
        <result property="jyxlh" column="JYXLH"/>
        <result property="jrxkzh" column="JRXKZH"/>
        <result property="nbjgh" column="NBJGH"/>
        <result property="ywbljgh" column="YWBLJGH"/>
        <result property="yhjgmc" column="YHJGMC"/>
        <result property="mxkmbh" column="MXKMBH"/>
        <result property="mxkmmc" column="MXKMMC"/>
        <result property="khtybh" column="KHTYBH"/>
        <result property="zhmc" column="ZHMC"/>
        <result property="dkfhzh" column="DKFHZH"/>
        <result property="xdjjh" column="XDJJH"/>
        <result property="hxjyrq" column="HXJYRQ"/>
        <result property="hxjysj" column="HXJYSJ"/>
        <result property="jylx" column="JYLX"/>
        <result property="jyjdbz" column="JYJDBZ"/>
        <result property="bz" column="BZ"/>
        <result property="jyje" column="JYJE"/>
        <result property="zhye" column="ZHYE"/>
        <result property="dfzh" column="DFZH"/>
        <result property="dfhm" column="DFHM"/>
        <result property="dfxh" column="DFXH"/>
        <result property="dfxm" column="DFXM"/>
        <result property="zy" column="ZY"/>
        <result property="jyqd" column="JYQD"/>
        <result property="cbmbz" column="CBMBZ"/>
        <result property="jygyh" column="JYGYH"/>
        <result property="sqgyh" column="SQGYH"/>
        <result property="xzbz" column="XZBZ"/>
        <result property="bbz" column="BBZ"/>
        <result property="cjrq" column="CJRQ"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <!-- 412  对公信贷分户账明细记录	DGXDFHZMX 贷款当日明细帐 -->
    <select id="getLoanDtlList" resultMap="eastDgxdfhzmxMap">
        select (replace(dtl.loan_acc, ' ', '') ||
                to_char(dtl.tran_date, '%Y%m%d') ||
                lpad(abs(dtl.host_seqno), 6, '0')) jyxlh,
               dtl.op_inst                         nbjgh,
               dtl.op_inst                         ywbljgh,
               dtl.ditm_no                         mxkmbh,
               trim(dict.ditm_name)                mxkmmc,
               iou.cstm_no                         khtybh,
               trim(base.cstm_name)                zhmc,
               trim(dtl.loan_acc)                  dkfhzh,
               dtl.loan_no                         xdjjh,
               to_char(dtl.tran_date, '%Y%m%d')    hxjyrq,
               to_char(dtl.tran_time, '%H%M%S')    hxjysj,
               dtl.tran_no                         jylx,
               dtl.dr_cr_flag                      jyjdbz,
               dtl.amt                             jyje,
               dtl.bal                             zhye,
               trim(dtl.peer_acc)                  dfzh,
               ''                                  dfxh,
               trim((
                   case
                       when dtl.peer_acc[1, 7] = '8888011'
                           then (select cdm.cstm_name name from t_cdm_ledger cdm where cdm.acc = dtl.peer_acc)
                       when dtl.peer_acc[1, 7] = '8888012'
                           then (select sdm.cstm_name name from t_sdm_ledger sdm where sdm.acc = dtl.peer_acc)
                       when dtl.peer_acc[1, 7] = '8888013'
                           then (select fix.acc_name name from t_fix_ledger fix where fix.acc = dtl.peer_acc)
                       when dtl.peer_acc[1, 7] = '8888014'
                           then (select led.cstm_name name from t_loan_ledger led where led.loan_acc = dtl.peer_acc)
                       when dtl.peer_acc[1, 3] = '000'
                           then (select inn.acc_name name from t_inn_ledger inn where inn.acc = dtl.peer_acc)
                       else ''
                       end
                   ))                              dfhm,
               ''                                  dfxm,
               trim(dtl.summ)                      zy,
               '柜面'                              jyqd,
               dtl.flag[1]                         cbmbz,
               dtl.tlr                             jygyh,
               dtl.csh_tsf_flag                    xzbz,
               'CNY'                               bz
        from t_loan_dtl dtl,
             t_loan_iou iou,
             t_cstm_corp_base base,
             t_srm_ditm_dict dict
        where dtl.tran_date <![CDATA[ >= ]]> #{startDate}
          and dtl.tran_date <![CDATA[ <= ]]> #{endDate}
          and iou.cstm_no[5] = '1'
          and dtl.loan_no = iou.loan_no
          and base.cstm_no = iou.cstm_no
          and dtl.ditm_no = dict.ditm_no
    </select>

    <!-- 412  对公信贷分户账明细记录	DGXDFHZMX 还息明细登记簿 -->
    <select id="getRetIntDtlList" resultMap="eastDgxdfhzmxMap">
        select (replace(dtl.loan_acc, ' ', '') ||
                to_char(dtl.acc_date, '%Y%m%d') ||
                lpad(abs(dtl.host_seqno), 6, '0')) jyxlh,
               dtl.inst_no                         nbjgh,
               dtl.inst_no                         ywbljgh,
               dtl.ditm_no                         mxkmbh,
               trim(dict.ditm_name)                mxkmmc,
               iou.cstm_no                         khtybh,
               trim(base.cstm_name)                zhmc,
               trim(dtl.loan_acc)                  dkfhzh,
               dtl.loan_no                         xdjjh,
               to_char(dtl.ret_date, '%Y%m%d')     hxjyrq,
               nvl(to_char(
                           nvl(
                                   (select idtl.time
                                    from t_inn_dtl idtl
                                    where idtl.acc = dtl.holdint_acc
                                      and idtl.date = acc_date
                                      and idtl.host_seqno = dtl.host_seqno),
                                   (select idtl.time
                                    from t_inn_dtl idtl
                                    where idtl.acc = dtl.recvint_acc
                                      and idtl.date = acc_date
                                      and idtl.host_seqno = dtl.host_seqno)
                           ), '%H%M%S')
                   , '000000')                     hxjysj,
               dtl.tran_code                       jylx,
               '2'                                 jyjdbz,
               dtl.ret_int                         jyje,
               (select bal
                from t_loan_dtl,
                     (select max(tran_time) trantime
                      from t_loan_dtl
                      where loan_acc = dtl.loan_acc
                        and to_char(tran_time, '%Y%m%d')  <![CDATA[ <= ]]> #{endDate}) a
                where loan_acc = dtl.loan_acc
                  and host_seqno = (select max(host_seqno)
                                    from t_loan_dtl
                                    where loan_acc = dtl.loan_acc
                                      and tran_time = a.trantime)
                  and tran_time = a.trantime)      zhye,
               trim(dtl.recvint_acc)               dfzh,
               trim((
                   case
                       when dtl.recvint_acc[1, 7] = '8888011'
                           then (select cdm.cstm_name name from t_cdm_ledger cdm where cdm.acc = dtl.recvint_acc)
                       when dtl.recvint_acc[1, 7] = '8888012'
                           then (select sdm.cstm_name name from t_sdm_ledger sdm where sdm.acc = dtl.recvint_acc)
                       when dtl.recvint_acc[1, 7] = '8888013'
                           then (select fix.acc_name name from t_fix_ledger fix where fix.acc = dtl.recvint_acc)
                       when dtl.recvint_acc[1, 7] = '8888014'
                           then (select led.cstm_name name from t_loan_ledger led where led.loan_acc = dtl.recvint_acc)
                       when dtl.recvint_acc[1, 3] = '000'
                           then (select inn.acc_name name from t_inn_ledger inn where inn.acc = dtl.recvint_acc)
                       else ''
                       end
                   ))                              dfhm,
               ''                                  dfxh,
               ''                                  dfxm,
               ''                                  zy,
               '柜面'                              jyqd,
               ''                                  cbmbz,
               SUBSTR(dtl.clt_seqno, 1, 6)         jygyh,
               ''                                  sqgyh,
               dtl.flag[5]                         xzbz,
               'CNY'                               bz
        from t_loan_retint_dtl dtl,
             t_loan_iou iou,
             t_srm_ditm_dict dict,
             t_cstm_corp_base base
        where dtl.ret_date <![CDATA[ >= ]]> #{startDate}
          and dtl.ret_date <![CDATA[ <= ]]> #{endDate}
          and iou.cstm_no[5] = '1'
          and dtl.loan_no = iou.loan_no
          and dtl.ditm_no = dict.ditm_no
          and base.cstm_no = iou.cstm_no
    </select>
</mapper>