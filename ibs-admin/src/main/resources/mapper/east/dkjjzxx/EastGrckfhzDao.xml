<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.dkjjzxx.EastGrckfhzDao">

    <resultMap type="io.ibs.modules.east.entity.dkjjzxx.EastGrckfhzEntity" id="eastGrckfhzMap">
        <result property="id" column="ID"/>
        <result property="jrxkzh" column="JRXKZH"/>
        <result property="nbjgh" column="NBJGH"/>
        <result property="yhjgmc" column="YHJGMC"/>
        <result property="mxkmbh" column="MXKMBH"/>
        <result property="mxkmmc" column="MXKMMC"/>
        <result property="khtybh" column="KHTYBH"/>
        <result property="zhmc" column="ZHMC"/>
        <result property="grckzh" column="GRCKZH"/>
        <result property="grckzhlx" column="GRCKZHLX"/>
        <result property="bzjzhbz" column="BZJZHBZ"/>
        <result property="ll" column="LL"/>
        <result property="bz" column="BZ"/>
        <result property="ckye" column="CKYE"/>
        <result property="khrq" column="KHRQ"/>
        <result property="khgyh" column="KHGYH"/>
        <result property="scdhrq" column="SCDHRQ"/>
        <result property="xhrq" column="XHRQ"/>
        <result property="chlb" column="CHLB"/>
        <result property="zhzt" column="ZHZT"/>
        <result property="bbz" column="BBZ"/>
        <result property="cjrq" column="CJRQ"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getGrckfhzOfSavingDeposite" resultType="io.ibs.modules.east.entity.dkjjzxx.EastGrckfhzEntity">
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.cstm_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.open_date KHRQ,led.open_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_cdm_dtl dtl where dtl.dm_out_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} and dtl.tran_code not in ('6149', '0207')) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[3]||led.flag[4]||led.flag[5] ZHZT
        from t_cdm_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.open_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]!='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>#{endDate})
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.cstm_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.open_date KHRQ,led.open_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_cdm_dtl dtl where dtl.dm_out_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} and dtl.tran_code not in ('6149', '0207')) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[3]||led.flag[4]||led.flag[5] ZHZT
        from t_cdm_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.cls_date <![CDATA[ >= ]]>#{bgnDate} and led.cls_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>#{endDate})
        )
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.cstm_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.open_date KHRQ,led.open_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_cdm_dtl dtl where dtl.dm_out_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} and dtl.tran_code not in ('6149', '0207')) SCDHRQ,
        '' XHRQ,'0'||led.flag[4]||led.flag[5] ZHZT
        from t_cdm_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.open_date <![CDATA[ <= ]]>#{endDate} and led.cls_date <![CDATA[ > ]]>#{endDate} and led.flag[3]='1' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>#{endDate})
        )
    </select>

    <select id="getGrckfhzOfTimeDeposite" resultType="io.ibs.modules.east.entity.dkjjzxx.EastGrckfhzEntity">
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.acc_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[3]||led.flag[4]||led.flag[5] ZHZT
        from t_fix_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]!='1' and led.cstm_no[5]='0' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.acc_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        to_char(led.cls_date,'%Y%m%d') XHRQ,led.flag[3]||led.flag[4]||led.flag[5] ZHZT
        from t_fix_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.cls_date <![CDATA[ >= ]]>#{bgnDate} and led.cls_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]='1' and led.cstm_no[5]='0'
        and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
        )
        union
        (
        select led.inst_no NBJGH,trim(mgmt.name) YHJGMC,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.acc_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.bal CKYE,led.op_date KHRQ,led.op_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        '' XHRQ,'0'||led.flag[4]||led.flag[5] ZHZT
        from t_fix_ledger led,t_srm_inst_mgmt mgmt,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.cls_date <![CDATA[ > ]]>#{endDate} and led.flag[3]='1' and led.cstm_no[5]='0' and led.inst_no=mgmt.inst_no and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
        )
    </select>

    <select id="getBalUntilDay" resultType="io.ibs.modules.east.entity.other.AccBalEntity">
        select trim(cdtl.dm_out_acc) acc,
            case
                when cdtl.dm_out_acc in (
                     select dm_out_acc
                          from (select dm_out_acc,max(tran_date) max_date
                                    from t_cdm_dtl group by  dm_out_acc ) where max_date <![CDATA[ < ]]>#{endDate})
                then dl.ledbal
                else cdtl.bal
            end bal,
            cdtl.amt,
            cdtl.tran_code trancode
        from t_cdm_dtl cdtl,
        table(multiset(select cdmdtl.dm_out_acc,max(cdmdtl.tran_date) tran_date,max(cdmdtl.host_seqno) host_seqno,ledbal from t_cdm_dtl cdmdtl,
        table(multiset(select led.acc,max(dtl.tran_date) trandate,led.bal ledbal from t_cdm_ledger led,t_cdm_dtl dtl
        where led.flag[3]!='1' and led.acc=dtl.dm_out_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc, led.bal)) d
        where cdmdtl.dm_out_acc=d.acc and cdmdtl.tran_date=d.trandate group by cdmdtl.dm_out_acc, ledbal)) dl
        where cdtl.dm_out_acc=dl.dm_out_acc and cdtl.tran_date=dl.tran_date and cdtl.host_seqno=dl.host_seqno
        UNION
        (
        select trim(cdtl.dm_out_acc) acc,cdtl.bal,cdtl.amt,cdtl.tran_code trancode from t_cdm_dtl cdtl,
            table(multiset(select cdmdtl.dm_out_acc,max(cdmdtl.tran_date) tran_date,max(cdmdtl.host_seqno) host_seqno from t_cdm_dtl cdmdtl,
                table(multiset(select led.acc,max(dtl.tran_date) trandate from t_cdm_ledger led,t_cdm_dtl dtl
                 where led.flag[3]='1' and led.cls_date <![CDATA[ > ]]>#{endDate} and led.acc=dtl.dm_out_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc)) d
            where cdmdtl.dm_out_acc=d.acc and cdmdtl.tran_date=d.trandate group by cdmdtl.dm_out_acc)) dl
        where cdtl.dm_out_acc=dl.dm_out_acc and cdtl.tran_date=dl.tran_date and cdtl.host_seqno=dl.host_seqno
        )
        UNION
        (
        select trim(fdtl.fix_inn_acc) acc,fdtl.bal,fdtl.amt,fdtl.tran_no trancode from t_fix_dtl fdtl,
            table(multiset(select fixdtl.fix_inn_acc,max(fixdtl.tran_date) tran_date,max(fixdtl.tran_time) trantime from t_fix_dtl fixdtl,
                table(multiset(select led.acc,max(dtl.tran_date) trandate from t_fix_ledger led,t_fix_dtl dtl
                 where led.flag[3]='0' and led.op_date <![CDATA[ <= ]]>#{endDate} and led.cstm_no[5]='0' and led.acc=dtl.fix_inn_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc)) d
            where fixdtl.fix_inn_acc=d.acc and fixdtl.tran_date=d.trandate group by fixdtl.fix_inn_acc)) dl
        where fdtl.fix_inn_acc=dl.fix_inn_acc and fdtl.tran_date=dl.tran_date and fdtl.tran_time=dl.trantime
        )
        UNION
        (
        select trim(fdtl.fix_inn_acc) acc,fdtl.bal,fdtl.amt,fdtl.tran_no trancode from t_fix_dtl fdtl,
           table(multiset(select fixdtl.fix_inn_acc,max(fixdtl.tran_date) tran_date,max(fixdtl.tran_time) trantime from t_fix_dtl fixdtl,
                table(multiset(select led.acc,max(dtl.tran_date) trandate from t_fix_ledger led,t_fix_dtl dtl
                 where led.flag[3]!='0' and led.cstm_no[5]='0' and led.op_date <![CDATA[ <= ]]>#{endDate} and led.cls_date <![CDATA[ > ]]>#{endDate} and led.acc=dtl.fix_inn_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by led.acc)) d
            where fixdtl.fix_inn_acc=d.acc and fixdtl.tran_date=d.trandate group by fixdtl.fix_inn_acc)) dl
        where fdtl.fix_inn_acc=dl.fix_inn_acc and fdtl.tran_date=dl.tran_date and fdtl.tran_time=dl.trantime
        )
    </select>

    <select id="getTempTimeDeposite" resultType="io.ibs.modules.east.entity.dkjjzxx.EastGrckfhzEntity">
        select led.inst_no NBJGH,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,
        trim(led.acc_name) ZHMC,trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,ddtl.bal CKYE,ddtl.op_date KHRQ,led.op_tlr KHGYH,
         (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        '' XHRQ,'0'||led.flag[4]||led.flag[5] ZHZT
        from t_fix_ledger led,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate,
        table(multiset((
        select dt.fix_inn_acc acc,dt.tran_date op_date,dt.amt bal from t_fix_dtl dt,
        table(multiset(select dtl.fix_inn_acc,min(tran_date) tran_date,min(dtl.tran_time) trantime from t_fix_ledger led,t_fix_dtl dtl
        where led.acc in ('88880130003016834','88880130007398691') and led.acc=dtl.fix_inn_acc and led.op_date>dtl.tran_date group by dtl.fix_inn_acc)) d
        where dt.fix_inn_acc=d.fix_inn_acc and dt.tran_time=d.trantime
        union
        (
        select led.acc,led.op_date,tmpdtl.amt bal from t_fix_ledger led,
        table(multiset(select fix_inn_acc,amt from t_fix_dtl dtl where dtl.peer_acc='88880130004191157' and tran_no='0362')) tmpdtl
        where led.acc=tmpdtl.fix_inn_acc
        )
        ))) ddtl
        where led.acc=ddtl.acc and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>ddtl.op_date)
    </select>

    <!-- 获取因自动转存开户日期改变的账户-->
    <select id="getTDOfAutoTransfer" resultType="io.ibs.modules.east.entity.dkjjzxx.EastGrckfhzEntity">
        select led.inst_no NBJGH,svt.ditm_no MXKMBH,trim(dict.ditm_name) MXKMMC,led.cstm_no KHTYBH,trim(led.acc_name) ZHMC,
        trim(led.acc) GRCKZH,svt.cal_flag GRCKZHLX,rate.rate_val LL,led.curr_type BZ,led.op_tlr KHGYH,
        (select to_char(max(dtl.tran_date),'%Y%m%d') from t_fix_dtl dtl where dtl.fix_inn_acc = led.acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate}) SCDHRQ,
        '' XHRQ,'0'||led.flag[4]||led.flag[5] ZHZT
        from t_fix_ledger led,t_pub_svt svt,t_srm_ditm_dict dict,t_pub_prdrate prdrate,t_srm_rate rate
        where led.op_date <![CDATA[ > ]]>#{endDate} and led.op_date <![CDATA[ > ]]>(select min(tran_date) from t_fix_dtl d where d.fix_inn_acc=led.acc and d.tran_date <![CDATA[ <= ]]>#{endDate})
        and led.cstm_no[5]='0' and led.prd_no=svt.prd_no and svt.ditm_no=dict.ditm_no
        and svt.prd_no=prdrate.prd_no and svt.term=prdrate.rate_lev and prdrate.rate_kind='0' and prdrate.curr_type='01'
        and prdrate.rate_no=rate.rate_no and rate.bgn_date=(select max(bgn_date) bgn_date from t_srm_rate where rate_no=prdrate.rate_no and bgn_date <![CDATA[ <= ]]>led.op_date)
    </select>

    <!-- 获取因自动转存开户日期改变的账户既定日期前的余额-->
    <select id="getTDATBal" resultType="io.ibs.modules.east.entity.other.AccBalEntity">
        select trim(dt.fix_inn_acc) acc,dt.tran_date op_date,dt.amt bal from t_fix_dtl dt,
        table(multiset(select dtl.fix_inn_acc,max(tran_date) tran_date,max(dtl.tran_time) trantime from t_fix_ledger led,t_fix_dtl dtl
        where led.acc in
        <foreach item="acc" collection="list" open="(" separator="," close=")">
            #{acc}
        </foreach>
         and led.acc=dtl.fix_inn_acc and dtl.tran_date <![CDATA[ <= ]]>#{endDate} group by dtl.fix_inn_acc)) d
        where dt.fix_inn_acc=d.fix_inn_acc and dt.tran_time=d.trantime
    </select>

    <!-- 获取发生在endDate后的部支，部支后旧帐户被销户余额保持不变，新账户的开户日期是旧帐户的开户日期-->
    <select id="getTDPartDraw" resultType="java.lang.String">
        select trim(led.acc) acc from t_fix_ledger led
        where led.op_date <![CDATA[ <= ]]>#{endDate} and led.flag[3]='0' and led.cstm_no[5]='0'
        and led.op_date <![CDATA[ < ]]>(select tran_date from t_fix_dtl d where d.fix_inn_acc=led.acc and d.tran_no='0343' and d.tran_date <![CDATA[ > ]]>#{endDate})
    </select>

    <select id="getMaxCdmDtlTranDate" resultType="java.lang.String">
        select to_char(max(tran_date), '%Y%m%d')
        from t_cdm_dtl
        where dm_out_acc = #{acc};
    </select>
</mapper>