<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.egxdk.EastDgxdywjjbDao">

    <resultMap type="io.ibs.modules.east.entity.egxdk.EastDgxdywjjbEntity" id="eastDgxdywjjbMap">
        <result property="id" column="ID"/>
        <result property="jrxkzh" column="JRXKZH"/>
        <result property="nbjgh" column="NBJGH"/>
        <result property="yhjgmc" column="YHJGMC"/>
        <result property="mxkmbh" column="MXKMBH"/>
        <result property="mxkmmc" column="MXKMMC"/>
        <result property="khtybh" column="KHTYBH"/>
        <result property="khmc" column="KHMC"/>
        <result property="xdhth" column="XDHTH"/>
        <result property="xdjjh" column="XDJJH"/>
        <result property="dkfhzh" column="DKFHZH"/>
        <result property="xdywzl" column="XDYWZL"/>
        <result property="dkfflx" column="DKFFLX"/>
        <result property="fkfs" column="FKFS"/>
        <result property="bz" column="BZ"/>
        <result property="dkje" column="DKJE"/>
        <result property="dkye" column="DKYE"/>
        <result property="dkwjfl" column="DKWJFL"/>
        <result property="zqs" column="ZQS"/>
        <result property="dqqs" column="DQQS"/>
        <result property="zqcs" column="ZQCS"/>
        <result property="dkffrq" column="DKFFRQ"/>
        <result property="dkdqrq" column="DKDQRQ"/>
        <result property="zjrq" column="ZJRQ"/>
        <result property="qbje" column="QBJE"/>
        <result property="qbrq" column="QBRQ"/>
        <result property="bnqxye" column="BNQXYE"/>
        <result property="bwqxye" column="BWQXYE"/>
        <result property="qxrq" column="QXRQ"/>
        <result property="lxqkqs" column="LXQKQS"/>
        <result property="ljqkqs" column="LJQKQS"/>
        <result property="sbxdjjh" column="SBXDJJH"/>
        <result property="dkrzzh" column="DKRZZH"/>
        <result property="dkrzhm" column="DKRZHM"/>
        <result property="rzzhsshmc" column="RZZHSSHMC"/>
        <result property="lllx" column="LLLX"/>
        <result property="sjll" column="SJLL"/>
        <result property="hkfs" column="HKFS"/>
        <result property="hkzh" column="HKZH"/>
        <result property="hkzhsshmc" column="HKZHSSHMC"/>
        <result property="jxfs" column="JXFS"/>
        <result property="xqhkrq" column="XQHKRQ"/>
        <result property="xqyhbj" column="XQYHBJ"/>
        <result property="xqyhlx" column="XQYHLX"/>
        <result property="jjdkyt" column="JJDKYT"/>
        <result property="dktxdq" column="DKTXDQ"/>
        <result property="dktxhy" column="DKTXHY"/>
        <result property="sfhlwdk" column="SFHLWDK"/>
        <result property="sflsdk" column="SFLSDK"/>
        <result property="sfsndk" column="SFSNDK"/>
        <result property="sfphxsndk" column="SFPHXSNDK"/>
        <result property="sfphxxwqydk" column="SFPHXXWQYDK"/>
        <result property="sfkjdk" column="SFKJDK"/>
        <result property="xdygh" column="XDYGH"/>
        <result property="dkzt" column="DKZT"/>
        <result property="bbz" column="BBZ"/>
        <result property="cjrq" column="CJRQ"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getDgxdywjjbEntityByBussNum" resultMap="eastDgxdywjjbMap" >
        select
            iou.loan_inst NBJGH,
            '' YHJGMC,
            '' MXKMBH,
            '' MXKMMC,
            iou.cstm_no KHTYBH,
            trim(iou.cstm_name) KHMC,
            '' ZJLB,
            base.paper_no ZJHM,
            iou.loan_no XDHTH,
            iou.loan_no XDJJH,
            '' DKFHZH,
            '' XDYWZL,
            '' DKFFLX,
            '自主支付' FKFS,
            'CNY' BZ,
            iou.dlv_guideline DKJE,
            0 DKYE,
            '' DKWJFL,
            0 ZQS,
            0 DQQS,
            iou.ext_num ZQCS,
            to_char(iou.bgnint_date, '%Y%m%d') DKFFRQ,
            to_char(iou.due_date, '%Y%m%d') DKDQRQ,
            to_char(iou.clr_date, '%Y%m%d') ZJRQ,
            (select sum(plan_dlv_totl - plan_recv_totl) from t_loan_ret where loan_no = iou.loan_no and due_date <![CDATA[ <= ]]> to_date(#{endDate}, '%Y%m%d')) QBJE,
            to_char((select min(due_date) from t_loan_ret where loan_no = iou.loan_no and plan_dlv_totl != plan_recv_totl and due_date <![CDATA[ <= ]]> to_date(#{endDate}, '%Y%m%d')), '%Y%m%d') QBRQ,
            (select sum(rb_int - recv_int) from t_loan_int_list where loan_no = iou.loan_no and endint_date <![CDATA[ <= ]]> to_date(#{endDate}, '%Y%m%d')) BNQXYE,
            0 BWQXYE,
            to_char((select min(endint_date) from t_loan_int_list where loan_no = iou.loan_no and rb_int != recv_int and endint_date <![CDATA[ <= ]]> to_date(#{endDate}, '%Y%m%d')), '%Y%m%d') QXRQ,
            0 LXQKQS,
            0 LJQKQS,
            '' SBXDJJH,
            trim(iou.payint_acc) DKRZZH,
            trim(iou.cstm_name) DKRZHM,
            '' RZZHSSHMC,
            'LPR' LLLX,
            (select round(
                 case
                     when a.rate_type = '1'
                         then (a.rate_val / 100 * 360) * ((100 + iou.fratio) / 100)
                     when a.rate_type = '2'
                         then (a.rate_val / 10 * 12) * ((100 + iou.fratio) / 100)
                     else a.rate_val * ((100 + iou.fratio) / 100)
                         end, 4) SJLL
             from t_srm_rate a,
                  (select rate.rate_no,max(rate.bgn_date) rate_date from t_srm_rate rate
                   where iou.rate_no = rate.rate_no and rate.bgn_date <![CDATA[ <= ]]> iou.bgnint_date group by rate.rate_no ) b
             where a.rate_no  = b.rate_no and a.bgn_date = b.rate_date) SJLL,
            '' HKFS,
            trim(iou.payint_acc) HKZH,
            '' HKZHSSHMC,
            iou.int_cal_flag[4]  JXFS,
            '' XQHKRQ,
            0 XQYHBJ,
            0 XQYHLX,
            trim(iou.loan_purp) JJDKYT,
            '520000' DKTXDQ,
            iou.cstm_indus DKTXHY,
            '否' SFHLWDK,
            '否' SFLSDK,
            '否' SFSNDK,
            '否' SFPHXSNDK,
            '否' SFPHXXWQYDK,
            '否' SFKJDK,
            iou.loaner XDYGH,
            '' DKZT,
            '' BBZ,
            concat('', #{endDate}) CJRQ
        from
            t_loan_iou iou left join t_cstm_corp_base base on iou.cstm_no = base.cstm_no
          where iou.loan_no = #{bussNum}
    </select>
</mapper>