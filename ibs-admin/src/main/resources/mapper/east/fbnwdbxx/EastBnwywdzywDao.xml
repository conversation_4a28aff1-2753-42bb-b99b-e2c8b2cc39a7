<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.fbnwdbxx.EastBnwywdzywDao">
    <resultMap type="io.ibs.modules.east.entity.fbnwdbxx.EastBnwywdzywEntity" id="eastBnwywdzywMap">
        <result property="id" column="ID"/>
        <result property="jrxkzh" column="JRXKZH"/>
        <result property="nbjgh" column="NBJGH"/>
        <result property="dbhth" column="DBHTH"/>
        <result property="ypbh" column="YPBH"/>
        <result property="yplx" column="YPLX"/>
        <result property="ypmc" column="YPMC"/>
        <result property="dzywzt" column="DZYWZT"/>
        <result property="bz" column="BZ"/>
        <result property="pgjz" column="PGJZ"/>
        <result property="yxrdjz" column="YXRDJZ"/>
        <result property="ydyjz" column="YDYJZ"/>
        <result property="dzyl" column="DZYL"/>
        <result property="czqsw" column="CZQSW"/>
        <result property="ypsyrmc" column="YPSYRMC"/>
        <result property="ypsyrzjlb" column="YPSYRZJLB"/>
        <result property="ypsyrzjhm" column="YPSYRZJHM"/>
        <result property="zypzhm" column="ZYPZHM"/>
        <result property="zypzqfjg" column="ZYPZQFJG"/>
        <result property="qzdjhm" column="QZDJHM"/>
        <result property="qzdjmj" column="QZDJMJ"/>
        <result property="dbhtzt" column="DBHTZT"/>
        <result property="bbz" column="BBZ"/>
        <result property="cjrq" column="CJRQ"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getBnwywdywData" resultMap="eastBnwywdzywMap">
        select
        sq.ffjg NBJGH,
        case
            when ht.zhsxhth is not null and trim(ht.zhsxhth) != ''
                then ht.zhsxhth
            else ht.htbh
                end DBHTH,
        sq.jjh   YPBH,
        dy.dywzl YPLX,
        dy.dywmc YPMC,
        '正常' DZYWZT,
        'CNY' BZ,
        sum(dy.pgjz) PGJZ,
        sum(dy.pgjz) YXRDJZ,
        0 YDYJZ,
        format(round(sum(dy.dyje) / sum(dy.pgjz), 4) * 100, 2) DZYL,
        '第一顺位' CZQSW,
        case
            when dy.syqrzjh is null  || dy.syqrzjh = ''
                then dy.khmc
            else dy.syqr
            end  YPSYRMC,
        case
            when dy.syqrzjh is null  || dy.syqrzjh = ''
                then dy.zjhm
            else dy.syqrzjh
            end  YPSYRZJHM,
        '' ZYPZHM,
        '' ZYPZQFJG,
        dy.hth QZDJHM,
        '' QZDJMJ,
        '' DBHTZT,
        concat(dy.khh, '#@#', dy.khmc, '#@#', dy.zjhm) BBZ
        FROM khdksq sq
            JOIN (SELECT sqbh, MAX(htbh) AS max_htbh
                 FROM khdkzht
                 GROUP BY sqbh) htbh_max ON sq.sqbh = htbh_max.sqbh
        JOIN khdkzht ht ON htbh_max.sqbh = ht.sqbh AND htbh_max.max_htbh = ht.htbh
        JOIN khdkdydb dy ON dy.sqbh = sq.sqbh AND dy.sqbh = ht.sqbh
        where sq.jjh = #{loanNo}
        group by sq.jjh
    </select>

    <select id="getBnwywzywData" resultMap="eastBnwywdzywMap">
        select
            sq.ffjg NBJGH,
            case
                when ht.zhsxhth is not null and trim(ht.zhsxhth) != ''
                    then ht.zhsxhth
                else ht.htbh
                end DBHTH,
            sq.jjh  YPBH,
            zy.zywzl YPLX,
            zy.zywmc YPMC,
            '正常' DZYWZT,
            'CNY' BZ,
            sum(zy.pgjz) PGJZ,
            sum(zy.pgjz) YXRDJZ,
            0 YDYJZ,
            format(round(sum(zy.zyje) / sum(zy.pgjz), 4) * 100, 2) DZYL,
            '第一顺位' CZQSW,
            case
                when zy.syqrzjh is null  || zy.syqrzjh = ''
                    then zy.khmc
                else zy.syqr
                end  YPSYRMC,
            case
                when zy.syqrzjh is null  || zy.syqrzjh = ''
                    then zy.zjhm
                else zy.syqrzjh
                end  YPSYRZJHM,
            '' ZYPZHM,
            '' ZYPZQFJG,
            zy.hth QZDJHM,
            '' QZDJMJ,
            '' DBHTZT,
            concat(zy.khh, '#@#', zy.khmc, '#@#', zy.zjhm) BBZ
        FROM khdksq sq
                 JOIN (SELECT sqbh, MAX(htbh) AS max_htbh
                       FROM khdkzht
                       GROUP BY sqbh) htbh_max ON sq.sqbh = htbh_max.sqbh
                 JOIN khdkzht ht ON htbh_max.sqbh = ht.sqbh AND htbh_max.max_htbh = ht.htbh
                 JOIN khdkzydb zy ON zy.sqbh = sq.sqbh AND zy.sqbh = ht.sqbh
        where sq.jjh = #{loanNo}
        group by sq.jjh
    </select>
</mapper>