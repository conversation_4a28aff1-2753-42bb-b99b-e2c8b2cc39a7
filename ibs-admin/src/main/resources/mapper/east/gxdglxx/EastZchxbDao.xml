<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.east.dao.gxdglxx.EastZchxbDao">

    <resultMap type="io.ibs.modules.east.entity.gxdglxx.EastZchxbEntity" id="eastZchxbMap">
        <result property="id" column="ID"/>
        <result property="jrxkzh" column="JRXKZH"/>
        <result property="nbjgh" column="NBJGH"/>
        <result property="khtybh" column="KHTYBH"/>
        <result property="khmc" column="KHMC"/>
        <result property="zclx" column="ZCLX"/>
        <result property="hth" column="HTH"/>
        <result property="jjh" column="JJH"/>
        <result property="bz" column="BZ"/>
        <result property="hxbj" column="HXBJ"/>
        <result property="shbnlx" column="SHBNLX"/>
        <result property="shbwlx" column="SHBWLX"/>
        <result property="hxrq" column="HXRQ"/>
        <result property="shbj" column="SHBJ"/>
        <result property="shlx" column="SHLX"/>
        <result property="shrq" column="SHRQ"/>
        <result property="shbz" column="SHBZ"/>
        <result property="shygh" column="SHYGH"/>
        <result property="hxzt" column="HXZT"/>
        <result property="bbz" column="BBZ"/>
        <result property="cjrq" column="CJRQ"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getZchxbData" resultMap="eastZchxbMap">
        select *
        from T_EAST_ZCHXB
        where (HXBJ > SHBJ or (SHBNLX + SHBWLX) > SHLX)
          and (HXRQ <![CDATA[ <= ]]> #{endDate})
          and CJRQ = '20191231'
        union all
        select *
        from T_EAST_ZCHXB
        where HXBJ = SHBJ
          and (SHBNLX + SHBWLX) = SHLX
          and HXRQ <![CDATA[ <= ]]> #{endDate}
          and SHRQ >= #{startDate}
          and CJRQ = '20191231'
    </select>
</mapper>