<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.ecif.etpr.dao.TEtprEcifBaseDao">

    <resultMap type="io.ibs.modules.irp.ecif.etpr.entity.TEtprEcifBaseEntity" id="tEtprEcifBaseMap">
        <result property="cstmNo" column="CSMT_NO"/>
        <result property="instNo" column="INST_NO"/>
        <result property="entName" column="ENT_NAME"/>
        <result property="entCertType" column="ENT_CERT_TYPE"/>
        <result property="entCertNum" column="ENT_CERT_NUM"/>
        <result property="entCertExpDate" column="ENT_CERT_EXP_DATE"/>
        <result property="customerType" column="CUSTOMER_TYPE"/>
        <result property="etpsts" column="ETPSTS"/>
        <result property="orgType" column="ORG_TYPE"/>
        <result property="state" column="STATE"/>
        <result property="creditLvl" column="CREDIT_LVL"/>
        <result property="staffNum" column="STAFF_NUM"/>
        <result property="isIpo" column="IS_IPO"/>
        <result property="assets" column="ASSETS"/>
        <result property="revenue" column="REVENUE"/>
        <result property="capital" column="CAPITAL"/>
        <result property="openBankno" column="OPEN_BANKNO"/>
        <result property="basicAccount" column="BASIC_ACCOUNT"/>
        <result property="openLicenseNo" column="OPEN_LICENSE_NO"/>
        <result property="entType" column="ENT_TYPE"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="updator" column="UPDATOR"/>
        <result property="lastModifyDate" column="LAST_MODIFY_DATE"/>
    </resultMap>

    <update id="updateLoanCustomerType">
        UPDATE T_ETPR_ECIF_BASE SET CUSTOMER_TYPE = '2'
        WHERE CSTM_NO IN
        <foreach item="cstmNo" collection="list" open="(" separator="," close=")">
            #{cstmNo}
        </foreach>
        AND CUSTOMER_TYPE!='2'
    </update>

    <update id="updateBasicAccCustomerType">
        UPDATE T_ETPR_ECIF_BASE SET CUSTOMER_TYPE = '1'
        WHERE CSTM_NO in
        <foreach item="cstmNo" collection="list" open="(" separator="," close=")">
            #{cstmNo}
        </foreach>
        AND CUSTOMER_TYPE='X'
    </update>
</mapper>