<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.ecif.prsn.dao.TPrsnEcifBaseDao">

    <resultMap type="io.ibs.modules.irp.ecif.prsn.entity.TPrsnEcifBaseEntity" id="tPrsnEcifBaseMap">
        <result property="cstmNo" column="CSTM_NO"/>
        <result property="instNo" column="INST_NO"/>
        <result property="name" column="NAME"/>
        <result property="idType" column="ID_TYPE"/>
        <result property="idNum" column="ID_NUM"/>
        <result property="idEfctDate" column="ID_EFCT_DATE"/>
        <result property="idDueDate" column="ID_DUE_DATE"/>
        <result property="idOrgName" column="ID_ORG_NAME"/>
        <result property="sex" column="SEX"/>
        <result property="nation" column="NATION"/>
        <result property="birthday" column="BIRTHDAY"/>
        <result property="houseAdd" column="HOUSE_ADD"/>
        <result property="hhDist" column="HH_DIST"/>
        <result property="cellPhone" column="CELL_PHONE"/>
        <result property="email" column="EMAIL"/>
        <result property="dutyFlag" column="DUTY_FLAG"/>
        <result property="customerType" column="CUSTOMER_TYPE"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="updator" column="UPDATOR"/>
    </resultMap>

    <update id="updateCustomerType">
        UPDATE T_PRSN_ECIF_BASE SET CUSTOMER_TYPE = '11'
        WHERE CSTM_NO IN
        <foreach item="cstmNo" collection="list" open="(" separator="," close=")">
            #{cstmNo}
        </foreach>
        AND CUSTOMER_TYPE!='11'
    </update>
</mapper>