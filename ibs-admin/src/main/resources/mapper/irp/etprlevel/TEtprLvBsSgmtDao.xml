<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.etprlevel.dao.TEtprLvBsSgmtDao">

    <resultMap type="io.ibs.modules.irp.etprlevel.entity.TEtprLvBsSgmtEntity" id="tEtprLvBsSgmtMap">
        <result property="id" column="ID"/>
        <result property="cstmNo" column="CSTM_NO"/>
        <result property="infRecType" column="INF_REC_TYPE"/>
        <result property="entName" column="ENT_NAME"/>
        <result property="entCertType" column="ENT_CERT_TYPE"/>
        <result property="entCertNum" column="ENT_CERT_NUM"/>
        <result property="finStaYear" column="FIN_STA_YEAR"/>
        <result property="sheetType" column="SHEET_TYPE"/>
        <result property="sheetTypeDivide" column="SHEET_TYPE_DIVIDE"/>
        <result property="rptDate" column="RPT_DATE"/>
        <result property="mngmtOrgCode" column="MNGMT_ORG_CODE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <update id="mergeRecord" parameterType="java.util.List">
        merge into T_ETPR_LV_BS_SGMT a
        using
        (select d.* from (
        <foreach collection="list" index="index" item="item" open="" close="" separator="union all">
            SELECT
            #{item.cstmNo,jdbcType=VARCHAR} as cstmNo,
            #{item.infRecType,jdbcType=VARCHAR} as infRecType,
            #{item.entName,jdbcType=VARCHAR} as entName,
            #{item.entCertType,jdbcType=VARCHAR} as entCertType,
            #{item.entCertNum,jdbcType=VARCHAR} as entCertNum,
            #{item.finStaYear,jdbcType=VARCHAR} as finStaYear,
            #{item.sheetType,jdbcType=VARCHAR} as sheetType,
            #{item.sheetTypeDivide,jdbcType=VARCHAR} as sheetTypeDivide,
            #{item.rptDate,jdbcType=VARCHAR} as rptDate,
            #{item.updater,jdbcType=NUMERIC} as updater,
            #{item.updateDate,jdbcType=VARCHAR} as updateDate
            FROM dual
        </foreach>
        ) d) b
        on (b.cstmNo=a.cstm_no)
        when matched then
        update
        <set>
            a.inf_rec_type=b.infRecType,
            a.ent_name=b.entName,
            a.ent_cert_type=b.entCertType,
            a.ent_cert_num=b.entCertNum,
            a.fin_sta_year=b.finStaYear,
            a.sheet_type=b.sheetType,
            a.sheet_type_divide=b.sheetTypeDivide,
            a.rpt_date=b.rptDate,
            a.updater=b.updater,
            a.update_date=b.updateDate
        </set>
        when not matched THEN
        insert (ID,CSTM_NO,INF_REC_TYPE,ENT_NAME,ENT_CERT_TYPE,ENT_CERT_NUM,FIN_STA_YEAR,SHEET_TYPE,SHEET_TYPE_DIVIDE,RPT_DATE,UPDATER,UPDATE_DATE)
        values(cast(dbms_random.value as varchar2(18))*1000000000000000000,b.cstmNo,b.infRecType,b.entName,b.entCertType,b.entCertNum,b.finStaYear,b.sheetType,b.sheetTypeDivide,b.rptDate,b.updater,b.updateDate)
        </update>
</mapper>