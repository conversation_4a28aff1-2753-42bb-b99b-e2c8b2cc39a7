<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.etprlevel.dao.TEtprLvLegRepInfSgmtDao">

    <resultMap type="io.ibs.modules.irp.etprlevel.entity.TEtprLvLegRepInfSgmtEntity" id="tEtprLvLegRepInfSgmtMap">
        <result property="id" column="ID"/>
        <result property="cstmNo" column="CSTM_NO"/>
        <result property="legRepName" column="LEG_REP_NAME"/>
        <result property="legRepIdType" column="LEG_REP_ID_TYPE"/>
        <result property="legRepIdNum" column="LEG_REP_ID_NUM"/>
        <result property="legRepHouseAddStat" column="LEG_REP_HOUSE_ADD_STAT"/>
        <result property="legRepEduLevel" column="LEG_REP_EDU_LEVEL"/>
        <result property="legRepWorkingYears" column="LEG_REP_WORKING_YEARS"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <update id="mergeRecord" parameterType="java.util.List">
        merge into T_ETPR_LV_LEG_REP_INF_SGMT a
        using
        (select d.* from (
        <foreach collection="list" index="index" item="item" open="" close="" separator="union all">
            SELECT
            #{item.cstmNo,jdbcType=VARCHAR} as cstmNo,
            #{item.legRepName,jdbcType=VARCHAR} as legRepName,
            #{item.legRepIdType,jdbcType=VARCHAR} as legRepIdType,
            #{item.legRepIdNum,jdbcType=VARCHAR} as legRepIdNum,
            #{item.updater,jdbcType=NUMERIC} as updater,
            #{item.updateDate,jdbcType=DATE} as updateDate
            FROM dual
        </foreach>
        ) d) b
        on (b.cstmNo=a.cstm_no)
        when matched then
        update
        <set>
        a.leg_rep_name=b.legRepName,
        a.leg_rep_id_type=b.legRepIdType,
        a.leg_rep_id_num=b.legRepIdNum,
        a.updater=b.updater,
        a.update_date=b.updateDate
        </set>
        when not matched then
        insert (ID,CSTM_NO,LEG_REP_NAME,LEG_REP_ID_TYPE,LEG_REP_ID_NUM,UPDATER,UPDATE_DATE)
        values(cast(dbms_random.value as varchar2(18))*1000000000000000000,b.cstmNo,b.legRepName,b.legRepIdType,b.legRepIdNum,b.updater,b.updateDate)
    </update>
</mapper>