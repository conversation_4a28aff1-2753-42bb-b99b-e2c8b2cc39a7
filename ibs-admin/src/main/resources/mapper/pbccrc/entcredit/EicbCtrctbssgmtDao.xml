<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.entcredit.dao.EicbCtrctbssgmtDao">

    <resultMap type="io.ibs.modules.irp.pbccrc.entcredit.entity.EicbCtrctbssgmtEntity" id="eicbCtrctbssgmtMap">
        <result property="id" column="ID"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="name" column="NAME"/>
        <result property="idType" column="ID_TYPE"/>
        <result property="idNum" column="ID_NUM"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="bussDate" column="BUSS_DATE"/>
        <result property="bussNum" column="BUSS_NUM"/>
        <result property="nativeBussNum" column="NATIVE_BUSS_NUM"/>
        <result property="itabGetDate" column="ITAB_GET_DATE"/>
        <result property="custId" column="CUST_ID"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="updator" column="UPDATOR"/>
    </resultMap>
    <update id="updateInstanceId">
        update EICB_CTRCTBSSGMT set PROC_INST_ID = #{instanceId} where id = #{id}
    </update>
</mapper>