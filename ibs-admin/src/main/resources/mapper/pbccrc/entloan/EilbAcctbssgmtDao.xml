<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.entloan.dao.EilbAcctbssgmtDao">

    <resultMap type="io.ibs.modules.irp.pbccrc.entloan.entity.EilbAcctbssgmtEntity" id="eilbAcctbssgmtMap">
        <result property="acctType" column="ACCT_TYPE"/>
        <result property="name" column="NAME"/>
        <result property="id" column="ID"/>
        <result property="idType" column="ID_TYPE"/>
        <result property="idNum" column="ID_NUM"/>
        <result property="custId" column="CUST_ID"/>
        <result property="bussNum" column="BUSS_NUM"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="bussDate" column="BUSS_DATE"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="updator" column="UPDATOR"/>
    </resultMap>
    <update id="updateInstanceId">
        update EILB_ACCTBSSGMT set PROC_INST_ID = #{instanceId} where id = #{id}
    </update>
</mapper>