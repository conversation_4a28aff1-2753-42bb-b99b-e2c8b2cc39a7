<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.entloan.dao.EilhActlbltyinfsgmtDao">

    <resultMap type="io.ibs.modules.irp.pbccrc.entloan.entity.EilhActlbltyinfsgmtEntity" id="eilhActlbltyinfsgmtMap">
        <result property="bussNum" column="BUSS_NUM"/>
        <result property="acctStatus" column="ACCT_STATUS"/>
        <result property="acctBal" column="ACCT_BAL"/>
        <result property="balChgDate" column="BAL_CHG_DATE"/>
        <result property="fiveCate" column="FIVE_CATE"/>
        <result property="fiveCateAdjDate" column="FIVE_CATE_ADJ_DATE"/>
        <result property="pymtPrd" column="PYMT_PRD"/>
        <result property="totOverd" column="TOT_OVERD"/>
        <result property="overdPrinc" column="OVERD_PRINC"/>
        <result property="overdDy" column="OVERD_DY"/>
        <result property="latRpyDate" column="LAT_RPY_DATE"/>
        <result property="latRpyAmt" column="LAT_RPY_AMT"/>
        <result property="latRpyPrinAmt" column="LAT_RPY_PRIN_AMT"/>
        <result property="rpmtType" column="RPMT_TYPE"/>
        <result property="latAgrrRpyDate" column="LAT_AGRR_RPY_DATE"/>
        <result property="latAgrrRpyAmt" column="LAT_AGRR_RPY_AMT"/>
        <result property="nxtAgrrRpyDate" column="NXT_AGRR_RPY_DATE"/>
        <result property="closeDate" column="CLOSE_DATE"/>
        <result property="rptDateCode" column="RPT_DATE_CODE"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="bussDate" column="BUSS_DATE"/>
        <result property="itabGetDate" column="ITAB_GET_DATE"/>
        <result property="id" column="ID"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="updator" column="UPDATOR"/>
        <result property="nativeBussNum" column="NATIVE_BUSS_NUM"/>
    </resultMap>
    <update id="updateInstanceId">
        update EILH_ACTLBLTYINFSGMT set PROC_INST_ID = #{instanceId} where id = #{id}
    </update>
</mapper>