<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.entrep.dao.Eisd2007ispaDao">

    <resultMap type="io.ibs.modules.irp.pbccrc.entrep.entity.Eisd2007ispaEntity" id="eisd2007ispaMap">
        <result property="id" column="ID"/>
        <result property="stat" column="STAT"/>
        <result property="noRpt" column="NO_RPT"/>
        <result property="batchNum" column="BATCH_NUM"/>
        <result property="msgNum" column="MSG_NUM"/>
        <result property="gettime" column="GETTIME"/>
        <result property="operator" column="OPERATOR"/>
        <result property="deptCode" column="DEPT_CODE"/>
        <result property="changeDate" column="CHANGE_DATE"/>
        <result property="bussDate" column="BUSS_DATE"/>
        <result property="rptDate" column="RPT_DATE"/>
        <result property="custId" column="CUST_ID"/>
        <result property="sheetVersion" column="SHEET_VERSION"/>
        <result property="infRecType" column="INF_REC_TYPE"/>
        <result property="entName" column="ENT_NAME"/>
        <result property="entCertType" column="ENT_CERT_TYPE"/>
        <result property="entCertNum" column="ENT_CERT_NUM"/>
        <result property="sheetYear" column="SHEET_YEAR"/>
        <result property="sheetType" column="SHEET_TYPE"/>
        <result property="sheetTypeDivide" column="SHEET_TYPE_DIVIDE"/>
        <result property="auditFirmName" column="AUDIT_FIRM_NAME"/>
        <result property="auditorName" column="AUDITOR_NAME"/>
        <result property="auditTime" column="AUDIT_TIME"/>
        <result property="cimoc" column="CIMOC"/>
        <result property="rptDateCode" column="RPT_DATE_CODE"/>
        <result property="revOfSal" column="REV_OF_SAL"/>
        <result property="cosOfSal" column="COS_OF_SAL"/>
        <result property="busAndOt" column="BUS_AND_OT"/>
        <result property="selExp" column="SEL_EXP"/>
        <result property="genAndAe" column="GEN_AND_AE"/>
        <result property="finExp" column="FIN_EXP"/>
        <result property="impLosAss" column="IMP_LOS_ASS"/>
        <result property="proOrLafcifv" column="PRO_OR_LAFCIFV"/>
        <result property="invInc" column="INV_INC"/>
        <result property="invIncFabace" column="INV_INC_FABACE"/>
        <result property="opePro" column="OPE_PRO"/>
        <result property="nonOpeInc" column="NON_OPE_INC"/>
        <result property="nonOpeExp" column="NON_OPE_EXP"/>
        <result property="nonCurAss" column="NON_CUR_ASS"/>
        <result property="proBefTax" column="PRO_BEF_TAX"/>
        <result property="incTaxExp" column="INC_TAX_EXP"/>
        <result property="netPro" column="NET_PRO"/>
        <result property="basEarPs" column="BAS_EAR_PS"/>
        <result property="dilEarPs" column="DIL_EAR_PS"/>
        <result property="itabGetDate" column="ITAB_GET_DATE"/>
        <result property="ex2" column="EX2"/>
        <result property="ex3" column="EX3"/>
        <result property="ex1" column="EX1"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="updator" column="UPDATOR"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="creator" column="CREATOR"/>
    </resultMap>

</mapper>