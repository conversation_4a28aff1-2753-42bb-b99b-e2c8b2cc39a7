<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.mortgage.dao.MortgageGetDataDao">
    <!-- 获取当天开立的抵质押贷款-->
    <select id="getMortgageOpenInCurrentDay" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageBaseEntity">
        select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,
        i.bgnint_date bgnintDate,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo,i.dlv_guideline loanAmt
        from t_loan_iou i,t_cstm_prsn_base b
        where i.bgnint_date=#{tranDate} and i.loan_type in ('0','2') and i.cstm_no[5]='0' and i.cstm_no=b.cstm_no
        UNION
        (
        select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,
        i.bgnint_date bgnintDate,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo,i.dlv_guideline loanAmt
        from t_loan_iou i,t_cstm_corp_base b
        where i.bgnint_date=#{tranDate} and i.loan_type in ('0','2') and i.cstm_no[5]='1' and i.cstm_no=b.cstm_no
        )
    </select>

    <!-- 获取当天结清的抵质押贷款-->
    <select id="getMortgageEndInCurrentDay" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageBaseEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,i.due_date dueDate,trim(i.cstm_name) cstmName,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo from t_loan_iou i,
            (select loan_no,sum(bal) bal,max(last_tran_date) lastDate from t_loan_ledger group by loan_no ) led,
            (select loan_no,sum(rb_int-recv_int) loanInt,max(last_tran_date) lastTranDate from t_loan_int_list group by loan_no ) loanlist,
            t_cstm_prsn_base b
        where i.loan_type in ('0','2') and i.loan_no=led.loan_no and led.bal=0 and led.loan_no=loanlist.loan_no and loanlist.loanInt=0
        and loanlist.lastTranDate=#{tranDate} and loanlist.lastTranDate<![CDATA[ >= ]]>led.lastDate and i.cstm_no=b.cstm_no
    </select>

    <!-- 获取借据的抵押信息-->
    <select id="getMortgageOfPle" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgagePleEntity">
        select sq.jjh as bussNum,dy.dywzl as pleType,dy.xh as pleCertId,dy.pgjz as pleValue,dy.pgjgmc as valOrgType,str_to_date(if(length(dy.pgsj),dy.pgsj,CURRENT_DATE()),'yyyymmdd') valDate,
        dy.dyr as pleDgorName,dy.zjhm as pleorCertNum,dy.dywmc as pleDesc,
        CASE SUBSTR(dy.khh, 5, 1)
            WHEN '0' THEN '1'
            ELSE '2' END as pleDgorType
        from khdkdydb dy,khdksq sq
        where sq.jjh in
        <foreach item="loanNo" collection="list" open="(" separator="," close=")">
            #{loanNo}
        </foreach>
        and sq.sqbh=dy.sqbh
    </select>

    <!-- 获取借据的质押信息-->
    <select id="getMortgageOfImp" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageImpEntity">
        select sq.jjh as bussNum,zy.zywzl as impType,zy.zyqr as pawnName,zy.zjhm as pawnCertNum,zy.khh as ippc,sum(zy.pgjz) ipmVal,sum(zy.zyje) impAmt
        from khdksq sq,khdkzydb zy
        where sq.jjh in
        <foreach item="loanNo" collection="list" open="(" separator="," close=")">
            #{loanNo}
        </foreach>
        and sq.sqbh=zy.sqbh
        group by sq.jjh,zy.zywzl,zy.zyqr,zy.zjhm,zy.khh
    </select>

    <!-- 获取抵押和质押金额-->
    <select id="getCCAmt" resultType="java.util.Map">
        select sq.jjh as bussNum, sum(dy.dyje) ccAmt, sq.zgedyf as highestFlag, sq.sqbh as sqbh from khdkdydb dy,khdksq sq
        where sq.jjh in
        <foreach item="loanNo" collection="list" open="(" separator="," close=")">
            #{loanNo}
        </foreach>
        and sq.sqbh=dy.sqbh
        group by sq.jjh, sq.zgedyf, sq.sqbh
        UNION
        (
        select sq.jjh as bussNum,sum(zy.zyje) ccAmt, sq.zgedyf as highestFlag, sq.sqbh
        from khdksq sq,khdkzydb zy
        where sq.jjh in
        <foreach item="loanNo" collection="list" open="(" separator="," close=")">
            #{loanNo}
        </foreach>
        and sq.sqbh=zy.sqbh
        group by sq.jjh, sq.zgedyf, sq.sqbh
        )
    </select>

    <select id="getMortgageInFirst" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageBaseEntity">
        select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,i.due_date dueDate,
        i.bgnint_date bgnintDate,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo,i.dlv_guideline loanAmt
        from t_loan_iou i,
            (select distinct loan_no from t_loan_ledger where bal>0) led,
            (select cstm_no,paper_no from t_cstm_prsn_base union select cstm_no,paper_no from t_cstm_corp_base) b
        where i.loan_type in ('0','2') and i.clr_flag='0' and i.loan_no=led.loan_no and i.cstm_no=b.cstm_no
        UNION
        (
        select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,i.due_date dueDate,
        i.bgnint_date bgnintDate,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo,i.dlv_guideline loanAmt
        from t_loan_iou i,
            (select distinct loan_no from t_loan_int_list where rb_int!=recv_int) loanlist,
            (select cstm_no,paper_no from t_cstm_prsn_base union select cstm_no,paper_no from t_cstm_corp_base) b
        where i.loan_type in ('0','2') and i.clr_flag='0' and i.loan_no=loanlist.loan_no and i.cstm_no=b.cstm_no
        )
    </select>
    <select id="getMortgageOpenBetweenTwoDate" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageBaseEntity">
        select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,
               i.bgnint_date bgnintDate,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo,i.dlv_guideline loanAmt
        from t_loan_iou i,t_cstm_prsn_base b
        where i.bgnint_date<![CDATA[ >= ]]>#{bgnDate} and i.bgnint_date<![CDATA[ <= ]]>#{endDate}
          and i.loan_type in ('0','2') and i.cstm_no[5]='0' and i.cstm_no=b.cstm_no
        UNION
        (
            select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,
                   i.bgnint_date bgnintDate,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo,i.dlv_guideline loanAmt
            from t_loan_iou i,t_cstm_corp_base b
            where i.bgnint_date<![CDATA[ >= ]]>#{bgnDate} and i.bgnint_date<![CDATA[ <= ]]>#{endDate}
              and i.loan_type in ('0','2') and i.cstm_no[5]='1' and i.cstm_no=b.cstm_no
        )
    </select>
    <select id="getMortgageEndBetweenTwoDate" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageBaseEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,i.due_date dueDate,trim(i.cstm_name) cstmName,substr(b.paper_no,1,1) paperType,substr(b.paper_no,2) paperNo from t_loan_iou i,
              (select loan_no,sum(bal) bal,max(last_tran_date) lastDate from t_loan_ledger group by loan_no ) led,
              (select loan_no,sum(rb_int-recv_int) loanInt,max(last_tran_date) lastTranDate from t_loan_int_list group by loan_no ) loanlist,
              t_cstm_prsn_base b
        where i.loan_type in ('0','2') and i.loan_no=led.loan_no and led.bal=0 and led.loan_no=loanlist.loan_no and loanlist.loanInt=0
          and loanlist.lastTranDate<![CDATA[ >= ]]>#{bgnDate} and loanlist.lastTranDate<![CDATA[ <= ]]>#{endDate}
          and loanlist.lastTranDate<![CDATA[ >= ]]>led.lastDate and i.cstm_no=b.cstm_no
    </select>

    <!--根据借据号获取抵质押物信息-->
    <select id="getMortgageByBussNum" resultType="io.ibs.modules.irp.pbccrc.mortgage.entity.MortgageBaseEntity">
        select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,i.due_date dueDate,
               i.bgnint_date bgnintDate,substr(prsn.paper_no,1,1) paperType,substr(prsn.paper_no,2) paperNo,i.dlv_guideline loanAmt
        from t_loan_iou i,t_cstm_prsn_base prsn where i.loan_type in ('0','2') and i.loan_no=#{bussNum} and i.cstm_no=prsn.cstm_no
        union
        select i.loan_no loanNo,trim(i.cstm_name) cstmName,i.cstm_no cstmNo,i.loan_type loanType,i.due_date dueDate,
               i.bgnint_date bgnintDate,substr(corp.paper_no,1,1) paperType,substr(corp.paper_no,2) paperNo,i.dlv_guideline loanAmt
        from t_loan_iou i,t_cstm_corp_base corp where i.loan_type in ('0','2') and i.loan_no=#{bussNum} and i.cstm_no=corp.cstm_no
    </select>
</mapper>