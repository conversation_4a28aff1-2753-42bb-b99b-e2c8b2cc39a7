<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.prsncredit.dao.PrsnCreditGetDataDao">

    <!--首次去核心获取个人授信信息
        从个人客户信息表(t_cstm_prsn_base)获取客户号(cstmNo)、客户名称(cstmName)、证件类型(idType)、证件号码(idNum)；
        从贷款户主账表(t_loan_ledger)获取借据号，累加分户指标(ledger_guideline)作为授信额度，累加余额(bal)作为已使用额度；
        从借据登记簿(t_loan_iou)获取起息日(bgnDate)作为额度有效起始日期，取到期日(endDate)作为额度有效终止日期；

        关联条件：
        1.贷款户主账表与借据登记簿关联条件：loan_no
        2.贷款户主账表与个人客户信息表关联条件：cstm_no
        3.借据登记簿与个人客户信息表关联条件：cstm_no

        其他条件：
        1.客户号为个人客户号，即第5位为0
        2.借据为未结清的借据：
          a.借据在贷款户主账表中余额大于0、借据客户号为个人、借据在贷款户主账表中状态为正常
          b.借据在贷款户主账表中余额为0、借据客户号为个人、借据在贷款户主账表中状态为正常，
            但是借据在息清单登记簿(t_loan_int_list)中的应收利息(rb_int)和已收利息(recv_int)不一致，说明该借据本金已还完但是利息没换完，属于未结清借据
    -->
    <select id="getPrsnCreditInfoInFirst" resultType="io.ibs.modules.irp.pbccrc.prsncredit.entity.PrsnCreditGetDataEntity">
        SELECT
            i.loan_no                   bussNum,
            led.cstm_no                 cstmNo,
            trim(cstm.name)             cstmName,
            substr(cstm.paper_no, 1, 1) idType,
            trim(substr(cstm.paper_no, 2))    idNum,
            led.loan_no                 loanNo,
            i.loan_type                 loanType,
            sum(led.ledger_guideline)   credAmt,
            sum(led.bal)                usedCred,
            i.bgnint_date               bgnDate,
            i.due_date                  endDate
        FROM
            t_loan_ledger led,
            t_loan_iou i,
            t_cstm_prsn_base cstm
        WHERE
            led.loan_no = i.loan_no
            AND cstm.cstm_no = led.cstm_no
            AND cstm.cstm_no = i.cstm_no
            AND cstm.cstm_no[5] = '0'
            AND led.loan_no IN (
                SELECT
                    DISTINCT loan_no
                FROM
                    t_loan_ledger
                WHERE
                    bal > 0
                    AND cstm_no[5]= '0'
    <!--            AND flag[3]= '0'-->
            UNION
            SELECT
                DISTINCT led.loan_no
            FROM
                t_loan_ledger led,
                t_loan_int_list loan
            WHERE
                led.bal = 0
    <!--            AND led.flag[3]= '0'-->
                    AND led.cstm_no[5]= '0'
                    AND led.loan_no = loan.loan_no
                    AND loan.rb_int != loan.recv_int)
        GROUP BY
            led.cstm_no,
            cstm.name,
            cstm.paper_no,
            led.loan_no,
            i.loan_type,
            i.bgnint_date,
            i.due_date,
            i.loan_no
        ORDER BY
            led.cstm_no,
            led.loan_no
    </select>

    <!--日间抽取在首次抽取的基础上，去掉未结清限制，抽取指定日期新增借据的授信信息-->
    <select id="getPrsnCreditInfo" resultType="io.ibs.modules.irp.pbccrc.prsncredit.entity.PrsnCreditGetDataEntity">
        SELECT
            i.loan_no                   bussNum,
            led.cstm_no                 cstmNo,
            trim(cstm.name)             cstmName,
            substr(cstm.paper_no, 1, 1) idType,
            trim(substr(cstm.paper_no, 2))    idNum,
            led.loan_no                 loanNo,
            i.loan_type                 loanType,
            sum(led.ledger_guideline)   credAmt,
            sum(led.bal)                usedCred,
            i.bgnint_date               bgnDate,
            i.due_date                  endDate
        FROM
            t_loan_ledger led,
            t_loan_iou i,
            t_cstm_prsn_base cstm
        WHERE
            led.loan_no = i.loan_no
            AND cstm.cstm_no = led.cstm_no
            AND cstm.cstm_no = i.cstm_no
            AND cstm.cstm_no[5] = '0'
            AND i.bgnint_date = #{reportDate}
        GROUP BY
            led.cstm_no,
            cstm.name,
            cstm.paper_no,
            led.loan_no,
            i.loan_type,
            i.bgnint_date,
            i.due_date,
            i.loan_no
        ORDER BY
            led.cstm_no,
            led.loan_no
    </select>
    <select id="getPrsnCreditInfoBetweenTwoDate" resultType="io.ibs.modules.irp.pbccrc.prsncredit.entity.PrsnCreditGetDataEntity">
        SELECT
            i.loan_no                   bussNum,
            led.cstm_no                 cstmNo,
            trim(cstm.name)             cstmName,
            substr(cstm.paper_no, 1, 1) idType,
            trim(substr(cstm.paper_no, 2))    idNum,
            led.loan_no                 loanNo,
            i.loan_type                 loanType,
            sum(led.ledger_guideline)   credAmt,
            sum(led.bal)                usedCred,
            i.bgnint_date               bgnDate,
            i.due_date                  endDate
        FROM
            t_loan_ledger led,
            t_loan_iou i,
            t_cstm_prsn_base cstm
        WHERE
            led.loan_no = i.loan_no
          AND cstm.cstm_no = led.cstm_no
          AND cstm.cstm_no = i.cstm_no
          AND cstm.cstm_no[5] = '0'
          AND i.bgnint_date <![CDATA[ >= ]]>#{bgnDate} and i.bgnint_date<![CDATA[ <= ]]>#{endDate}
        GROUP BY
            led.cstm_no,
            cstm.name,
            cstm.paper_no,
            led.loan_no,
            i.loan_type,
            i.bgnint_date,
            i.due_date,
            i.loan_no
        ORDER BY
            led.cstm_no,
            led.loan_no
    </select>
</mapper>