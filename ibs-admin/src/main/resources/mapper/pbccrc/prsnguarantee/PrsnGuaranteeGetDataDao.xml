<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.prsnguarantee.dao.PrsnGuaranteeGetDataDao">

    <select id="getLoanOfUnclear" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select distinct i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,sum(led.bal) bal,i.bgnint_date bgnintDate,
        i.dlv_guideline loanBal,i.due_date dueDate,i.flag[4] fiveFlag,max(led.last_tran_date) tranDate
        from t_loan_ledger led,t_loan_iou i,t_cstm_prsn_base b
        where led.bal>0.01 and led.flag[3]='0' and led.cstm_no[5]='0' and led.loan_no=i.loan_no and i.loan_type='1' and i.cstm_no=b.cstm_no
        group by i.loan_no,i.cstm_no,i.cstm_name,b.paper_no,i.bgnint_date,i.dlv_guideline,i.due_date,i.flag
        UNION
        (
        select cav.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,cav.bal,i.bgnint_date bgnintDate,
        i.dlv_guideline loanBal,i.due_date dueDate,i.flag[4] fiveFlag,cav.last_tran_date tranDate
        from t_loan_cav cav,t_loan_iou i,t_cstm_prsn_base b
        where cav.bal>0.01 and cav.dlv_totl!=cav.recv_totl and cav.cstm_no[5]='0' and cav.loan_no=i.loan_no and i.loan_type='1'  and i.cstm_no=b.cstm_no
        )
        UNION
        (
        select distinct i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,sum(loan.rb_int-loan.recv_int) bal,i.bgnint_date bgnintDate,
        i.dlv_guideline loanBal,i.due_date dueDate,i.flag[4] fiveFlag,led.last_tran_date tranDate
        from (select loan_no,sum(bal) bal,max(last_tran_date) last_tran_date from t_loan_ledger group by loan_no) led,t_loan_int_list loan,t_loan_iou i,t_cstm_prsn_base b
        where led.bal=0 and led.loan_no=i.loan_no and i.cstm_no[5]='0' and i.loan_type='1' and i.loan_no=loan.loan_no and loan.rb_int!=loan.recv_int and i.cstm_no=b.cstm_no
        group by i.loan_no,i.cstm_no,i.cstm_name,b.paper_no,i.bgnint_date,i.dlv_guideline,i.due_date,led.last_tran_date,i.flag
        )
    </select>

    <select id="getPrsnGuarantee" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnGuaranteeGetDataEntity">
        select
        sq.sqbh,sq.khh,sq.jjh,db.dbfs,db.zrr_dbrxm zrrDbrxm,db.zrr_zjlx zrrZjlx,db.zrr_zjhm zrrZjhm,db.dbgs_xmmc dbgsXmmc,
        db.qy_qymc qyQymc,db.qy_zzjgdm qyZzjgdm,ht.htbh,db.dbgs_bzjbl dbgsBzjbl
        from khdksq sq,khdkbzdb db,khdkzht ht
        where sq.khh IN
        <foreach item="entity" collection="list" open="(" separator="," close=")">
            #{entity.cstmNo}
        </foreach>
        and sq.jjh IN
        <foreach item="entity" collection="list" open="(" separator="," close=")">
            #{entity.loanNo}
        </foreach>
        and sq.sqbh=db.sqbh and sq.sqbh=ht.sqbh
    </select>

    <!-- 当天开立的保证贷款-->
    <select id="getPrsnGuarOpenInCurrentDay" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,i.bgnint_date bgnintDate,i.dlv_guideline loanBal,
        i.due_date dueDate,i.flag[4] fiveFlag,i.bgnint_date tranDate,i.bgnint_date bgnintDate
        from t_loan_iou i,t_cstm_prsn_base b
        where i.bgnint_date=#{tranDate} and i.loan_type='1' and i.cstm_no[5]='0' and i.cstm_no=b.cstm_no
    </select>

    <!-- 当天结清的保证贷款-->
    <select id="getPrsnGuarCloseInCurrentDay" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,i.bgnint_date bgnintDate,i.dlv_guideline loanBal,
               i.due_date dueDate,i.flag[4] fiveFlag,loanlist.accDate tranDate
        from t_loan_iou i,
             table(multiset(select loan_no,sum(bal) bal,max(last_tran_date) lastDate from t_loan_ledger where cstm_no[5]='0' group by loan_no )) led,
             table(multiset(select loan_no,sum(rb_int-recv_int) loanInt,max(acc_date) accDate from t_loan_int_list group by loan_no )) loanlist,
             t_cstm_prsn_base b
        where i.cstm_no[5]='0' and i.loan_type='1' and i.loan_no=led.loan_no and led.bal=0 and led.loan_no=loanlist.loan_no and loanlist.loanInt=0
          and loanlist.accDate <![CDATA[ >= ]]> led.lastDate and loanlist.accDate=#{tranDate} and i.cstm_no=b.cstm_no
    </select>

    <!-- 当天有余额改变的保证贷款-->
    <select id="getPrsnGuarOfBalUpdate" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,i.bgnint_date bgnintDate,i.dlv_guideline bal,
        i.due_date dueDate,dtl.tranDate
        from t_loan_iou i,
        table(multiset(select loan_no,min(bal) bal,max(tran_date) tranDate from t_loan_dtl where tran_date=#{tranDate} and corr_date='1899-12-31' and dr_cr_flag='2' group by loan_no)) dtl,
        t_cstm_prsn_base b
        where i.cstm_no[5]='0' and i.loan_type='1' and i.loan_no=dtl.loan_no and i.cstm_no=b.cstm_no
    </select>

    <!-- 当天有五级分类变化的保证贷款-->
    <!--<select id="getPrsnGuarOfFiveCate" resultType="java.lang.String">
        select bg.jjh loanNo from dhwjflbg bg,khdksq sq,khdkbzdb db
        where bg.clsj=#{tranDate} and substr(bg.khh,5,1)='0' and bg.jjh=sq.jjh and bg.khh=sq.khh and sq.sqbh=db.sqbh
    </select>-->
    <!-- 获取指定日期前未还清本金的保证贷款-->
    <select id="getPrsnGuarOfRetUnclear" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select loan_no loanNo,due_date dueDate from t_loan_ret
        where loan_no in (
          select distinct led.loan_no from t_loan_ledger led,t_loan_iou iou
          where led.cstm_no[5]='0' and led.bal>0.01 and led.loan_no=iou.loan_no and iou.loan_type='1'
        ) and  plan_dlv_totl!=plan_recv_totl and due_date<![CDATA[ < ]]>#{tranDate}
    </select>

    <select id="getPrsnGuarByLoanNo" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,i.bgnint_date bgnintDate,led.bal bal,
        i.due_date dueDate,dtl.tranDate
        from t_loan_iou i,
        (select loan_no,sum(bal) bal from t_loan_ledger where cstm_no[5]='0' and loan_no IN
        <foreach item="loanNo" collection="list" open="(" separator="," close=")">
            #{loanNo}
        </foreach>
        group by loan_no ) led,
        (select loan_no,max(tran_date) tranDate from t_loan_dtl where loan_no IN
        <foreach item="loanNo" collection="list" open="(" separator="," close=")">
            #{loanNo}
        </foreach>
        and corr_date='1899-12-31' and dr_cr_flag='2' group by loan_no) dtl,
        t_cstm_prsn_base b
        where i.cstm_no[5]='0' and i.loan_type='1' and i.loan_no=led.loan_no and i.loan_no=dtl.loan_no and i.cstm_no=b.cstm_no
    </select>

    <!-- 获取指定借据号的还款计划-->
    <select id="getPrsnGuarRetByLoanNo" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select ret.loan_no loanNo,ret.due_date dueDate from t_loan_ret ret
        where ret.loan_no IN
        <foreach item="loanNo" collection="list" open="(" separator="," close=")">
            #{loanNo}
        </foreach>
        and ret.plan_dlv_totl!=ret.plan_recv_totl
    </select>
    <select id="getPrsnGuarOpenBetweenTwoDate" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,i.bgnint_date bgnintDate,i.dlv_guideline loanBal,
               i.due_date dueDate,i.flag[4] fiveFlag,i.bgnint_date tranDate,i.bgnint_date bgnintDate
        from t_loan_iou i,t_cstm_prsn_base b
        where i.bgnint_date <![CDATA[ >= ]]>#{bgnDate} and i.bgnint_date<![CDATA[ <= ]]>#{endDate}
          and i.loan_type='1' and i.cstm_no[5]='0' and i.cstm_no=b.cstm_no
    </select>
    <select id="getPrsnGuarCloseBetweenTwoDate" resultType="io.ibs.modules.irp.pbccrc.prsnguarantee.entity.PrsnLoanUnclearEntity">
        select i.loan_no loanNo,i.cstm_no cstmNo,trim(i.cstm_name) name,b.paper_no paperNo,i.bgnint_date bgnintDate,i.dlv_guideline loanBal,
               i.due_date dueDate,i.flag[4] fiveFlag,loanlist.accDate tranDate
        from t_loan_iou i,
             table(multiset(select loan_no,sum(bal) bal,max(last_tran_date) lastDate from t_loan_ledger where cstm_no[5]='0' group by loan_no )) led,
             table(multiset(select loan_no,sum(rb_int-recv_int) loanInt,max(acc_date) accDate from t_loan_int_list group by loan_no )) loanlist,
             t_cstm_prsn_base b
        where i.cstm_no[5]='0' and i.loan_type='1' and i.loan_no=led.loan_no and led.bal=0 and led.loan_no=loanlist.loan_no and loanlist.loanInt=0
          and loanlist.accDate <![CDATA[ >= ]]> led.lastDate and i.cstm_no=b.cstm_no and loanlist.accDate<![CDATA[ >= ]]>#{bgnDate} and loanlist.accDate<![CDATA[ <= ]]>#{endDate}
    </select>

</mapper>