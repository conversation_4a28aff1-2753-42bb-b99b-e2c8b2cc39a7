<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.prsnloan.dao.AsyPilcAcctbsinfsgmtDao">

    <resultMap type="io.ibs.modules.irp.pbccrc.prsnloan.entity.AsyPilcAcctbsinfsgmtEntity" id="AsypilcAcctbsinfsgmtMap">
                <result property="bussNum" column="BUSS_NUM"/>
                <result property="busiLines" column="BUSI_LINES"/>
                <result property="busiDtlLines" column="BUSI_DTL_LINES"/>
                <result property="openDate" column="OPEN_DATE"/>
                <result property="acctcy" column="ACCTCY"/>
                <result property="acctCredLine" column="ACCT_CRED_LINE"/>
                <result property="loanAmt" column="LOAN_AMT"/>
                <result property="flag" column="FLAG"/>
                <result property="dueDate" column="DUE_DATE"/>
                <result property="repayMode" column="REPAY_MODE"/>
                <result property="repayFreqcy" column="REPAY_FREQCY"/>
                <result property="repayPrd" column="REPAY_PRD"/>
                <result property="applyBusiDist" column="APPLY_BUSI_DIST"/>
                <result property="guarMode" column="GUAR_MODE"/>
                <result property="othRepyGuarWay" column="OTH_REPY_GUAR_WAY"/>
                <result property="assetTrandFlag" column="ASSET_TRAND_FLAG"/>
                <result property="fundSou" column="FUND_SOU"/>
                <result property="creditId" column="CREDIT_ID"/>
                <result property="loanForm" column="LOAN_FORM"/>
                <result property="loanConCode" column="LOAN_CON_CODE"/>
                <result property="firstHouLoanFlag" column="FIRST_HOU_LOAN_FLAG"/>
                <result property="bussDate" column="BUSS_DATE"/>
                <result property="deptCode" column="DEPT_CODE"/>
        <result property="nativeBussNum" column="NATIVE_BUSS_NUM"/>
        <result property="itabGetDate" column="ITAB_GET_DATE"/>
    </resultMap>
</mapper>