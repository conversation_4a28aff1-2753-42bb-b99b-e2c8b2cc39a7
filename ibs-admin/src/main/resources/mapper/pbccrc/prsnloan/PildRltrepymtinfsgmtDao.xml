<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.irp.pbccrc.prsnloan.dao.PildRltrepymtinfsgmtDao">

    <resultMap type="io.ibs.modules.irp.pbccrc.prsnloan.entity.PildRltrepymtinfsgmtEntity" id="pildRltrepymtinfsgmtMap">
        <result property="bussNum" column="BUSS_NUM"/>
        <result property="maxGuarBussNum" column="MAX_GUAR_BUSS_NUM"/>
        <result property="infoIdType" column="INFO_ID_TYPE"/>
        <result property="arlpName" column="ARLP_NAME"/>
        <result property="arlpCertType" column="ARLP_CERT_TYPE"/>
        <result property="arlpCertNum" column="ARLP_CERT_NUM"/>
        <result property="arlpType" column="ARLP_TYPE"/>
        <result property="arlpAmt" column="ARLP_AMT"/>
        <result property="wartySign" column="WARTY_SIGN"/>
        <result property="updator" column="UPDATOR"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="bussDate" column="BUSS_DATE"/>
        <result property="id" column="ID"/>
        <result property="deptCode" column="DEPT_CODE"/>
    </resultMap>
    <update id="updateInstanceId">
        update PILD_RLTREPYMTINFSGMT set PROC_INST_ID = #{instanceId} where id = #{id}
    </update>
</mapper>