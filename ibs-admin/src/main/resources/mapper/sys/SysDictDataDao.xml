<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.sys.dao.SysDictDataDao">

    <select id="getDictDataList" resultType="io.ibs.modules.sys.entity.DictData">
        select dict_type_id, dict_label, dict_value from sys_dict_data order by dict_type_id, sort
    </select>

    <select id="getDataByType" resultType="io.ibs.modules.sys.entity.DictData">
        SELECT d.* FROM SYS_DICT_DATA d,SYS_DICT_TYPE t WHERE t.dict_type=#{dictType} and t.ID=d.DICT_TYPE_ID
    </select>

    <select id="getDataByDictType" resultType="io.ibs.modules.sys.entity.SysDictDataEntity">
        SELECT d.* FROM SYS_DICT_DATA d,SYS_DICT_TYPE t WHERE t.dict_type=#{dictType} and t.ID=d.DICT_TYPE_ID
    </select>
</mapper>