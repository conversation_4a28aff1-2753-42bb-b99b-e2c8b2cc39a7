<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.ibs.modules.temp.dao.RecoverCreditDao">
    <select id="getAllOfCredit" resultType="io.ibs.modules.temp.entity.TLoanCreditEntity">
        select * from t_loan_credit order by cstm_no
    </select>

    <select id="getCreditReflectLoanNo" resultType="io.ibs.modules.temp.entity.CreditAndLoanEntity">
        select sx.khh as cstmNo,ff.jjh as loanNo,sx.dkpz as loanType,ff.SQED as loanAmt,sx.xh as serno from khsxmx sx,DKFFTZS ff
        where sx.khh=ff.khh and sx.xh=ff.sxxyh and sx.dkpz=ff.dkpz
        order by sx.khh,ff.jjh
    </select>

    <select id="getCreditReflectLoanNoFromLoanIou" resultType="io.ibs.modules.temp.entity.CreditAndLoanEntity">
        select cstm_no cstmNo,loan_no loanNo,loan_type loanType,dlv_guideline loanAmt,serno from t_loan_iou order by cstm_no,loan_no
    </select>

    <select id="getCstmLoanBal" resultType="io.ibs.modules.temp.entity.TLoanInfoEntity">
        select led.cstm_no cstmNo,led.loan_no loanNo,i.loan_type loanType,sum(led.ledger_guideline) totl,sum(led.bal) bal
        from t_loan_ledger led,t_loan_iou i
        where led.loan_no=i.loan_no
        group by led.cstm_no,led.loan_no,i.loan_type
    </select>

    <update id="updateCredit" parameterType="io.ibs.modules.temp.entity.TLoanCreditEntity">
        update t_loan_credit set cred_amt=#{credAmt},used_cred=#{usedCred}
        where cstm_no=#{cstmNo} and cred_type=#{credType} and serno=#{serno}
    </update>
</mapper>