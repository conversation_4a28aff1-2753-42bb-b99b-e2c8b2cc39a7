package io.ibs.common.utils;

import io.ibs.common.exception.RenException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期处理
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
@Slf4j
public class DateUtils {
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        throw new RenException("日期转换失败，所传日期不能为空");
    }

    /**
     * 日期解析
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回Date
     */
    public static Date parse(String date, String pattern) {
        try {
            return new SimpleDateFormat(pattern).parse(date);
        } catch (ParseException e) {
            log.error("捕获到异常", e);
        }
        throw new RenException("日期转换失败");
    }

    /**
     * 字符串转换成日期
     *
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)) {
            throw new RenException("日期转换失败，所传日期不能为空");
        }

        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 根据周数，获取开始日期、结束日期
     *
     * @param week 周期  0本周，-1上周，-2上上周，1下周，2下下周
     * @return 返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));

        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date    日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     *
     * @param date    日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 对日期的【小时】进行加/减
     *
     * @param date  日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }

    /**
     * 对日期的【周】进行加/减
     *
     * @param date  日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     *
     * @param date   日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     *
     * @param date  日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }

    /**
     * 获取指定月份的最后一天
     * 月份的最后一天 = 下个月1号 - 1天
     * 例如：getMonthLastDay(202206)，返回 30 Jun 2022
     *
     * @param month 指定月份
     * @return 指定月份的最后一天
     */
    public static Date getMonthLastDay(String month) {
        return DateUtils.addDateDays(DateUtils.addDateMonths(DateUtils.parse(month, "yyyyMM"), 1), -1);
    }

    /**
     * 获取不含时间的当天日期
     *
     * @return 含时间的日期
     */
    public static Date getDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 去除指定日期的时间
     *
     * @param date 日期
     * @return 含时间的日期
     */
    public static Date getDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 比较两个日期大小
     * 使用 “前 - 后”
     *
     * @param before 前
     * @param after  后
     * @return -1 - 小于；0 - 等于；1 - 大于
     */
    public static int compareTwoDate(Date before, Date after) {
        return Integer.compare((int) ((before.getTime() - after.getTime()) / (1000 * 60 * 60 * 24)), 0);
    }

    /**
     * 根据传入日期获取当前季度季末日期（季度最后一个月的21号）
     * <p>
     * 例如：
     * 传入20220319，返回20220321
     * 传入20220320，返回20220321
     * 传入20220321，返回20220621
     * 传入20221221，返回20230321
     *
     * @param date 传入日期 格式 yyyyMMdd
     * @return 当前季度季末日期（季度最后一个月的21号）
     */
    public static String getSeasonDay(String date) {
        Integer year = Integer.valueOf(date.substring(0, 4));
        String month = date.substring(4, 6);
        String day = date.substring(6, 8);
        switch (month) {
            case "01":
            case "02":
                return year + "0321";
            case "03":
                if (Integer.valueOf(day) <= 21) {
                    return year + "0321";
                } else {
                    return year + "0621";
                }
            case "04":
            case "05":
                return year + "0621";
            case "06":
                if (Integer.valueOf(day) <= 21) {
                    return year + "0621";
                } else {
                    return year + "0921";
                }
            case "07":
            case "08":
                return year + "0921";
            case "09":
                if (Integer.valueOf(day) <= 21) {
                    return year + "0921";
                } else {
                    return year + "1221";
                }
            case "10":
            case "11":
                return year + "1221";
            case "12":
                if (Integer.valueOf(day) <= 21) {
                    return year + "1221";
                } else {
                    return (year + 1) + "0321";
                }
            default:
                throw new IllegalArgumentException("错误的日期[" + date + "]！必须满足yyyyMMdd格式");
        }
    }

    /**
     * 获取两个日期之间相差的天数
     *
     * @param date1 日期
     * @param date2 日期
     * @return
     */
    public static int getDistanceOfTwoDay(Date date1, Date date2) {
        Long dis = (date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24);
        return dis.intValue();
    }
}
