#简体中文
500=服务器内部异常
401=未授权
403=拒绝访问，没有权限
10001={0}不能为空
10002=数据库中已存在该记录
10003=获取参数失败
10004=账号或密码错误
10005=账号已被停用
10006=唯一标识不能为空
10007=验证码不正确
10008=先删除子菜单或按钮
10009=原密码不正确
10010=账号不存在
10011=上级部门选择错误
10012=上级菜单不能为自身
10013=数据权限接口，只能是Map类型参数
10014=请先删除下级部门
10015=请先删除部门下的用户
10016=部署失败，没有流程
10017=模型图不正确，请检查
10018=导出失败，模型ID为{0}
10019=请上传文件
10020=token不能为空
10021=token失效，请重新登录
10022=账号已被锁定
10023=请上传zip、bar、bpmn、bpmn20.xml格式文件
10024=上传文件失败{0}
10025=发送短信失败{0}
10026=邮件模板不存在
10027=Redis服务异常
10028=定时任务失败
10029=不能包含非法字符
10030=参数格式不正确，请使用JSON格式
10031=请先完成短信配置
10032=任务已被签收，操作失败
10033=不存在的流程定义
10034=上级节点不存在
10035=驳回
10036=回退
10037=任务没有分组，无法取消认领
10038=上级区域选择错误
10039=请先删除下级区域
10040=流程已挂起，不能启动实例
10041=多实例任务不能驳回
10042=存在多个处理中的任务，不能驳回
10043=多实例任务不能终止
10044=存在多个处理中的任务，不能终止流程
10045=终止
10046=多实例任务不能回退
10047=存在多个并行执行的任务，不能回退
10048=登录账号，无权删除