#繁体中文
500=服務器內部異常
401=未授權
403=拒絕訪問，沒有權限
10001={0}不能為空
10002=數據庫中已存在該記錄
10003=獲取參數失敗
10004=賬號或密碼錯誤
10005=賬號已被停用
10006=唯一標識不能為空
10007=驗證碼不正確
10008=先刪除子菜單或按鈕
10009=原密碼不正確
10010=賬號不存在
10011=上級部門選擇錯誤
10012=上級菜單不能為自身
10013=數據權限接口，只能是Map類型參數
10014=請先刪除下級部門
10015=請先刪除部門下的用戶
10016=部署失敗，沒有流程
10017=模型圖不正確，請檢查
10018=導出失敗，模型ID為{0}
10019=請上傳文件
10020=token不能為空
10021=token失效，請重新登錄
10022=賬號已被鎖定
10023=請上傳zip、bar、bpmn、bpmn20.xml格式文件
10024=上傳文件失敗{0}
10025=發送短信失敗{0}
10026=郵件模板不存在
10027=Redis服務異常
10028=定時任務失敗
10029=不能包含非法字符
10030=參數格式不正確，請使用JSON格式
10031=請先完成短信配置
10032=任務已被簽收，操作失敗
10033=不存在的流程定義
10034=上級節點不存在
10035=駁回
10036=回退
10037=任務沒有分組，無法取消認領
10038=上級區域選擇錯誤
10039=請先刪除下級區域
10040=流程已掛起，不能啟動實例
10041=多實例任務不能駁回
10042=存在多個處理中的任務，不能駁回
10043=多實例任務不能終止
10044=存在多個處理中的任務，不能終止流程
10045=終止
10046=多實例任務不能回退
10047=存在多個並行執行的任務，不能回退
10048=登入帳號，無權删除