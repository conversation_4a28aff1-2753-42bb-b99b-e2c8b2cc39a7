#简体中文
id.require=ID不能为空
id.null=ID必须为空
pid.require=上级ID，不能为空
sort.number=排序值不能小于0

sysparams.paramcode.require=参数编码不能为空
sysparams.paramvalue.require=参数值不能为空

sysuser.username.require=用户名不能为空
sysuser.password.require=密码不能为空
sysuser.realname.require=姓名不能为空
sysuser.gender.range=性别取值范围0~2
sysuser.email.require=邮箱不能为空
sysuser.email.error=邮箱格式不正确
sysuser.mobile.require=手机号不能为空
sysuser.deptId.require=部门不能为空
sysuser.superadmin.range=超级管理员取值范围0~1
sysuser.status.range=状态取值范围0~1
sysuser.captcha.require=验证码不能为空
sysuser.uuid.require=唯一标识不能为空

sysmenu.pid.require=请选择上级菜单
sysmenu.name.require=菜单名称不能为空
sysmenu.type.range=菜单类型取值范围0~1
sysmenu.openstyle.range=菜单打开方式取值范围0~1

sysdept.pid.require=请选择上级部门
sysdept.name.require=部门名称不能为空

sysrole.name.require=角色名称不能为空

sysdict.type.require=字典类型不能为空
sysdict.name.require=字典名称不能为空
sysdict.label.require=字典标签不能为空

schedule.status.range=状态取值范围0~1
schedule.cron.require=cron表达式不能为空
schedule.bean.require=bean名称不能为空

oss.type.range=类型取值范围1~5

aliyun.accesskeyid.require=阿里云AccessKeyId不能为空
aliyun.accesskeysecret.require=阿里云AccessKeySecret不能为空
aliyun.signname.require=阿里云短信签名不能为空
aliyun.templatecode.require=阿里云短信模板不能为空
aliyun.domain.require=阿里云绑定的域名不能为空
aliyun.domain.url=阿里云绑定的域名格式不正确
aliyun.endPoint.require=阿里云EndPoint不能为空
aliyun.bucketname.require=阿里云BucketName不能为空

qcloud.appid.require=腾讯云AppId不能为空
qcloud.appkey.require=腾讯云AppKey不能为空
qcloud.secretId.require=腾讯云SecretId不能为空
qcloud.secretkey.require=腾讯云SecretKey不能为空
qcloud.signname.require=腾讯云短信签名不能为空
qcloud.templateid.require=腾讯云短信模板ID不能为空
qcloud.domain.require=腾讯云绑定的域名不能为空
qcloud.domain.url=腾讯云绑定的域名格式不正确
qcloud.bucketname.require=腾讯云BucketName不能为空
qcloud.region.require=所属地区不能为空

qiniu.domain.require=七牛绑定的域名不能为空
qiniu.domain.url=七牛绑定的域名格式不正确
qiniu.accesskey.require=七牛AccessKey不能为空
qiniu.secretkey.require=七牛SecretKey不能为空
qiniu.bucketname.require=七牛空间名不能为空
qiniu.templateId.require=七牛模板ID不能为空

fastdfs.domain.require=FastDFS绑定的域名不能为空
fastdfs.domain.url=FastDFS绑定的域名格式不正确

local.domain.require=本地上传绑定的域名不能为空
local.domain.url=本地上传绑定的域名格式不正确
local.path.url=存储目录不能为空

minio.endPoint.require=Minio EndPoint不能为空
minio.accesskey.require=Minio AccessKey不能为空
minio.secretkey.require=Minio SecretKey不能为空
minio.bucketname.require=Minio BucketName不能为空

sms.platform.range=平台类型取值范围1~2
email.smtp.require=SMTP不能为空
email.port.require=端口号不能为空
email.username.require=邮箱账号不能为空
email.password.require=邮箱密码不能为空

mail.name.require=模板名称不能为空
mail.subject.require=邮件主题不能为空
mail.content.require=邮件正文不能为空

model.name.require=模型名称不能为空
model.key.require=模型标识不能为空

news.title.require=标题不能为空
news.content.require=内容不能为空
news.pubdate.require=发布时间不能为空

region.id.require=区域标识不能为空
region.pid.require=上级区域不能为空
region.name.require=区域名称不能为空

processBizRoute.procDefId.require=流程定义ID不能为空
processBizRoute.bizRoute.require=业务路由不能为空
processBizRoute.procDefKey.require=流程定义KEY不能为空
processBizRoute.version.require=流程定义版本号不能为空

ProcessStart.processDefinitionKey.require=流程定义KEY不能为空
ProcessStart.businessKey.require=业务KEY不能为空