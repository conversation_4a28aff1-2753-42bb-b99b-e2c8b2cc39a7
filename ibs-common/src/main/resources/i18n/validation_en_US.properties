#English
id.require=ID can not be empty
id.null=ID has to be empty
pid.require=Parent ID, cannot be empty
sort.number=The sort value cannot be less than 0

sysparams.paramcode.require=Parameter encoding cannot be empty
sysparams.paramvalue.require=Parameter values cannot be empty

sysuser.username.require=The username cannot be empty
sysuser.password.require=The password cannot be empty
sysuser.realname.require=The realname cannot be empty
sysuser.gender.range=Gender ranges from 0 to 2
sysuser.email.require=Mailbox cannot be empty
sysuser.email.error=Incorrect email format
sysuser.mobile.require=The phone number cannot be empty
sysuser.deptId.require=Departments cannot be empty
sysuser.superadmin.range=Super administrator values range from 0 to 1
sysuser.status.range=State ranges from 0 to 1
sysuser.captcha.require=The captcha cannot be empty
sysuser.uuid.require=The unique identifier cannot be empty

sysmenu.pid.require=Please select superior menu
sysmenu.name.require=Menu name cannot be empty
sysmenu.type.range=Menu type ranges from 0 to 1
sysmenu.openstyle.range=Menu open style ranges from 0 to 1

sysdept.pid.require=Please select superior department
sysdept.name.require=The department name cannot be empty

sysrole.name.require=The role name cannot be empty

sysdict.type.require=The dictionary type cannot be empty
sysdict.name.require=The dictionary name cannot be empty
sysdict.label.require=Dictionary tag cannot be empty

schedule.status.range=Status ranges from 0 to 1
schedule.cron.require=Cron expression cannot be empty
schedule.bean.require=Bean name cannot be empty

oss.type.range=Type ranges from 1 to 5

aliyun.accesskeyid.require=Aliyun AccessKeyId cannot be empty
aliyun.accesskeysecret.require=Aliyun AccessKeySecret cannot be empty
aliyun.signname.require=Aliyun message signature cannot be empty
aliyun.templatecode.require=Aliyun message template cannot be empty
aliyun.domain.require=Aliyun bound domain name cannot be empty
aliyun.domain.url=Aliyun binding domain name format is incorrect
aliyun.endPoint.require=The aliyun EndPoint cannot be empty
aliyun.bucketname.require=Aliyun BucketName cannot be empty

qcloud.appid.require=Tencent cloud AppId cannot be empty
qcloud.appkey.require=Tencent cloud AppKey cannot be empty
qcloud.secretId.require=Tencent cloud SecretId cannot be empty
qcloud.secretkey.require=Tencent cloud SecretKey cannot be empty
qcloud.signname.require=Tencent cloud SMS signature cannot be empty
qcloud.templateid.require=Tencent cloud SMS template ID cannot be empty
qcloud.domain.require=Tencent cloud bound domain name cannot be empty
qcloud.domain.url=Tencent cloud binding domain name format is incorrect
qcloud.bucketname.require=Tencent cloud BucketName cannot be empty
qcloud.region.require=The region cannot be empty

qiniu.domain.require=Bound domain name cannot be empty
qiniu.domain.url=Binding domain name format is incorrect
qiniu.accesskey.require=The AccessKey cannot be empty
qiniu.secretkey.require=The SecretKey of seven cows cannot be empty
qiniu.bucketname.require=Space names cannot be empty
qiniu.templateId.require=Template ID cannot be empty

fastdfs.domain.require=FastDFS bound domain name cannot be empty
fastdfs.domain.url=FastDFS bound domain name format is incorrect

local.domain.require=Local upload bound domain name cannot be empty
local.domain.url=The domain name bound for local upload is not formatted correctly
local.path.url=The storage directory cannot be empty

minio.endPoint.require=Minio EndPoint cannot be empty
minio.accesskey.require=Minio AccessKey cannot be empty
minio.secretkey.require=Minio SecretKey cannot be empty
minio.bucketname.require=Minio BucketName cannot be empty

sms.platform.range=Platform type ranges from 1 to 2

email.smtp.require=SMTP cannot be empty
email.port.require=Port number cannot be empty
email.username.require=The email account cannot be empty
email.password.require=The password cannot be empty

mail.name.require=The template name cannot be empty
mail.subject.require=Mail subject cannot be empty
mail.content.require=Message text cannot be empty

model.name.require=The model name cannot be empty
model.key.require=The model key cannot be empty

news.title.require=The title cannot be empty
news.content.require=Content cannot be empty
news.pubdate.require=The release time cannot be empty

region.id.require=Region ID cannot be empty
region.pid.require=Upper area cannot be empty
region.name.require=Region name cannot be empty

processBizRoute.procDefId.require=Process Definition ID cannot be empty
processBizRoute.bizRoute.require=Service routing cannot be empty
processBizRoute.procDefKey.require=Process Definition KEY cannot be empty
processBizRoute.version.require=Process Definition Version number cannot be empty

ProcessStart.processDefinitionKey.require=Process Definition KEY cannot be empty
ProcessStart.businessKey.require=Business KEY cannot be empty