package com.xlc.excel.controller;

import com.xlc.excel.dto.ExcelExportRequest;
import com.xlc.excel.dto.ExcelImportRequest;
import com.xlc.excel.dto.ExcelImportResult;
import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.service.ExcelService;
import com.xlc.excel.service.ExcelExportService.ExcelExportResult;
import com.xlc.excel.service.ExcelImportService.ExcelFileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Excel导入导出控制器
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@RestController
@RequestMapping("/api/excel")
@CrossOrigin(origins = "*")
public class ExcelController {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelController.class);
    
    @Autowired
    private ExcelService excelService;
    
    // ==================== 导入相关接口 ====================
    
    /**
     * 根据表名导入Excel文件
     */
    @PostMapping("/import/table/{tableName}")
    public ResponseEntity<ApiResponse<ExcelImportResult<Object>>> importByTableName(
            @PathVariable String tableName,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "targetClass", defaultValue = "java.util.Map") String targetClassName) {
        
        try {
            logger.info("接收到表名导入请求：表名={}，文件名={}", tableName, file.getOriginalFilename());
            
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件不能为空"));
            }
            
            // 获取目标类型
            Class<?> targetClass = getTargetClass(targetClassName);
            
            // 执行导入
            ExcelImportResult<Object> result = excelService.<Object>importByTableName(
                    file.getInputStream(),
                    file.getOriginalFilename(),
                    tableName,
                    (Class<Object>) targetClass
            );
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            logger.error("表名导入失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("导入失败：" + e.getMessage()));
        }
    }
    
    /**
     * 根据模板导入Excel文件
     */
    @PostMapping("/import/template/{template}")
    public ResponseEntity<ApiResponse<ExcelImportResult<Object>>> importByTemplate(
            @PathVariable ExcelTemplateEnum template,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "targetClass", defaultValue = "java.util.Map") String targetClassName) {
        
        try {
            logger.info("接收到模板导入请求：模板={}，文件名={}", template.getName(), file.getOriginalFilename());
            
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件不能为空"));
            }
            
            // 获取目标类型
            Class<?> targetClass = getTargetClass(targetClassName);
            
            // 执行导入
            ExcelImportResult<Object> result = excelService.<Object>importByTemplate(
                    file.getInputStream(),
                    file.getOriginalFilename(),
                    template,
                    (Class<Object>) targetClass
            );
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            logger.error("模板导入失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("导入失败：" + e.getMessage()));
        }
    }
    
    /**
     * 预览Excel文件
     */
    @PostMapping("/preview")
    public ResponseEntity<ApiResponse<ExcelImportResult<Object>>> previewExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "previewRows", defaultValue = "10") int previewRows,
            @RequestParam(value = "targetClass", defaultValue = "java.util.Map") String targetClassName) {
        
        try {
            logger.info("接收到预览请求：文件名={}，预览行数={}", file.getOriginalFilename(), previewRows);
            
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件不能为空"));
            }
            
            // 获取目标类型
            Class<?> targetClass = getTargetClass(targetClassName);
            
            // 执行预览
            ExcelImportResult<Object> result = excelService.<Object>previewExcel(
                    file.getInputStream(),
                    file.getOriginalFilename(),
                    (Class<Object>) targetClass,
                    previewRows
            );
            
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            logger.error("预览失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("预览失败：" + e.getMessage()));
        }
    }
    
    /**
     * 异步导入Excel文件
     */
    @PostMapping("/import/async/table/{tableName}")
    public ResponseEntity<ApiResponse<String>> importAsyncByTableName(
            @PathVariable String tableName,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "targetClass", defaultValue = "java.util.Map") String targetClassName) {
        
        try {
            logger.info("接收到异步导入请求：表名={}，文件名={}", tableName, file.getOriginalFilename());
            
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件不能为空"));
            }
            
            // 获取目标类型
            Class<?> targetClass = getTargetClass(targetClassName);
            
            // 创建导入请求
            ExcelImportRequest request = ExcelImportRequest.byTableName(
                    file.getInputStream(),
                    file.getOriginalFilename(),
                    tableName,
                    targetClass
            );
            
            // 执行异步导入
            CompletableFuture<ExcelImportResult<Object>> future = excelService.<Object>importExcelAsync(request);
            
            // 返回任务ID（这里简化处理，实际应该生成唯一的任务ID）
            String taskId = "task_" + System.currentTimeMillis();
            
            return ResponseEntity.ok(ApiResponse.success(taskId, "异步导入任务已提交"));
            
        } catch (Exception e) {
            logger.error("异步导入提交失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("异步导入提交失败：" + e.getMessage()));
        }
    }
    
    // ==================== 导出相关接口 ====================
    
    /**
     * 根据表名导出Excel文件
     */
    @PostMapping("/export/table/{tableName}")
    public ResponseEntity<byte[]> exportByTableName(
            @PathVariable String tableName,
            @RequestBody ExportDataRequest exportRequest,
            HttpServletResponse response) {
        
        try {
            logger.info("接收到表名导出请求：表名={}，数据行数={}", tableName, 
                    exportRequest.getData() != null ? exportRequest.getData().size() : 0);
            
            // 创建输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            // 执行导出
            ExcelExportResult result = excelService.exportByTableName(
                    outputStream,
                    tableName + ".xlsx",
                    tableName,
                    exportRequest.getData(),
                    Object.class
            );
            
            if (!result.isSuccess()) {
                logger.error("导出失败：{}", result.getErrorMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
            
            // 设置响应头
            String fileName = URLEncoder.encode(tableName + ".xlsx", StandardCharsets.UTF_8.toString());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(outputStream.toByteArray());
            
        } catch (Exception e) {
            logger.error("表名导出失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 根据模板导出Excel文件
     */
    @PostMapping("/export/template/{template}")
    public ResponseEntity<byte[]> exportByTemplate(
            @PathVariable ExcelTemplateEnum template,
            @RequestBody ExportDataRequest exportRequest,
            HttpServletResponse response) {
        
        try {
            logger.info("接收到模板导出请求：模板={}，数据行数={}", template.getName(), 
                    exportRequest.getData() != null ? exportRequest.getData().size() : 0);
            
            // 创建输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            // 执行导出
            ExcelExportResult result = excelService.exportByTemplate(
                    outputStream,
                    template.getName() + ".xlsx",
                    template,
                    exportRequest.getData(),
                    Object.class
            );
            
            if (!result.isSuccess()) {
                logger.error("导出失败：{}", result.getErrorMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
            
            // 设置响应头
            String fileName = URLEncoder.encode(template.getName() + ".xlsx", StandardCharsets.UTF_8.toString());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(outputStream.toByteArray());
            
        } catch (Exception e) {
            logger.error("模板导出失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 导出Excel模板文件
     */
    @GetMapping("/template/{template}")
    public ResponseEntity<byte[]> downloadTemplate(@PathVariable ExcelTemplateEnum template) {
        
        try {
            logger.info("接收到模板下载请求：模板={}", template.getName());
            
            // 创建输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            
            // 执行模板导出
            ExcelExportResult result = excelService.exportTemplate(
                    outputStream,
                    template.getName() + "_template.xlsx",
                    template
            );
            
            if (!result.isSuccess()) {
                logger.error("模板导出失败：{}", result.getErrorMessage());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
            
            // 设置响应头
            String fileName = URLEncoder.encode(template.getName() + "_template.xlsx", StandardCharsets.UTF_8.toString());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(outputStream.toByteArray());
            
        } catch (Exception e) {
            logger.error("模板下载失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // ==================== 工具接口 ====================
    
    /**
     * 验证Excel文件格式
     */
    @PostMapping("/validate")
    public ResponseEntity<ApiResponse<Boolean>> validateExcelFormat(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "tableName", required = false) String tableName,
            @RequestParam(value = "template", required = false) ExcelTemplateEnum template) {
        
        try {
            logger.info("接收到文件格式验证请求：文件名={}", file.getOriginalFilename());
            
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件不能为空"));
            }
            
            boolean isValid;
            if (template != null) {
                isValid = excelService.validateExcelFormat(file.getInputStream(), file.getOriginalFilename(), template);
            } else if (tableName != null) {
                isValid = excelService.validateExcelFormat(file.getInputStream(), file.getOriginalFilename(), tableName);
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("必须指定表名或模板"));
            }
            
            return ResponseEntity.ok(ApiResponse.success(isValid));
            
        } catch (Exception e) {
            logger.error("文件格式验证失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("验证失败：" + e.getMessage()));
        }
    }
    
    /**
     * 获取Excel文件信息
     */
    @PostMapping("/info")
    public ResponseEntity<ApiResponse<ExcelFileInfo>> getExcelFileInfo(@RequestParam("file") MultipartFile file) {
        
        try {
            logger.info("接收到文件信息获取请求：文件名={}", file.getOriginalFilename());
            
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件不能为空"));
            }
            
            ExcelFileInfo fileInfo = excelService.getExcelFileInfo(file.getInputStream(), file.getOriginalFilename());
            
            return ResponseEntity.ok(ApiResponse.success(fileInfo));
            
        } catch (Exception e) {
            logger.error("获取文件信息失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取文件信息失败：" + e.getMessage()));
        }
    }
    
    /**
     * 获取支持的模板列表
     */
    @GetMapping("/templates")
    public ResponseEntity<ApiResponse<List<ExcelTemplateEnum>>> getSupportedTemplates() {
        
        try {
            logger.info("接收到获取支持模板列表请求");
            
            List<ExcelTemplateEnum> templates = excelService.getSupportedTemplates();
            
            return ResponseEntity.ok(ApiResponse.success(templates));
            
        } catch (Exception e) {
            logger.error("获取支持模板列表失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取模板列表失败：" + e.getMessage()));
        }
    }
    
    /**
     * 服务健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<ExcelService.ServiceHealthInfo>> getServiceHealth() {
        
        try {
            ExcelService.ServiceHealthInfo healthInfo = excelService.getServiceHealth();
            
            if (healthInfo.isHealthy()) {
                return ResponseEntity.ok(ApiResponse.success(healthInfo));
            } else {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                        .body(ApiResponse.error("服务不健康", healthInfo));
            }
            
        } catch (Exception e) {
            logger.error("健康检查失败：{}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("健康检查失败：" + e.getMessage()));
        }
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 获取目标类型
     */
    private Class<?> getTargetClass(String className) {
        try {
            if ("java.util.Map".equals(className)) {
                return Map.class;
            }
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            logger.warn("无法找到类：{}，使用Map作为默认类型", className);
            return Map.class;
        }
    }
    
    // ==================== 内部类 ====================
    
    /**
     * API响应封装类
     */
    public static class ApiResponse<T> {
        private boolean success;
        private String message;
        private T data;
        private long timestamp;
        
        public ApiResponse() {
            this.timestamp = System.currentTimeMillis();
        }
        
        public ApiResponse(boolean success, String message, T data) {
            this();
            this.success = success;
            this.message = message;
            this.data = data;
        }
        
        public static <T> ApiResponse<T> success(T data) {
            return new ApiResponse<>(true, "操作成功", data);
        }
        
        public static <T> ApiResponse<T> success(T data, String message) {
            return new ApiResponse<>(true, message, data);
        }
        
        public static <T> ApiResponse<T> error(String message) {
            return new ApiResponse<>(false, message, null);
        }
        
        public static <T> ApiResponse<T> error(String message, T data) {
            return new ApiResponse<>(false, message, data);
        }
        
        // Getter and Setter methods
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public T getData() {
            return data;
        }
        
        public void setData(T data) {
            this.data = data;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }
    
    /**
     * 导出数据请求类
     */
    public static class ExportDataRequest {
        private List<Object> data;
        
        public List<Object> getData() {
            return data;
        }
        
        public void setData(List<Object> data) {
            this.data = data;
        }
    }
}
