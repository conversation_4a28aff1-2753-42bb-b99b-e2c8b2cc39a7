package com.xlc.excel.dto;

import com.xlc.excel.config.ExcelConfig;
import com.xlc.excel.enums.ExcelTemplateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * Excel导出请求参数封装类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelExportRequest<T> {
    
    /**
     * 输出流
     */
    private OutputStream outputStream;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 表名（用于通用导出）
     */
    private String tableName;
    
    /**
     * 模板标识（枚举）
     */
    private ExcelTemplateEnum template;
    
    /**
     * 要导出的数据列表
     */
    private List<T> data;
    
    /**
     * 数据类型
     */
    private Class<T> dataClass;
    
    /**
     * Excel配置
     */
    @Builder.Default
    private ExcelConfig config = ExcelConfig.defaultConfig();
    
    /**
     * 工作表名称
     */
    private String sheetName;
    
    /**
     * 表头信息（如果不指定，则根据注解自动生成）
     */
    private List<String> headers;
    
    /**
     * 列宽设置（列索引 -> 宽度）
     */
    private Map<Integer, Integer> columnWidths;
    
    /**
     * 是否包含表头
     */
    @Builder.Default
    private boolean includeHeader = true;
    
    /**
     * 是否自动调整列宽
     */
    @Builder.Default
    private boolean autoSizeColumn = true;
    
    /**
     * 最大导出行数（0表示不限制）
     */
    @Builder.Default
    private int maxExportRows = 0;
    
    /**
     * 是否启用样式
     */
    @Builder.Default
    private boolean enableStyle = true;
    
    /**
     * 自定义样式类名
     */
    private String customStyleClass;
    
    /**
     * 自定义转换器类名
     */
    private String customConverterClass;
    
    /**
     * 导出模式
     */
    @Builder.Default
    private ExportMode exportMode = ExportMode.NORMAL;
    
    /**
     * 分页导出时的页大小
     */
    @Builder.Default
    private int pageSize = 10000;
    
    /**
     * 额外的参数（用于扩展）
     */
    private Object extraParams;
    
    /**
     * 导出模式枚举
     */
    public enum ExportMode {
        /**
         * 普通模式
         */
        NORMAL("NORMAL", "普通模式"),
        
        /**
         * 分页模式（大数据量）
         */
        PAGED("PAGED", "分页模式"),
        
        /**
         * 流式模式（超大数据量）
         */
        STREAMING("STREAMING", "流式模式"),
        
        /**
         * 模板模式
         */
        TEMPLATE("TEMPLATE", "模板模式");
        
        private final String code;
        private final String description;
        
        ExportMode(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建基于表名的导出请求
     */
    public static <T> ExcelExportRequest<T> byTableName(OutputStream outputStream, String fileName, String tableName, List<T> data, Class<T> dataClass) {
        return ExcelExportRequest.<T>builder()
                .outputStream(outputStream)
                .fileName(fileName)
                .tableName(tableName)
                .data(data)
                .dataClass(dataClass)
                .build();
    }
    
    /**
     * 创建基于模板的导出请求
     */
    public static <T> ExcelExportRequest<T> byTemplate(OutputStream outputStream, String fileName, ExcelTemplateEnum template, List<T> data, Class<T> dataClass) {
        return ExcelExportRequest.<T>builder()
                .outputStream(outputStream)
                .fileName(fileName)
                .template(template)
                .data(data)
                .dataClass(dataClass)
                .build();
    }
    
    /**
     * 创建分页导出请求
     */
    public static <T> ExcelExportRequest<T> pagedExport(OutputStream outputStream, String fileName, String tableName, Class<T> dataClass, int pageSize) {
        return ExcelExportRequest.<T>builder()
                .outputStream(outputStream)
                .fileName(fileName)
                .tableName(tableName)
                .dataClass(dataClass)
                .exportMode(ExportMode.PAGED)
                .pageSize(pageSize)
                .build();
    }
    
    /**
     * 创建流式导出请求
     */
    public static <T> ExcelExportRequest<T> streamingExport(OutputStream outputStream, String fileName, String tableName, Class<T> dataClass) {
        return ExcelExportRequest.<T>builder()
                .outputStream(outputStream)
                .fileName(fileName)
                .tableName(tableName)
                .dataClass(dataClass)
                .exportMode(ExportMode.STREAMING)
                .build();
    }
    
    /**
     * 设置自定义配置
     */
    public ExcelExportRequest<T> withConfig(ExcelConfig config) {
        this.config = config;
        return this;
    }
    
    /**
     * 设置工作表名称
     */
    public ExcelExportRequest<T> withSheetName(String sheetName) {
        this.sheetName = sheetName;
        return this;
    }
    
    /**
     * 设置表头
     */
    public ExcelExportRequest<T> withHeaders(List<String> headers) {
        this.headers = headers;
        return this;
    }
    
    /**
     * 设置列宽
     */
    public ExcelExportRequest<T> withColumnWidths(Map<Integer, Integer> columnWidths) {
        this.columnWidths = columnWidths;
        return this;
    }
    
    /**
     * 禁用表头
     */
    public ExcelExportRequest<T> withoutHeader() {
        this.includeHeader = false;
        return this;
    }
    
    /**
     * 禁用自动调整列宽
     */
    public ExcelExportRequest<T> withoutAutoSize() {
        this.autoSizeColumn = false;
        return this;
    }
    
    /**
     * 设置最大导出行数
     */
    public ExcelExportRequest<T> withMaxRows(int maxRows) {
        this.maxExportRows = maxRows;
        return this;
    }
    
    /**
     * 禁用样式
     */
    public ExcelExportRequest<T> withoutStyle() {
        this.enableStyle = false;
        return this;
    }
    
    /**
     * 设置自定义样式
     */
    public ExcelExportRequest<T> withStyle(String styleClass) {
        this.customStyleClass = styleClass;
        return this;
    }
    
    /**
     * 设置自定义转换器
     */
    public ExcelExportRequest<T> withConverter(String converterClass) {
        this.customConverterClass = converterClass;
        return this;
    }
    
    /**
     * 设置额外参数
     */
    public ExcelExportRequest<T> withExtraParams(Object extraParams) {
        this.extraParams = extraParams;
        return this;
    }
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }
        
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        if (dataClass == null) {
            throw new IllegalArgumentException("数据类型不能为空");
        }
        
        if (tableName == null && template == null) {
            throw new IllegalArgumentException("表名和模板标识不能同时为空");
        }
        
        if (exportMode == ExportMode.NORMAL && (data == null || data.isEmpty())) {
            throw new IllegalArgumentException("普通模式下数据列表不能为空");
        }
        
        if (exportMode == ExportMode.PAGED && pageSize <= 0) {
            throw new IllegalArgumentException("分页模式下页大小必须大于0");
        }
        
        if (config == null) {
            this.config = ExcelConfig.defaultConfig();
        }
        
        if (maxExportRows > 0 && data != null && data.size() > maxExportRows) {
            throw new IllegalArgumentException("数据行数超过最大导出限制：" + maxExportRows);
        }
    }
    
    /**
     * 获取标识符（表名或模板编码）
     */
    public String getIdentifier() {
        if (template != null) {
            return template.getCode();
        }
        return tableName;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        if (template != null) {
            return template.getName();
        }
        return tableName;
    }
    
    /**
     * 获取工作表名称（如果未设置则使用默认名称）
     */
    public String getEffectiveSheetName() {
        if (sheetName != null && !sheetName.trim().isEmpty()) {
            return sheetName;
        }
        
        if (template != null) {
            return template.getName();
        }
        
        if (tableName != null && !tableName.trim().isEmpty()) {
            return tableName;
        }
        
        return "Sheet1";
    }
}
