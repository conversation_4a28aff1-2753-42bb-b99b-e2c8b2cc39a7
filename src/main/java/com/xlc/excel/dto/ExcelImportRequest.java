package com.xlc.excel.dto;

import com.xlc.excel.config.ExcelConfig;
import com.xlc.excel.enums.ExcelTemplateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.InputStream;

/**
 * Excel导入请求参数封装类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelImportRequest {
    
    /**
     * 文件输入流
     */
    private InputStream inputStream;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 表名（用于通用导入）
     */
    private String tableName;
    
    /**
     * 模板标识（枚举）
     */
    private ExcelTemplateEnum template;
    
    /**
     * 目标数据类型
     */
    private Class<?> targetClass;
    
    /**
     * Excel配置
     */
    @Builder.Default
    private ExcelConfig config = ExcelConfig.defaultConfig();
    
    /**
     * 工作表索引（从0开始，默认第一个工作表）
     */
    @Builder.Default
    private Integer sheetIndex = 0;
    
    /**
     * 工作表名称（如果指定了工作表名称，则忽略sheetIndex）
     */
    private String sheetName;
    
    /**
     * 是否启用严格模式（遇到错误立即停止）
     */
    @Builder.Default
    private boolean strictMode = false;
    
    /**
     * 是否启用预览模式（只读取前几行数据用于预览）
     */
    @Builder.Default
    private boolean previewMode = false;
    
    /**
     * 预览行数（预览模式下有效）
     */
    @Builder.Default
    private int previewRows = 10;
    
    /**
     * 是否启用异步处理
     */
    @Builder.Default
    private boolean asyncMode = false;
    
    /**
     * 自定义校验器类名
     */
    private String customValidatorClass;
    
    /**
     * 自定义转换器类名
     */
    private String customConverterClass;
    
    /**
     * 额外的参数（用于扩展）
     */
    private Object extraParams;
    
    /**
     * 创建基于表名的导入请求
     */
    public static ExcelImportRequest byTableName(InputStream inputStream, String fileName, String tableName, Class<?> targetClass) {
        return ExcelImportRequest.builder()
                .inputStream(inputStream)
                .fileName(fileName)
                .tableName(tableName)
                .targetClass(targetClass)
                .build();
    }
    
    /**
     * 创建基于模板的导入请求
     */
    public static ExcelImportRequest byTemplate(InputStream inputStream, String fileName, ExcelTemplateEnum template, Class<?> targetClass) {
        return ExcelImportRequest.builder()
                .inputStream(inputStream)
                .fileName(fileName)
                .template(template)
                .targetClass(targetClass)
                .build();
    }
    
    /**
     * 创建预览模式的导入请求
     */
    public static ExcelImportRequest forPreview(InputStream inputStream, String fileName, Class<?> targetClass, int previewRows) {
        return ExcelImportRequest.builder()
                .inputStream(inputStream)
                .fileName(fileName)
                .targetClass(targetClass)
                .previewMode(true)
                .previewRows(previewRows)
                .build();
    }
    
    /**
     * 创建严格模式的导入请求
     */
    public static ExcelImportRequest strictMode(InputStream inputStream, String fileName, String tableName, Class<?> targetClass) {
        return ExcelImportRequest.builder()
                .inputStream(inputStream)
                .fileName(fileName)
                .tableName(tableName)
                .targetClass(targetClass)
                .strictMode(true)
                .config(ExcelConfig.strictConfig())
                .build();
    }
    
    /**
     * 创建宽松模式的导入请求
     */
    public static ExcelImportRequest lenientMode(InputStream inputStream, String fileName, String tableName, Class<?> targetClass) {
        return ExcelImportRequest.builder()
                .inputStream(inputStream)
                .fileName(fileName)
                .tableName(tableName)
                .targetClass(targetClass)
                .strictMode(false)
                .config(ExcelConfig.lenientConfig())
                .build();
    }
    
    /**
     * 设置自定义配置
     */
    public ExcelImportRequest withConfig(ExcelConfig config) {
        this.config = config;
        return this;
    }
    
    /**
     * 设置工作表
     */
    public ExcelImportRequest withSheet(int sheetIndex) {
        this.sheetIndex = sheetIndex;
        this.sheetName = null;
        return this;
    }
    
    /**
     * 设置工作表名称
     */
    public ExcelImportRequest withSheet(String sheetName) {
        this.sheetName = sheetName;
        this.sheetIndex = null;
        return this;
    }
    
    /**
     * 启用异步模式
     */
    public ExcelImportRequest enableAsync() {
        this.asyncMode = true;
        return this;
    }
    
    /**
     * 设置自定义校验器
     */
    public ExcelImportRequest withValidator(String validatorClass) {
        this.customValidatorClass = validatorClass;
        return this;
    }
    
    /**
     * 设置自定义转换器
     */
    public ExcelImportRequest withConverter(String converterClass) {
        this.customConverterClass = converterClass;
        return this;
    }
    
    /**
     * 设置额外参数
     */
    public ExcelImportRequest withExtraParams(Object extraParams) {
        this.extraParams = extraParams;
        return this;
    }
    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }
        
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        if (targetClass == null) {
            throw new IllegalArgumentException("目标数据类型不能为空");
        }
        
        if (tableName == null && template == null) {
            throw new IllegalArgumentException("表名和模板标识不能同时为空");
        }
        
        if (config == null) {
            this.config = ExcelConfig.defaultConfig();
        }
        
        if (previewMode && previewRows <= 0) {
            throw new IllegalArgumentException("预览行数必须大于0");
        }
    }
    
    /**
     * 获取标识符（表名或模板编码）
     */
    public String getIdentifier() {
        if (template != null) {
            return template.getCode();
        }
        return tableName;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        if (template != null) {
            return template.getName();
        }
        return tableName;
    }
}
