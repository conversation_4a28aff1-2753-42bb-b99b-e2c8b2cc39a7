package com.xlc.excel.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Excel行数据封装类
 * 用于封装单行数据及其相关的错误信息
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelRowData<T> {
    
    /**
     * 行号（从1开始）
     */
    private Integer rowIndex;
    
    /**
     * 原始数据对象
     */
    private T data;
    
    /**
     * 该行的错误信息列表
     */
    @Builder.Default
    private List<ExcelErrorInfo> errors = new ArrayList<>();
    
    /**
     * 是否有错误
     */
    private boolean hasError;
    
    /**
     * 行状态
     */
    private RowStatus status;
    
    /**
     * 额外的备注信息
     */
    private String remark;
    
    /**
     * 行状态枚举
     */
    public enum RowStatus {
        /**
         * 成功
         */
        SUCCESS("SUCCESS", "成功"),
        
        /**
         * 失败
         */
        FAILURE("FAILURE", "失败"),
        
        /**
         * 跳过
         */
        SKIPPED("SKIPPED", "跳过"),
        
        /**
         * 警告
         */
        WARNING("WARNING", "警告");
        
        private final String code;
        private final String description;
        
        RowStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 添加错误信息
     */
    public void addError(ExcelErrorInfo error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.hasError = true;
        this.status = RowStatus.FAILURE;
    }
    
    /**
     * 添加多个错误信息
     */
    public void addErrors(List<ExcelErrorInfo> errorList) {
        if (errorList != null && !errorList.isEmpty()) {
            if (errors == null) {
                errors = new ArrayList<>();
            }
            errors.addAll(errorList);
            this.hasError = true;
            this.status = RowStatus.FAILURE;
        }
    }
    
    /**
     * 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 获取错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    /**
     * 获取所有错误信息的描述
     */
    public String getErrorMessages() {
        if (errors == null || errors.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < errors.size(); i++) {
            if (i > 0) {
                sb.append("; ");
            }
            sb.append(errors.get(i).getErrorMessage());
        }
        return sb.toString();
    }
    
    /**
     * 获取详细的错误描述
     */
    public String getDetailedErrorMessages() {
        if (errors == null || errors.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < errors.size(); i++) {
            if (i > 0) {
                sb.append("\n");
            }
            sb.append(errors.get(i).getFullErrorDescription());
        }
        return sb.toString();
    }
    
    /**
     * 创建成功的行数据
     */
    public static <T> ExcelRowData<T> success(Integer rowIndex, T data) {
        return ExcelRowData.<T>builder()
                .rowIndex(rowIndex)
                .data(data)
                .hasError(false)
                .status(RowStatus.SUCCESS)
                .build();
    }
    
    /**
     * 创建失败的行数据
     */
    public static <T> ExcelRowData<T> failure(Integer rowIndex, T data, List<ExcelErrorInfo> errors) {
        ExcelRowData<T> rowData = ExcelRowData.<T>builder()
                .rowIndex(rowIndex)
                .data(data)
                .hasError(true)
                .status(RowStatus.FAILURE)
                .build();
        
        if (errors != null && !errors.isEmpty()) {
            rowData.addErrors(errors);
        }
        
        return rowData;
    }
    
    /**
     * 创建跳过的行数据
     */
    public static <T> ExcelRowData<T> skipped(Integer rowIndex, String reason) {
        return ExcelRowData.<T>builder()
                .rowIndex(rowIndex)
                .hasError(false)
                .status(RowStatus.SKIPPED)
                .remark(reason)
                .build();
    }
    
    /**
     * 创建警告的行数据
     */
    public static <T> ExcelRowData<T> warning(Integer rowIndex, T data, String warningMessage) {
        return ExcelRowData.<T>builder()
                .rowIndex(rowIndex)
                .data(data)
                .hasError(false)
                .status(RowStatus.WARNING)
                .remark(warningMessage)
                .build();
    }
    
    /**
     * 设置为成功状态
     */
    public void setSuccess() {
        this.hasError = false;
        this.status = RowStatus.SUCCESS;
    }
    
    /**
     * 设置为跳过状态
     */
    public void setSkipped(String reason) {
        this.hasError = false;
        this.status = RowStatus.SKIPPED;
        this.remark = reason;
    }
    
    /**
     * 设置为警告状态
     */
    public void setWarning(String warningMessage) {
        this.hasError = false;
        this.status = RowStatus.WARNING;
        this.remark = warningMessage;
    }
}
