package com.xlc.excel.exception;

/**
 * Excel操作异常类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public class ExcelException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 错误行号
     */
    private Integer rowIndex;
    
    /**
     * 错误列号
     */
    private Integer columnIndex;
    
    /**
     * 文件名
     */
    private String fileName;
    
    public ExcelException() {
        super();
    }
    
    public ExcelException(String message) {
        super(message);
    }
    
    public ExcelException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public ExcelException(Throwable cause) {
        super(cause);
    }
    
    public ExcelException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public ExcelException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public ExcelException(String message, String fileName, Integer rowIndex, Integer columnIndex) {
        super(message);
        this.fileName = fileName;
        this.rowIndex = rowIndex;
        this.columnIndex = columnIndex;
    }
    
    public ExcelException(String errorCode, String message, String fileName, Integer rowIndex, Integer columnIndex) {
        super(message);
        this.errorCode = errorCode;
        this.fileName = fileName;
        this.rowIndex = rowIndex;
        this.columnIndex = columnIndex;
    }
    
    public ExcelException(String errorCode, String message, String fileName, Integer rowIndex, Integer columnIndex, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.fileName = fileName;
        this.rowIndex = rowIndex;
        this.columnIndex = columnIndex;
    }
    
    // Getter and Setter methods
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public Integer getRowIndex() {
        return rowIndex;
    }
    
    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }
    
    public Integer getColumnIndex() {
        return columnIndex;
    }
    
    public void setColumnIndex(Integer columnIndex) {
        this.columnIndex = columnIndex;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (fileName != null) {
            sb.append("文件[").append(fileName).append("]");
        }
        
        if (rowIndex != null) {
            sb.append("第").append(rowIndex).append("行");
        }
        
        if (columnIndex != null) {
            sb.append("第").append(columnIndex).append("列");
        }
        
        if (sb.length() > 0) {
            sb.append("：");
        }
        
        sb.append(getMessage());
        
        if (errorCode != null) {
            sb.append("（错误代码：").append(errorCode).append("）");
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "ExcelException{" +
                "errorCode='" + errorCode + '\'' +
                ", rowIndex=" + rowIndex +
                ", columnIndex=" + columnIndex +
                ", fileName='" + fileName + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
    
    // 静态工厂方法
    
    /**
     * 创建文件读取异常
     */
    public static ExcelException fileReadError(String fileName, Throwable cause) {
        return new ExcelException("FILE_READ_ERROR", "文件读取失败", fileName, null, null, cause);
    }
    
    /**
     * 创建文件写入异常
     */
    public static ExcelException fileWriteError(String fileName, Throwable cause) {
        return new ExcelException("FILE_WRITE_ERROR", "文件写入失败", fileName, null, null, cause);
    }
    
    /**
     * 创建数据解析异常
     */
    public static ExcelException parseError(String fileName, Integer rowIndex, Integer columnIndex, String message) {
        return new ExcelException("PARSE_ERROR", message, fileName, rowIndex, columnIndex);
    }
    
    /**
     * 创建数据解析异常
     */
    public static ExcelException parseError(String fileName, Integer rowIndex, Integer columnIndex, String message, Throwable cause) {
        return new ExcelException("PARSE_ERROR", message, fileName, rowIndex, columnIndex, cause);
    }
    
    /**
     * 创建数据校验异常
     */
    public static ExcelException validationError(String fileName, Integer rowIndex, String message) {
        return new ExcelException("VALIDATION_ERROR", message, fileName, rowIndex, null);
    }
    
    /**
     * 创建模板不匹配异常
     */
    public static ExcelException templateMismatchError(String fileName, String expectedTemplate, String actualTemplate) {
        String message = String.format("模板不匹配，期望：%s，实际：%s", expectedTemplate, actualTemplate);
        return new ExcelException("TEMPLATE_MISMATCH", message, fileName, null, null);
    }
    
    /**
     * 创建表头不匹配异常
     */
    public static ExcelException headerMismatchError(String fileName, String expectedHeader, String actualHeader) {
        String message = String.format("表头不匹配，期望：%s，实际：%s", expectedHeader, actualHeader);
        return new ExcelException("HEADER_MISMATCH", message, fileName, 1, null);
    }
    
    /**
     * 创建数据类型转换异常
     */
    public static ExcelException typeConversionError(String fileName, Integer rowIndex, Integer columnIndex, String expectedType, Object actualValue) {
        String message = String.format("数据类型转换失败，期望类型：%s，实际值：%s", expectedType, actualValue);
        return new ExcelException("TYPE_CONVERSION_ERROR", message, fileName, rowIndex, columnIndex);
    }
    
    /**
     * 创建数据类型转换异常
     */
    public static ExcelException typeConversionError(String fileName, Integer rowIndex, Integer columnIndex, String expectedType, Object actualValue, Throwable cause) {
        String message = String.format("数据类型转换失败，期望类型：%s，实际值：%s", expectedType, actualValue);
        return new ExcelException("TYPE_CONVERSION_ERROR", message, fileName, rowIndex, columnIndex, cause);
    }
    
    /**
     * 创建配置错误异常
     */
    public static ExcelException configError(String message) {
        return new ExcelException("CONFIG_ERROR", message);
    }
    
    /**
     * 创建业务逻辑异常
     */
    public static ExcelException businessError(String fileName, Integer rowIndex, String message) {
        return new ExcelException("BUSINESS_ERROR", message, fileName, rowIndex, null);
    }
    
    /**
     * 创建系统异常
     */
    public static ExcelException systemError(String message, Throwable cause) {
        return new ExcelException("SYSTEM_ERROR", message, cause);
    }
    
    /**
     * 创建未知异常
     */
    public static ExcelException unknownError(String message, Throwable cause) {
        return new ExcelException("UNKNOWN_ERROR", message, cause);
    }
}
