package com.xlc.excel.handler;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xlc.excel.config.ExcelConfig;
import com.xlc.excel.dto.ExcelErrorInfo;
import com.xlc.excel.utils.ExcelValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel数据校验处理器
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public class ExcelValidationHandler<T> {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelValidationHandler.class);
    
    /**
     * Excel配置
     */
    private ExcelConfig config;
    
    /**
     * 字段信息缓存
     */
    private Map<Class<?>, List<FieldValidationInfo>> fieldInfoCache = new HashMap<>();
    
    /**
     * 构造函数
     */
    public ExcelValidationHandler(ExcelConfig config) {
        this.config = config != null ? config : ExcelConfig.defaultConfig();
    }
    
    /**
     * 校验数据
     * 
     * @param data 数据对象
     * @param rowIndex 行号
     * @return 错误信息列表
     */
    public List<ExcelErrorInfo> validate(T data, Integer rowIndex) {
        List<ExcelErrorInfo> errors = new ArrayList<>();
        
        if (data == null) {
            errors.add(ExcelErrorInfo.builder()
                    .rowIndex(rowIndex)
                    .errorMessage("数据对象为空")
                    .errorType(ExcelErrorInfo.ErrorType.NULL_ERROR)
                    .build());
            return errors;
        }
        
        if (!config.isEnableValidation()) {
            return errors; // 如果禁用校验，直接返回空错误列表
        }
        
        try {
            // 获取字段校验信息
            List<FieldValidationInfo> fieldInfoList = getFieldValidationInfo(data.getClass());
            
            // 逐个字段校验
            for (FieldValidationInfo fieldInfo : fieldInfoList) {
                List<ExcelErrorInfo> fieldErrors = validateField(data, fieldInfo, rowIndex);
                errors.addAll(fieldErrors);
                
                // 如果错误数量超过限制，停止校验
                if (errors.size() >= config.getMaxErrorCount()) {
                    logger.warn("第{}行校验错误数量超过限制：{}", rowIndex, config.getMaxErrorCount());
                    break;
                }
            }
            
        } catch (Exception e) {
            logger.error("校验第{}行数据时发生异常：{}", rowIndex, e.getMessage(), e);
            errors.add(ExcelErrorInfo.builder()
                    .rowIndex(rowIndex)
                    .errorMessage("校验过程中发生异常：" + e.getMessage())
                    .errorType(ExcelErrorInfo.ErrorType.OTHER_ERROR)
                    .build());
        }
        
        return errors;
    }
    
    /**
     * 校验单个字段
     */
    private List<ExcelErrorInfo> validateField(T data, FieldValidationInfo fieldInfo, Integer rowIndex) {
        List<ExcelErrorInfo> errors = new ArrayList<>();
        
        try {
            Field field = fieldInfo.getField();
            field.setAccessible(true);
            Object value = field.get(data);
            
            String columnName = fieldInfo.getColumnName();
            Integer columnIndex = fieldInfo.getColumnIndex();
            
            // 必填校验
            if (fieldInfo.isRequired()) {
                errors.addAll(ExcelValidationUtils.validateRequired(value, rowIndex, columnIndex, columnName));
            }
            
            // 如果值为空且不是必填，跳过其他校验
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                return errors;
            }
            
            // 字符串长度校验
            if (value instanceof String) {
                String strValue = (String) value;
                
                if (fieldInfo.getMinLength() != null || fieldInfo.getMaxLength() != null) {
                    errors.addAll(ExcelValidationUtils.validateLength(strValue, 
                            fieldInfo.getMinLength(), fieldInfo.getMaxLength(), 
                            rowIndex, columnIndex, columnName));
                }
                
                // 正则表达式校验
                if (fieldInfo.getPattern() != null) {
                    errors.addAll(ExcelValidationUtils.validatePattern(strValue, 
                            fieldInfo.getPattern(), fieldInfo.getPatternMessage(), 
                            rowIndex, columnIndex, columnName));
                }
                
                // 枚举值校验
                if (fieldInfo.getValidValues() != null) {
                    errors.addAll(ExcelValidationUtils.validateEnum(strValue, 
                            fieldInfo.getValidValues(), rowIndex, columnIndex, columnName));
                }
                
                // 特殊格式校验
                if (fieldInfo.getValidationType() != null) {
                    switch (fieldInfo.getValidationType()) {
                        case EMAIL:
                            errors.addAll(ExcelValidationUtils.validateEmail(strValue, rowIndex, columnIndex, columnName));
                            break;
                        case PHONE:
                            errors.addAll(ExcelValidationUtils.validatePhone(strValue, rowIndex, columnIndex, columnName));
                            break;
                        case ID_CARD:
                            errors.addAll(ExcelValidationUtils.validateIdCard(strValue, rowIndex, columnIndex, columnName));
                            break;
                        case DATE:
                            errors.addAll(ExcelValidationUtils.validateDate(strValue, rowIndex, columnIndex, columnName));
                            break;
                        case NUMBER:
                            errors.addAll(ExcelValidationUtils.validateNumber(strValue, rowIndex, columnIndex, columnName));
                            break;
                        case INTEGER:
                            errors.addAll(ExcelValidationUtils.validateInteger(strValue, rowIndex, columnIndex, columnName));
                            break;
                    }
                }
            }
            
            // 数值范围校验
            if (value instanceof Number) {
                Number numValue = (Number) value;
                if (fieldInfo.getMinValue() != null || fieldInfo.getMaxValue() != null) {
                    errors.addAll(ExcelValidationUtils.validateRange(numValue, 
                            fieldInfo.getMinValue(), fieldInfo.getMaxValue(), 
                            rowIndex, columnIndex, columnName));
                }
            }
            
            // 自定义校验
            if (fieldInfo.getCustomValidator() != null) {
                errors.addAll(ExcelValidationUtils.validateCustom(value, 
                        fieldInfo.getCustomValidator(), rowIndex, columnIndex, columnName));
            }
            
        } catch (Exception e) {
            logger.error("校验字段{}时发生异常：{}", fieldInfo.getFieldName(), e.getMessage(), e);
            errors.add(ExcelErrorInfo.builder()
                    .rowIndex(rowIndex)
                    .columnIndex(fieldInfo.getColumnIndex())
                    .columnName(fieldInfo.getColumnName())
                    .errorMessage("字段校验异常：" + e.getMessage())
                    .errorType(ExcelErrorInfo.ErrorType.OTHER_ERROR)
                    .build());
        }
        
        return errors;
    }
    
    /**
     * 获取字段校验信息
     */
    private List<FieldValidationInfo> getFieldValidationInfo(Class<?> clazz) {
        // 从缓存中获取
        if (fieldInfoCache.containsKey(clazz)) {
            return fieldInfoCache.get(clazz);
        }
        
        List<FieldValidationInfo> fieldInfoList = new ArrayList<>();
        
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                FieldValidationInfo fieldInfo = createFieldValidationInfo(field, excelProperty);
                fieldInfoList.add(fieldInfo);
            }
        }
        
        // 缓存结果
        fieldInfoCache.put(clazz, fieldInfoList);
        
        return fieldInfoList;
    }
    
    /**
     * 创建字段校验信息
     */
    private FieldValidationInfo createFieldValidationInfo(Field field, ExcelProperty excelProperty) {
        FieldValidationInfo fieldInfo = new FieldValidationInfo();
        
        fieldInfo.setField(field);
        fieldInfo.setFieldName(field.getName());
        fieldInfo.setFieldType(field.getType());
        fieldInfo.setColumnIndex(excelProperty.index());
        
        String[] value = excelProperty.value();
        fieldInfo.setColumnName(value.length > 0 ? value[0] : field.getName());
        
        // 这里可以根据注解或配置设置校验规则
        // 由于EasyExcel的ExcelProperty注解功能有限，可以考虑自定义注解来扩展校验功能
        // 或者通过配置文件来定义校验规则
        
        // 示例：根据字段名设置一些默认校验规则
        setDefaultValidationRules(fieldInfo);
        
        return fieldInfo;
    }
    
    /**
     * 设置默认校验规则
     */
    private void setDefaultValidationRules(FieldValidationInfo fieldInfo) {
        String fieldName = fieldInfo.getFieldName().toLowerCase();
        
        // 根据字段名设置默认校验规则
        if (fieldName.contains("email") || fieldName.contains("邮箱")) {
            fieldInfo.setValidationType(ValidationType.EMAIL);
        } else if (fieldName.contains("phone") || fieldName.contains("手机") || fieldName.contains("电话")) {
            fieldInfo.setValidationType(ValidationType.PHONE);
        } else if (fieldName.contains("idcard") || fieldName.contains("身份证")) {
            fieldInfo.setValidationType(ValidationType.ID_CARD);
        } else if (fieldName.contains("date") || fieldName.contains("time") || fieldName.contains("日期") || fieldName.contains("时间")) {
            fieldInfo.setValidationType(ValidationType.DATE);
        } else if (fieldName.contains("age") || fieldName.contains("count") || fieldName.contains("num") || 
                   fieldName.contains("年龄") || fieldName.contains("数量") || fieldName.contains("编号")) {
            if (fieldInfo.getFieldType() == Integer.class || fieldInfo.getFieldType() == int.class) {
                fieldInfo.setValidationType(ValidationType.INTEGER);
            } else {
                fieldInfo.setValidationType(ValidationType.NUMBER);
            }
        }
        
        // 设置一些通用的必填字段
        if (fieldName.contains("name") || fieldName.contains("title") || fieldName.contains("code") ||
            fieldName.contains("姓名") || fieldName.contains("名称") || fieldName.contains("标题") || fieldName.contains("编码")) {
            fieldInfo.setRequired(true);
        }
    }
    
    /**
     * 字段校验信息类
     */
    public static class FieldValidationInfo {
        private Field field;
        private String fieldName;
        private Class<?> fieldType;
        private String columnName;
        private Integer columnIndex;
        private boolean required = false;
        private Integer minLength;
        private Integer maxLength;
        private Number minValue;
        private Number maxValue;
        private String pattern;
        private String patternMessage;
        private String[] validValues;
        private ValidationType validationType;
        private ExcelValidationUtils.CustomValidator customValidator;
        
        // Getter and Setter methods
        public Field getField() {
            return field;
        }
        
        public void setField(Field field) {
            this.field = field;
        }
        
        public String getFieldName() {
            return fieldName;
        }
        
        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }
        
        public Class<?> getFieldType() {
            return fieldType;
        }
        
        public void setFieldType(Class<?> fieldType) {
            this.fieldType = fieldType;
        }
        
        public String getColumnName() {
            return columnName;
        }
        
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }
        
        public Integer getColumnIndex() {
            return columnIndex;
        }
        
        public void setColumnIndex(Integer columnIndex) {
            this.columnIndex = columnIndex;
        }
        
        public boolean isRequired() {
            return required;
        }
        
        public void setRequired(boolean required) {
            this.required = required;
        }
        
        public Integer getMinLength() {
            return minLength;
        }
        
        public void setMinLength(Integer minLength) {
            this.minLength = minLength;
        }
        
        public Integer getMaxLength() {
            return maxLength;
        }
        
        public void setMaxLength(Integer maxLength) {
            this.maxLength = maxLength;
        }
        
        public Number getMinValue() {
            return minValue;
        }
        
        public void setMinValue(Number minValue) {
            this.minValue = minValue;
        }
        
        public Number getMaxValue() {
            return maxValue;
        }
        
        public void setMaxValue(Number maxValue) {
            this.maxValue = maxValue;
        }
        
        public String getPattern() {
            return pattern;
        }
        
        public void setPattern(String pattern) {
            this.pattern = pattern;
        }
        
        public String getPatternMessage() {
            return patternMessage;
        }
        
        public void setPatternMessage(String patternMessage) {
            this.patternMessage = patternMessage;
        }
        
        public String[] getValidValues() {
            return validValues;
        }
        
        public void setValidValues(String[] validValues) {
            this.validValues = validValues;
        }
        
        public ValidationType getValidationType() {
            return validationType;
        }
        
        public void setValidationType(ValidationType validationType) {
            this.validationType = validationType;
        }
        
        public ExcelValidationUtils.CustomValidator getCustomValidator() {
            return customValidator;
        }
        
        public void setCustomValidator(ExcelValidationUtils.CustomValidator customValidator) {
            this.customValidator = customValidator;
        }
    }
    
    /**
     * 校验类型枚举
     */
    public enum ValidationType {
        EMAIL,
        PHONE,
        ID_CARD,
        DATE,
        NUMBER,
        INTEGER
    }
}
