package com.xlc.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.xlc.excel.config.ExcelConfig;
import com.xlc.excel.dto.ExcelErrorInfo;
import com.xlc.excel.dto.ExcelImportResult;
import com.xlc.excel.dto.ExcelRowData;
import com.xlc.excel.exception.ExcelException;
import com.xlc.excel.handler.ExcelValidationHandler;
import com.xlc.excel.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Excel数据读取监听器
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public class ExcelDataListener<T> implements ReadListener<T> {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelDataListener.class);
    
    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    
    /**
     * 缓存的数据
     */
    private List<T> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    
    /**
     * 导入结果
     */
    private ExcelImportResult<T> importResult;
    
    /**
     * Excel配置
     */
    private ExcelConfig config;
    
    /**
     * 数据校验处理器
     */
    private ExcelValidationHandler<T> validationHandler;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 表名或模板标识
     */
    private String tableNameOrTemplate;
    
    /**
     * 目标数据类型
     */
    private Class<T> targetClass;
    
    /**
     * 当前行号
     */
    private int currentRowIndex = 0;
    
    /**
     * 是否启用预览模式
     */
    private boolean previewMode = false;
    
    /**
     * 预览行数
     */
    private int previewRows = 10;
    
    /**
     * 构造函数
     */
    public ExcelDataListener(String fileName, String tableNameOrTemplate, Class<T> targetClass, ExcelConfig config) {
        this.fileName = fileName;
        this.tableNameOrTemplate = tableNameOrTemplate;
        this.targetClass = targetClass;
        this.config = config != null ? config : ExcelConfig.defaultConfig();
        this.validationHandler = new ExcelValidationHandler<>(this.config);
        
        // 初始化导入结果
        this.importResult = ExcelImportResult.<T>builder()
                .fileName(fileName)
                .tableNameOrTemplate(tableNameOrTemplate)
                .startTime(LocalDateTime.now())
                .build();
        
        logger.info("开始读取Excel文件：{}, 表名/模板：{}", fileName, tableNameOrTemplate);
    }
    
    /**
     * 设置预览模式
     */
    public void setPreviewMode(boolean previewMode, int previewRows) {
        this.previewMode = previewMode;
        this.previewRows = previewRows;
    }
    
    /**
     * 这个每一条数据解析都会来调用
     */
    @Override
    public void invoke(T data, AnalysisContext context) {
        currentRowIndex = context.readRowHolder().getRowIndex() + 1; // 从1开始计数
        
        logger.debug("解析到一条数据：第{}行，数据：{}", currentRowIndex, data);
        
        try {
            // 预览模式检查
            if (previewMode && importResult.getDataRows() >= previewRows) {
                logger.info("预览模式：已读取{}行数据，停止读取", previewRows);
                return;
            }
            
            // 更新总行数
            importResult.setDataRows(importResult.getDataRows() + 1);
            
            // 跳过空行检查
            if (config.isSkipEmptyRows() && isEmptyRow(data)) {
                importResult.setSkippedRows(importResult.getSkippedRows() + 1);
                logger.debug("跳过空行：第{}行", currentRowIndex);
                return;
            }
            
            // 数据清理
            if (config.isAutoTrim()) {
                data = cleanData(data);
            }
            
            // 数据校验
            List<ExcelErrorInfo> validationErrors = null;
            if (config.isEnableValidation()) {
                validationErrors = validationHandler.validate(data, currentRowIndex);
            }
            
            // 处理校验结果
            if (validationErrors != null && !validationErrors.isEmpty()) {
                // 有校验错误
                importResult.setFailureRows(importResult.getFailureRows() + 1);
                importResult.addErrors(validationErrors);
                
                ExcelRowData<T> failureRowData = ExcelRowData.failure(currentRowIndex, data, validationErrors);
                importResult.addFailureData(failureRowData);
                
                logger.warn("第{}行数据校验失败：{}", currentRowIndex, validationErrors);
                
                // 严格模式下遇到错误立即停止
                if (!config.isContinueOnError()) {
                    throw new ExcelException("VALIDATION_ERROR", 
                            String.format("第%d行数据校验失败，严格模式下停止处理", currentRowIndex));
                }
                
                // 检查错误数量限制
                if (importResult.getErrorCount() >= config.getMaxErrorCount()) {
                    throw new ExcelException("MAX_ERROR_EXCEEDED", 
                            String.format("错误数量超过限制：%d", config.getMaxErrorCount()));
                }
            } else {
                // 校验通过
                importResult.setSuccessRows(importResult.getSuccessRows() + 1);
                importResult.addSuccessData(data);
                cachedDataList.add(data);
                
                logger.debug("第{}行数据校验通过", currentRowIndex);
            }
            
            // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
            if (cachedDataList.size() >= BATCH_COUNT) {
                saveData();
                // 存储完成清理 list
                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
            
        } catch (Exception e) {
            logger.error("处理第{}行数据时发生异常：{}", currentRowIndex, e.getMessage(), e);
            
            // 添加异常错误信息
            ExcelErrorInfo errorInfo = ExcelErrorInfo.builder()
                    .rowIndex(currentRowIndex)
                    .errorMessage(e.getMessage())
                    .errorType(ExcelErrorInfo.ErrorType.OTHER_ERROR)
                    .errorDetail(String.format("第%d行处理异常：%s", currentRowIndex, e.getMessage()))
                    .build();
            
            importResult.addError(errorInfo);
            importResult.setFailureRows(importResult.getFailureRows() + 1);
            
            // 严格模式下抛出异常
            if (!config.isContinueOnError()) {
                throw new ExcelException("PROCESSING_ERROR", 
                        String.format("第%d行数据处理失败", currentRowIndex), e);
            }
        }
    }
    
    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        
        // 设置总行数（包含表头）
        importResult.setTotalRows(context.readSheetHolder().getApproximateTotalRowNumber());
        
        // 设置导入完成时间
        importResult.setImportCompleted();
        
        // 判断导入是否成功
        boolean success = importResult.getFailureRows() == 0 || 
                         (config.isContinueOnError() && importResult.getSuccessRows() > 0);
        importResult.setSuccess(success);
        
        logger.info("Excel文件解析完成：{}", importResult.getSummary());
    }
    
    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则 继续读取下一行。
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) {
        logger.error("读取Excel文件时发生异常：第{}行", currentRowIndex, exception);
        
        // 创建错误信息
        ExcelErrorInfo errorInfo = ExcelErrorInfo.builder()
                .rowIndex(currentRowIndex)
                .errorMessage(exception.getMessage())
                .errorType(ExcelErrorInfo.ErrorType.PARSE_ERROR)
                .errorDetail(String.format("第%d行解析异常：%s", currentRowIndex, exception.getMessage()))
                .build();
        
        importResult.addError(errorInfo);
        importResult.setFailureRows(importResult.getFailureRows() + 1);
        
        // 严格模式下抛出异常停止读取
        if (!config.isContinueOnError()) {
            throw new ExcelException("PARSE_ERROR", 
                    String.format("第%d行数据解析失败", currentRowIndex), exception);
        }
    }
    
    /**
     * 加上存储数据库
     */
    private void saveData() {
        if (cachedDataList.isEmpty()) {
            return;
        }
        
        logger.debug("批量保存{}条数据到数据库", cachedDataList.size());
        
        // 这里可以根据实际需求实现数据库保存逻辑
        // 例如：调用Service层的批量保存方法
        // dataService.batchSave(cachedDataList);
        
        // 目前只是记录日志
        logger.info("成功保存{}条数据", cachedDataList.size());
    }
    
    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(T data) {
        if (data == null) {
            return true;
        }
        
        // 通过反射检查所有字段是否都为空
        try {
            java.lang.reflect.Field[] fields = data.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(data);
                if (value != null && !value.toString().trim().isEmpty()) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            logger.warn("检查空行时发生异常：{}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 清理数据（去除前后空格等）
     */
    private T cleanData(T data) {
        if (data == null) {
            return null;
        }
        
        try {
            java.lang.reflect.Field[] fields = data.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(data);
                
                if (value instanceof String) {
                    String cleanedValue = ExcelUtils.cleanString((String) value);
                    field.set(data, cleanedValue);
                }
            }
        } catch (Exception e) {
            logger.warn("清理数据时发生异常：{}", e.getMessage());
        }
        
        return data;
    }
    
    /**
     * 获取导入结果
     */
    public ExcelImportResult<T> getImportResult() {
        return importResult;
    }
    
    /**
     * 获取成功数据列表
     */
    public List<T> getSuccessDataList() {
        return importResult.getSuccessData();
    }
    
    /**
     * 获取失败数据列表
     */
    public List<ExcelRowData<T>> getFailureDataList() {
        return importResult.getFailureData();
    }
    
    /**
     * 获取错误信息列表
     */
    public List<ExcelErrorInfo> getErrorList() {
        return importResult.getErrors();
    }
}
