package com.xlc.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息实体类
 * 用于演示Excel导入导出功能
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo {
    
    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID", index = 0)
    private Long userId;
    
    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名", index = 1)
    private String username;
    
    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 2)
    private String realName;
    
    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱", index = 3)
    private String email;
    
    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号", index = 4)
    private String phone;
    
    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄", index = 5)
    private Integer age;
    
    /**
     * 性别（1-男，2-女）
     */
    @ExcelProperty(value = "性别", index = 6)
    private Integer gender;
    
    /**
     * 部门
     */
    @ExcelProperty(value = "部门", index = 7)
    private String department;
    
    /**
     * 职位
     */
    @ExcelProperty(value = "职位", index = 8)
    private String position;
    
    /**
     * 入职日期
     */
    @ExcelProperty(value = "入职日期", index = 9)
    private LocalDate hireDate;
    
    /**
     * 薪资
     */
    @ExcelProperty(value = "薪资", index = 10)
    private Double salary;
    
    /**
     * 状态（1-正常，0-禁用）
     */
    @ExcelProperty(value = "状态", index = 11)
    private Integer status;
    
    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 12)
    private String remark;
    
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 13)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", index = 14)
    private LocalDateTime updateTime;
    
    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        if (gender == null) {
            return "未知";
        }
        switch (gender) {
            case 1:
                return "男";
            case 2:
                return "女";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 0:
                return "禁用";
            default:
                return "未知";
        }
    }
    
    /**
     * 创建示例用户数据
     */
    public static UserInfo createSample(Long userId, String username) {
        return UserInfo.builder()
                .userId(userId)
                .username(username)
                .realName("用户" + userId)
                .email(username + "@example.com")
                .phone("138" + String.format("%08d", userId))
                .age(25 + (int)(userId % 20))
                .gender(userId % 2 == 0 ? 1 : 2)
                .department(userId % 3 == 0 ? "技术部" : (userId % 3 == 1 ? "销售部" : "市场部"))
                .position(userId % 2 == 0 ? "工程师" : "专员")
                .hireDate(LocalDate.now().minusDays(userId * 10))
                .salary(8000.0 + userId * 100)
                .status(1)
                .remark("这是用户" + userId + "的备注信息")
                .createTime(LocalDateTime.now().minusDays(userId))
                .updateTime(LocalDateTime.now())
                .build();
    }
    
    @Override
    public String toString() {
        return "UserInfo{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", realName='" + realName + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", age=" + age +
                ", gender=" + gender +
                ", department='" + department + '\'' +
                ", position='" + position + '\'' +
                ", hireDate=" + hireDate +
                ", salary=" + salary +
                ", status=" + status +
                ", remark='" + remark + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
