package com.xlc.excel.service;

import com.xlc.excel.service.ExcelService.ServiceHealthInfo;

import java.util.Map;

/**
 * Excel服务健康检查服务接口
 * 提供服务健康状态监控、指标收集等功能
 * 
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface ExcelHealthService {
    
    /**
     * 获取服务健康状态
     * 
     * @return 健康状态信息
     */
    ServiceHealthInfo getServiceHealth();
    
    /**
     * 记录导入任务开始
     */
    void recordImportTaskStart();
    
    /**
     * 记录导入任务结束
     */
    void recordImportTaskEnd();
    
    /**
     * 记录导出任务开始
     */
    void recordExportTaskStart();
    
    /**
     * 记录导出任务结束
     */
    void recordExportTaskEnd();
    
    /**
     * 记录错误信息
     * 
     * @param errorMessage 错误消息
     */
    void recordError(String errorMessage);
    
    /**
     * 获取活跃导入任务数量
     * 
     * @return 活跃导入任务数量
     */
    int getActiveImportTasks();
    
    /**
     * 获取活跃导出任务数量
     * 
     * @return 活跃导出任务数量
     */
    int getActiveExportTasks();
    
    /**
     * 获取内存使用情况
     * 
     * @return 内存使用信息
     */
    Map<String, Object> getMemoryUsage();
    
    /**
     * 获取服务运行时间
     * 
     * @return 运行时间（毫秒）
     */
    long getUptime();
    
    /**
     * 获取最后一次错误信息
     * 
     * @return 错误信息
     */
    String getLastError();
    
    /**
     * 获取最后一次错误时间
     * 
     * @return 错误时间
     */
    java.time.LocalDateTime getLastErrorTime();
    
    /**
     * 重置错误信息
     */
    void clearLastError();
    
    /**
     * 检查服务是否健康
     * 
     * @return 是否健康
     */
    boolean isHealthy();
}
