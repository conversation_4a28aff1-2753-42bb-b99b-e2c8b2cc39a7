package com.xlc.excel.service.impl;

import com.xlc.excel.service.ExcelHealthService;
import com.xlc.excel.service.ExcelService.ServiceHealthInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Excel服务健康检查服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
public class ExcelHealthServiceImpl implements ExcelHealthService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelHealthServiceImpl.class);
    
    /**
     * 服务启动时间
     */
    private final LocalDateTime serviceStartTime = LocalDateTime.now();
    
    /**
     * 活跃任务计数器
     */
    private volatile int activeImportTasks = 0;
    private volatile int activeExportTasks = 0;
    
    /**
     * 最后一次错误信息
     */
    private volatile String lastError;
    private volatile LocalDateTime lastErrorTime;
    
    @Override
    public ServiceHealthInfo getServiceHealth() {
        try {
            ServiceHealthInfo healthInfo = ServiceHealthInfo.healthy();
            
            // 设置基本信息
            healthInfo.setVersion("1.0.0");
            healthInfo.setUptime(java.time.Duration.between(serviceStartTime, LocalDateTime.now()).toMillis());
            healthInfo.setActiveImportTasks(activeImportTasks);
            healthInfo.setActiveExportTasks(activeExportTasks);
            healthInfo.setLastError(lastError);
            healthInfo.setLastErrorTime(lastErrorTime);
            
            // 设置指标信息
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("totalActiveTasks", activeImportTasks + activeExportTasks);
            metrics.put("serviceStartTime", serviceStartTime);
            metrics.put("memoryUsage", getMemoryUsage());
            healthInfo.setMetrics(metrics);
            
            logger.debug("服务健康检查完成：{}", healthInfo.getStatus());
            
            return healthInfo;
            
        } catch (Exception e) {
            logger.error("服务健康检查失败：{}", e.getMessage(), e);
            return ServiceHealthInfo.unhealthy("健康检查异常：" + e.getMessage());
        }
    }
    
    @Override
    public void recordImportTaskStart() {
        activeImportTasks++;
        logger.debug("导入任务开始，当前活跃任务数：{}", activeImportTasks);
    }
    
    @Override
    public void recordImportTaskEnd() {
        activeImportTasks--;
        logger.debug("导入任务结束，当前活跃任务数：{}", activeImportTasks);
    }
    
    @Override
    public void recordExportTaskStart() {
        activeExportTasks++;
        logger.debug("导出任务开始，当前活跃任务数：{}", activeExportTasks);
    }
    
    @Override
    public void recordExportTaskEnd() {
        activeExportTasks--;
        logger.debug("导出任务结束，当前活跃任务数：{}", activeExportTasks);
    }
    
    @Override
    public void recordError(String errorMessage) {
        this.lastError = errorMessage;
        this.lastErrorTime = LocalDateTime.now();
        logger.error("记录错误：{}", errorMessage);
    }
    
    @Override
    public int getActiveImportTasks() {
        return activeImportTasks;
    }
    
    @Override
    public int getActiveExportTasks() {
        return activeExportTasks;
    }
    
    @Override
    public Map<String, Object> getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memoryInfo = new HashMap<>();
        
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        memoryInfo.put("totalMemory", formatMemorySize(totalMemory));
        memoryInfo.put("freeMemory", formatMemorySize(freeMemory));
        memoryInfo.put("usedMemory", formatMemorySize(usedMemory));
        memoryInfo.put("maxMemory", formatMemorySize(maxMemory));
        memoryInfo.put("usagePercentage", String.format("%.2f%%", (double) usedMemory / maxMemory * 100));
        
        return memoryInfo;
    }
    
    @Override
    public long getUptime() {
        return java.time.Duration.between(serviceStartTime, LocalDateTime.now()).toMillis();
    }
    
    @Override
    public String getLastError() {
        return lastError;
    }
    
    @Override
    public LocalDateTime getLastErrorTime() {
        return lastErrorTime;
    }
    
    @Override
    public void clearLastError() {
        this.lastError = null;
        this.lastErrorTime = null;
        logger.debug("已清除最后错误信息");
    }
    
    @Override
    public boolean isHealthy() {
        // 简单的健康检查逻辑
        // 可以根据需要添加更复杂的检查条件
        return activeImportTasks >= 0 && activeExportTasks >= 0;
    }
    
    /**
     * 格式化内存大小
     */
    private String formatMemorySize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
