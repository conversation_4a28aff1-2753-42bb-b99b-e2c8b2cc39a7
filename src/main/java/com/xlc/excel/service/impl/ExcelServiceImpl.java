package com.xlc.excel.service.impl;

import com.xlc.excel.dto.ExcelExportRequest;
import com.xlc.excel.dto.ExcelImportRequest;
import com.xlc.excel.dto.ExcelImportResult;
import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.service.ExcelExportService;
import com.xlc.excel.service.ExcelImportService;
import com.xlc.excel.service.ExcelService;
import com.xlc.excel.service.ExcelHealthService;
import com.xlc.excel.service.ExcelUtilService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Excel服务主实现类
 * 整合导入导出功能的统一入口
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class ExcelServiceImpl implements ExcelService {

    private static final Logger logger = LoggerFactory.getLogger(ExcelServiceImpl.class);

    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private ExcelExportService excelExportService;

    @Autowired
    private ExcelHealthService excelHealthService;

    @Autowired
    private ExcelUtilService excelUtilService;
    
    // ==================== 导入相关方法 ====================
    
    @Override
    public <T> ExcelImportResult<T> importExcel(ExcelImportRequest request) {
        excelHealthService.recordImportTaskStart();
        try {
            logger.info("开始导入Excel文件：{}", request.getFileName());
            return excelImportService.importExcel(request);
        } catch (Exception e) {
            excelHealthService.recordError("导入Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordImportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelImportResult<T> importByTableName(InputStream inputStream, String fileName, String tableName, Class<T> targetClass) {
        excelHealthService.recordImportTaskStart();
        try {
            logger.info("根据表名导入Excel文件：{}，表名：{}", fileName, tableName);
            return excelImportService.importByTableName(inputStream, fileName, tableName, targetClass);
        } catch (Exception e) {
            excelHealthService.recordError("根据表名导入Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordImportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelImportResult<T> importByTemplate(InputStream inputStream, String fileName, ExcelTemplateEnum template, Class<T> targetClass) {
        excelHealthService.recordImportTaskStart();
        try {
            logger.info("根据模板导入Excel文件：{}，模板：{}", fileName, template.getName());
            return excelImportService.importByTemplate(inputStream, fileName, template, targetClass);
        } catch (Exception e) {
            excelHealthService.recordError("根据模板导入Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            excelHealthService.recordImportTaskEnd();
        }
    }
    
    @Override
    public <T> ExcelImportResult<T> previewExcel(InputStream inputStream, String fileName, Class<T> targetClass, int previewRows) {
        activeImportTasks++;
        try {
            logger.info("预览Excel文件：{}，预览行数：{}", fileName, previewRows);
            return excelImportService.previewExcel(inputStream, fileName, targetClass, previewRows);
        } catch (Exception e) {
            recordError("预览Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            activeImportTasks--;
        }
    }
    
    @Override
    public <T> CompletableFuture<ExcelImportResult<T>> importExcelAsync(ExcelImportRequest request) {
        activeImportTasks++;
        logger.info("开始异步导入Excel文件：{}", request.getFileName());
        
        return excelImportService.importExcelAsync(request)
                .whenComplete((result, throwable) -> {
                    activeImportTasks--;
                    if (throwable != null) {
                        recordError("异步导入Excel文件失败：" + throwable.getMessage());
                    }
                });
    }
    
    // ==================== 导出相关方法 ====================
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportExcel(ExcelExportRequest<T> request) {
        activeExportTasks++;
        try {
            logger.info("开始导出Excel文件：{}", request.getFileName());
            return excelExportService.exportExcel(request);
        } catch (Exception e) {
            recordError("导出Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            activeExportTasks--;
        }
    }
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportByTableName(OutputStream outputStream, String fileName, String tableName, List<T> data, Class<T> dataClass) {
        activeExportTasks++;
        try {
            logger.info("根据表名导出Excel文件：{}，表名：{}，数据行数：{}", fileName, tableName, data != null ? data.size() : 0);
            return excelExportService.exportByTableName(outputStream, fileName, tableName, data, dataClass);
        } catch (Exception e) {
            recordError("根据表名导出Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            activeExportTasks--;
        }
    }
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportByTemplate(OutputStream outputStream, String fileName, ExcelTemplateEnum template, List<T> data, Class<T> dataClass) {
        activeExportTasks++;
        try {
            logger.info("根据模板导出Excel文件：{}，模板：{}，数据行数：{}", fileName, template.getName(), data != null ? data.size() : 0);
            return excelExportService.exportByTemplate(outputStream, fileName, template, data, dataClass);
        } catch (Exception e) {
            recordError("根据模板导出Excel文件失败：" + e.getMessage());
            throw e;
        } finally {
            activeExportTasks--;
        }
    }
    
    @Override
    public ExcelExportService.ExcelExportResult exportTemplate(OutputStream outputStream, String fileName, ExcelTemplateEnum template) {
        activeExportTasks++;
        try {
            logger.info("导出Excel模板文件：{}，模板：{}", fileName, template.getName());
            return excelExportService.exportTemplate(outputStream, fileName, template);
        } catch (Exception e) {
            recordError("导出Excel模板文件失败：" + e.getMessage());
            throw e;
        } finally {
            activeExportTasks--;
        }
    }
    
    @Override
    public <T> ExcelExportService.ExcelExportResult exportTemplate(OutputStream outputStream, String fileName, String tableName, Class<T> dataClass) {
        activeExportTasks++;
        try {
            logger.info("导出Excel模板文件：{}，表名：{}", fileName, tableName);
            return excelExportService.exportTemplate(outputStream, fileName, tableName, dataClass);
        } catch (Exception e) {
            recordError("导出Excel模板文件失败：" + e.getMessage());
            throw e;
        } finally {
            activeExportTasks--;
        }
    }
    
    @Override
    public <T> CompletableFuture<ExcelExportService.ExcelExportResult> exportExcelAsync(ExcelExportRequest<T> request) {
        activeExportTasks++;
        logger.info("开始异步导出Excel文件：{}", request.getFileName());
        
        return excelExportService.exportExcelAsync(request)
                .whenComplete((result, throwable) -> {
                    activeExportTasks--;
                    if (throwable != null) {
                        recordError("异步导出Excel文件失败：" + throwable.getMessage());
                    }
                });
    }
    
    // ==================== 工具方法 ====================
    
    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, ExcelTemplateEnum template) {
        try {
            logger.debug("验证Excel文件格式：{}，模板：{}", fileName, template.getName());
            return excelImportService.validateExcelFormat(inputStream, fileName, template);
        } catch (Exception e) {
            recordError("验证Excel文件格式失败：" + e.getMessage());
            logger.error("验证Excel文件格式失败：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean validateExcelFormat(InputStream inputStream, String fileName, String tableName) {
        try {
            logger.debug("验证Excel文件格式：{}，表名：{}", fileName, tableName);
            return excelImportService.validateExcelFormat(inputStream, fileName, tableName);
        } catch (Exception e) {
            recordError("验证Excel文件格式失败：" + e.getMessage());
            logger.error("验证Excel文件格式失败：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public ExcelImportService.ExcelFileInfo getExcelFileInfo(InputStream inputStream, String fileName) {
        try {
            logger.debug("获取Excel文件信息：{}", fileName);
            return excelImportService.getExcelFileInfo(inputStream, fileName);
        } catch (Exception e) {
            recordError("获取Excel文件信息失败：" + e.getMessage());
            logger.error("获取Excel文件信息失败：{}", e.getMessage(), e);
            
            // 返回错误信息
            ExcelImportService.ExcelFileInfo errorInfo = new ExcelImportService.ExcelFileInfo();
            errorInfo.setFileName(fileName);
            errorInfo.setValid(false);
            errorInfo.setErrorMessage("获取文件信息失败：" + e.getMessage());
            return errorInfo;
        }
    }
    
    @Override
    public List<ExcelTemplateEnum> getSupportedTemplates() {
        logger.debug("获取支持的模板列表");
        return Arrays.asList(ExcelTemplateEnum.values());
    }
    
    @Override
    public ExcelTemplateEnum getTemplateByTableName(String tableName) {
        logger.debug("根据表名获取模板：{}", tableName);
        return ExcelTemplateEnum.getByTableName(tableName);
    }
    
    @Override
    public ServiceHealthInfo getServiceHealth() {
        try {
            ServiceHealthInfo healthInfo = ServiceHealthInfo.healthy();
            
            // 设置基本信息
            healthInfo.setVersion("1.0.0");
            healthInfo.setUptime(java.time.Duration.between(serviceStartTime, LocalDateTime.now()).toMillis());
            healthInfo.setActiveImportTasks(activeImportTasks);
            healthInfo.setActiveExportTasks(activeExportTasks);
            healthInfo.setLastError(lastError);
            healthInfo.setLastErrorTime(lastErrorTime);
            
            // 设置指标信息
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("totalActiveTasks", activeImportTasks + activeExportTasks);
            metrics.put("serviceStartTime", serviceStartTime);
            metrics.put("memoryUsage", getMemoryUsage());
            healthInfo.setMetrics(metrics);
            
            logger.debug("服务健康检查完成：{}", healthInfo.getStatus());
            
            return healthInfo;
            
        } catch (Exception e) {
            logger.error("服务健康检查失败：{}", e.getMessage(), e);
            return ServiceHealthInfo.unhealthy("健康检查异常：" + e.getMessage());
        }
    }
    
    /**
     * 记录错误信息
     */
    private void recordError(String errorMessage) {
        this.lastError = errorMessage;
        this.lastErrorTime = LocalDateTime.now();
        logger.error("记录错误：{}", errorMessage);
    }
    
    /**
     * 获取内存使用情况
     */
    private Map<String, Object> getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memoryInfo = new HashMap<>();
        
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        memoryInfo.put("totalMemory", formatMemorySize(totalMemory));
        memoryInfo.put("freeMemory", formatMemorySize(freeMemory));
        memoryInfo.put("usedMemory", formatMemorySize(usedMemory));
        memoryInfo.put("maxMemory", formatMemorySize(maxMemory));
        memoryInfo.put("usagePercentage", String.format("%.2f%%", (double) usedMemory / maxMemory * 100));
        
        return memoryInfo;
    }
    
    /**
     * 格式化内存大小
     */
    private String formatMemorySize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
