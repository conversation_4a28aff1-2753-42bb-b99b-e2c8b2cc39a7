package com.xlc.excel.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xlc.excel.exception.ExcelException;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Excel工具类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
public class ExcelUtils {
    
    /**
     * 常用日期格式
     */
    private static final String[] DATE_PATTERNS = {
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyyMMdd",
            "yyyy年MM月dd日",
            "MM/dd/yyyy",
            "dd/MM/yyyy"
    };
    
    /**
     * 数字格式正则表达式
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");
    
    /**
     * 整数格式正则表达式
     */
    private static final Pattern INTEGER_PATTERN = Pattern.compile("^-?\\d+$");
    
    /**
     * 邮箱格式正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");
    
    /**
     * 手机号格式正则表达式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    /**
     * 身份证号格式正则表达式
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    
    /**
     * 检查文件是否为Excel格式
     * 
     * @param fileName 文件名
     * @return 是否为Excel格式
     */
    public static boolean isExcelFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        
        String lowerCaseFileName = fileName.toLowerCase();
        return lowerCaseFileName.endsWith(".xlsx") || 
               lowerCaseFileName.endsWith(".xls") || 
               lowerCaseFileName.endsWith(".xlsm");
    }
    
    /**
     * 获取Excel文件类型
     * 
     * @param fileName 文件名
     * @return 文件类型
     */
    public static String getExcelFileType(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "unknown";
        }
        
        String lowerCaseFileName = fileName.toLowerCase();
        if (lowerCaseFileName.endsWith(".xlsx")) {
            return "xlsx";
        } else if (lowerCaseFileName.endsWith(".xls")) {
            return "xls";
        } else if (lowerCaseFileName.endsWith(".xlsm")) {
            return "xlsm";
        }
        
        return "unknown";
    }
    
    /**
     * 验证文件大小
     * 
     * @param inputStream 输入流
     * @param maxSizeInMB 最大文件大小（MB）
     * @return 是否符合大小限制
     */
    public static boolean validateFileSize(InputStream inputStream, int maxSizeInMB) {
        try {
            long maxSizeInBytes = maxSizeInMB * 1024L * 1024L;
            return inputStream.available() <= maxSizeInBytes;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 格式化文件大小
     * 
     * @param sizeInBytes 文件大小（字节）
     * @return 格式化后的文件大小
     */
    public static String formatFileSize(long sizeInBytes) {
        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.2f KB", sizeInBytes / 1024.0);
        } else if (sizeInBytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", sizeInBytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", sizeInBytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取类的Excel列信息
     * 
     * @param clazz 类
     * @return 列信息映射（列索引 -> 字段信息）
     */
    public static Map<Integer, FieldInfo> getExcelColumnInfo(Class<?> clazz) {
        Map<Integer, FieldInfo> columnMap = new HashMap<>();
        
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (excelProperty != null) {
                int index = excelProperty.index();
                String[] value = excelProperty.value();
                String columnName = value.length > 0 ? value[0] : field.getName();
                
                FieldInfo fieldInfo = new FieldInfo();
                fieldInfo.setField(field);
                fieldInfo.setColumnName(columnName);
                fieldInfo.setColumnIndex(index);
                fieldInfo.setFieldName(field.getName());
                fieldInfo.setFieldType(field.getType());
                
                columnMap.put(index, fieldInfo);
            }
        }
        
        return columnMap;
    }
    
    /**
     * 字段信息类
     */
    public static class FieldInfo {
        private Field field;
        private String columnName;
        private int columnIndex;
        private String fieldName;
        private Class<?> fieldType;
        
        // Getter and Setter methods
        public Field getField() {
            return field;
        }
        
        public void setField(Field field) {
            this.field = field;
        }
        
        public String getColumnName() {
            return columnName;
        }
        
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }
        
        public int getColumnIndex() {
            return columnIndex;
        }
        
        public void setColumnIndex(int columnIndex) {
            this.columnIndex = columnIndex;
        }
        
        public String getFieldName() {
            return fieldName;
        }
        
        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }
        
        public Class<?> getFieldType() {
            return fieldType;
        }
        
        public void setFieldType(Class<?> fieldType) {
            this.fieldType = fieldType;
        }
    }
    
    /**
     * 转换字符串为指定类型
     * 
     * @param value 字符串值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    public static Object convertStringToType(String value, Class<?> targetType) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        
        value = value.trim();
        
        try {
            if (targetType == String.class) {
                return value;
            } else if (targetType == Integer.class || targetType == int.class) {
                return Integer.valueOf(value);
            } else if (targetType == Long.class || targetType == long.class) {
                return Long.valueOf(value);
            } else if (targetType == Double.class || targetType == double.class) {
                return Double.valueOf(value);
            } else if (targetType == Float.class || targetType == float.class) {
                return Float.valueOf(value);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.valueOf(value);
            } else if (targetType == Date.class) {
                return parseDate(value);
            } else if (targetType == LocalDate.class) {
                return parseLocalDate(value);
            } else if (targetType == LocalDateTime.class) {
                return parseLocalDateTime(value);
            }
        } catch (Exception e) {
            throw new ExcelException("TYPE_CONVERSION_ERROR", 
                    String.format("无法将值 '%s' 转换为类型 %s", value, targetType.getSimpleName()), e);
        }
        
        return value;
    }
    
    /**
     * 解析日期字符串
     * 
     * @param dateStr 日期字符串
     * @return Date对象
     */
    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                sdf.setLenient(false);
                return sdf.parse(dateStr.trim());
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        throw new ExcelException("DATE_PARSE_ERROR", "无法解析日期格式：" + dateStr);
    }
    
    /**
     * 解析LocalDate字符串
     * 
     * @param dateStr 日期字符串
     * @return LocalDate对象
     */
    public static LocalDate parseLocalDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        
        String[] patterns = {"yyyy-MM-dd", "yyyy/MM/dd", "yyyyMMdd", "yyyy年MM月dd日"};
        
        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDate.parse(dateStr.trim(), formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        throw new ExcelException("DATE_PARSE_ERROR", "无法解析日期格式：" + dateStr);
    }
    
    /**
     * 解析LocalDateTime字符串
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseLocalDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            return null;
        }
        
        String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy-MM-dd HH:mm",
                "yyyy/MM/dd HH:mm",
                "yyyyMMdd HHmmss",
                "yyyy年MM月dd日 HH时mm分ss秒"
        };
        
        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDateTime.parse(dateTimeStr.trim(), formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        throw new ExcelException("DATETIME_PARSE_ERROR", "无法解析日期时间格式：" + dateTimeStr);
    }
    
    /**
     * 验证数字格式
     * 
     * @param value 值
     * @return 是否为数字格式
     */
    public static boolean isNumber(String value) {
        return StringUtils.isNotBlank(value) && NUMBER_PATTERN.matcher(value.trim()).matches();
    }
    
    /**
     * 验证整数格式
     * 
     * @param value 值
     * @return 是否为整数格式
     */
    public static boolean isInteger(String value) {
        return StringUtils.isNotBlank(value) && INTEGER_PATTERN.matcher(value.trim()).matches();
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱
     * @return 是否为有效邮箱格式
     */
    public static boolean isValidEmail(String email) {
        return StringUtils.isNotBlank(email) && EMAIL_PATTERN.matcher(email.trim()).matches();
    }
    
    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return 是否为有效手机号格式
     */
    public static boolean isValidPhone(String phone) {
        return StringUtils.isNotBlank(phone) && PHONE_PATTERN.matcher(phone.trim()).matches();
    }
    
    /**
     * 验证身份证号格式
     * 
     * @param idCard 身份证号
     * @return 是否为有效身份证号格式
     */
    public static boolean isValidIdCard(String idCard) {
        return StringUtils.isNotBlank(idCard) && ID_CARD_PATTERN.matcher(idCard.trim()).matches();
    }
    
    /**
     * 清理字符串（去除前后空格和特殊字符）
     * 
     * @param value 原始值
     * @return 清理后的值
     */
    public static String cleanString(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        
        // 去除前后空格
        value = value.trim();
        
        // 去除不可见字符
        value = value.replaceAll("[\\u0000-\\u001f\\u007f-\\u009f]", "");
        
        // 去除多余的空格
        value = value.replaceAll("\\s+", " ");
        
        return StringUtils.isBlank(value) ? null : value;
    }
    
    /**
     * 生成Excel列名（A, B, C, ..., AA, AB, ...）
     * 
     * @param columnIndex 列索引（从0开始）
     * @return 列名
     */
    public static String getExcelColumnName(int columnIndex) {
        StringBuilder columnName = new StringBuilder();
        while (columnIndex >= 0) {
            columnName.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return columnName.toString();
    }
    
    /**
     * 解析Excel列名为索引
     * 
     * @param columnName 列名（A, B, C, ..., AA, AB, ...）
     * @return 列索引（从0开始）
     */
    public static int parseExcelColumnIndex(String columnName) {
        if (StringUtils.isBlank(columnName)) {
            return -1;
        }
        
        columnName = columnName.toUpperCase();
        int result = 0;
        for (int i = 0; i < columnName.length(); i++) {
            result = result * 26 + (columnName.charAt(i) - 'A' + 1);
        }
        return result - 1;
    }
}
