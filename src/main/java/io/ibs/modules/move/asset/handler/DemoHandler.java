package io.ibs.modules.move.asset.handler;

import io.ibs.modules.move.AbstractDataMoveService;
import lombok.NoArgsConstructor;

import java.util.Map;

@NoArgsConstructor
public class DemoHandler extends AbstractDataMoveService {

    @Override
    protected void execute(Map<String, Object> args) {
        // 查询数据库
        // List<DemoEntity> demo = new ArrayList<>();
        // setDefaultParams(demo);
    }
    //
    // private void setDefaultParams(List<DemoEntity> demo) {
    //     for (DemoEntity entity : demo) {
    //         demo.setName(null); // 中文注释
    //         demo.setAge(null); // 中文注释
    //     ...
    //     }
    // }
}
