package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.AbstractDataMoveService;
import io.ibs.modules.move.entity.*;
import io.ibs.modules.move.handler.*;
import lombok.Getter;

/**
 * 数据迁移表信息枚举
 * 
 * 使用说明：
 * 1. 在代码生成完成后，需要手动添加每个表的枚举项
 * 2. 枚举项格式：枚举名(编号, "表中文名", "表名", 实体类.class, new Handler实例())
 * 3. 编号建议按顺序递增
 * 4. 表中文名用于显示和说明
 * 5. 表名为数据库中的实际表名
 * 6. 实体类为生成的Entity类
 * 7. Handler为对应的数据迁移处理器实例
 * 
 * <AUTHOR>
 */
@Getter
public enum DMTableInfo {

    // 电子账户相关表
    DMDDNCAA(101, "电子账户绑定关系表", "DMDDNCAA", DmddncaaEntity.class, new DmddncaaHandler()),
    DMDDNCRA(102, "电子账户登记簿", "DMDDNCRA", DmddncraEntity.class, new DmddncraHandler()),
    DMDDNCRP(103, "电子账户开户补充信息表", "DMDDNCRP", DmddncrpEntity.class, new DmddncrpHandler()),

    // 账户相关表
    DMDPABLA(104, "账户当前积数表", "DMDPABLA", DmdpablaEntity.class, new DmdpablaHandler()),
    DMDPABSA(105, "账户分段积数表", "DMDPABSA", DmdpabsaEntity.class, new DmdpabsaHandler()),
    DMDPMSDA(106, "账户迁移表", "DMDPMSDA", DmdpmsdaEntity.class, new DmdpmsdaHandler()),
    DMDPSDBA(107, "强制扣划登记簿中间文件", "DMDPSDBA", DmdpsdbEntity.class, new DmdpsdbHandler()),
    DMDPSLGA(108, "单位睡眠户客户账户登记簿", "DMDPSLGA", DmdpslgaEntity.class, new DmdpslgaHandler()),
    DMDPSRBA(109, "特种业务登记簿", "DMDPSRBA", DmdpsrbaEntity.class, new DmdpsrbaHandler()),
    DMDPSRBP(110, "特种业务司法登记薄附表", "DMDPSRBP", DmdpsrbpEntity.class, new DmdpsrbpHandler()),
    DMDPUPDA(111, "未登折明细记录表", "DMDPUPDA", DmdpupdaEntity.class, new DmdpupdaHandler()),

    // 代理和定期相关表
    DMPMAAHA(112, "代理人开户历史档", "DMPMAAHA", DmpmaahaEntity.class, new DmpmaahaHandler()),
    DMTDARTA(113, "代村镇银行支付定期利息明细文件", "DMTDARTA", DmtdartaEntity.class, new DmtdartaHandler()),
    DMTDPCDA(114, "个人周期计划文件-关联表", "DMTDPCDA", DmtdpcdaEntity.class, new DmtdpcdaHandler()),
    DMTDRDDA(115, "定期账户明细文件", "DMTDRDDA", DmtdrddaEntity.class, new DmtdrddaHandler()),

    // 对公对私账户表
    DMDDCDSA(116, "对公活期账户明细文件", "DMDDCDSA", DmddcdsaEntity.class, new DmddcdsaHandler()),
    DMDDRDSA(117, "对私活期账户明细文件", "DMDDRDSA", DmddrdsaEntity.class, new DmddrdsaHandler()),
    DMDPCTLA(118, "控制对象累计限额档", "DMDPCTLA", DmdpctlaEntity.class, new DmdpctlaHandler()),

    // 总账相关表
    DMGLMSTB_201(201, "日总账", "DMGLMSTB", DmglmstbEntity.class, new DmglmstbHandler()),
    DMIAIAIA(202, "内部账户", "DMIAIAIA", DmiaiaiaEntity.class, new DmiaiaiaHandler()),
    DMGLMSTB_203(203, "老核心日总账表中间表", "DMGLMSTB", DmglmstbEntity.class, new DmglmstbHandler()),

    // 贷款相关表
    DMLNCOFA(301, "协议基本信息", "DMLNCOFA", DmlncofaEntity.class, new DmlncofaHandler()),
    DMLNDDFA(302, "协议放款基本信息", "DMLNDDFA", DmlnddfaEntity.class, new DmlnddfaHandler()),
    DMLNETDA(303, "登记受托支付处理明细信息", "DMLNETDA", DmlnetdaEntity.class, new DmlnetdaHandler()),
    DMLNFJNA(304, "表内交易明细", "DMLNFJNA", DmlnfjnaEntity.class, new DmlnfjnaHandler()),
    DMLNIBFA(305, "借据主档", "DMLNIBFA", DmlnibfaEntity.class, new DmlnibfaHandler()),
    DMLNIBLA(306, "借据余额主档", "DMLNIBLA", DmlniblaEntity.class, new DmlniblaHandler()),
    DMLNIRHA(307, "执行利率资料基本信息", "DMLNIRHA", DmlnirhaEntity.class, new DmlnirhaHandler()),
    DMLNIRPA(308, "贷款利率重定价资料", "DMLNIRPA", DmlnirpaEntity.class, new DmlnirpaHandler()),
    DMLNIRTA(309, "贷款正常利率资料基本信息", "DMLNIRTA", DmlnirtaEntity.class, new DmlnirtaHandler()),
    DMLNKJNA1(310, "关键交易明细-正交易", "DMLNKJNA1", Dmlnkjna1Entity.class, new Dmlnkjna1Handler()),
    DMLNKJNA2(311, "关键交易明细-冲正", "DMLNKJNA2", Dmlnkjna2Entity.class, new Dmlnkjna2Handler()),
    DMLNPIRA(312, "贷款罚息利率资料基本信息", "DMLNPIRA", DmlnpiraEntity.class, new DmlnpiraHandler()),
    DMLNRJNA(313, "贷款还款交易明细", "DMLNRJNA", DmlnrjnaEntity.class, new DmlnrjnaHandler()),
    DMLNRLAA(314, "跨模块关联账户登记薄", "DMLNRLAA", DmlnrlaaEntity.class, new DmlnrlaaHandler()),
    DMLNRPFA(315, "协议还款基本信息", "DMLNRPFA", DmlnrpfaEntity.class, new DmlnrpfaHandler()),
    DMLNRPPA1(316, "还款计划表（应还款明细）", "DMLNRPPA1", Dmlnrppa1Entity.class, new Dmlnrppa1Handler()),
    DMLNRPPA2(317, "还款计划表（其他）", "DMLNRPPA2", Dmlnrppa2Entity.class, new Dmlnrppa2Handler()),
    DMLNWOFA(318, "核销及收回登记簿", "DMLNWOFA", DmlnwofaEntity.class, new DmlnwofaHandler());

    /**
     * 编号
     */
    private final Integer code;
    
    /**
     * 表中文名称
     */
    private final String name;
    
    /**
     * 数据库表名
     */
    private final String tableName;
    
    /**
     * 实体类Class
     */
    private final Class<?> entityClass;
    
    /**
     * 数据迁移处理器
     */
    private final AbstractDataMoveService handler;

    /**
     * 构造函数
     */
    DMTableInfo(Integer code, String name, String tableName, Class<?> entityClass, AbstractDataMoveService handler) {
        this.code = code;
        this.name = name;
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    /**
     * 根据编号获取枚举
     */
    public static DMTableInfo getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DMTableInfo tableInfo : values()) {
            if (tableInfo.getCode().equals(code)) {
                return tableInfo;
            }
        }
        return null;
    }

    /**
     * 根据表名获取枚举
     */
    public static DMTableInfo getByTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return null;
        }
        for (DMTableInfo tableInfo : values()) {
            if (tableInfo.getTableName().equalsIgnoreCase(tableName.trim())) {
                return tableInfo;
            }
        }
        return null;
    }

    /**
     * 根据实体类获取枚举
     */
    public static DMTableInfo getByEntityClass(Class<?> entityClass) {
        if (entityClass == null) {
            return null;
        }
        for (DMTableInfo tableInfo : values()) {
            if (tableInfo.getEntityClass().equals(entityClass)) {
                return tableInfo;
            }
        }
        return null;
    }
}
