package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmdpablaEntityHandler;
import io.ibs.modules.move.common.entity.DmdpablaEntityEntity;

public enum DMTableInfo {
    DMDPABLAENTITY("DMDPABLA", DmdpablaEntityEntity.class, new DmdpablaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}