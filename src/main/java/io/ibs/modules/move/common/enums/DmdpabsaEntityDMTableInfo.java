package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmdpabsaEntityHandler;
import io.ibs.modules.move.common.entity.DmdpabsaEntityEntity;

public enum DMTableInfo {
    DMDPABSAENTITY("DMDPABSA", DmdpabsaEntityEntity.class, new DmdpabsaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}