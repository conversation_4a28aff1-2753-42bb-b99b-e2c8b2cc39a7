package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmdpmsdaEntityHandler;
import io.ibs.modules.move.common.entity.DmdpmsdaEntityEntity;

public enum DMTableInfo {
    DMDPMSDAENTITY("DMDPMSDA", DmdpmsdaEntityEntity.class, new DmdpmsdaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}