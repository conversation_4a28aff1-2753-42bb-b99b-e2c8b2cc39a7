package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmdpsdbaEntityHandler;
import io.ibs.modules.move.common.entity.DmdpsdbaEntityEntity;

public enum DMTableInfo {
    DMDPSDBAENTITY("DMDPSDBA", DmdpsdbaEntityEntity.class, new DmdpsdbaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}