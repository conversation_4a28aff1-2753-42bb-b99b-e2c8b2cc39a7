package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmdpsrbaEntityHandler;
import io.ibs.modules.move.common.entity.DmdpsrbaEntityEntity;

public enum DMTableInfo {
    DMDPSRBAENTITY("DMDPSRBA", DmdpsrbaEntityEntity.class, new DmdpsrbaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}