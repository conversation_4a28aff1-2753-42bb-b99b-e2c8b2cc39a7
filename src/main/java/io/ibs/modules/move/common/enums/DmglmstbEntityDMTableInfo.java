package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmglmstbEntityHandler;
import io.ibs.modules.move.common.entity.DmglmstbEntityEntity;

public enum DMTableInfo {
    DMGLMSTBENTITY("DMGLMSTB", DmglmstbEntityEntity.class, new DmglmstbEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}