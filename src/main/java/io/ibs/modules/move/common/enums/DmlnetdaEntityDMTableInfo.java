package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmlnetdaEntityHandler;
import io.ibs.modules.move.common.entity.DmlnetdaEntityEntity;

public enum DMTableInfo {
    DMLNETDAENTITY("DMLNETDA", DmlnetdaEntityEntity.class, new DmlnetdaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}