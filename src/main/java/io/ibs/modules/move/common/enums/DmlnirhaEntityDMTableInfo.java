package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmlnirhaEntityHandler;
import io.ibs.modules.move.common.entity.DmlnirhaEntityEntity;

public enum DMTableInfo {
    DMLNIRHAENTITY("DMLNIRHA", DmlnirhaEntityEntity.class, new DmlnirhaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}