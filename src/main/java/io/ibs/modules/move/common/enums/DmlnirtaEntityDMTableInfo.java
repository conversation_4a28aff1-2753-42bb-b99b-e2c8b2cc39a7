package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmlnirtaEntityHandler;
import io.ibs.modules.move.common.entity.DmlnirtaEntityEntity;

public enum DMTableInfo {
    DMLNIRTAENTITY("DMLNIRTA", DmlnirtaEntityEntity.class, new DmlnirtaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}