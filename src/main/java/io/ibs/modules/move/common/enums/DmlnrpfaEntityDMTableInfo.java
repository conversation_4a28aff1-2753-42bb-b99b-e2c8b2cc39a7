package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmlnrpfaEntityHandler;
import io.ibs.modules.move.common.entity.DmlnrpfaEntityEntity;

public enum DMTableInfo {
    DMLNRPFAENTITY("DMLNRPFA", DmlnrpfaEntityEntity.class, new DmlnrpfaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}