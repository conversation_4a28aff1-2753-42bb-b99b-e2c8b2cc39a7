package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.Dmlnrppa2EntityHandler;
import io.ibs.modules.move.common.entity.Dmlnrppa2EntityEntity;

public enum DMTableInfo {
    DMLNRPPA2ENTITY("DMLNRPPA2", Dmlnrppa2EntityEntity.class, new Dmlnrppa2EntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}