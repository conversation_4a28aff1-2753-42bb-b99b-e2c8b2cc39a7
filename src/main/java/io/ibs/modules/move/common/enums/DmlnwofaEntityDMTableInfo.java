package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmlnwofaEntityHandler;
import io.ibs.modules.move.common.entity.DmlnwofaEntityEntity;

public enum DMTableInfo {
    DMLNWOFAENTITY("DMLNWOFA", DmlnwofaEntityEntity.class, new DmlnwofaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}