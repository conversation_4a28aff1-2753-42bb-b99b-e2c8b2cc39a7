package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmpmaahaEntityHandler;
import io.ibs.modules.move.common.entity.DmpmaahaEntityEntity;

public enum DMTableInfo {
    DMPMAAHAENTITY("DMPMAAHA", DmpmaahaEntityEntity.class, new DmpmaahaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}