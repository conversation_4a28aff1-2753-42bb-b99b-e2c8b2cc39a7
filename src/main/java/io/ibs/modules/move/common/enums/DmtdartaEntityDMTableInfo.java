package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmtdartaEntityHandler;
import io.ibs.modules.move.common.entity.DmtdartaEntityEntity;

public enum DMTableInfo {
    DMTDARTAENTITY("DMTDARTA", DmtdartaEntityEntity.class, new DmtdartaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}