package io.ibs.modules.move.common.enums;

import io.ibs.modules.move.asset.handler.DmtdrddaEntityHandler;
import io.ibs.modules.move.common.entity.DmtdrddaEntityEntity;

public enum DMTableInfo {
    DMTDRDDAENTITY("DMTDRDDA", DmtdrddaEntityEntity.class, new DmtdrddaEntityHandler());

    private final String tableName;
    private final Class<?> entityClass;
    private final Object handler;

    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.handler = handler;
    }

    // getters
}