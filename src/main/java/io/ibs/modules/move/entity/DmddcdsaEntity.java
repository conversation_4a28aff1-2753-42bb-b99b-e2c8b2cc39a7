package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMDDCDSA）对公活期账户明细文件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@Builder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName("DMDDCDSA")
public class DmddcdsaEntity extends DMBaseEntity {

    /**
     * 技术分片键；CUST_NUM
     */
    private String dtbtKey;

    /**
     * 银行号；001
     */
    private String bankNum;

    /**
     * 客户号
     */
    private String custNum;

    /**
     * 客户账号；账号
     */
    private String custActnum;

    /**
     * 账户序号；有一本通，送迁出账户序号
     */
    private String acctSqnum;

    /**
     * 系统账号；移植送空值
     */
    private String systActnum;

    /**
     * 币种；001
     */
    private String ccyDgtlCod;

    /**
     * 钞汇标识；移植送空值
     */
    private String cashRmitFlg;

    /**
     * 交易日期
     */
    private Integer tranDate;

    /**
     * 账户交易序号；必输。当发生账务交易时系统账户主表的账户交易序号累加，同时登记到账户明细
     */
    private String acctTranSqnum;

    /**
     * 交易机构号；待确定送新机构还是原机构
     */
    private String tranOrgnNum;

    /**
     * 归属机构；待确定送新机构还是原机构
     */
    private String afltOrgn;

    /**
     * 管理机构；待确定送新机构还是原机构
     */
    private String mangOrgn;

    /**
     * 交易日志号；迁出方核心流水
     */
    private String tranLogNum;

    /**
     * 用户名；交易柜员号
     */
    private String userNam;

    /**
     * 核心交易码；交易码
     */
    private String coreTranCod;

    /**
     * 现转标识
     */
    private String cashTrfrAcctFlg;

    /**
     * 借贷标识
     */
    private String dbitCrdtFlg;

    /**
     * 扣账方式；迁出方只有1，默认送1
     */
    private String dducBillMode;

    /**
     * 交易金额
     */
    private Long tranAmt;

    /**
     * 账户余额
     */
    private Long acctBalc;

    /**
     * 透支金额；移植送空值
     */
    private Long ovdfAmt;

    /**
     * 起息日期；查一下有没有展示
     */
    private Integer effvInrsDate;

    /**
     * 明细冲账标识；迁出方无数据
     */
    private String dtalStrkBalcFlg;

    /**
     * 冲账类型；迁出方无数据
     */
    private String strkBalcTyp;

    /**
     * 冲账对方交易日期；迁出方无数据
     */
    private Integer rvrsAcctCnpyTranDate;

    /**
     * 冲账对方日志号；迁出方无数据
     */
    private String strkBalcCnpyLogNum;

    /**
     * 支付凭证代码；有就给，没有送空
     */
    private String payVochCod;

    /**
     * 冠字号；有就给，没有送空
     */
    private String headSeriNum;

    /**
     * 凭证号码；有就给，没有送空
     */
    private BigDecimal vochNum;

    /**
     * 凭证签发日期；有就给，没有送空
     */
    private Integer vochSignIssuDate;

    /**
     * 系统账户产品编码；移植送空值
     */
    private String systAcctPrduNum;

    /**
     * 服务类产品编码；移植送空值
     */
    private String srveClssPrduNum;

    /**
     * 客户账户产品编码；移植送空值
     */
    private String custAcctPrduEncd;

    /**
     * 管理任务号；移植送空值
     */
    private String mangTaskNum;

    /**
     * 利润中心；移植送空值
     */
    private String prftCter;

    /**
     * 转账原因；移植送空值
     */
    private String tractReas;

    /**
     * 现金项目代码；待定
     */
    private String cashPrjtCod;

    /**
     * 代理经办标识；按实际情况送，在代理人信息中查到送Y
     */
    private String agntOperFlg;

    /**
     * 发起方渠道；按实际登记送
     */
    private String intorChnl;

    /**
     * 发起方系统代码；迁出方没有
     */
    private String intorSystCod;

    /**
     * 发起方终端号；迁出方没有
     */
    private String intorTmnlNum;

    /**
     * 发起方交易码；迁出方没有
     */
    private String intorTranCod;

    /**
     * 发起方交易流水号；迁出方没有
     */
    private String intorTranSenum;

    /**
     * 影像ID；迁出方没有
     */
    private String imagId;

    /**
     * 商户类型；迁出方没有
     */
    private String mrchTyp;

    /**
     * 商户编号；迁出方没有
     */
    private String mrchNum;

    /**
     * 发起方日期/申请日期；迁出方没有
     */
    private Integer intorDate;

    /**
     * 通用对方客户账号；按实际登记送
     */
    private String uvslCnpyCustActnum;

    /**
     * 对方账户名称；按实际登记送
     */
    private String cnpyAcctNam;

    /**
     * 对方行号；待确定送新机构还是原机构
     */
    private String cnpyBankNum;

    /**
     * 对方账户序号；按实际登记送
     */
    private String cnpyAcctSqnum;

    /**
     * 对方行名称；待确定送新机构还是原机构
     */
    private String cnpyBankNam;

    /**
     * 网银链接号；移植送空值
     */
    private String iebakLinkNum;

    /**
     * 摘要代码；移植送空值
     */
    private String abstCod;

    /**
     * 摘要；迁出方送摘要内容
     */
    private String abstTxt;

    /**
     * 业务操作码；移植送空值
     */
    private String bsinOperCod;

    /**
     * 静态授权主管；按实际登记送
     */
    private String sttcAthoDrcr;

    /**
     * 动态授权主管；移植送空值
     */
    private String dynmAthoDrcr;

    /**
     * 交易内明细序号；0001
     */
    private String tranInnrDtalSqnum;

    /**
     * 交易时间；按实际登记送
     */
    private Integer tranTime;

    /**
     * 关联交易日期；移植送空值
     */
    private Integer relaTranDate;

    /**
     * 关联交易日志号；移植送空值
     */
    private String relaTranLogNum;

    /**
     * 退汇原因；移植送空值
     */
    private String retnExhgReas;

    /**
     * 退汇处理状态；移植送空值
     */
    private String retnExhgPrcsStas;

    /**
     * 资金相关标识；默认送N
     */
    private String cptlRelaFlg;

    /**
     * 金融交易标识；默认送Y
     */
    private String fincTranFlg;

    /**
     * 代理支付标志；默认送N
     */
    private String agntPayFlg;

    /**
     * 是否有附表；默认送0
     */
    private String ifHavApedForm;

    /**
     * 明细预留日期1；预留字段暂不处理
     */
    private Integer dtalPrsvDate1;

    /**
     * 明细预留日期2；预留字段暂不处理
     */
    private Integer dtalPrsvDate2;

    /**
     * 明细预留账户1；预留字段暂不处理
     */
    private String dtalPrsvAcct1;

    /**
     * 明细预留账户2；预留字段暂不处理
     */
    private String dtalPrsvAcct2;

    /**
     * 明细扩展属性域；移出方无相关场景，移植送空值
     */
    private String dtalExteAttrDomn;

    /**
     * 预留数值；移植送空值
     */
    private Long preResvNum;

    /**
     * 时间戳；转为时间类型，形如：2022/10/14 11:08:25
     */
    private LocalDateTime tmtpTime;

    /**
     * 建立时间戳；转为时间类型，形如：2022/10/14 11:08:25
     */
    private LocalDateTime cratTmtp;

    /**
     * 发起方系统时间
     */
    private Integer intorSystTime;

    /**
     * 本地交易日期
     */
    private Integer loclTranDate;

    /**
     * 本地交易时间
     */
    private Integer loclTranTime;

    /**
     * 本地时间戳；转为时间类型，形如：2022/10/14 11:08:25
     */
    private LocalDateTime loclTmtp;

    /**
     * 结单展示标志；移植送空值
     */
    private String stmtShowFlg;
}
