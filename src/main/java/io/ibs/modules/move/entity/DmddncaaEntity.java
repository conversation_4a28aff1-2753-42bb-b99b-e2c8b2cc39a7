package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMDDNCAA）电子账户绑定关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMDDNCAA")
public class DmddncaaEntity extends DMBaseEntity {

    /**
     * 技术分片键；客户号
     */
    private String dtbtKey;

    /**
     * 银行号；默认001
     */
    private String bankNum;

    /**
     * 客户账号；绑定时，写入，二三类账户
     */
    private String custActnum;

    /**
     * 序号；绑定时，写入，送0001
     */
    private String sqnumLeng6;

    /**
     * 绑定账号；写入接口传入值，绑定主账户
     */
    private String bindActnum;

    /**
     * 是否默认账户；修改默认账户时写入，默认Y
     */
    private String ifDfltAcct;

    /**
     * 本行他行标识；写入接口传入值，送本
     */
    private String selbkOthbkFlg;

    /**
     * 对方行名称；写入接口传入值，送空
     */
    private String cnpyBankNam;

    /**
     * 对方行支付联行号；写入接口传入值，送空
     */
    private String cnpyBankPayUninBankNum;

    /**
     * 央行金融机构编码；央行金融机构编码，送空
     */
    private String cterBankFincOrgnNum;

    /**
     * 绑定状态；只迁移绑定的，送1；绑定时，写入；解绑时，修改
     */
    private String bindStas;

    /**
     * 绑定日期；绑定时，写入，送开户日期
     */
    private Integer bindDate;

    /**
     * 绑定发起方系统；绑定时，写入，待定发起方系统编码统一送值
     */
    private String bindIntorSyst;

    /**
     * 绑定交易日志号；绑定时，写入
     */
    private String bindTranLogNum;

    /**
     * 解绑日期；解绑时，写入
     */
    private Integer canlBindDate;

    /**
     * 解绑发起方系统；解绑时，写入，送空
     */
    private String unbdIntorSyst;

    /**
     * 解绑交易日志号；解绑时，写入
     */
    private String canlBindTranLogNum;

    /**
     * 预留位120；作为预留信息，送空
     */
    private String preResvBit120;

    /**
     * 时间戳；记录信息变化时更新
     */
    private LocalDateTime tmtpTime;
}
