package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMDDNCRA）电子账户登记簿
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMDDNCRA")
public class DmddncraEntity extends DMBaseEntity {

    /**
     * 技术分片键；客户号
     */
    private String dtbtKey;

    /**
     * 银行号；默认001
     */
    private String bankNum;

    /**
     * 归属机构；系统账户建立时从开户机构获取，新机构
     */
    private String afltOrgn;

    /**
     * 客户号；写入接口传入值
     */
    private String custNum;

    /**
     * 公私标识；写入接口传入值
     */
    private String coprPesnFlg;

    /**
     * 客户账号；一个客户只能开一个网络客户账号
     */
    private String custActnum;

    /**
     * 是否绑定本行卡；迁入方确认绑定关系；绑定卡中有本行卡为Y，无本行卡为N，未绑定卡为空
     */
    private String ifBindSelbkCard;

    /**
     * 本行他行标识；写入接口传入值，默认1
     */
    private String selbkOthbkFlg;

    /**
     * 绑定账号；写入接口传入值，迁出送主账号
     */
    private String bindActnum;

    /**
     * 电子账户开户项目；迁入方处理，迁出方送空；自此段，在移植过程中，单独确认。
     */
    private String eletAcctOpactPrjt;

    /**
     * 电子账户主标识；电子账户主标识，迁出方2类户送2、3类户送3
     */
    private String eletAcctPrimFlg;

    /**
     * 发起方系统代码；开户时登记，送ACP
     */
    private String intorSystCod;

    /**
     * 身份验证状态；写入接口传入值，送1
     */
    private String idVldtStas;

    /**
     * 绑定账户验证状态；写入接口传入值，送1
     */
    private String bindAcctVldtStas;

    /**
     * 网络账号状态；1- 待激活：绑定账户验证状态不是1- 已通过，并且账户验证未通过；2- 已激活：绑定账户验证状态=1- 已通过。或：账户验证通过；3-已销户：销户时更新；4.-已开卡：开卡时更新
     */
    private String netActnumStas;

    /**
     * 是否需要补充客户信息；根据客户信息反馈数据更新，送0
     */
    private String ifNeedSupyCustInfo;

    /**
     * 账户级别；迁入方确认送值，待定；建立时写入，绑定账户、身份认证变化时修改0-入门级1-初级2-中级3-高级，即已换实体卡
     */
    private String acctLevl;

    /**
     * 是否人脸识别；默认送N；客户人脸识别完成后修改为Y
     */
    private String ifFacRcog;

    /**
     * 身份审核方式；默认1；根据输入登记
     */
    private String idChkrMode;

    /**
     * 身份证是否上传；迁出方不确认是否有资料，默认N；身份证是否上传
     */
    private String idIfUpld;

    /**
     * 三类户是否具备非绑定卡入金功能；需要迁出方确认；三类户是否具备非绑定卡入金功能
     */
    private String thrdAcctIfNoBindCardDeps;

    /**
     * 三类户累计绑定入金金额；需要迁出方确认；三类户累计绑定入金金额
     */
    private BigDecimal threTypAcctAcmlDepsAmt;

    /**
     * 上次绑定卡入金发生日期；需要迁出方确认；上次绑定卡入金发生日期
     */
    private Integer lastTimeBindCardDepsDate;

    /**
     * 首笔非绑定入金发生日期；需要迁出方确认；首笔入金发生日
     */
    private Integer frstNotBindDepsHapnDate;

    /**
     * 首笔非绑定出金发生日期；需要迁出方确认；首笔出金发生日期
     */
    private Integer frstNotBindWhdwHapnDate;

    /**
     * 首笔绑定入金发生日期；需要迁出方确认；首笔绑定入金发生日期
     */
    private Integer frstBindDepsHapnDate;

    /**
     * 首笔绑定出金发生日期；需要迁出方确认；首笔绑定出金发生日期
     */
    private Integer frstBindWhdwHapnDate;

    /**
     * 最近手动结息日期；默认送0；最近手动结息日期
     */
    private Integer rcenManlInrsSettDate;

    /**
     * 行政区划；默认送空；行政区划
     */
    private String admtDivi;

    /**
     * 特种业务编号；默认送空；特种业务编号
     */
    private String spclBsinNum;

    /**
     * 在线建立客户标识；送N；开立电子账户时建立客户信息为Y，否则为N
     */
    private String onleCratCustFlg;

    /**
     * 原账号/卡号；默认送空；直销银行，手机银行电子账户变更时写入客户账号、或换卡时写入客户账号
     */
    private String fomrActnumOrCardNum;

    /**
     * 激活日期；激活时写入，送开户日期
     */
    private Integer acteDate;

    /**
     * 清算/销户/销号/关闭日期；销户时写入，送0
     */
    private Integer clrgClactClosDate;

    /**
     * 开卡日期；配发卡时写入，送0
     */
    private Integer openCardDate;

    /**
     * 维护日期；表中任何字段变更均更新，送开户日期
     */
    private Integer mantDate;

    /**
     * 推荐代码；初始写入，送空
     */
    private String rcmmCod;

    /**
     * 在线开户拒绝原因；身份验证未通过时写入，送空
     */
    private String onleOpactRejtReas;

    /**
     * 建立日期；签约建立协议时写入，不再变化，送开户日期
     */
    private Integer cratDate;

    /**
     * 建立用户；签约建立协议时写入，不再变化，柜员
     */
    private String cratUser;

    /**
     * 备用字段；第1-6位：百度、众安E户宝标识第7位：京东电子账户标识第8位：电子账户项目支付方式（参看1001169），送空
     */
    private String auxyFeld;

    /**
     * 时间戳；2021-05-20 13:38:55.915522
     */
    private LocalDateTime tmtpTime;
}
