package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMDPSDBA）强制扣划登记簿中间文件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMDPSDBA")
public class DmdpsdbaEntity extends DMBaseEntity {

    /**
     * 技术分片键；客户号
     */
    private String dtbtKey;

    /**
     * 银行号；001
     */
    private String bankNum;

    /**
     * 客户账号
     */
    private String custActnum;

    /**
     * 货币数字代码/币种；001
     */
    private String ccyDgtlCod;

    /**
     * 钞汇标识；送空
     */
    private String cashRmitFlg;

    /**
     * 账户序号
     */
    private String acctSqnum;

    /**
     * 交易日期
     */
    private Integer tranDate;

    /**
     * 交易日志号
     */
    private String tranLogNum;

    /**
     * 扣划类型；行内扣划可能包含司法扣划，无法区分
     */
    private String dducTyp;

    /**
     * 扣划机构号；送新机构，还是原机构，待定
     */
    private String dducOrgnNum;

    /**
     * 司法扣划文书号；按实际送，送文书编号
     */
    private String judDducDoctNum;

    /**
     * 特种业务编号；迁出方无关联关系，找不到冻结编号，送空
     */
    private String spclBsinNum;

    /**
     * 计息标识；迁出方待定
     */
    private String calcInrsFlg;

    /**
     * 计划扣划金额；送0
     */
    private Long planDducAmt;

    /**
     * 账户余额；账户余额
     */
    private Long acctBalc;

    /**
     * 可扣划金额；账户余额
     */
    private Long admiDducAmt;

    /**
     * 实际扣划金额；小于等于账户余额
     */
    private Long actuDducAmt;

    /**
     * 划入账号；收款账号
     */
    private String dducInActnum;

    /**
     * 划入账户户名；收款账号名称
     */
    private String dducInAcctNam;

    /**
     * 本金；扣划金额
     */
    private Long prcpAmt;

    /**
     * 利息；迁出方送0
     */
    private Long inrsAmtAmt;

    /**
     * 利息税；送0
     */
    private Long inrsTax;

    /**
     * 扣划原因；行内扣划，送扣划原因
     */
    private String redcReas;

    /**
     * 扣划日期
     */
    private Integer dducDate;

    /**
     * 扣划时间
     */
    private Integer dducTime;

    /**
     * 通用对方客户账号；没有，送空
     */
    private String uvslCnpyCustActnum;

    /**
     * 对方账户名称；没有，送空
     */
    private String cnpyAcctNam;

    /**
     * 对方行号；没有，送空
     */
    private String cnpyBankNum;

    /**
     * 对方行名称；没有，送空
     */
    private String cnpyBankNam;

    /**
     * 机构号；交易机构？送新机构，还是原机构，待定
     */
    private String orgnNum;

    /**
     * 操作用户；柜员
     */
    private String operUser;

    /**
     * 备注；没有，送空
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;

    /**
     * 挂销账编号；没有，送空
     */
    private String htwofNum;

    /**
     * 扣划事项；没有，送空
     */
    private String dducMatt;

    /**
     * 发起方系统代码；没有，送空
     */
    private String intorSystCod;

    /**
     * 发起方交易流水号；没有，送空
     */
    private String intorTranSenum;

    /**
     * 发起方交易码；没有，送空
     */
    private String intorTranCod;

    /**
     * 预留日期；没有，送空
     */
    private Integer prsvDate;

    /**
     * 备用金额；没有，送空
     */
    private Long auxyAmt;

    /**
     * 备用域；没有，送空
     */
    private String auxyDomn;

    /**
     * 建立时间戳
     */
    private LocalDateTime cratTmtp;

    /**
     * 扣划文书号；送空
     */
    private String dducDoctNum;

    /**
     * 本地交易日期
     */
    private Integer loclTranDate;

    /**
     * 本地交易时间
     */
    private Integer loclTranTime;

    /**
     * 本地时间戳
     */
    private LocalDateTime loclTmtp;
}
