package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMDPUPDA）未登折明细记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMDPUPDA")
public class DmdpupdaEntity extends DMBaseEntity {

    /**
     * 技术分片键；客户号
     */
    private String dtbtKey;

    /**
     * 银行号；001
     */
    private String bankNum;

    /**
     * 交易日期
     */
    private Integer tranDate;

    /**
     * 交易日志号
     */
    private String tranLogNum;

    /**
     * 交易时间
     */
    private Integer tranTime;

    /**
     * 登折标识；未销户活期迁移未登折，未销户定期迁移所有
     */
    private String regrPsbkFlg;

    /**
     * 客户账号
     */
    private String custActnum;

    /**
     * 系统账号；送空
     */
    private String systActnum;

    /**
     * 账户序号
     */
    private String acctSqnum;

    /**
     * 产品编码；送空
     */
    private String prduNum;

    /**
     * 存期
     */
    private String depsPerd;

    /**
     * 起息日期；待定
     */
    private Integer effvInrsDate;

    /**
     * 开户利率
     */
    private Integer opactInrat;

    /**
     * 币种；001
     */
    private String ccyDgtlCod;

    /**
     * 钞汇标识；送空
     */
    private String cashRmitFlg;

    /**
     * 借贷标识；D-借C-贷
     */
    private String dbitCrdtFlg;

    /**
     * 交易金额
     */
    private Long tranAmt;

    /**
     * 交易后余额
     */
    private Long balcAftrTran;

    /**
     * 凭证号码
     */
    private BigDecimal vochNum;

    /**
     * 打印行数
     */
    private Short pritRow;

    /**
     * 最后补打日期；送0
     */
    private Integer lastSplmPritDate;

    /**
     * 补打次数；送0
     */
    private Short splmPritTims;

    /**
     * 交易机构号；新机构
     */
    private String tranOrgnNum;

    /**
     * 摘要代码；送空
     */
    private String abstCod;

    /**
     * 摘要短内容；送摘要内容
     */
    private String abstShrtCntt;

    /**
     * 交易用户；柜员
     */
    private String tranUser;

    /**
     * 到期日期；账户的到期日期
     */
    private Integer expiDate;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
