package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p>
 * 会计（DMGLMSTB）日总账
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMGLMSTB")
public class DmglmstbEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号
     */
    private String bankNum;

    /**
     * 科目
     */
    private String resvBit12;

    /**
     * 机构号
     */
    private String orgnNum;

    /**
     * 日期
     */
    private String dateInfo;

    /**
     * 货币数字代码/币种
     */
    private String ccyDgtlCod;

    /**
     * 当日贷方余额
     */
    private BigDecimal sadayCrdtBalc;

    /**
     * 当日借方余额
     */
    private BigDecimal sadayDbitBalc;
}
