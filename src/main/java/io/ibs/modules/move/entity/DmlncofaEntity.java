package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNCOFA）协议基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNCOFA")
public class DmlncofaEntity extends DMBaseEntity {

    /**
     * 技术分片键_客户号
     */
    private String dtbtKey;

    /**
     * 银行号
     */
    private String bankNum;

    /**
     * 机构号
     */
    private String orgnNum;

    /**
     * 协议编号
     */
    private String prtcNumLeng13;

    /**
     * 贷款协议状态
     */
    private String loanPrtcStas;

    /**
     * 客户号
     */
    private String custNum;

    /**
     * 产品编码
     */
    private String prduNum;

    /**
     * 相关产品编码
     */
    private String relaPrduNum;

    /**
     * 贷款大类
     */
    private String loanBigClss;

    /**
     * 币种
     */
    private String ccyDgtlCod;

    /**
     * 协议金额
     */
    private Long prtcAmt;

    /**
     * 循环标识
     */
    private String cyclFlg;

    /**
     * 贷款实际投向
     */
    private String loanActuInveDrec;

    /**
     * 用途细类
     */
    private String useSbcl;

    /**
     * 担保分类
     */
    private String grteClss;

    /**
     * 担保细类
     */
    private String grteSbcl;

    /**
     * 起始日期
     */
    private Integer origDate;

    /**
     * 生效日期
     */
    private Integer takeEfftDate;

    /**
     * 到期日期
     */
    private Integer expiDate;

    /**
     * 原始到期日
     */
    private Integer origExpiDate;

    /**
     * 停息/取消停息日
     */
    private Integer stopInrsSwtcDate;

    /**
     * 协议期限
     */
    private String prtcLimt;

    /**
     * 短中长期标识
     */
    private String shrtMidlLongPerdFlg;

    /**
     * 贷款经营类型
     */
    private String loanMangTyp;

    /**
     * 银团贷款牵参标识
     */
    private String syndLoanLeadFlg;

    /**
     * 本行份额
     */
    private Long selbkLot;

    /**
     * 收息方式
     */
    private String chagInrsMode;

    /**
     * 计息天数类型
     */
    private String calcInrsDayCntTyp;

    /**
     * 罚息计息天数类型
     */
    private String defitCalcInrsDayTyp;

    /**
     * 年率基数
     */
    private String yearInrsRatCrlt;

    /**
     * 年率基数实际天数
     */
    private String annlRatBaseRealDayCnt;

    /**
     * 是否结息标识
     */
    private String ifSettInrsFlg;

    /**
     * 是否转逾期
     */
    private String ifTrfrOdue;

    /**
     * 是否免罚息复利
     */
    private String ifExmtDefitComit;

    /**
     * 是否转后收息
     */
    private String ifLevyInrsAftrTrfr;

    /**
     * 还款宽限方式
     */
    private String defitGracMode;

    /**
     * 还款宽限期
     */
    private Short defitGracDate;

    /**
     * 逾期宽限期
     */
    private Integer odueGracDate;

    /**
     * 到期宽限期
     */
    private Short expiGracPerd;

    /**
     * 到期宽限期使用利率标识
     */
    private String expiGracUseInratFlg;

    /**
     * 合同到期前使用利率标识
     */
    private String expiGracPerdInratUseFlg;

    /**
     * 贴息利率
     */
    private Integer sbsiInrsInrat;

    /**
     * 管理机构
     */
    private String mangOrgn;

    /**
     * 对应账户类型
     */
    private String crspAcctTyp;

    /**
     * 小额质押标识
     */
    private String smalAmtPldgFlg;

    /**
     * 随借随还标识
     */
    private String loanWithRepyFlg;

    /**
     * 关联授信额度编号
     */
    private String relaCrdtLimtNum;

    /**
     * 关联项目协议
     */
    private String relaPrjtPrtc;

    /**
     * 子项目协议编号
     */
    private String subPrjtPrtcNum;

    /**
     * 公积金中心代码
     */
    private String pbrfdCterCod;

    /**
     * 公积金贷款协议号
     */
    private String pbrfdLoanPrtcNum;

    /**
     * 委托人客户号
     */
    private String prcpCustNum;

    /**
     * 委贷实现方式
     */
    private String entsLoanReliMode;

    /**
     * 收款人客户号
     */
    private String payeCustNum;

    /**
     * 共享金额
     */
    private Long sharAmt;

    /**
     * 专用金额
     */
    private Long spclAmt;

    /**
     * 已用共享金额
     */
    private Long alryUseSharAmt;

    /**
     * 已用专用金额
     */
    private Long apldSpclAmt;

    /**
     * 线上线下标识
     */
    private String onleOflnFlg;

    /**
     * 交易对手类型
     */
    private String cnpyTyp;

    /**
     * 票据类型
     */
    private String billTyp;

    /**
     * 清算类型
     */
    private String clrgTyp;

    /**
     * 系统内外标识
     */
    private String systInnrAndExtrFlg;

    /**
     * 自动借据结清标识
     */
    private String autmBorwRecpSettFlg;

    /**
     * 是否我行直贴票据
     */
    private String ifSelfBankDrctDiscNote;

    /**
     * 预留字符
     */
    private String prsvChar;

    /**
     * 预留数值
     */
    private Long preResvNum;

    /**
     * 经办人姓名
     */
    private String agntNam;

    /**
     * 客户经理1
     */
    private String custMagr1;

    /**
     * 客户经理2
     */
    private String custMagr2;

    /**
     * 开户日期
     */
    private Integer opactDate;

    /**
     * 开户机构
     */
    private String opactOrgn;

    /**
     * 开户用户
     */
    private String opactUser;

    /**
     * 开户交易日志号
     */
    private String opactTranLogNum;

    /**
     * 发起方系统代码
     */
    private String intorSystCod;

    /**
     * 归属业务系统代码
     */
    private String afltBsinSystCod;

    /**
     * 发起方交易流水号
     */
    private String intorTranSenum;

    /**
     * 上次/最后交易日期
     */
    private Integer lastLastTranDate;

    /**
     * 最后交易日志号
     */
    private String lastTranLogNum;

    /**
     * 最后交易用户
     */
    private String lastTranUser;

    /**
     * 备注
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
