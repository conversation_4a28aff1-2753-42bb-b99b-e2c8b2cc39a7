package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <p>
 * 资产-（DMLNDDFA）协议放款基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNDDFA")
public class DmlnddfaEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号
     */
    private String bankNum;

    /**
     * 协议编号
     */
    private String prtcNumLeng13;

    /**
     * 放款状态
     */
    private String makLoanStas;

    /**
     * 放款方式
     */
    private String makLoanMode;

    /**
     * 实际首次放款日
     */
    private Integer actuFrstMakLoanDate;

    /**
     * 是否追加放款
     */
    private String ifApedMakLoan;

    /**
     * 放款账户是否本行
     */
    private String ifSelbkLoanAcct;

    /**
     * 通过借款人账户中转标识
     */
    private String brwrAcctTrasFlg;

    /**
     * 贴息类型
     */
    private String sbsiInrsTyp;

    /**
     * 放款支付方式
     */
    private String disbPayMode;

    /**
     * 是否指定放款账户
     */
    private String ifDesigMakLoanAcct;
}
