package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNETDA）登记受托支付处理明细信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNETDA")
public class DmlnetdaEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号；PK
     */
    private String bankNum;

    /**
     * 机构号；PK
     */
    private String orgnNum;

    /**
     * 交易码（含应用码）
     */
    private String tranCodCntnAplyCod;

    /**
     * 协议编号
     */
    private String prtcNunLeng13;

    /**
     * 借据序号
     */
    private String iouSqnum;

    /**
     * 特种业务编号
     */
    private String spclBsinNum;

    /**
     * 币种
     */
    private String ccy;

    /**
     * 交易日期；PK
     */
    private Integer tranDate;

    /**
     * 交易日志号；PK
     */
    private String tranLogNum;

    /**
     * 顺序号；PK
     */
    private String sqnumNum;

    /**
     * 挂销账编号
     */
    private String htwofNum;

    /**
     * 转出账号
     */
    private String trfouActNum;

    /**
     * 转出账户名称
     */
    private String trfouAcctNam;

    /**
     * 交易金额
     */
    private Long tranAmt;

    /**
     * 受托支付跨行标识
     */
    private String acrsBankFlg;

    /**
     * 通用对方客户账号
     */
    private String cnpyActNum;

    /**
     * 对方账户名称
     */
    private String cnpyAcctNam;

    /**
     * 明细冲账标识
     */
    private String dtalStrkBalcFlg;

    /**
     * 退汇标志
     */
    private String exitDrafFlg;

    /**
     * 交易用户
     */
    private String tranUser;

    /**
     * 冲正日期
     */
    private Integer dateOfRvrs;

    /**
     * 冲正日志号
     */
    private String logNumOfRvrs;

    /**
     * 建立时间戳
     */
    private LocalDateTime cratTmtp;

    /**
     * 最后时间戳
     */
    private LocalDateTime lastTmtp;
}
