package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNIBFA）借据主档
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNIBFA")
public class DmlnibfaEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号
     */
    private String bankNum;

    /**
     * 机构号
     */
    private String orgnNum;

    /**
     * 协议编号
     */
    private String prtcNumLeng13;

    /**
     * 借据序号
     */
    private String iouSqnum;

    /**
     * 科目
     */
    private String acctTile;

    /**
     * 产品编码
     */
    private String prduNum;

    /**
     * 客户号
     */
    private String custNum;

    /**
     * 币种
     */
    private String ccyDgtlCod;

    /**
     * 借据金额
     */
    private Long iouAmt;

    /**
     * 调整还款日；首次还款日
     */
    private Integer ajstRepyMneyDate;

    /**
     * 起始日期
     */
    private Integer origDate;

    /**
     * 起息日期
     */
    private Integer effvInrsDate;

    /**
     * 到期日期
     */
    private Integer expiDate;

    /**
     * 原始到期日
     */
    private Integer origExpiDate;

    /**
     * 远期到期日
     */
    private Integer fowdExpiDate;

    /**
     * 借据期限
     */
    private String iouDedl;

    /**
     * 利率
     */
    private Integer inrat;

    /**
     * 罚息利率
     */
    private Integer defitInrat;

    /**
     * 计提标识
     */
    private String acruFlg;

    /**
     * 是否结息标识
     */
    private String ifSettInrsFlg;

    /**
     * 收息方式
     */
    private String chagInrsMode;

    /**
     * 每期还款额
     */
    private Long evryPerdRepyAmt;

    /**
     * 开户日期
     */
    private Integer opactDate;

    /**
     * 开户用户
     */
    private String opactUser;

    /**
     * 开户交易日志号
     */
    private String opactTranLogNum;

    /**
     * 截息日；T+1日
     */
    private Integer dedlDate;

    /**
     * 上次批量结息日；上次结息日
     */
    private Integer lastBathInrsSettDate;

    /**
     * 转让产品编码
     */
    private String trfrPrduNum;

    /**
     * 转让状态
     */
    private String trfrStas;

    /**
     * 买入状态
     */
    private String buyStas;

    /**
     * 是否拆分
     */
    private String ifSplt;

    /**
     * 自助放款标识
     */
    private String selfHelpMakLoanFlg;

    /**
     * 是否追加放款
     */
    private String ifApedMakLoan;

    /**
     * 本行他行标识
     */
    private String selbkOthbkFlg;

    /**
     * 参与行客户号
     */
    private String prtpBankCustNum;

    /**
     * 参与行名称
     */
    private String ptpaBankNam;

    /**
     * 贷款大类
     */
    private String loanBigClss;

    /**
     * 借据状态
     */
    private String iouStas;

    /**
     * 上次/最后交易日期
     */
    private Integer lastLastTranDate;

    /**
     * 最后交易日志号
     */
    private String lastTranLogNum;

    /**
     * 最后交易用户
     */
    private String lastTranUser;

    /**
     * 结清方式
     */
    private String clarMode;

    /**
     * 在途天数
     */
    private Short inTrasDayCnt;

    /**
     * 冠字号
     */
    private String headSeriNum;

    /**
     * 票据号码(电/纸)
     */
    private String billNumEletPapr;

    /**
     * 发起方系统代码
     */
    private String intorSystCod;

    /**
     * 发起方交易流水号
     */
    private String intorTranSenum;

    /**
     * 指令编号
     */
    private String insuNum;

    /**
     * 预留字符
     */
    private String prsvChar;

    /**
     * 预留数值
     */
    private Long preResvNum;

    /**
     * 备注
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
