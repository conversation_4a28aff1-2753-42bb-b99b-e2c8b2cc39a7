package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <p>
 * 资产-（DMLNIBLA）借据余额主档
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNIBLA")
public class DmlniblaEntity extends DMBaseEntity {

    /**
     * 技术分片键_客户号
     */
    private String dtbtKey;

    /**
     * 银行号
     */
    private String bankNum;

    /**
     * 协议编号
     */
    private String prtcNumLeng13;

    /**
     * 借据序号
     */
    private String iouSqnum;

    /**
     * 借据金额
     */
    private Long todyBalc1;

    /**
     * 正常本金余额
     */
    private Long sadayBalc2;

    /**
     * 逾期本金余额
     */
    private Long sadayBalc3;

    /**
     * 正常欠息；T+1日结息的部分。与还款计划中的T+1日结息的数据需要对得上
     */
    private Long sadayBalc4;

    /**
     * 表外欠息；旧系统的表外欠息
     */
    private Long sadayBalc5;

    /**
     * 转逾期利息；到期日之后，转逾期利息。；不包括T+1日结息的部分
     */
    private Long sadayBalc6;

    /**
     * 表内罚息
     */
    private Long sadayBalc7;

    /**
     * 表外罚息
     */
    private Long sadayBalc8;

    /**
     * 复利
     */
    private Long sadayBalc9;

    /**
     * 截计利息；对于此次村镇行迁移，SADAY_BALC_16（截计利息）与SADAY_BALC_28（已计提利息）相等；；需要跟LNIBFA中的截息日对应赋值
     */
    private Long sadayBalc16;

    /**
     * 表外已税利息
     */
    private Long sadayBalc25;

    /**
     * 表外已税罚息
     */
    private Long sadayBalc26;

    /**
     * 已计提利息；每日计提利息，对应未计划本金。；截止到T+1日的计提息（算头不算尾），已经减去了T+1日结息的部分
     */
    private Long sadayBalc28;

    /**
     * 已计提罚息
     */
    private Long sadayBalc29;

    /**
     * 垫缴增值税；待定
     */
    private Long sadayBalc30;

    /**
     * 已计提复利
     */
    private Long sadayBalc31;

    /**
     * 已计提罚息复利
     */
    private Long sadayBalc32;

    /**
     * 已计提复利复利
     */
    private Long sadayBalc33;

    /**
     * 借据金额上日余额
     */
    private Long lastDayBalc1;

    /**
     * 正常本金余额上日余额
     */
    private Long lastDayBalc2;

    /**
     * 逾期本金余额上日余额
     */
    private Long lastDayBalc3;

    /**
     * 正常欠息上日余额
     */
    private Long lastDayBalc4;

    /**
     * 表外欠息上日余额
     */
    private Long lastDayBalc5;

    /**
     * 表内罚息上日余额
     */
    private Long lastDayBalc7;

    /**
     * 表外罚息上日余额
     */
    private Long lastDayBalc8;

    /**
     * 复利上日余额
     */
    private Long lastDayBalc9;

    /**
     * 截计利息上日余额
     */
    private Long lastDayBalc16;

    /**
     * 表外已税利息上日余额
     */
    private Long lastDayBalc25;

    /**
     * 表外已税罚息上日余额
     */
    private Long lastDayBalc26;

    /**
     * 已计提利息上日余额
     */
    private Long lastDayBalc28;

    /**
     * 已计提罚息上日余额
     */
    private Long lastDayBalc29;

    /**
     * 已计提罚息复利上日余额
     */
    private Long lastDayBalc32;

    /**
     * 已计提复利复利上日余额
     */
    private Long lastDayBalc33;
}
