package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNIRHA）执行利率资料基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNIRHA")
public class DmlnirhaEntity extends DMBaseEntity {

    /**
     * 技术分片键_客户号
     */
    private String dtbtKey;

    /**
     * 银行号；PK
     */
    private String bankNum;

    /**
     * 协议编号；PK
     */
    private String prtcNumLeng13;

    /**
     * 借据序号；PK
     */
    private String iouSqnum;

    /**
     * 利率类型；PK
     */
    private String inratTyp;

    /**
     * 生效日期；PK
     */
    private Integer takeEfftDate;

    /**
     * 基准利率值
     */
    private Integer benmInratVlu;

    /**
     * 基准利率日期
     */
    private Integer benmInratDate;

    /**
     * 利率
     */
    private Integer inrat;

    /**
     * 比较利率选择
     */
    private String cmpaInratSele;

    /**
     * 记录状态
     */
    private String rcrdStas;

    /**
     * 交易日期
     */
    private Integer tranDate;

    /**
     * 交易用户
     */
    private String tranUser;

    /**
     * 交易日志号
     */
    private String tranLogNum;

    /**
     * 预留字符
     */
    private String prsvChar;

    /**
     * 预留数值
     */
    private Long preResvNum;

    /**
     * 备注
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
