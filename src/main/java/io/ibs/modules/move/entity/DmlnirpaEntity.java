package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNIRPA）贷款利率重定价资料
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNIRPA")
public class DmlnirpaEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号；PK
     */
    private String bankNum;

    /**
     * 协议编号；PK
     */
    private String prtcNumLeng13;

    /**
     * 借据序号；PK
     */
    private String iouSqnum;

    /**
     * 生效日期；PK
     */
    private Integer takeEfftDate;

    /**
     * 利率重定价方式选择
     */
    private String inratReprModeSele;

    /**
     * 利率重定价周期
     */
    private String inratReprPerd;

    /**
     * 周期重定价固定日
     */
    private Short perdReprFixDate;

    /**
     * 重定价浮动方向限制
     */
    private String reprFlotDrecCsta;

    /**
     * 前一重定价日期
     */
    private Integer bforReprDate;

    /**
     * 下次重定价日
     */
    private Integer nextReprDate;

    /**
     * 重定价日调整方式
     */
    private String reprDayAjstMode;

    /**
     * 上次/最后交易日期
     */
    private Integer lastLastTranDate;

    /**
     * 预留字符
     */
    private String prsvChar;

    /**
     * 预留数值
     */
    private Long preResvNum;

    /**
     * 备注
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
