package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNIRTA）贷款正常利率资料基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNIRTA")
public class DmlnirtaEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号；PK
     */
    private String bankNum;

    /**
     * 协议编号；PK
     */
    private String prtcNumLeng13;

    /**
     * 借据序号；PK
     */
    private String iouSqnum;

    /**
     * 生效日期；PK
     */
    private Integer takeEfftDate;

    /**
     * 贷款利率模式；OPTIONGROUPDCBS8126F
     */
    private String loanInratMode;

    /**
     * 基准利率种类
     */
    private String benmInratTyp;

    /**
     * 利率档期
     */
    private String inratShdl;

    /**
     * 加减档标识
     */
    private String pluMinuDoctFlg;

    /**
     * 利率浮动方式；固定利率为空
     */
    private String inratFlotMode;

    /**
     * 正常利率浮动方向；浮动方式为不浮动或固定利率时，为空
     */
    private String norlInratFlotDrec;

    /**
     * 利率浮动比例；固定利率为空
     */
    private Integer inratFlotPrpn;

    /**
     * 利率浮动值；固定利率为空
     */
    private Integer inratFlotVlu;

    /**
     * 基准利率值
     */
    private Integer benmInratVlu;

    /**
     * 利率
     */
    private Integer inrat;

    /**
     * 首次利率取值日选择类型
     */
    private String frstInratVluDateSeleTyp;

    /**
     * 取基准利率日差
     */
    private Short benmInratDateOfst;

    /**
     * 基准利率日期
     */
    private Integer benmInratDate;

    /**
     * 利率重定价频度
     */
    private String inratReprFreq;

    /**
     * 调整后重定价日期
     */
    private Integer ajstReprDate;

    /**
     * 利率定价状态
     */
    private String inratPricStas;

    /**
     * 上次/最后交易日期
     */
    private Integer lastLastTranDate;

    /**
     * 预留字符
     */
    private String prsvChar;

    /**
     * 预留数值
     */
    private Long preResvNum;

    /**
     * 备注
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
