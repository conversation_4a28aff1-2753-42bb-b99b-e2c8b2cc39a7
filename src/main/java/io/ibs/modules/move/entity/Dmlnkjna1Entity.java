package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <p>
 * 资产-（DMLNKJNA1）关键交易明细-正交易
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNKJNA1")
public class Dmlnkjna1Entity extends DMBaseEntity {

    /**
     * 技术分片键_客户号
     */
    private String dtbtKey;

    /**
     * 银行号；PK
     */
    private String bankNum;

    /**
     * 协议编号；PK
     */
    private String prtcNumLeng13;

    /**
     * 借据序号；PK
     */
    private String iouSqnum;

    /**
     * 交易日期；PK
     */
    private Integer tranDate;

    /**
     * 交易日志号；PK
     */
    private String tranLogNum;

    /**
     * 生效日期
     */
    private Integer takeEfftDate;

    /**
     * 交易码（含应用码）；PK
     */
    private String tranCodCntnAplyCod;

    /**
     * 交易时间
     */
    private Integer tranTime;

    /**
     * 明细冲账标识
     */
    private String dtalStrkBalcFlg;

    /**
     * 发起平台标识
     */
    private String intaPlfmFlg;
}
