package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNRLAA）跨模块关联账户登记薄
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNRLAA")
public class DmlnrlaaEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号
     */
    private String bankNum;

    /**
     * 关联类型
     */
    private String relaTypLeng1;

    /**
     * 贷款相关编号
     */
    private String loanRelaNum;

    /**
     * 借据序号
     */
    private String iouSqnum;

    /**
     * 贷款相关账户类型
     */
    private String loanRelaAcctTyp;

    /**
     * 序号
     */
    private String sqnumLeng6;

    /**
     * 客户账号
     */
    private String custActnum;

    /**
     * 账号类型标识
     */
    private String actnumTypFlg;

    /**
     * 客户号
     */
    private String custNum;

    /**
     * 币种
     */
    private String ccyDgtlCod;

    /**
     * 钞汇标识
     */
    private String cashRmitFlg;

    /**
     * 账户序号
     */
    private String acctSqnum;

    /**
     * 挂销账编号
     */
    private String htwofNum;

    /**
     * 关联金额
     */
    private Long relaAmt;

    /**
     * 上次/最后交易日期
     */
    private Integer lastLastTranDate;

    /**
     * 最后交易日志号
     */
    private String lastTranLogNum;

    /**
     * 最后交易用户
     */
    private String lastTranUser;

    /**
     * 预留字符
     */
    private String prsvChar;

    /**
     * 预留数值
     */
    private Long preResvNum;

    /**
     * 备注
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
