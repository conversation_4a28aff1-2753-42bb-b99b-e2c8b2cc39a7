package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <p>
 * 资产（DMLNRPPA1）-还款计划表（应还款明细）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNRPPA1")
public class Dmlnrppa1Entity extends DMBaseEntity {

    /**
     * 技术分片键_客户号
     */
    private String dtbtKey;

    /**
     * 银行号
     */
    private String bankNum;

    /**
     * 机构号
     */
    private String orgnNum;

    /**
     * 协议编号
     */
    private String prtcNumLeng13;

    /**
     * 借据序号
     */
    private String iouSqnum;

    /**
     * 扣款顺序；类似期数
     */
    private String demonSequ;

    /**
     * 还款计划分类
     */
    private String repyPlanClss;

    /**
     * 还款计划类型
     */
    private String repyPlanTyp;

    /**
     * 转逾期日期
     */
    private Integer trfrOdueDate;

    /**
     * 起息日期
     */
    private Integer effvInrsDate;

    /**
     * 计划日
     */
    private Integer planDate;

    /**
     * 结息日期
     */
    private Integer settInrsDate;

    /**
     * 截计日期；T+1日
     */
    private Integer intcpDate;

    /**
     * 计划本金
     */
    private Long planPrcp;

    /**
     * 计划利息
     */
    private Long planInrs;

    /**
     * 应还本金
     */
    private Long shldRepyPrcp;

    /**
     * 表内欠息
     */
    private Long bashtOwInrs;

    /**
     * 表外欠息
     */
    private Long ofbshOwInrs;

    /**
     * 表内罚息；已结计的罚息
     */
    private Long bashtDefit;

    /**
     * 表外罚息
     */
    private Long ofbshDefit;

    /**
     * 复利
     */
    private Long comit;

    /**
     * 罚息复利
     */
    private Long defitComit;

    /**
     * 复利复利
     */
    private Long comitVlu;

    /**
     * 截计停息罚息
     */
    private Long intcpCeasInrsDefit;

    /**
     * 截计罚息；旧系统只有一个罚息栏位（对应的是新系统的罚息+利息复利），迁移到新系统，放在已结还是截计？；20241027：会议结论，迁移到截计，该栏位与IBLA中的已计提罚息需要对应
     */
    private Long intcpDefit;

    /**
     * 截计罚息复利
     */
    private Long intcpDefitComit;

    /**
     * 截计复利
     */
    private Long intcpComit;

    /**
     * 截计复利复利
     */
    private Long intcpComitVlu;

    /**
     * 回购截计表内欠息
     */
    private Long repcIntcpBashtOwInrs;

    /**
     * 回购截计表内罚息
     */
    private Long repcIntcpDefitBasht;

    /**
     * 回购截计复利
     */
    private Long repcIntcpComit;

    /**
     * 回购表内欠息余额
     */
    private Long repcOwInrsBalcBasht;

    /**
     * 回购表内罚息余额
     */
    private Long repcDefitBalcBasht;

    /**
     * 回购复利余额
     */
    private Long repcComitBalc;

    /**
     * 收回本金金额
     */
    private Long takeBackPrcpAmt;

    /**
     * 收回表内欠息
     */
    private Long takeBackBashtOwInrs;

    /**
     * 收回表外欠息
     */
    private Long relmOfbshOwInrs;

    /**
     * 收回表内罚息
     */
    private Long relmBashtDefit;

    /**
     * 收回表外罚息
     */
    private Long relmOfbshDefit;

    /**
     * 收回复利
     */
    private Long takeBackComit;

    /**
     * 收回罚息复利
     */
    private Long takeBackDefitComit;

    /**
     * 收回复利复利
     */
    private Long takeBackComitComit;

    /**
     * 调整表内利息
     */
    private Long ajstBashtInrs;

    /**
     * 调整表外利息
     */
    private Long ajstOfbshInrs;

    /**
     * 调整表内罚息
     */
    private Long ajstDefitBasht;

    /**
     * 调整表外罚息
     */
    private Long ajstOfbshDefit;

    /**
     * 调整复利
     */
    private Long ajstComit;

    /**
     * 调整罚息复利
     */
    private Long ajstDefitComit;

    /**
     * 调整复利复利
     */
    private Long ajstComitVlu;

    /**
     * 还款计划状态
     */
    private String repyPlanStas;

    /**
     * 余额归属类型
     */
    private String balcAttrTyp;

    /**
     * 借据余额；产生还款计划时的未计划本金
     */
    private Long iouBalc;
}
