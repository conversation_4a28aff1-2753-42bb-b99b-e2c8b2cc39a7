package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 资产-（DMLNWOFA）核销及收回登记簿
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMLNWOFA")
public class DmlnwofaEntity extends DMBaseEntity {

    /**
     * 技术分片键
     */
    private String dtbtKey;

    /**
     * 银行号；PK
     */
    private String bankNum;

    /**
     * 协议编号；PK
     */
    private String prtcNumLeng13;

    /**
     * 借据序号；PK
     */
    private String iouSqnum;

    /**
     * 核销日期
     */
    private Integer wrtfDate;

    /**
     * 本金
     */
    private Long prcpAmt;

    /**
     * 表内欠息
     */
    private Long bashtOwInrs;

    /**
     * 表外欠息
     */
    private Long ofbshOwInrs;

    /**
     * 表内罚息
     */
    private Long bashtDefit;

    /**
     * 表外罚息
     */
    private Long ofbshDefit;

    /**
     * 复利
     */
    private Long comit;

    /**
     * 罚息复利
     */
    private Long defitComit;

    /**
     * 复利复利
     */
    private Long comitVlu;

    /**
     * 核销后计提罚息
     */
    private Long wrtfBackAcruDefit;

    /**
     * 收回本金金额
     */
    private Long takeBackPrcpAmt;

    /**
     * 收回表内欠息
     */
    private Long takeBackBashtOwInrs;

    /**
     * 收回表外欠息
     */
    private Long relmOfbshOwInrs;

    /**
     * 收回表内罚息
     */
    private Long relmBashtDefit;

    /**
     * 收回表外罚息
     */
    private Long relmOfbshDefit;

    /**
     * 收回复利
     */
    private Long takeBackComit;

    /**
     * 收回罚息复利
     */
    private Long takeBackDefitComit;

    /**
     * 收回复利复利
     */
    private Long takeBackComitComit;

    /**
     * 收回核销后计提罚息
     */
    private Long takeBackWrtfBakAcruDefit;

    /**
     * 上次/最后交易日期
     */
    private Integer lastLastTranDate;

    /**
     * 最后交易日志号
     */
    private String lastTranLogNum;

    /**
     * 最后交易用户
     */
    private String lastTranUser;

    /**
     * 记录状态
     */
    private String rcrdStas;

    /**
     * 预留字符
     */
    private String prsvChar;

    /**
     * 预留数值
     */
    private Long preResvNum;

    /**
     * 备注
     */
    private String noteTxt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
