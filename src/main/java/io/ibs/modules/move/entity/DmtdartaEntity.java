package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMTDARTA）代村镇银行支付定期利息明细文件-0.2
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMTDARTA")
public class DmtdartaEntity extends DMBaseEntity {

    /**
     * 技术分片键；技术分片键（老客户号）
     */
    private String dtbtKey;

    /**
     * 客户账号；村行客户账号
     */
    private String custActnum;

    /**
     * 账户序号；村行账户序号
     */
    private String acctSqnum;

    /**
     * 村镇银行编号；01
     */
    private String czyhNo;

    /**
     * 村行机构号；村行原始机构号
     */
    private String afltOrgn;

    /**
     * 产品编码；村行原始产品编码，具体参照《新旧产品对照表》
     */
    private String prduNum;

    /**
     * 账户余额；村行原始信息
     */
    private Long acctBalc;

    /**
     * 起息日期；村行原始信息
     */
    private Integer effvInrsDate;

    /**
     * 到期日期；村行原始信息
     */
    private Integer expiDate;

    /**
     * 存期；村行原始信息
     */
    private String depsPerd;

    /**
     * 转存方式；村行原始信息
     */
    private String rdepMode;

    /**
     * 村行利率；村行原始信息
     */
    private Integer oldInrat;

    /**
     * 标准利率
     */
    private Integer newInrat;

    /**
     * 计息基本天数；村行原始信息
     */
    private String acruInrsBascDayCnt;

    /**
     * 积数；超额处理过程，辅助核对；零存整取、聚宝盘都需要给到期积数
     */
    private BigDecimal agrgNum;

    /**
     * 开户日至到期日标准定期应付利息；超额处理过程，辅助核对
     */
    private Long inrsAmt1;

    /**
     * 开户日到轧断日标准定期利息；超额处理过程，辅助核对
     */
    private Long inrsAmt2;

    /**
     * 到期日至轧断日活期应付利息；超额处理过程，辅助核对
     */
    private Long inrsAmt3;

    /**
     * 开户日至到期日超额利息；超额处理过程，辅助核对
     */
    private Long inrsAmt4;

    /**
     * 开户日至轧断日超额利息；超额处理过程，辅助核对
     */
    private Long inrsAmt5;

    /**
     * 轧断日至到期日超额利息；超额处理过程，辅助核对
     */
    private Long inrsAmt6;

    /**
     * 缩短存期计提的利息；超额处理过程，辅助核对
     */
    private Long inrsAmt7;

    /**
     * 合计超额利息；迁移时的总超额利息，需要与内部户余额一致；；INRS_AMT_8 = INRS_AMT_4 + INRS_AMT_5 + INRS_AMT_6 + INRS_AMT_7
     */
    private Long inrsAmt8;

    /**
     * 剩余未支付超额利息；迁移时的总超额利息，需要与内部户余额一致；
     */
    private Long inrsAmt;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
