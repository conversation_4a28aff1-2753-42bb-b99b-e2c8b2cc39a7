package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMTDPCDA)  个人周期计划文件-关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMTDPCDA")
public class DmtdpcdaEntity extends DMBaseEntity {

    /**
     * 技术分片键；客户号
     */
    private String dtbtKey;

    /**
     * 银行号；001
     */
    private String bankNum;

    /**
     * 系统账号；旧客户账号，迁入方转新客户账号
     */
    private String systActnum;

    /**
     * 起息日期；开户日期
     */
    private Integer effvInrsDate;

    /**
     * 到期日期；定期账户到期日期
     */
    private Integer expiDate;

    /**
     * 计划交易日期；每一期的计划存入日期
     */
    private Integer planTranDate;

    /**
     * 交易金额
     */
    private Long tranAmt;

    /**
     * 利率
     */
    private Integer inrat;

    /**
     * 利息
     */
    private Long inrsAmtAmt;

    /**
     * 税率；送0
     */
    private Integer taxRat;

    /**
     * 利息税；送0
     */
    private Long inrsTax;

    /**
     * 计划执行状态
     */
    private String exeuStas;

    /**
     * 执行日期；实际入账日期
     */
    private Integer exeuDate;

    /**
     * 失效日期；送0
     */
    private Integer invdDate;

    /**
     * 失效日志号
     */
    private String invdLogNum;

    /**
     * 建立日期
     */
    private Integer cratDate;

    /**
     * 建立用户；柜员
     */
    private String cratUser;

    /**
     * 建立时间戳
     */
    private LocalDateTime cratTmtp;

    /**
     * 维护日期
     */
    private Integer mantDate;

    /**
     * 维护用户；柜员
     */
    private String mantUser;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;
}
