package io.ibs.modules.move.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMTDRDDA）定期账户明细文件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@ToString
@TableName("DMTDRDDA")
public class DmtdrddaEntity extends DMBaseEntity {

    /**
     * 技术分片键；客户号
     */
    private String dtbtKey;

    /**
     * 银行号；001
     */
    private String bankNum;

    /**
     * 公私标识
     */
    private String coprPesnFlg;

    /**
     * 客户号
     */
    private String custNum;

    /**
     * 客户账号
     */
    private String custActnum;

    /**
     * 账户序号
     */
    private String acctSqnum;

    /**
     * 系统账号；送空
     */
    private String systActnum;

    /**
     * 币种；001
     */
    private String ccyDgtlCod;

    /**
     * 钞汇标识；送空
     */
    private String cashRmitFlg;

    /**
     * 交易日期
     */
    private Integer tranDate;

    /**
     * 交易机构号；送新机构还是原机构，待定
     */
    private String tranOrgnNum;

    /**
     * 归属机构；送新机构还是原机构，待定
     */
    private String afltOrgn;

    /**
     * 管理机构；送新机构还是原机构，待定
     */
    private String mangOrgn;

    /**
     * 交易日志号
     */
    private String tranLogNum;

    /**
     * 用户名；交易柜员
     */
    private String userNam;

    /**
     * 核心交易码
     */
    private String coreTranCod;

    /**
     * 现转标识
     */
    private String cashTrfrAcctFlg;

    /**
     * 借贷标识
     */
    private String dbitCrdtFlg;

    /**
     * 交易金额
     */
    private Long tranAmt;

    /**
     * 账户余额
     */
    private Long acctBalc;

    /**
     * 透支金额；送0
     */
    private Long ovdfAmt;

    /**
     * 起息日期；待定
     */
    private Integer effvInrsDate;

    /**
     * 明细冲账标识；默认送N
     */
    private String dtalStrkBalcFlg;

    /**
     * 冲账类型；送空
     */
    private String strkBalcTyp;

    /**
     * 冲账对方交易日期；默认送0；冲正交易登记被冲正的交易的日期；被冲正交易，登记冲正日期
     */
    private Integer rvrsAcctCnpyTranDate;

    /**
     * 冲账对方日志号；默认送空；冲正交易登记被冲正的交易的日志号；被冲正交易，登记冲正交易日志号
     */
    private String strkBalcCnpyLogNum;

    /**
     * 支付凭证代码；支付方式为票据时登记
     */
    private String payVochCod;

    /**
     * 冠字号；支付方式为票据时登记
     */
    private String headSeriNum;

    /**
     * 凭证号码；支付方式为票据时登记
     */
    private BigDecimal vochNum;

    /**
     * 凭证签发日期；支付方式为票据时登记
     */
    private Integer vochSignIssuDate;

    /**
     * 系统账户产品编码；送空，迁入补录
     */
    private String systAcctPrduNum;

    /**
     * 服务类产品编码；无需录入
     */
    private String srveClssPrduNum;

    /**
     * 客户账户产品编码；一本通、卡、一户通登记，迁入补录
     */
    private String custAcctPrduEncd;

    /**
     * 管理任务号；无需录入
     */
    private String mangTaskNum;

    /**
     * 利润中心；无需录入
     */
    private String prftCter;

    /**
     * 转账原因；无需录入
     */
    private String tractReas;

    /**
     * 现金项目代码；待业务确定
     */
    private String cashPrjtCod;

    /**
     * 代理经办标识；根据实际情况送
     */
    private String agntOperFlg;

    /**
     * 发起方渠道；送空
     */
    private String intorChnl;

    /**
     * 发起方系统代码；送空
     */
    private String intorSystCod;

    /**
     * 发起方终端号；无需录入
     */
    private String intorTmnlNum;

    /**
     * 发起方交易码；无需录入
     */
    private String intorTranCod;

    /**
     * 发起方交易流水号；送空
     */
    private String intorTranSenum;

    /**
     * 发起方日期/申请日期；送空
     */
    private Integer intorDate;

    /**
     * 影像ID；无需录入
     */
    private String imagId;

    /**
     * 商户类型；POS消费时登记
     */
    private String mrchTyp;

    /**
     * 商户编号；POS消费时登记
     */
    private String mrchNum;

    /**
     * 通用对方客户账号；交易对方账号（包含内部户）
     */
    private String uvslCnpyCustActnum;

    /**
     * 对方账户序号；根据实际情况送
     */
    private String cnpyAcctSqnum;

    /**
     * 对方账户名称；根据实际情况送
     */
    private String cnpyAcctNam;

    /**
     * 对方行号；送新机构还是原机构，待定
     */
    private String cnpyBankNum;

    /**
     * 对方行名称；根据实际情况送
     */
    private String cnpyBankNam;

    /**
     * 网银链接号；无需录入
     */
    private String iebakLinkNum;

    /**
     * 相关服务产品编码；无需录入
     */
    private String relaSrvePrduNum;

    /**
     * 摘要
     */
    private String abstTxt;

    /**
     * 摘要代码；送空
     */
    private String abstCod;

    /**
     * 静态授权主管
     */
    private String sttcAthoDrcr;

    /**
     * 动态授权主管；送空
     */
    private String dynmAthoDrcr;

    /**
     * 交易内明细序号；默认0001；同一笔交易涉及多笔账务（如借贷双方或多账户交易），登记顺序号，与会计模块登记的顺序号一致
     */
    private String tranInnrDtalSqnum;

    /**
     * 交易时间
     */
    private Integer tranTime;

    /**
     * 账户交易序号；无需录入
     */
    private String acctTranSqnum;

    /**
     * 资金相关标识；默认N
     */
    private String cptlRelaFlg;

    /**
     * 金融交易标识；默认Y
     */
    private String fincTranFlg;

    /**
     * 明细预留日期1；无需录入
     */
    private Integer dtalPrsvDate1;

    /**
     * 明细预留日期2；无需录入
     */
    private Integer dtalPrsvDate2;

    /**
     * 明细预留账户1；无需录入
     */
    private String dtalPrsvAcct1;

    /**
     * 明细预留账户2；无需录入
     */
    private String dtalPrsvAcct2;

    /**
     * 明细扩展属性域；无需录入
     */
    private String dtalExteAttrDomn;

    /**
     * 预留数值；无需录入
     */
    private Long preResvNum;

    /**
     * 时间戳
     */
    private LocalDateTime tmtpTime;

    /**
     * 建立时间戳
     */
    private LocalDateTime cratTmtp;

    /**
     * 本地交易日期
     */
    private Integer loclTranDate;

    /**
     * 本地交易时间
     */
    private Integer loclTranTime;

    /**
     * 本地时间戳
     */
    private LocalDateTime loclTmtp;

    /**
     * 结单展示标志；默认N
     */
    private String stmtShowFlg;
}
