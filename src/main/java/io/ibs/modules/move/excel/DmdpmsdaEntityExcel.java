package io.ibs.modules.move.move.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 负债-（DMDPMSDA）账户迁移表 Excel导出对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(25)
public class DmdpmsdaEntityExcel {

    /**
     * 技术分片键；技术分片键（新核心客户号）
     */
    @ExcelProperty(value = "技术分片键；技术分片键（新核心客户号）", index = 0)
    @ColumnWidth(20)
    private String dtbtKey;

    /**
     * 银行号；银行号
     */
    @ExcelProperty(value = "银行号；银行号", index = 1)
    @ColumnWidth(20)
    private String bankNum;

    /**
     * 客户号；客户账户对应的客户号（ECIF生成并上传对应关系表）
     */
    @ExcelProperty(value = "客户号；客户账户对应的客户号（ECIF生成并上传对应关系表）", index = 2)
    @ColumnWidth(20)
    private String custNum;

    /**
     * 原客户账号；旧核心账户账号（包含二、三类账户）
     */
    @ExcelProperty(value = "原客户账号；旧核心账户账号（包含二、三类账户）", index = 3)
    @ColumnWidth(20)
    private String fomrCustActnum;

    /**
     * 客户账号；核心客户账号（移入方自动生成）
     */
    @ExcelProperty(value = "客户账号；核心客户账号（移入方自动生成）", index = 4)
    @ColumnWidth(20)
    private String custActnum;

    /**
     * 公私标识；代表该客户是个人还是单位客户
     */
    @ExcelProperty(value = "公私标识；代表该客户是个人还是单位客户", index = 5)
    @ColumnWidth(20)
    private String coprPesnFlg;

    /**
     * 账户名称；1、 对私产品必须与客户名称相同；2、 对公可与客户名称不同，可修改。；3、 附属账户体系中，开户时输入。
     */
    @ExcelProperty(value = "账户名称；1、 对私产品必须与客户名称相同；2、 对公可与客户名称不同，可修改。；3、 附属账户体系中，开户时输入。", index = 6)
    @ColumnWidth(20)
    private String acctNam;

    /**
     * 账户类别；迁出方23类账户没有介质，送4-网络账户，其他送1-实体账户
     */
    @ExcelProperty(value = "账户类别；迁出方23类账户没有介质，送4-网络账户，其他送1-实体账户", index = 7)
    @ColumnWidth(20)
    private String acctTyp4;

    /**
     * 归属机构；账户的业务操作归属机构，新机构
     */
    @ExcelProperty(value = "归属机构；账户的业务操作归属机构，新机构", index = 8)
    @ColumnWidth(20)
    private String afltOrgn;

    /**
     * 客户账户到期日期；目前卡产品使用.送0
     */
    @ExcelProperty(value = "客户账户到期日期；目前卡产品使用.送0", index = 9)
    @ColumnWidth(20)
    private Integer expiDate;

    /**
     * 普通冻结状态；指全部冻结，部分冻结体现在冻结金额及特种业务登记簿中和冻结金额中，若仅为金额冻结，该状态为1-正常。
     */
    @ExcelProperty(value = "普通冻结状态；指全部冻结，部分冻结体现在冻结金额及特种业务登记簿中和冻结金额中，若仅为金额冻结，该状态为1-正常。", index = 10)
    @ColumnWidth(20)
    private String comnFrzStas;

    /**
     * 司法冻结状态；指客户账户司法冻结的状态，部分冻结体现在冻结金额及特种业务登记簿中和冻结金额中，若仅为金额冻结，该状态为1-正常。；。
     */
    @ExcelProperty(value = "司法冻结状态；指客户账户司法冻结的状态，部分冻结体现在冻结金额及特种业务登记簿中和冻结金额中，若仅为金额冻结，该状态为1-正常。；。", index = 11)
    @ColumnWidth(20)
    private String judFrzStas;

    /**
     * 支付监管状态；默认送1，迁出方没有；必输；开立客户账户时，登记为1正常，发生支付监管交易时，修改为2、3、4，解除支付监管后修改为1正常
     */
    @ExcelProperty(value = "支付监管状态；默认送1，迁出方没有；必输；开立客户账户时，登记为1正常，发生支付监管交易时，修改为2、3、4，解除支付监管后修改为1正常", index = 12)
    @ColumnWidth(20)
    private String payReglStas;

    /**
     * 挂失状态；开立客户账户时，登记为1正常，发生挂失交易后修该为2、3；解挂后修改为1正常
     */
    @ExcelProperty(value = "挂失状态；开立客户账户时，登记为1正常，发生挂失交易后修该为2、3；解挂后修改为1正常", index = 13)
    @ColumnWidth(20)
    private String rpotLossStas;

    /**
     * 客户账户状态；客户账户状态，睡眠主体由睡眠户模块负责设置。设置为睡眠状态的客户账户下所有系统账户不收不付。
     */
    @ExcelProperty(value = "客户账户状态；客户账户状态，睡眠主体由睡眠户模块负责设置。设置为睡眠状态的客户账户下所有系统账户不收不付。", index = 14)
    @ColumnWidth(20)
    private String custAcctStas;

    /**
     * 系统账户到期日期；定期的到期日期；开立单位临时户或专用账户时输入；单位活期账户信息变更时可修改
     */
    @ExcelProperty(value = "系统账户到期日期；定期的到期日期；开立单位临时户或专用账户时输入；单位活期账户信息变更时可修改", index = 15)
    @ColumnWidth(20)
    private Integer systAcctExpiDate;

    /**
     * 是否支票户；迁出方结算账户都可以买支票，单位活期送Y，其他送N；开立的客户账号允许出售支票户则该字段登记为Y,否则为N
     */
    @ExcelProperty(value = "是否支票户；迁出方结算账户都可以买支票，单位活期送Y，其他送N；开立的客户账号允许出售支票户则该字段登记为Y,否则为N", index = 16)
    @ColumnWidth(20)
    private String ifCheqUser;

    /**
     * 介质类型；1、卡；2、折（如果是折，活期系统账户主表的是否配折字段默认为Y）；3、单；4、其它
     */
    @ExcelProperty(value = "介质类型；1、卡；2、折（如果是折，活期系统账户主表的是否配折字段默认为Y）；3、单；4、其它", index = 17)
    @ColumnWidth(20)
    private String medaTyp;

    /**
     * 多账户产品标识；一本通送Y，其他送N；用于区分该产品属于单一账户或多账户，一本通、卡、一户通为多产品
     */
    @ExcelProperty(value = "多账户产品标识；一本通送Y，其他送N；用于区分该产品属于单一账户或多账户，一本通、卡、一户通为多产品", index = 18)
    @ColumnWidth(20)
    private String mutpAcctPrduFlg;

    /**
     * 支付方式；迁出只有1、2、3、8；可选范围来源于产品
     */
    @ExcelProperty(value = "支付方式；迁出只有1、2、3、8；可选范围来源于产品", index = 19)
    @ColumnWidth(20)
    private String payMode;

    /**
     * 可支付生效日；设置静默期，迁入方处理，待确定；单位账户激活三日以后的日期
     */
    @ExcelProperty(value = "可支付生效日；设置静默期，迁入方处理，待确定；单位账户激活三日以后的日期", index = 20)
    @ColumnWidth(20)
    private Integer permPayEfftDate;

    /**
     * 产品编码；迁出方有定期一本通，定期一本通送DP5046100001，非一本通送储种+控制字（科目），字段长度可能不足；单账户产品对应单一账户的产品编码，组合产品对应组合产品编码，如卡产品、一户通产品
     */
    @ExcelProperty(value = "产品编码；迁出方有定期一本通，定期一本通送DP5046100001，非一本通送储种+控制字（科目），字段长度可能不足；单账户产品对应单一账户的产品编码，组合产品对应组合产品编码，如卡产品、一户通产品", index = 21)
    @ColumnWidth(20)
    private String prduNum;

    /**
     * 风险锁状态；默认送1；开立客户账户时登记为1正常，发生风险锁锁定交易时修改为2锁定，发生风险锁解锁交易时修改为1正常
     */
    @ExcelProperty(value = "风险锁状态；默认送1；开立客户账户时登记为1正常，发生风险锁锁定交易时修改为2锁定，发生风险锁解锁交易时修改为1正常", index = 22)
    @ColumnWidth(20)
    private String riskLockStas;

    /**
     * 签约信息；迁出方没有相关签约，送空
     */
    @ExcelProperty(value = "签约信息；迁出方没有相关签约，送空", index = 23)
    @ColumnWidth(20)
    private String sigctInfo;

    /**
     * 通兑标识；迁出方，个人的，全行通兑，对公的，定期不通兑，活期通兑，需业务确认；单位开户后默认为“不通存”，个人开户后默认为“通存”，可通过账户信息维护交易进行修改。
     */
    @ExcelProperty(value = "通兑标识；迁出方，个人的，全行通兑，对公的，定期不通兑，活期通兑，需业务确认；单位开户后默认为“不通存”，个人开户后默认为“通存”，可通过账户信息维护交易进行修改。", index = 24)
    @ColumnWidth(20)
    private String uvslWhdwFlg;

    /**
     * 客户账户扩展属性域；第2、4位根据实际情况赋值，其他栏位留空；第一位：一户通标识         1-签约一户通、0-未签约；第二位：二三类账户标识        1-一类   2-二类   3-三类；第三位：外围系统分户账        1-外围系统分户账（不被查、不被特种业务处理、只被专用交易处理）；第四位：二类账户柜台核实标识        第二位为2时，第四位取值1-柜台已核实的二类户，空-非柜台已核实的二类户（电子账户）第五六位：保证金业务种类        01.代客衍生品保证金        02.国际信用证业务保证金        04.国内信用证进口保证金         05.保函业务保证金         06.承兑业务保证金         07.公务卡保证金         08.零售业务保证金         99.其他保证金；第七位： NRA 账户为期货专用账户标识，1 - 是、 0 - 否；第九位：客户可信标识检查        为1且客户可信标识为F，则客户账户不收不付        为2且客户可信标识为S，则客户账户只收不付；第十位：附属卡停用标识 1-停用；第十一位：地铁保证金签约标识 1-签约
     */
    @ExcelProperty(value = "客户账户扩展属性域；第2、4位根据实际情况赋值，其他栏位留空；第一位：一户通标识         1-签约一户通、0-未签约；第二位：二三类账户标识        1-一类   2-二类   3-三类；第三位：外围系统分户账        1-外围系统分户账（不被查、不被特种业务处理、只被专用交易处理）；第四位：二类账户柜台核实标识        第二位为2时，第四位取值1-柜台已核实的二类户，空-非柜台已核实的二类户（电子账户）第五六位：保证金业务种类        01.代客衍生品保证金        02.国际信用证业务保证金        04.国内信用证进口保证金         05.保函业务保证金         06.承兑业务保证金         07.公务卡保证金         08.零售业务保证金         99.其他保证金；第七位： NRA 账户为期货专用账户标识，1 - 是、 0 - 否；第九位：客户可信标识检查        为1且客户可信标识为F，则客户账户不收不付        为2且客户可信标识为S，则客户账户只收不付；第十位：附属卡停用标识 1-停用；第十一位：地铁保证金签约标识 1-签约", index = 25)
    @ColumnWidth(20)
    private String custAcctExteAttrDomn;

    /**
     * 凭证代码；指账户本身关联的凭证代码种类，如存单、存折等
     */
    @ExcelProperty(value = "凭证代码；指账户本身关联的凭证代码种类，如存单、存折等", index = 26)
    @ColumnWidth(20)
    private String vochCod;

    /**
     * 凭证号码；凭证号码，账户本身关联的凭证代码，如存单号、存折号等
     */
    @ExcelProperty(value = "凭证号码；凭证号码，账户本身关联的凭证代码，如存单号、存折号等", index = 27)
    @ColumnWidth(20)
    private BigDecimal vochNum;

    /**
     * 冠字号；凭证批次号或冠字号，账户本身关联的凭证批次号，如存单号批次、存折号批次等
     */
    @ExcelProperty(value = "冠字号；凭证批次号或冠字号，账户本身关联的凭证批次号，如存单号批次、存折号批次等", index = 28)
    @ColumnWidth(20)
    private String headSeriNum;

    /**
     * 印鉴卡编号；送空，迁移后补录
     */
    @ExcelProperty(value = "印鉴卡编号；送空，迁移后补录", index = 29)
    @ColumnWidth(20)
    private String sealCardNum;

    /**
     * 共用印鉴标识；送N；共用印鉴标识，不同账号共用印鉴的情况
     */
    @ExcelProperty(value = "共用印鉴标识；送N；共用印鉴标识，不同账号共用印鉴的情况", index = 30)
    @ColumnWidth(20)
    private String comnSealFlg;

    /**
     * 账户持有支票上限；单位账户，默认送50，个人送0
     */
    @ExcelProperty(value = "账户持有支票上限；单位账户，默认送50，个人送0", index = 31)
    @ColumnWidth(20)
    private Integer acctCheqMax;

    /**
     * 下一打印行数；默认送1；开立存折时初始化为1，补登折处理或承前打印时，下一打印行数+1，凭证更换时，更换原因为挂失新开或损坏更换时修改为1
     */
    @ExcelProperty(value = "下一打印行数；默认送1；开立存折时初始化为1，补登折处理或承前打印时，下一打印行数+1，凭证更换时，更换原因为挂失新开或损坏更换时修改为1", index = 32)
    @ColumnWidth(20)
    private Short nextPritRowSum;

    /**
     * 账户序号；账户下子账户，若有子账户按照子子账户序号迁移
     */
    @ExcelProperty(value = "账户序号；账户下子账户，若有子账户按照子子账户序号迁移", index = 33)
    @ColumnWidth(20)
    private String acctSqnum;

    /**
     * 新账户序号；新核心自动生成的账户对应序号
     */
    @ExcelProperty(value = "新账户序号；新核心自动生成的账户对应序号", index = 34)
    @ColumnWidth(20)
    private String newAcctSqnum;

    /**
     * 系统账号；送空；系统账号（移入方自动生成）
     */
    @ExcelProperty(value = "系统账号；送空；系统账号（移入方自动生成）", index = 35)
    @ColumnWidth(20)
    private String systActnum;

    /**
     * 币种；人民币填 001
     */
    @ExcelProperty(value = "币种；人民币填 001", index = 36)
    @ColumnWidth(20)
    private String ccyDgtlCod;

    /**
     * 钞汇标识；送空
     */
    @ExcelProperty(value = "钞汇标识；送空", index = 37)
    @ColumnWidth(20)
    private String cashRmitFlg;

    /**
     * 系统账户产品编码；储种+控制字（科目），字段长度可能不足，映射关系待业务确定；系统账号层账户产品，和账户核算相关，如整存整取、单位活期产品等
     */
    @ExcelProperty(value = "系统账户产品编码；储种+控制字（科目），字段长度可能不足，映射关系待业务确定；系统账号层账户产品，和账户核算相关，如整存整取、单位活期产品等", index = 38)
    @ColumnWidth(20)
    private String systAcctPrduNum;

    /**
     * 账户余额；记录建立时为0；账户发生入账业务时累加入账金额（冲正时减少原入账金额）；发生出账业务时减少支取金额（冲正时加回原支取金额）；；详见概要设计-活期存款文档的活期存入组件、活期出账组件的处理；
     */
    @ExcelProperty(value = "账户余额；记录建立时为0；账户发生入账业务时累加入账金额（冲正时减少原入账金额）；发生出账业务时减少支取金额（冲正时加回原支取金额）；；详见概要设计-活期存款文档的活期存入组件、活期出账组件的处理；", index = 39)
    @ColumnWidth(20)
    private Long acctBalc;

    /**
     * 账户名称（子账户）；若有子账户单独名称请填入，没有则不填
     */
    @ExcelProperty(value = "账户名称（子账户）；若有子账户单独名称请填入，没有则不填", index = 40)
    @ColumnWidth(20)
    private String acctNam1;

    /**
     * 账户交易序号；交易内明细序号为从账户登记的第一笔明细开始的累计值，第一笔为0000000000001、第二笔为0000000000002；必输。当发生账务交易时系统账户主表的账户交易序号累加，同时登记到账户明细，记录当前已发生了多少笔交易明细
     */
    @ExcelProperty(value = "账户交易序号；交易内明细序号为从账户登记的第一笔明细开始的累计值，第一笔为0000000000001、第二笔为0000000000002；必输。当发生账务交易时系统账户主表的账户交易序号累加，同时登记到账户明细，记录当前已发生了多少笔交易明细", index = 41)
    @ColumnWidth(20)
    private String acctTranSqnum;

    /**
     * 累计付息金额；迁出方无相关值，送0；存本取息/协议存款等周期付息账户的周期付息金额
     */
    @ExcelProperty(value = "累计付息金额；迁出方无相关值，送0；存本取息/协议存款等周期付息账户的周期付息金额", index = 42)
    @ColumnWidth(20)
    private Long acmlPaidInrsAmt;

    /**
     * 计提金额；待业务确定，12个月以下存期的定期账户迁出时是否计提；定期账户移出当日需计提完成后进行迁移，活期账户没有计提不用迁移，需准确迁移积数以及上次计提日
     */
    @ExcelProperty(value = "计提金额；待业务确定，12个月以下存期的定期账户迁出时是否计提；定期账户移出当日需计提完成后进行迁移，活期账户没有计提不用迁移，需准确迁移积数以及上次计提日", index = 43)
    @ColumnWidth(20)
    private Long acruAmt;

    /**
     * 实际支取次数；账户实际已发生部提的次数
     */
    @ExcelProperty(value = "实际支取次数；账户实际已发生部提的次数", index = 44)
    @ColumnWidth(20)
    private Short actuWhdwTims;

    /**
     * 核准件编号；单位账户核准处理时登记更新，补录
     */
    @ExcelProperty(value = "核准件编号；单位账户核准处理时登记更新，补录", index = 45)
    @ColumnWidth(20)
    private String aprvPrduNum;

    /**
     * 核准备案标识；开立单位人民币结算账户时输入；补录；单位活期账户信息变更时可修改；；（系统可根据结算账户性质自动判别账户的核准、报备种类 或其他：基本户及临时户（验资户除外）为核准类账户，一般账户为备案类账户，专用账户可进行修改，验资户选可选其他）
     */
    @ExcelProperty(value = "核准备案标识；开立单位人民币结算账户时输入；补录；单位活期账户信息变更时可修改；；（系统可根据结算账户性质自动判别账户的核准、报备种类 或其他：基本户及临时户（验资户除外）为核准类账户，一般账户为备案类账户，专用账户可进行修改，验资户选可选其他）", index = 46)
    @ColumnWidth(20)
    private String aprvRcrdFlg;

    /**
     * 营业证件到期日；开户时输入登记；；单位活期账户信息变更时可更新
     */
    @ExcelProperty(value = "营业证件到期日；开户时输入登记；；单位活期账户信息变更时可更新", index = 47)
    @ColumnWidth(20)
    private Integer bsinIdExpiDate;

    /**
     * 计息标识；必输；开户输入登记
     */
    @ExcelProperty(value = "计息标识；必输；开户输入登记", index = 48)
    @ColumnWidth(20)
    private String calcInrsFlg;

    /**
     * 存现限制；个人账户，默认Y，23类账户默认N，单位活期，默认Y，单位定期，默认N；1.产品属性中有相关设置（允许现金存入标识）；；2.单位活期账户开户时可输入；单位活期账户信息变更时可修改（需满足产品控制）；；3.开户无输入时取产品属性值；
     */
    @ExcelProperty(value = "存现限制；个人账户，默认Y，23类账户默认N，单位活期，默认Y，单位定期，默认N；1.产品属性中有相关设置（允许现金存入标识）；；2.单位活期账户开户时可输入；单位活期账户信息变更时可修改（需满足产品控制）；；3.开户无输入时取产品属性值；", index = 49)
    @ColumnWidth(20)
    private String cashDepsLimt;

    /**
     * 取现限制；迁出方，基本户允许取现，一般户、临时户、专户不允许取现；1.产品属性中有相关设置（允许现金支取标识）；；2.单位活期账户开户时可输入；单位活期账户信息变更时可修改（需满足产品控制）；；3.开户无输入时取产品属性值；
     */
    @ExcelProperty(value = "取现限制；迁出方，基本户允许取现，一般户、临时户、专户不允许取现；1.产品属性中有相关设置（允许现金支取标识）；；2.单位活期账户开户时可输入；单位活期账户信息变更时可修改（需满足产品控制）；；3.开户无输入时取产品属性值；", index = 50)
    @ColumnWidth(20)
    private String dbitCashLimt;

    /**
     * 核准日期；单位账户核准处理时登记更新，补录
     */
    @ExcelProperty(value = "核准日期；单位账户核准处理时登记更新，补录", index = 51)
    @ColumnWidth(20)
    private Integer chckAprvDate;

    /**
     * 圈存金额；通过系统间指定业务场景发起的冻结类型，如预授权、理财购买等，默认送0
     */
    @ExcelProperty(value = "圈存金额；通过系统间指定业务场景发起的冻结类型，如预授权、理财购买等，默认送0", index = 52)
    @ColumnWidth(20)
    private Long crfldAmt;

    /**
     * 冻结金额；1.记录建立时为0；金额不准，需业务确认，迁出方司法冻结可能通过行内冻结处理（某些情况下）；2.建立普通部分冻结时累加冻结金额；3.续冻处理时减少原冻结金额，累加修改后的冻结金额；；4.解冻处理时减少冻结金额；
     */
    @ExcelProperty(value = "冻结金额；1.记录建立时为0；金额不准，需业务确认，迁出方司法冻结可能通过行内冻结处理（某些情况下）；2.建立普通部分冻结时累加冻结金额；3.续冻处理时减少原冻结金额，累加修改后的冻结金额；；4.解冻处理时减少冻结金额；", index = 53)
    @ColumnWidth(20)
    private Long frzAmt;

    /**
     * 司法冻结金额；1.记录建立时为0；金额不准，需业务确认，迁出方司法冻结可能通过行内冻结处理（某些情况下）；2.建立司法部分冻结时累加冻结金额；；3.续冻处理时减少原冻结金额，累加修改后的冻结金额；；4.解冻处理时减少冻结金额；
     */
    @ExcelProperty(value = "司法冻结金额；1.记录建立时为0；金额不准，需业务确认，迁出方司法冻结可能通过行内冻结处理（某些情况下）；2.建立司法部分冻结时累加冻结金额；；3.续冻处理时减少原冻结金额，累加修改后的冻结金额；；4.解冻处理时减少冻结金额；", index = 54)
    @ColumnWidth(20)
    private Long judFrzAmt;

    /**
     * 单位存款人类别
     */
    @ExcelProperty(value = "单位存款人类别", index = 55)
    @ColumnWidth(20)
    private String coprSavPesnTyp;

    /**
     * 当前利率保存位置；必输，默认产品层；开户、重定价时修改次利率位置，其他情况不会变动
     */
    @ExcelProperty(value = "当前利率保存位置；必输，默认产品层；开户、重定价时修改次利率位置，其他情况不会变动", index = 56)
    @ColumnWidth(20)
    private String crntInratSavLoca;

    /**
     * 客户账户交易日；同最后交易日期，但自动结息和收取小额管理费处理时不更新此字段(目前暂时给睡眠户用）
     */
    @ExcelProperty(value = "客户账户交易日；同最后交易日期，但自动结息和收取小额管理费处理时不更新此字段(目前暂时给睡眠户用）", index = 57)
    @ColumnWidth(20)
    private Integer custAcctTranDate;

    /**
     * 存期；存期
     */
    @ExcelProperty(value = "存期；存期", index = 58)
    @ColumnWidth(20)
    private String depsPerd;

    /**
     * 起息日期；送开户日期；1.非零金额开户时登记开户输入的起息日期；；2.零金额开户时登记开户日期；；3.批量结息处理时更新；
     */
    @ExcelProperty(value = "起息日期；送开户日期；1.非零金额开户时登记开户输入的起息日期；；2.零金额开户时登记开户日期；；3.批量结息处理时更新；", index = 59)
    @ColumnWidth(20)
    private Integer effvInrsDate;

    /**
     * 是否基本账户；必输，默认送N；从此系统账户的产品属性中获取，不可变更；取数只能为Y-是 或 N-否
     */
    @ExcelProperty(value = "是否基本账户；必输，默认送N；从此系统账户的产品属性中获取，不可变更；取数只能为Y-是 或 N-否", index = 60)
    @ColumnWidth(20)
    private String ifBascAcct;

    /**
     * 是否默认账户；必输，定期默认送N，活期送Y；从此系统账户的产品属性中获取，不可变更，一户通账户默认为N；取值只能为Y-是 或 N-否，一般非一户通账户均为默认户，若为定期一本通下子账户没有默认户
     */
    @ExcelProperty(value = "是否默认账户；必输，定期默认送N，活期送Y；从此系统账户的产品属性中获取，不可变更，一户通账户默认为N；取值只能为Y-是 或 N-否，一般非一户通账户均为默认户，若为定期一本通下子账户没有默认户", index = 61)
    @ColumnWidth(20)
    private String ifDfltAcct;

    /**
     * 利率种类；默认送3；利率种类，无特殊账户利率，则为3-系统利率
     */
    @ExcelProperty(value = "利率种类；默认送3；利率种类，无特殊账户利率，则为3-系统利率", index = 62)
    @ColumnWidth(20)
    private String inratClss;

    /**
     * 利息调整金额；默认0，月月红已结未付利息，登记到此处，仅限定期使用；初始为0；；暂定其他带结息金额放入此字段
     */
    @ExcelProperty(value = "利息调整金额；默认0，月月红已结未付利息，登记到此处，仅限定期使用；初始为0；；暂定其他带结息金额放入此字段", index = 63)
    @ColumnWidth(20)
    private Long inrsAjstAmt;

    /**
     * 扣划利息金额；迁出方，扣划不会产生利息，默认送0，待定；扣划产生的利息累加在此处，下次支付利息时支付
     */
    @ExcelProperty(value = "扣划利息金额；迁出方，扣划不会产生利息，默认送0，待定；扣划产生的利息累加在此处，下次支付利息时支付", index = 64)
    @ColumnWidth(20)
    private Long inrsDervDduc;

    /**
     * 利息收入账号；默认送空；单位活期账户开户时可输入；；单位活期账户信息变更时可修改；
     */
    @ExcelProperty(value = "利息收入账号；默认送空；单位活期账户开户时可输入；；单位活期账户信息变更时可修改；", index = 65)
    @ColumnWidth(20)
    private String inrsIncmActnum;

    /**
     * 转存方式；自动转存的定期，送2-本息转存
     */
    @ExcelProperty(value = "转存方式；自动转存的定期，送2-本息转存", index = 66)
    @ColumnWidth(20)
    private String rdepMode;

    /**
     * 转存关联账号；不转存、本息转存时无需录入，送空
     */
    @ExcelProperty(value = "转存关联账号；不转存、本息转存时无需录入，送空", index = 67)
    @ColumnWidth(20)
    private String rdepRelaActnum;

    /**
     * 转存关联账户序号；转存账号非默认结算账户时输入对应序号，送空
     */
    @ExcelProperty(value = "转存关联账户序号；转存账号非默认结算账户时输入对应序号，送空", index = 68)
    @ColumnWidth(20)
    private String rdepRelaAcctSqnum;

    /**
     * 上次积数日；默认迁移日+1；初始为0；批量时进行动户滚积数处理时更新
     */
    @ExcelProperty(value = "上次积数日；默认迁移日+1；初始为0；批量时进行动户滚积数处理时更新", index = 69)
    @ColumnWidth(20)
    private Integer lastAcmuBalcDate;

    /**
     * 上次余额；送余额；1.开户建立记录时为0；；2.每日第一笔出入账处理时在更新账户余额之前，更新此字段为原账户余额；；3.当前交易日期小于账户最后交易日时，上次余额需要相应的增加或减少交易金额（存入或支取）；；详见概要设计-活期存款文档的活期存入组件、活期出账组件的处理；
     */
    @ExcelProperty(value = "上次余额；送余额；1.开户建立记录时为0；；2.每日第一笔出入账处理时在更新账户余额之前，更新此字段为原账户余额；；3.当前交易日期小于账户最后交易日时，上次余额需要相应的增加或减少交易金额（存入或支取）；；详见概要设计-活期存款文档的活期存入组件、活期出账组件的处理；", index = 70)
    @ColumnWidth(20)
    private Long lastBalc;

    /**
     * 上次扣划日期；扣划之后需按照活期付息时，登记该次扣划日期
     */
    @ExcelProperty(value = "上次扣划日期；扣划之后需按照活期付息时，登记该次扣划日期", index = 71)
    @ColumnWidth(20)
    private Integer lastDateOfDduc;

    /**
     * 上日余额；1.开户建立记录时为0；；2.批量时计息模块进行动户积数处理时根据相关规则计算更新此字段；详见概要设计-负债公共（计结息）文档中的相关处理
     */
    @ExcelProperty(value = "上日余额；1.开户建立记录时为0；；2.批量时计息模块进行动户积数处理时根据相关规则计算更新此字段；详见概要设计-负债公共（计结息）文档中的相关处理", index = 72)
    @ColumnWidth(20)
    private Long lastDayBalc;

    /**
     * 积数起始日期；用以表示当前累计积数的起始日期
     */
    @ExcelProperty(value = "积数起始日期；用以表示当前累计积数的起始日期", index = 73)
    @ColumnWidth(20)
    private Integer agrgStrtDate;

    /**
     * 积数；积数分段时产生，发生冲账、或解冻不计息时积数调整时进行修改，若为单层利率迁移累计积数信息
     */
    @ExcelProperty(value = "积数；积数分段时产生，发生冲账、或解冻不计息时积数调整时进行修改，若为单层利率迁移累计积数信息", index = 74)
    @ColumnWidth(20)
    private BigDecimal agrgNum;

    /**
     * 上次结息日；月月红，上次结息日期登记为迁移日；初始为0；批量时进行结息处理时更新
     */
    @ExcelProperty(value = "上次结息日；月月红，上次结息日期登记为迁移日；初始为0；批量时进行结息处理时更新", index = 75)
    @ColumnWidth(20)
    private Integer lastInrsSettDate;

    /**
     * 最后交易日期；1.记录建立时登记交易日期；；2.账户发生记账处理时若交易会计日晚于此字段，则将字段值更新为交易会计日；
     */
    @ExcelProperty(value = "最后交易日期；1.记录建立时登记交易日期；；2.账户发生记账处理时若交易会计日晚于此字段，则将字段值更新为交易会计日；", index = 76)
    @ColumnWidth(20)
    private Integer lastLastTranDate;

    /**
     * 上次计提日期
     */
    @ExcelProperty(value = "上次计提日期", index = 77)
    @ColumnWidth(20)
    private Integer lastTimeAcruDate;

    /**
     * 通知存款提前通知天数；通知存款提前通知天数
     */
    @ExcelProperty(value = "通知存款提前通知天数；通知存款提前通知天数", index = 78)
    @ColumnWidth(20)
    private Short notiDepsAdveNotiDayCnt;

    /**
     * 开户金额；开户金额
     */
    @ExcelProperty(value = "开户金额；开户金额", index = 79)
    @ColumnWidth(20)
    private Long opactAmt;

    /**
     * 开户利率；送执行利率；预留字段
     */
    @ExcelProperty(value = "开户利率；送执行利率；预留字段", index = 80)
    @ColumnWidth(20)
    private Integer opactInrat;

    /**
     * 开户日期；开户时从系统获取
     */
    @ExcelProperty(value = "开户日期；开户时从系统获取", index = 81)
    @ColumnWidth(20)
    private Integer opactDate;

    /**
     * 开户渠道；全渠道ACP为柜面，若无其他渠道开户则全部迁移为ACP
     */
    @ExcelProperty(value = "开户渠道；全渠道ACP为柜面，若无其他渠道开户则全部迁移为ACP", index = 82)
    @ColumnWidth(20)
    private String opactChnl;

    /**
     * 开户发起方系统代码；开户发起方系统代码（新系统），无送空
     */
    @ExcelProperty(value = "开户发起方系统代码；开户发起方系统代码（新系统），无送空", index = 83)
    @ColumnWidth(20)
    private String opactIntorSystCod;

    /**
     * 开户机构；新机构
     */
    @ExcelProperty(value = "开户机构；新机构", index = 84)
    @ColumnWidth(20)
    private String opactOrgn;

    /**
     * 开户时间
     */
    @ExcelProperty(value = "开户时间", index = 85)
    @ColumnWidth(20)
    private Integer opactTime;

    /**
     * 开户交易日志号；开户时从系统获取
     */
    @ExcelProperty(value = "开户交易日志号；开户时从系统获取", index = 86)
    @ColumnWidth(20)
    private String opactTranLogNum;

    /**
     * 开户用户；柜员
     */
    @ExcelProperty(value = "开户用户；柜员", index = 87)
    @ColumnWidth(20)
    private String opactUser;

    /**
     * 定期自动处理标识；是否自动续存/取息/取本标志，送N
     */
    @ExcelProperty(value = "定期自动处理标识；是否自动续存/取息/取本标志，送N", index = 88)
    @ColumnWidth(20)
    private String perdAutmPrcsFlg;

    /**
     * 定期自动处理对方账户；自动续存/取息/取本对方账户，送空
     */
    @ExcelProperty(value = "定期自动处理对方账户；自动续存/取息/取本对方账户，送空", index = 89)
    @ColumnWidth(20)
    private String perdAutmPrcsCnpyAcct;

    /**
     * 法人\负责人身份证件到期日；开户时输入登记；；单位活期账户信息变更时可更新
     */
    @ExcelProperty(value = "法人\负责人身份证件到期日；开户时输入登记；；单位活期账户信息变更时可更新", index = 90)
    @ColumnWidth(20)
    private Integer prcpLeglPesnIdExpiDate;

    /**
     * 人民币账户专门用途（预算类）；开立单位人民币账户时选输；，业务补录；单位活期账户信息变更时可修改
     */
    @ExcelProperty(value = "人民币账户专门用途（预算类）；开立单位人民币账户时选输；，业务补录；单位活期账户信息变更时可修改", index = 91)
    @ColumnWidth(20)
    private String rmbAcctSpclUse;

    /**
     * 人民币结算账户性质；开立单位人民币结算账户时输入；；单位活期账户信息变更时可修改
     */
    @ExcelProperty(value = "人民币结算账户性质；开立单位人民币结算账户时输入；；单位活期账户信息变更时可修改", index = 92)
    @ColumnWidth(20)
    private String rmbSettAcctAttr;

    /**
     * 存款资金用途；开立单位活期及保证金账户时输入，非保证金，默认01
     */
    @ExcelProperty(value = "存款资金用途；开立单位活期及保证金账户时输入，非保证金，默认01", index = 93)
    @ColumnWidth(20)
    private String savCptlUse;

    /**
     * 储种；必输；从产品取出
     */
    @ExcelProperty(value = "储种；必输；从产品取出", index = 94)
    @ColumnWidth(20)
    private String savTypLeng2;

    /**
     * 分段计息标识；必输，默认送Y；从产品获取
     */
    @ExcelProperty(value = "分段计息标识；必输，默认送Y；从产品获取", index = 95)
    @ColumnWidth(20)
    private String sgmtCalcInrsFlg;

    /**
     * 专用存款账户资金种类；业务补录；开立单位人民币专用账户时选输；；单位活期账户信息变更时可修改
     */
    @ExcelProperty(value = "专用存款账户资金种类；业务补录；开立单位人民币专用账户时选输；；单位活期账户信息变更时可修改", index = 96)
    @ColumnWidth(20)
    private String spclDepsAcctCptlTyp;

    /**
     * 系统账户扩展属性域；计息基准天数送值（由移入方根据产品参数更新）待定；零整已违约，第三十六位，送：1-违约
     */
    @ExcelProperty(value = "系统账户扩展属性域；计息基准天数送值（由移入方根据产品参数更新）待定；零整已违约，第三十六位，送：1-违约", index = 97)
    @ColumnWidth(20)
    private String systAcctExteAttrDomn;

    /**
     * 系统账户扩展属性域(附属）；送空
     */
    @ExcelProperty(value = "系统账户扩展属性域(附属）；送空", index = 98)
    @ColumnWidth(20)
    private String systAcctExteAttrDomnAflt;

    /**
     * 系统账户状态；必输，只有1、8；1.个人活期或单位活期非结算户开立时为正常；；2.单位结算户核准类的开立时为待核准，核准后为待支付或正常（核准交易晚于可支付生效日时直接更新为正常状态）；；3.备案类单位结算户开户时为待支付，可支付生效日日初系统自动更新为正常；；4.单位账户销户计息处理后为预销户；；5.销户后为销户；开户后进行开户冲正撤消为作废；多账户产品下各活期系统账号结清处理后为结清；；6、开户后冲正时，账户为作废状态。；7、结清（作废）；8、个人和单位转睡眠账户时为久悬状态，待转久悬时为正常状态。
     */
    @ExcelProperty(value = "系统账户状态；必输，只有1、8；1.个人活期或单位活期非结算户开立时为正常；；2.单位结算户核准类的开立时为待核准，核准后为待支付或正常（核准交易晚于可支付生效日时直接更新为正常状态）；；3.备案类单位结算户开户时为待支付，可支付生效日日初系统自动更新为正常；；4.单位账户销户计息处理后为预销户；；5.销户后为销户；开户后进行开户冲正撤消为作废；多账户产品下各活期系统账号结清处理后为结清；；6、开户后冲正时，账户为作废状态。；7、结清（作废）；8、个人和单位转睡眠账户时为久悬状态，待转久悬时为正常状态。", index = 99)
    @ColumnWidth(20)
    private String systAcctStas;

    /**
     * 临时户性质；业务补录；开立单位人民币临时账户时输入；；单位活期账户信息变更时可修改；
     */
    @ExcelProperty(value = "临时户性质；业务补录；开立单位人民币临时账户时输入；；单位活期账户信息变更时可修改；", index = 100)
    @ColumnWidth(20)
    private String tempAcctNtur;

    /**
     * 定活标识；必输；开立系统账户时输入，不可变更；取值只能是1-定期或2-活期
     */
    @ExcelProperty(value = "定活标识；必输；开立系统账户时输入，不可变更；取值只能是1-定期或2-活期", index = 101)
    @ColumnWidth(20)
    private String termAndCrntFlg;

    /**
     * 支取周期；整零/存本取息账户支取周期，送0，零整（经沟通确认，零整只有按月周期）送1
     */
    @ExcelProperty(value = "支取周期；整零/存本取息账户支取周期，送0，零整（经沟通确认，零整只有按月周期）送1", index = 102)
    @ColumnWidth(20)
    private Short whdwCycl;

    /**
     * 保证金业务种类；按实际上送，通过业务补录文件补充；01-承兑保证金；02-担保保证金；03-信用证保证金；04-信用卡保证金；05-保理保证金；06-住房按揭保证金；07-汽车按揭保证金；08-提货担保保证金；09-远期结售汇保证金；10-委托理财保证金；11-外汇买卖保证金；12-联合基金保证金；13-联保保证金；14-保函保证金；15-其他贸易融资保证金；16-其他外汇业务保证金；17-境内同业存入保证金；18-境外同业存入保证金；19-票据池保证金；99-其他保证金
     */
    @ExcelProperty(value = "保证金业务种类；按实际上送，通过业务补录文件补充；01-承兑保证金；02-担保保证金；03-信用证保证金；04-信用卡保证金；05-保理保证金；06-住房按揭保证金；07-汽车按揭保证金；08-提货担保保证金；09-远期结售汇保证金；10-委托理财保证金；11-外汇买卖保证金；12-联合基金保证金；13-联保保证金；14-保函保证金；15-其他贸易融资保证金；16-其他外汇业务保证金；17-境内同业存入保证金；18-境外同业存入保证金；19-票据池保证金；99-其他保证金", index = 103)
    @ColumnWidth(20)
    private String cadpsBsinVrty;

    /**
     * 保证金关联金额；金额单一，没有冻结额度，送值与账户金额一致
     */
    @ExcelProperty(value = "保证金关联金额；金额单一，没有冻结额度，送值与账户金额一致", index = 104)
    @ColumnWidth(20)
    private LocalDateTime cadpsRelaAmt;

    /**
     * 关联账号；保证金关联账号，通过业务补录文件补充
     */
    @ExcelProperty(value = "关联账号；保证金关联账号，通过业务补录文件补充", index = 105)
    @ColumnWidth(20)
    private String relaActnum;

    /**
     * 关联账户序号；保证金关联账号序号，通过业务补录文件补充
     */
    @ExcelProperty(value = "关联账户序号；保证金关联账号序号，通过业务补录文件补充", index = 106)
    @ColumnWidth(20)
    private String relaAcctSqnum;

    /**
     * 时间戳
     */
    @ExcelProperty(value = "时间戳", index = 107)
    @ColumnWidth(20)
    private LocalDateTime tmtpTime;

}
