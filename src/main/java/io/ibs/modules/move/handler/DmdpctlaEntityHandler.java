package io.ibs.modules.move.handler;

import io.ibs.modules.move.common.entity.DmdpctlaEntity;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class DmdpctlaEntityHandler {
    public void setDefaultParams(DmdpctlaEntity entity) {
        try {
            Class<?> clazz = entity.getClass();
            List<Field> allFields = getAllFields(clazz);
            
            for (Field field : allFields) {
                field.setAccessible(true);
                field.set(entity, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        
        // 添加当前类字段
        try {
            fields.add(clazz.getDeclaredField("dtbtKey"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("bankNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ctrlObjtTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ctrlObjtNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp3"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu3"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu3"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate3"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp4"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu4"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu4"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate4"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp5"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu5"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu5"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate5"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp6"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu6"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu6"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate6"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp7"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu7"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu7"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate7"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp8"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu8"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu8"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate8"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp9"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu9"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu9"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate9"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp10"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu10"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu10"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate10"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp11"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu11"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu11"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate11"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp12"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu12"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu12"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate12"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp13"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu13"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu13"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate13"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp14"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu14"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu14"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate14"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp15"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu15"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu15"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate15"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp16"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu16"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu16"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate16"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp17"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu17"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu17"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate17"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp18"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu18"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu18"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate18"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp19"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu19"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu19"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate19"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp20"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu20"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu20"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate20"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp21"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu21"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu21"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate21"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp22"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu22"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu22"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate22"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp23"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu23"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu23"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate23"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp24"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu24"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu24"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate24"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp25"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu25"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu25"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate25"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp26"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu26"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu26"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate26"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp27"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu27"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu27"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate27"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp28"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu28"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu28"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate28"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp29"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu29"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu29"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate29"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp30"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu30"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu30"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate30"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp31"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu31"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu31"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate31"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp32"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu32"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu32"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate32"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp33"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu33"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu33"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate33"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp34"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu34"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu34"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate34"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp35"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu35"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu35"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate35"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp36"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu36"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu36"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate36"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp37"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu37"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu37"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate37"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp38"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu38"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu38"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate38"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp39"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu39"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu39"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate39"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("quotTyp40"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastPerdAcmlVlu40"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crntCyclAcmlVlu40"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastUpdtDate40"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("rcrdStas"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tmtpTime"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loclTranDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loclTranTime"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loclTmtp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        
        // 递归添加父类字段
        Class<?> superClass = clazz.getSuperclass();
        while (superClass != null && superClass != Object.class) {
            for (Field field : superClass.getDeclaredFields()) {
                fields.add(field);
            }
            superClass = superClass.getSuperclass();
        }
        
        return fields;
    }
}