package io.ibs.modules.move.handler;

import io.ibs.modules.move.common.entity.DmlncofaEntity;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class DmlncofaEntityHandler {
    public void setDefaultParams(DmlncofaEntity entity) {
        try {
            Class<?> clazz = entity.getClass();
            List<Field> allFields = getAllFields(clazz);
            
            for (Field field : allFields) {
                field.setAccessible(true);
                field.set(entity, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        
        // 添加当前类字段
        try {
            fields.add(clazz.getDeclaredField("dtbtKey"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("bankNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("orgnNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prtcNumLeng13"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loanPrtcStas"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("custNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prduNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("relaPrduNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loanBigClss"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ccyDgtlCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prtcAmt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cyclFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loanActuInveDrec"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("useSbcl"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("grteClss"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("grteSbcl"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("origDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("takeEfftDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("expiDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("origExpiDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("stopInrsSwtcDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prtcLimt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("shrtMidlLongPerdFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loanMangTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("syndLoanLeadFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("selbkLot"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("chagInrsMode"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("calcInrsDayCntTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("defitCalcInrsDayTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("yearInrsRatCrlt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("annlRatBaseRealDayCnt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ifSettInrsFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ifTrfrOdue"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ifExmtDefitComit"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ifLevyInrsAftrTrfr"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("defitGracMode"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("defitGracDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("odueGracDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("expiGracPerd"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("expiGracUseInratFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("expiGracPerdInratUseFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sbsiInrsInrat"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mangOrgn"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("crspAcctTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("smalAmtPldgFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loanWithRepyFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("relaCrdtLimtNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("relaPrjtPrtc"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("subPrjtPrtcNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("pbrfdCterCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("pbrfdLoanPrtcNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prcpCustNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("entsLoanReliMode"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("payeCustNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sharAmt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("spclAmt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("alryUseSharAmt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("apldSpclAmt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("onleOflnFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cnpyTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("billTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("clrgTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("systInnrAndExtrFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("autmBorwRecpSettFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ifSelfBankDrctDiscNote"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prsvChar"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("preResvNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("agntNam"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("custMagr1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("custMagr2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("opactDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("opactOrgn"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("opactUser"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("opactTranLogNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorSystCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("afltBsinSystCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorTranSenum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastLastTranDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastTranLogNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastTranUser"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("noteTxt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tmtpTime"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        
        // 递归添加父类字段
        Class<?> superClass = clazz.getSuperclass();
        while (superClass != null && superClass != Object.class) {
            for (Field field : superClass.getDeclaredFields()) {
                fields.add(field);
            }
            superClass = superClass.getSuperclass();
        }
        
        return fields;
    }
}