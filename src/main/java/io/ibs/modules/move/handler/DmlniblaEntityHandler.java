package io.ibs.modules.move.handler;

import io.ibs.modules.move.common.entity.DmlniblaEntity;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class DmlniblaEntityHandler {
    public void setDefaultParams(DmlniblaEntity entity) {
        try {
            Class<?> clazz = entity.getClass();
            List<Field> allFields = getAllFields(clazz);
            
            for (Field field : allFields) {
                field.setAccessible(true);
                field.set(entity, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        
        // 添加当前类字段
        try {
            fields.add(clazz.getDeclaredField("dtbtKey"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("bankNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prtcNumLeng13"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("iouSqnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("todyBalc1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc3"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc4"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc5"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc6"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc7"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc8"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc9"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc16"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc25"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc26"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc28"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc29"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc30"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc31"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc32"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sadayBalc33"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc3"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc4"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc5"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc7"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc8"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc9"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc16"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc25"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc26"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc28"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc29"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc32"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("lastDayBalc33"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        
        // 递归添加父类字段
        Class<?> superClass = clazz.getSuperclass();
        while (superClass != null && superClass != Object.class) {
            for (Field field : superClass.getDeclaredFields()) {
                fields.add(field);
            }
            superClass = superClass.getSuperclass();
        }
        
        return fields;
    }
}