package io.ibs.modules.move.handler;

import io.ibs.modules.move.common.entity.DmpmaahaEntity;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class DmpmaahaEntityHandler {
    public void setDefaultParams(DmpmaahaEntity entity) {
        try {
            Class<?> clazz = entity.getClass();
            List<Field> allFields = getAllFields(clazz);
            
            for (Field field : allFields) {
                field.setAccessible(true);
                field.set(entity, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        
        // 添加当前类字段
        try {
            fields.add(clazz.getDeclaredField("dtbtKey"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("bankNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("custActnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("acctSqnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("coprPesnFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ifSettAcctFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("acctStas"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("agntIdCardTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("agntIdCardNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("agntPesnNam"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("agntCntaTlph"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("opactDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("opactTranLogNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("clrgClactClosDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("clactTranLogNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cratUser"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cratOrgn"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cratDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mantUser"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mantOrgn"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mantDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tmtpTime"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        
        // 递归添加父类字段
        Class<?> superClass = clazz.getSuperclass();
        while (superClass != null && superClass != Object.class) {
            for (Field field : superClass.getDeclaredFields()) {
                fields.add(field);
            }
            superClass = superClass.getSuperclass();
        }
        
        return fields;
    }
}