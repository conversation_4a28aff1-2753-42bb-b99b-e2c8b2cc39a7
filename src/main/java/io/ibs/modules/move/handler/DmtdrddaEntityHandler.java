package io.ibs.modules.move.handler;

import io.ibs.modules.move.common.entity.DmtdrddaEntity;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class DmtdrddaEntityHandler {
    public void setDefaultParams(DmtdrddaEntity entity) {
        try {
            Class<?> clazz = entity.getClass();
            List<Field> allFields = getAllFields(clazz);
            
            for (Field field : allFields) {
                field.setAccessible(true);
                field.set(entity, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        
        // 添加当前类字段
        try {
            fields.add(clazz.getDeclaredField("dtbtKey"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("bankNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("coprPesnFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("custNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("custActnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("acctSqnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("systActnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ccyDgtlCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cashRmitFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tranDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tranOrgnNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("afltOrgn"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mangOrgn"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tranLogNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("userNam"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("coreTranCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cashTrfrAcctFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dbitCrdtFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tranAmt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("acctBalc"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("ovdfAmt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("effvInrsDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dtalStrkBalcFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("strkBalcTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("rvrsAcctCnpyTranDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("strkBalcCnpyLogNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("payVochCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("headSeriNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("vochNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("vochSignIssuDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("systAcctPrduNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("srveClssPrduNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("custAcctPrduEncd"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mangTaskNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("prftCter"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tractReas"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cashPrjtCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("agntOperFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorChnl"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorSystCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorTmnlNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorTranCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorTranSenum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("intorDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("imagId"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mrchTyp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("mrchNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("uvslCnpyCustActnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cnpyAcctSqnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cnpyAcctNam"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cnpyBankNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cnpyBankNam"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("iebakLinkNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("relaSrvePrduNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("abstTxt"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("abstCod"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("sttcAthoDrcr"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dynmAthoDrcr"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tranInnrDtalSqnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tranTime"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("acctTranSqnum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cptlRelaFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("fincTranFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dtalPrsvDate1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dtalPrsvDate2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dtalPrsvAcct1"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dtalPrsvAcct2"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("dtalExteAttrDomn"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("preResvNum"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("tmtpTime"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("cratTmtp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loclTranDate"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loclTranTime"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("loclTmtp"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        try {
            fields.add(clazz.getDeclaredField("stmtShowFlg"));
        } catch (NoSuchFieldException e) {
            // 忽略不存在的字段
        }
        
        // 递归添加父类字段
        Class<?> superClass = clazz.getSuperclass();
        while (superClass != null && superClass != Object.class) {
            for (Field field : superClass.getDeclaredFields()) {
                fields.add(field);
            }
            superClass = superClass.getSuperclass();
        }
        
        return fields;
    }
}