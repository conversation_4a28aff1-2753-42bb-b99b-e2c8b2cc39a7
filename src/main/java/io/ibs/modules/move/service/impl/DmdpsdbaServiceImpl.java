package io.ibs.modules.move.service.impl;

import io.ibs.modules.move.common.entity.DmdpsdbaEntity;
import io.ibs.modules.move.dao.DmdpsdbaMapper;
import io.ibs.modules.move.service.DmdpsdbaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 负债-（DMDPSDBA）强制扣划登记簿中间文件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
public class DmdpsdbaServiceImpl extends ServiceImpl<DmdpsdbaMapper, DmdpsdbaEntity> implements DmdpsdbaService {

}
