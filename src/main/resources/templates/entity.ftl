package ${package.Entity};

import io.ibs.modules.move.common.entity.DMBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.ToString;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnore;

<#list table.fields as field>
    <#if field.propertyName != "id" && field.propertyName != "createTime" && field.propertyName != "updateTime">
        <#if field.comment??>/**
         * ${field.comment}
         */</#if>
        @ExcelProperty("${field.comment!}")
    <#else>
        @ExcelIgnore
    </#if>
    private ${field.propertyType} ${field.propertyName};
</#list>

@ToString(callSuper = true)
public class ${entity} extends DMBaseEntity {
    private static final long serialVersionUID = 1L;
}