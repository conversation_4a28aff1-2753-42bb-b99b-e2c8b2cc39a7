package com.xlc.excel.service;

import com.xlc.excel.dto.ExcelExportRequest;
import com.xlc.excel.dto.ExcelImportRequest;
import com.xlc.excel.dto.ExcelImportResult;
import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.model.UserInfo;
import com.xlc.excel.service.ExcelExportService.ExcelExportResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel服务测试类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@SpringBootTest
public class ExcelServiceTest {
    
    @Autowired
    private ExcelService excelService;
    
    private List<UserInfo> testUserList;
    
    @BeforeEach
    void setUp() {
        // 准备测试数据
        testUserList = new ArrayList<>();
        for (long i = 1; i <= 10; i++) {
            testUserList.add(UserInfo.createSample(i, "user" + i));
        }
    }
    
    @Test
    void testExportByTableName() {
        // 测试根据表名导出
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        ExcelExportResult result = excelService.exportByTableName(
                outputStream,
                "用户信息.xlsx",
                "user_info",
                testUserList,
                UserInfo.class
        );
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(10, result.getTotalRows());
        assertTrue(outputStream.size() > 0);
        
        System.out.println("导出结果：" + result.getSummary());
    }
    
    @Test
    void testExportByTemplate() {
        // 测试根据模板导出
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        ExcelExportResult result = excelService.exportByTemplate(
                outputStream,
                "用户信息模板.xlsx",
                ExcelTemplateEnum.USER_INFO,
                testUserList,
                UserInfo.class
        );
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(10, result.getTotalRows());
        assertTrue(outputStream.size() > 0);
        
        System.out.println("模板导出结果：" + result.getSummary());
    }
    
    @Test
    void testExportTemplate() {
        // 测试导出模板文件
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        ExcelExportResult result = excelService.exportTemplate(
                outputStream,
                "用户信息模板.xlsx",
                ExcelTemplateEnum.USER_INFO
        );
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getTotalRows()); // 模板文件只有表头，没有数据
        assertTrue(outputStream.size() > 0);
        
        System.out.println("模板文件导出结果：" + result.getSummary());
    }
    
    @Test
    void testImportByTableName() {
        // 先导出一个Excel文件
        ByteArrayOutputStream exportStream = new ByteArrayOutputStream();
        ExcelExportResult exportResult = excelService.exportByTableName(
                exportStream,
                "测试用户数据.xlsx",
                "user_info",
                testUserList,
                UserInfo.class
        );
        
        assertTrue(exportResult.isSuccess());
        
        // 再导入这个Excel文件
        ByteArrayInputStream importStream = new ByteArrayInputStream(exportStream.toByteArray());
        
        ExcelImportResult<UserInfo> importResult = excelService.importByTableName(
                importStream,
                "测试用户数据.xlsx",
                "user_info",
                UserInfo.class
        );
        
        assertNotNull(importResult);
        assertTrue(importResult.isSuccess());
        assertEquals(10, importResult.getSuccessRows());
        assertEquals(0, importResult.getFailureRows());
        assertNotNull(importResult.getSuccessData());
        assertEquals(10, importResult.getSuccessData().size());
        
        System.out.println("导入结果：" + importResult.getSummary());
    }
    
    @Test
    void testImportByTemplate() {
        // 先导出一个Excel文件
        ByteArrayOutputStream exportStream = new ByteArrayOutputStream();
        ExcelExportResult exportResult = excelService.exportByTemplate(
                exportStream,
                "测试用户模板数据.xlsx",
                ExcelTemplateEnum.USER_INFO,
                testUserList,
                UserInfo.class
        );
        
        assertTrue(exportResult.isSuccess());
        
        // 再导入这个Excel文件
        ByteArrayInputStream importStream = new ByteArrayInputStream(exportStream.toByteArray());
        
        ExcelImportResult<UserInfo> importResult = excelService.importByTemplate(
                importStream,
                "测试用户模板数据.xlsx",
                ExcelTemplateEnum.USER_INFO,
                UserInfo.class
        );
        
        assertNotNull(importResult);
        assertTrue(importResult.isSuccess());
        assertEquals(10, importResult.getSuccessRows());
        assertEquals(0, importResult.getFailureRows());
        
        System.out.println("模板导入结果：" + importResult.getSummary());
    }
    
    @Test
    void testPreviewExcel() {
        // 先导出一个Excel文件
        ByteArrayOutputStream exportStream = new ByteArrayOutputStream();
        ExcelExportResult exportResult = excelService.exportByTableName(
                exportStream,
                "预览测试数据.xlsx",
                "user_info",
                testUserList,
                UserInfo.class
        );
        
        assertTrue(exportResult.isSuccess());
        
        // 预览Excel文件（只读取前5行）
        ByteArrayInputStream importStream = new ByteArrayInputStream(exportStream.toByteArray());
        
        ExcelImportResult<UserInfo> previewResult = excelService.previewExcel(
                importStream,
                "预览测试数据.xlsx",
                UserInfo.class,
                5
        );
        
        assertNotNull(previewResult);
        assertTrue(previewResult.isSuccess());
        assertTrue(previewResult.getSuccessRows() <= 5); // 预览最多5行
        
        System.out.println("预览结果：" + previewResult.getSummary());
    }
    
    @Test
    void testValidateExcelFormat() {
        // 先导出一个Excel文件
        ByteArrayOutputStream exportStream = new ByteArrayOutputStream();
        ExcelExportResult exportResult = excelService.exportByTableName(
                exportStream,
                "格式验证测试.xlsx",
                "user_info",
                testUserList,
                UserInfo.class
        );
        
        assertTrue(exportResult.isSuccess());
        
        // 验证Excel文件格式
        ByteArrayInputStream validateStream = new ByteArrayInputStream(exportStream.toByteArray());
        
        boolean isValid = excelService.validateExcelFormat(
                validateStream,
                "格式验证测试.xlsx",
                "user_info"
        );
        
        assertTrue(isValid);
        
        System.out.println("文件格式验证结果：" + isValid);
    }
    
    @Test
    void testGetExcelFileInfo() {
        // 先导出一个Excel文件
        ByteArrayOutputStream exportStream = new ByteArrayOutputStream();
        ExcelExportResult exportResult = excelService.exportByTableName(
                exportStream,
                "文件信息测试.xlsx",
                "user_info",
                testUserList,
                UserInfo.class
        );
        
        assertTrue(exportResult.isSuccess());
        
        // 获取Excel文件信息
        ByteArrayInputStream infoStream = new ByteArrayInputStream(exportStream.toByteArray());
        
        ExcelImportService.ExcelFileInfo fileInfo = excelService.getExcelFileInfo(
                infoStream,
                "文件信息测试.xlsx"
        );
        
        assertNotNull(fileInfo);
        assertTrue(fileInfo.isValid());
        assertEquals("文件信息测试.xlsx", fileInfo.getFileName());
        assertEquals("xlsx", fileInfo.getFileType());
        
        System.out.println("文件信息：" + fileInfo.toString());
    }
    
    @Test
    void testGetSupportedTemplates() {
        List<ExcelTemplateEnum> templates = excelService.getSupportedTemplates();
        
        assertNotNull(templates);
        assertFalse(templates.isEmpty());
        assertTrue(templates.contains(ExcelTemplateEnum.USER_INFO));
        
        System.out.println("支持的模板数量：" + templates.size());
        templates.forEach(template -> System.out.println("模板：" + template.getName()));
    }
    
    @Test
    void testGetTemplateByTableName() {
        ExcelTemplateEnum template = excelService.getTemplateByTableName("user_info");
        
        assertNotNull(template);
        assertEquals(ExcelTemplateEnum.USER_INFO, template);
        
        System.out.println("根据表名获取的模板：" + template.getName());
    }
    
    @Test
    void testGetServiceHealth() {
        ExcelService.ServiceHealthInfo healthInfo = excelService.getServiceHealth();
        
        assertNotNull(healthInfo);
        assertTrue(healthInfo.isHealthy());
        assertEquals("UP", healthInfo.getStatus());
        assertNotNull(healthInfo.getVersion());
        
        System.out.println("服务健康状态：" + healthInfo.toString());
    }
    
    @Test
    void testAsyncImport() throws Exception {
        // 先导出一个Excel文件
        ByteArrayOutputStream exportStream = new ByteArrayOutputStream();
        ExcelExportResult exportResult = excelService.exportByTableName(
                exportStream,
                "异步导入测试.xlsx",
                "user_info",
                testUserList,
                UserInfo.class
        );
        
        assertTrue(exportResult.isSuccess());
        
        // 创建异步导入请求
        ByteArrayInputStream importStream = new ByteArrayInputStream(exportStream.toByteArray());
        ExcelImportRequest request = ExcelImportRequest.byTableName(
                importStream,
                "异步导入测试.xlsx",
                "user_info",
                UserInfo.class
        );
        
        // 执行异步导入
        CompletableFuture<ExcelImportResult<UserInfo>> future = excelService.importExcelAsync(request);
        
        // 等待异步任务完成
        ExcelImportResult<UserInfo> result = future.get();
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(10, result.getSuccessRows());
        
        System.out.println("异步导入结果：" + result.getSummary());
    }
    
    @Test
    void testAsyncExport() throws Exception {
        // 创建异步导出请求
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelExportRequest<UserInfo> request = ExcelExportRequest.byTableName(
                outputStream,
                "异步导出测试.xlsx",
                "user_info",
                testUserList,
                UserInfo.class
        );
        
        // 执行异步导出
        CompletableFuture<ExcelExportService.ExcelExportResult> future = excelService.exportExcelAsync(request);
        
        // 等待异步任务完成
        ExcelExportResult result = future.get();
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(10, result.getTotalRows());
        
        System.out.println("异步导出结果：" + result.getSummary());
    }
    
    /**
     * 性能测试：大数据量导出
     */
    @Test
    void testLargeDataExport() {
        // 创建大量测试数据
        List<UserInfo> largeUserList = new ArrayList<>();
        for (long i = 1; i <= 1000; i++) {
            largeUserList.add(UserInfo.createSample(i, "user" + i));
        }
        
        long startTime = System.currentTimeMillis();
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelExportResult result = excelService.exportByTableName(
                outputStream,
                "大数据量测试.xlsx",
                "user_info",
                largeUserList,
                UserInfo.class
        );
        
        long endTime = System.currentTimeMillis();
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1000, result.getTotalRows());
        
        System.out.println("大数据量导出结果：" + result.getSummary());
        System.out.println("导出耗时：" + (endTime - startTime) + "ms");
        System.out.println("文件大小：" + outputStream.size() + " bytes");
    }
}
