package io.ibs.modules;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 增强版MyBatis-Plus代码生成器
 * <p>
 * 功能特性：
 * 1. 实体类继承DMBaseEntity，添加@Data、@Builder、@ToString(callSuper = true)、@EqualsAndHashCode(callSuper = true)注解
 * 2. Dao接口添加@Mapper注解
 * 3. Service不使用I前缀
 * 4. 生成Excel导出类（使用EasyExcel注解）
 * 5. 生成DTO类（继承自实体类）
 * 6. 生成Handler类（继承AbstractDataMoveService）
 * 7. 不生成Controller
 *
 * <AUTHOR>
 */
@SpringBootTest
public class EnhancedMybatisPlusGenerator {

    /**
     * 项目路径
     */
    private static final String PROJECT_PATH = "E:/source/JavaProjects/data-move-test";

    /**
     * 数据库连接配置
     */
    private static final String DB_URL = "*****************************************";
    private static final String DB_USERNAME = "DATAMOVE";
    private static final String DB_PASSWORD = "DATAMOVE";
    private static final String DB_SCHEMA = "DATAMOVE";

    /**
     * 包配置
     */
    private static final String PARENT_PACKAGE = "io.ibs.modules";
    private static final String MODULE_NAME = "move";

    @Test
    void generateCode() {
        // 数据源配置
        DataSourceConfig.Builder dataSourceConfig = new DataSourceConfig
                .Builder(DB_URL, DB_USERNAME, DB_PASSWORD)
                .schema(DB_SCHEMA);

        // 自定义输出路径
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.xml, PROJECT_PATH + "/src/main/resources/mapper");

        FastAutoGenerator.create(dataSourceConfig)
                // 全局配置
                .globalConfig(builder -> builder
                        .author("System")
                        .outputDir(PROJECT_PATH + "/src/main/java")
                        .disableOpenDir()
                        .commentDate("yyyy-MM-dd"))

                // 包配置
                .packageConfig(builder -> builder
                        .parent(PARENT_PACKAGE)
                        .moduleName(MODULE_NAME)
                        .entity("entity")
                        .mapper("dao")
                        .service("service")
                        .serviceImpl("service.impl")
                        .pathInfo(pathInfo))

                // 策略配置
                .strategyConfig(builder -> builder
                        // 表配置 - 可以指定具体表名
                        // .addInclude("TABLE_NAME1", "TABLE_NAME2") // 指定要生成的表名
                        // .addTablePrefix("DM") // 设置过滤表前缀

                        // 实体类策略
                        .entityBuilder()
                        .disableSerialVersionUID()
                        .superClass(DMBaseEntity.class)
                        .enableLombok()
                        .addSuperEntityColumns("id", "CREATE_DATE", "UPDATE_DATE", "CREATOR", "UPDATER")
                        .formatFileName("%sEntity")
                        .javaTemplate("/templates/entity.java.ftl") // 设置实体类模板
                        .enableFileOverride()

                        // Service策略
                        .serviceBuilder()
                        .formatServiceFileName("%sService") // 不使用I前缀
                        .formatServiceImplFileName("%sServiceImpl")
                        .serviceTemplate("/templates/service.java.ftl") // 设置Service模板
                        .serviceImplTemplate("/templates/serviceImpl.java.ftl") // 设置ServiceImpl模板
                        .enableFileOverride()

                        // Mapper策略
                        .mapperBuilder()
                        .enableMapperAnnotation() // 启用@Mapper注解
                        .formatMapperFileName("%sDao")
                        .formatXmlFileName("%sMapper")
                        .mapperTemplate("/templates/mapper.java.ftl") // 设置Mapper模板
                        .enableFileOverride()

                        // 控制器策略 - 禁用Controller生成
                        .controllerBuilder()
                        .disable())


                // 注入配置
                .injectionConfig(builder -> builder
                        // Excel导出类
                        .customFile(new CustomFile.Builder()
                                .fileName("Excel.java")
                                .templatePath("/templates/excel.java.ftl")
                                .packageName("excel")
                                .enableFileOverride()
                                .build())

                        // DTO类
                        .customFile(new CustomFile.Builder()
                                .fileName("DTO.java")
                                .templatePath("/templates/dto.java.ftl")
                                .packageName("dto")
                                .enableFileOverride()
                                .build())

                        // Handler类
                        .customFile(new CustomFile.Builder()
                                .fileName("Handler.java")
                                .templatePath("/templates/handler.java.ftl")
                                .packageName("handler")
                                .enableFileOverride()
                                .build()))

                // 注意：templateConfig在3.5.6之后已弃用，模板配置已迁移到StrategyConfig中
                
                // 模板引擎
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();

        System.out.println("代码生成完成！");
        System.out.println("请注意：");
        System.out.println("1. 检查生成的实体类是否正确继承了DMBaseEntity");
        System.out.println("2. 检查Dao接口是否添加了@Mapper注解");
        System.out.println("3. 检查Excel类是否包含了EasyExcel注解");
        System.out.println("4. 检查Handler类是否正确继承了AbstractDataMoveService");
        System.out.println("5. 可以运行generateDMTableInfoEnum()方法生成枚举类");
    }

    /**
     * 生成指定表的代码
     */
    @Test
    void generateSpecificTables() {
        // 指定要生成的表名
        String[] tableNames = {
                // "YOUR_TABLE_NAME_1",
                // "YOUR_TABLE_NAME_2"
        };

        if (tableNames.length == 0) {
            System.out.println("请在tableNames数组中指定要生成的表名");
            return;
        }

        DataSourceConfig.Builder dataSourceConfig = new DataSourceConfig
                .Builder(DB_URL, DB_USERNAME, DB_PASSWORD)
                .schema(DB_SCHEMA);

        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.xml, PROJECT_PATH + "/src/main/resources/mapper");

        FastAutoGenerator.create(dataSourceConfig)
                .globalConfig(builder -> builder
                        .author("System")
                        .outputDir(PROJECT_PATH + "/src/main/java")
                        .disableOpenDir())
                .packageConfig(builder -> builder
                        .parent(PARENT_PACKAGE)
                        .moduleName(MODULE_NAME)
                        .entity("entity")
                        .mapper("dao")
                        .service("service")
                        .serviceImpl("service.impl")
                        .pathInfo(pathInfo))
                .strategyConfig(builder -> builder
                        .addInclude(tableNames) // 指定表名
                        .entityBuilder()
                        .disableSerialVersionUID()
                        .superClass(DMBaseEntity.class)
                        .enableLombok()
                        .addSuperEntityColumns("id", "CREATE_DATE", "UPDATE_DATE", "CREATOR", "UPDATER")
                        .formatFileName("%sEntity")
                        .javaTemplate("/templates/entity.java.ftl")
                        .serviceBuilder()
                        .formatServiceFileName("%sService")
                        .formatServiceImplFileName("%sServiceImpl")
                        .serviceTemplate("/templates/service.java.ftl")
                        .serviceImplTemplate("/templates/serviceImpl.java.ftl")
                        .mapperBuilder()
                        .enableMapperAnnotation()
                        .formatMapperFileName("%sDao")
                        .formatXmlFileName("%sMapper")
                        .mapperTemplate("/templates/mapper.java.ftl")
                        .controllerBuilder()
                        .disable())
                .injectionConfig(builder -> builder
                        .customFile(new CustomFile.Builder()
                                .fileName("Excel.java")
                                .templatePath("/templates/excel.java.ftl")
                                .packageName("excel")
                                .build())
                        .customFile(new CustomFile.Builder()
                                .fileName("DTO.java")
                                .templatePath("/templates/dto.java.ftl")
                                .packageName("dto")
                                .build())
                        .customFile(new CustomFile.Builder()
                                .fileName("Handler.java")
                                .templatePath("/templates/handler.java.ftl")
                                .packageName("handler")
                                .build()))
                // 模板配置已迁移到StrategyConfig中
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();

        System.out.println("指定表代码生成完成！");
    }

    /**
     * 生成DMTableInfo枚举类
     * 需要在所有表生成完成后手动调用
     */
    @Test
    void generateDMTableInfoEnum() {
        String enumContent = generateTableInfoEnumContent();
        System.out.println("=== DMTableInfo.java 内容 ===");
        System.out.println(enumContent);
        System.out.println("=== 请将以上内容保存为 src/main/java/io/ibs/modules/move/common/enums/DMTableInfo.java ===");
    }

    private String generateTableInfoEnumContent() {
        StringBuilder sb = new StringBuilder();
        sb.append("package io.ibs.modules.move.common.enums;\n\n");
        sb.append("import io.ibs.modules.move.AbstractDataMoveService;\n");
        sb.append("import lombok.Getter;\n\n");
        sb.append("/**\n");
        sb.append(" * 数据迁移表信息枚举\n");
        sb.append(" * \n");
        sb.append(" * <AUTHOR>
        sb.append(" */\n");
        sb.append("@Getter\n");
        sb.append("public enum DMTableInfo {\n\n");

        // 示例枚举项 - 需要根据实际生成的表进行修改
        sb.append("    // 示例：\n");
        sb.append("    // DEMO(1, \"示例表\", \"DEMO_TABLE\", DemoEntity.class, new DemoHandler()),\n");
        sb.append("    \n");
        sb.append("    // TODO: 请在这里添加实际生成的表信息\n");
        sb.append("    // 格式：枚举名(编号, \"表中文名\", \"表名\", 实体类.class, new Handler实例())\n");
        sb.append("    ;\n\n");

        sb.append("    private final Integer code;\n");
        sb.append("    private final String name;\n");
        sb.append("    private final String tableName;\n");
        sb.append("    private final Class<?> entityClass;\n");
        sb.append("    private final AbstractDataMoveService handler;\n\n");

        sb.append("    DMTableInfo(Integer code, String name, String tableName, Class<?> entityClass, AbstractDataMoveService handler) {\n");
        sb.append("        this.code = code;\n");
        sb.append("        this.name = name;\n");
        sb.append("        this.tableName = tableName;\n");
        sb.append("        this.entityClass = entityClass;\n");
        sb.append("        this.handler = handler;\n");
        sb.append("    }\n");
        sb.append("}\n");

        return sb.toString();
    }
}
