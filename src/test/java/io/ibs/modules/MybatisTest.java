package io.ibs.modules;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import io.ibs.modules.move.common.entity.DMBaseEntity;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collections;

@SpringBootTest
public class MybatisTest {

    String projectPath = "E:/source/JavaProjects/data-move-test";

    @Test
    void testGenerator() {
        DataSourceConfig.Builder dataSourceConfig = new DataSourceConfig
                .Builder("*****************************************", "DATAMOVE", "DATAMOVE")
                .schema("DATAMOVE");// Oracle 12c兼容配置

        FastAutoGenerator.create(dataSourceConfig)
                .globalConfig(builder -> builder
                        .author("System")
                        .outputDir(projectPath + "/src/main/java")
                        .disableOpenDir())
                .packageConfig(builder -> builder
                        .parent("io.ibs.modules")
                        .moduleName("move")
                        .entity("entity")
                        .mapper("dao")
                        .service("service")
                        .serviceImpl("service.impl")
                        .controller("controller")
                        .pathInfo(Collections.singletonMap(OutputFile.xml,
                                projectPath + "/src/main/resources/mapper")))
                .strategyConfig(builder -> builder
                        // .addInclude("your_table_name")
                        // .addTablePrefix("t_", "c_")
                        .entityBuilder()
                        .disableSerialVersionUID()
                        .superClass(DMBaseEntity.class)
                        .enableLombok()
                        .addSuperEntityColumns("id", "CREATE_DATE", "UPDATE_DATE", "CREATOR", "UPDATER")
                        .formatFileName("%sEntity")
                        .controllerBuilder()
                        .enableRestStyle()
                        .serviceBuilder()
                        .formatServiceFileName("%sService")
                        .formatServiceImplFileName("%sServiceImpl"))
                .injectionConfig(builder -> builder
                        .beforeOutputFile((tableInfo, objectMap) -> {
                            // 生成DMTableInfo枚举
                            objectMap.put("tableInfoEnum", generateTableInfoEnum(tableInfo));

                            // 生成Handler类
                            objectMap.put("handlerContent", generateHandlerClass(tableInfo));
                        })
                        // .customFile(new CustomFile.Builder()
                        //         .fileName("DMTableInfo.java")
                        //         .templatePath("/templates/enum.ftl")
                        //         .packageName("common.enums")
                        //         .build())
                        .customFile(new CustomFile.Builder()
                                .fileName("Handler.java")
                                .templatePath("/templates/handler.ftl")
                                .packageName("handler")
                                .build()))
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }

    private String generateTableInfoEnum(TableInfo tableInfo) {
        return String.format("package io.ibs.modules.move.common.enums;\n\n" +
                        "import io.ibs.modules.move.asset.handler.%sHandler;\n" +
                        "import io.ibs.modules.move.common.entity.%sEntity;\n\n" +
                        "public enum DMTableInfo {\n" +
                        "    %s(\"%s\", %sEntity.class, new %sHandler());\n\n" +
                        "    private final String tableName;\n" +
                        "    private final Class<?> entityClass;\n" +
                        "    private final Object handler;\n\n" +
                        "    DMTableInfo(String tableName, Class<?> entityClass, Object handler) {\n" +
                        "        this.tableName = tableName;\n" +
                        "        this.entityClass = entityClass;\n" +
                        "        this.handler = handler;\n" +
                        "    }\n\n" +
                        "    // getters\n" +
                        "}",
                tableInfo.getEntityName(),
                tableInfo.getEntityName(),
                tableInfo.getEntityName().toUpperCase(),
                tableInfo.getName(),
                tableInfo.getEntityName(),
                tableInfo.getEntityName());
    }

    private String generateHandlerClass(TableInfo tableInfo) {
        return String.format("package io.ibs.modules.move.asset.handler;\n\n" +
                        "import io.ibs.modules.move.common.entity.%sEntity;\n" +
                        "import java.lang.reflect.Field;\n" +
                        "import java.util.ArrayList;\n" +
                        "import java.util.List;\n\n" +
                        "public class %sHandler {\n" +
                        "    public void setDefaultParams(%sEntity entity) {\n" +
                        "        try {\n" +
                        "            Class<?> clazz = entity.getClass();\n" +
                        "            List<Field> allFields = getAllFields(clazz);\n" +
                        "            \n" +
                        "            for (Field field : allFields) {\n" +
                        "                field.setAccessible(true);\n" +
                        "                field.set(entity, null);\n" +
                        "            }\n" +
                        "        } catch (Exception e) {\n" +
                        "            e.printStackTrace();\n" +
                        "        }\n" +
                        "    }\n\n" +
                        "    private List<Field> getAllFields(Class<?> clazz) {\n" +
                        "        List<Field> fields = new ArrayList<>();\n" +
                        "        \n" +
                        "        // 添加当前类字段\n" +
                        "        for (Field field : clazz.getDeclaredFields()) {\n" +
                        "            fields.add(field);\n" +
                        "        }\n" +
                        "        \n" +
                        "        // 递归添加父类字段\n" +
                        "        Class<?> superClass = clazz.getSuperclass();\n" +
                        "        while (superClass != null && superClass != Object.class) {\n" +
                        "            for (Field field : superClass.getDeclaredFields()) {\n" +
                        "                fields.add(field);\n" +
                        "            }\n" +
                        "            superClass = superClass.getSuperclass();\n" +
                        "        }\n" +
                        "        \n" +
                        "        return fields;\n" +
                        "    }\n" +
                        "}",
                tableInfo.getEntityName(),
                tableInfo.getEntityName(),
                tableInfo.getEntityName());
    }
}

