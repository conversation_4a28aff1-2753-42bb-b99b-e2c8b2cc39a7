# MyBatis-Plus 增强代码生成器使用说明

## 概述

本项目提供了一个增强版的MyBatis-Plus代码生成器，专门针对数据迁移项目的需求进行了定制化开发。

## 最新优化内容

### v2.0 优化内容：
1. **修复中文注释转义符问题**：将注释中的 `\` 转换为 `/`
2. **优化Excel注解配置**：`@ColumnWidth` 统一设置在类级别
3. **修复Handler包路径问题**：移除多余的move包层级
4. **修复Handler文件名问题**：确保文件名与类名一致
5. **增强DTO注解**：添加 `@Data`、`@Builder`、`@ToString(callSuper = true)`、`@EqualsAndHashCode(callSuper = true)`
6. **完善DMTableInfo枚举**：根据提供的表格完善所有38个表的枚举项
7. **升级模板配置**：将templateConfig迁移到StrategyConfig中（适配MyBatis-Plus 3.5.6+）

## 功能特性

### 1. 实体类(Entity)
- 继承 `DMBaseEntity` 基类
- 自动排除基类中的公共字段（id, CREATE_DATE, UPDATE_DATE, CREATOR, UPDATER）
- 添加注解：
  - `@Data`
  - `@Builder`
  - `@ToString(callSuper = true)`
  - `@EqualsAndHashCode(callSuper = true)`

### 2. 数据访问层(Dao)
- 继承 `BaseMapper<Entity>`
- 添加 `@Mapper` 注解
- 文件名格式：`{表名}Dao.java`

### 3. 服务层(Service)
- Service接口不使用I前缀
- ServiceImpl实现类
- 文件名格式：
  - Service接口：`{表名}Service.java`
  - Service实现：`{表名}ServiceImpl.java`

### 4. Excel导出类
- 使用EasyExcel框架注解
- 包含 `@ExcelProperty`、`@ColumnWidth`（类级别统一设置）、`@ContentRowHeight`、`@HeadRowHeight` 等注解
- 自动处理中文注释转义符问题
- 文件名格式：`{表名}Excel.java`
- 位置：`excel` 包下

### 5. DTO类
- 继承自对应的实体类
- 添加注解：`@Data`、`@Builder`、`@ToString(callSuper = true)`、`@EqualsAndHashCode(callSuper = true)`
- 文件名格式：`{表名}DTO.java`
- 位置：`dto` 包下

### 6. Handler类
- 继承 `AbstractDataMoveService` 抽象类
- 实现数据迁移逻辑
- 包含 `setDefaultParams` 方法，将所有字段设置为null，并带有中文注释
- 自动处理中文注释转义符问题
- 文件名格式：`{表名}Handler.java`（修复了文件名与类名不一致的问题）
- 位置：`handler` 包下（修复了多层move包的问题）

### 7. 不生成Controller
- 根据需求，不生成Controller层代码

## 使用方法

### 1. 配置数据库连接

在 `EnhancedMybatisPlusGenerator.java` 中修改数据库连接配置：

```java
private static final String DB_URL = "*****************************************";
private static final String DB_USERNAME = "DATAMOVE";
private static final String DB_PASSWORD = "DATAMOVE";
private static final String DB_SCHEMA = "DATAMOVE";
```

### 2. 生成所有表的代码

运行测试方法：
```java
@Test
void generateCode()
```

### 3. 生成指定表的代码

1. 在 `generateSpecificTables()` 方法中指定表名：
```java
String[] tableNames = {
    "YOUR_TABLE_NAME_1",
    "YOUR_TABLE_NAME_2"
};
```

2. 运行测试方法：
```java
@Test
void generateSpecificTables()
```

### 4. 生成DMTableInfo枚举类

在所有表代码生成完成后，运行：
```java
@Test
void generateDMTableInfoEnum()
```

该方法会自动生成包含所有38个表的完整枚举类内容，包括：
- 电子账户相关表（101-103）
- 账户相关表（104-111）
- 代理和定期相关表（112-115）
- 对公对私账户表（116-118）
- 总账相关表（201-203）
- 贷款相关表（301-318）

生成的枚举类包含完整的实体类引用和Handler实例。

## 生成的文件结构

```
src/main/java/io/ibs/modules/move/
├── entity/          # 实体类
│   ├── DemoEntity.java
│   └── ...
├── dao/             # 数据访问层
│   ├── DemoDao.java
│   └── ...
├── service/         # 服务接口
│   ├── DemoService.java
│   └── ...
├── service/impl/    # 服务实现
│   ├── DemoServiceImpl.java
│   └── ...
├── excel/           # Excel导出类
│   ├── DemoExcel.java
│   └── ...
├── dto/             # DTO类
│   ├── DemoDTO.java
│   └── ...
├── handler/         # 数据迁移处理器
│   ├── DemoHandler.java
│   └── ...
└── common/enums/    # 枚举类
    └── DMTableInfo.java
```

## 模板文件

项目包含以下自定义模板文件：

- `src/main/resources/templates/entity.java.ftl` - 实体类模板
- `src/main/resources/templates/mapper.java.ftl` - Mapper接口模板
- `src/main/resources/templates/service.java.ftl` - Service接口模板
- `src/main/resources/templates/serviceImpl.java.ftl` - Service实现模板
- `src/main/resources/templates/excel.java.ftl` - Excel导出类模板
- `src/main/resources/templates/dto.java.ftl` - DTO类模板
- `src/main/resources/templates/handler.java.ftl` - Handler类模板

## 注意事项

1. **数据库兼容性**：当前配置适用于Oracle 12c数据库
2. **基类依赖**：确保 `DMBaseEntity` 类存在且字段配置正确
3. **包结构**：生成的代码遵循项目既定的包结构规范
4. **手动配置**：DMTableInfo枚举类需要在代码生成后手动添加枚举项
5. **模板定制**：可以根据项目需求修改模板文件

## 依赖要求

确保项目中包含以下依赖：

```xml
<!-- MyBatis-Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.12</version>
</dependency>

<!-- MyBatis-Plus Generator -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-generator</artifactId>
    <version>3.5.12</version>
</dependency>

<!-- EasyExcel -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>easyexcel</artifactId>
    <version>3.3.2</version>
</dependency>

<!-- FreeMarker -->
<dependency>
    <groupId>org.freemarker</groupId>
    <artifactId>freemarker</artifactId>
    <version>2.3.31</version>
</dependency>

<!-- Oracle JDBC -->
<dependency>
    <groupId>com.oracle.ojdbc</groupId>
    <artifactId>ojdbc8</artifactId>
    <version>19.3.0.0</version>
</dependency>
```

## 示例代码

### Handler类示例

```java
@NoArgsConstructor
public class DemoHandler extends AbstractDataMoveService {

    @Override
    protected void execute(Map<String, Object> args) throws Exception {
        // 查询数据库
        List<DemoEntity> demoList = new ArrayList<>();
        setDefaultParams(demoList);
    }

    private void setDefaultParams(List<DemoEntity> demoList) {
        for (DemoEntity entity : demoList) {
            entity.setName(null); // 姓名
            entity.setAge(null); // 年龄
            // ... 其他字段
        }
    }
}
```

### DMTableInfo枚举示例

```java
public enum DMTableInfo {
    DEMO(1, "示例表", "DEMO_TABLE", DemoEntity.class, new DemoHandler()),
    USER(2, "用户表", "SYS_USER", UserEntity.class, new UserHandler());
    
    // ... 构造函数和方法
}
```
