# 数据迁移项目需求架构文档

## 1. 项目概述

### 1.1 项目背景
本项目是一个基于MyBatis-Plus的数据迁移系统，主要用于Oracle 12数据库的数据迁移和处理。系统采用代码生成器自动生成标准化的数据访问层、业务层和数据传输对象。

### 1.2 技术栈
- **框架**: Spring Boot 3.5.0
- **ORM**: MyBatis-Plus 3.5.12
- **数据库**: Oracle 12c
- **模板引擎**: FreeMarker 2.3.31
- **Excel处理**: EasyExcel 3.3.2
- **构建工具**: Maven
- **Java版本**: JDK 17

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    数据迁移系统架构                           │
├─────────────────────────────────────────────────────────────┤
│  代码生成器层 (Code Generator Layer)                         │
│  ├── EnhancedMybatisPlusGenerator                           │
│  ├── FreeMarker模板引擎                                      │
│  └── 自定义模板文件                                          │
├─────────────────────────────────────────────────────────────┤
│  业务处理层 (Business Layer)                                │
│  ├── Handler (数据迁移处理器)                                │
│  ├── Service (业务服务层)                                    │
│  └── AbstractDataMoveService (抽象数据迁移服务)              │
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (Data Access Layer)                             │
│  ├── Entity (实体类)                                        │
│  ├── Dao (数据访问接口)                                      │
│  └── DMBaseEntity (基础实体类)                               │
├─────────────────────────────────────────────────────────────┤
│  数据传输层 (Data Transfer Layer)                           │
│  ├── DTO (数据传输对象)                                      │
│  ├── Excel (Excel导出对象)                                  │
│  └── DMTableInfo (表信息枚举)                                │
├─────────────────────────────────────────────────────────────┤
│  数据库层 (Database Layer)                                  │
│  └── Oracle 12c Database                                   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 包结构设计
```
io.ibs.modules.move
├── common/                     # 公共模块
│   ├── entity/                 # 基础实体类
│   │   └── DMBaseEntity.java
│   └── enums/                  # 枚举类
│       └── DMTableInfo.java
├── entity/                     # 实体类
├── dao/                        # 数据访问层
├── service/                    # 服务接口
├── service/impl/               # 服务实现
├── dto/                        # 数据传输对象
├── excel/                      # Excel导出对象
├── handler/                    # 数据迁移处理器
└── AbstractDataMoveService.java # 抽象数据迁移服务
```

## 3. 核心组件设计

### 3.1 代码生成器 (EnhancedMybatisPlusGenerator)

#### 3.1.1 功能特性
- 基于MyBatis-Plus 3.5.12的增强代码生成器
- 支持Oracle 12c数据库
- 自动生成7种类型的文件
- 支持自定义模板和配置

#### 3.1.2 生成文件类型
| 文件类型 | 说明 | 模板文件 | 输出位置 |
|---------|------|----------|----------|
| Entity | 实体类 | entity.java.ftl | entity/ |
| Dao | 数据访问接口 | mapper.java.ftl | dao/ |
| Service | 服务接口 | service.java.ftl | service/ |
| ServiceImpl | 服务实现 | serviceImpl.java.ftl | service/impl/ |
| Excel | Excel导出类 | excel.java.ftl | excel/ |
| DTO | 数据传输对象 | dto.java.ftl | dto/ |
| Handler | 数据迁移处理器 | handler.java.ftl | handler/ |

### 3.2 实体类设计 (Entity)

#### 3.2.1 继承关系
```java
DMBaseEntity (基础实体类)
├── id: Long                    // 主键ID
├── createDate: Date           // 创建时间
├── updateDate: Date           // 更新时间
├── creator: Long              // 创建人
└── updater: Long              // 更新人

具体实体类 extends DMBaseEntity
├── 业务字段1
├── 业务字段2
└── ...
```

#### 3.2.2 注解配置
```java
@Data                          // Lombok数据注解
@Builder                       // 建造者模式
@ToString(callSuper = true)    // 包含父类字段的toString
@EqualsAndHashCode(callSuper = true) // 包含父类字段的equals和hashCode
@TableName("表名")             // MyBatis-Plus表名映射
```

### 3.3 数据访问层设计 (Dao)

#### 3.3.1 接口设计
```java
@Mapper
public interface XxxDao extends BaseMapper<XxxEntity> {
    // 继承BaseMapper的基础CRUD方法
    // 可扩展自定义查询方法
}
```

### 3.4 服务层设计 (Service)

#### 3.4.1 接口设计
```java
public interface XxxService extends IService<XxxEntity> {
    // 继承IService的基础服务方法
    // 可扩展业务方法
}
```

#### 3.4.2 实现类设计
```java
@Service
public class XxxServiceImpl extends ServiceImpl<XxxDao, XxxEntity> 
    implements XxxService {
    // 实现业务逻辑
}
```

### 3.5 数据迁移处理器设计 (Handler)

#### 3.5.1 继承关系
```java
AbstractDataMoveService (抽象基类)
└── execute(Map<String, Object> args) // 抽象方法

具体Handler extends AbstractDataMoveService
├── execute() // 实现数据迁移逻辑
└── setDefaultParams() // 设置默认参数
```

#### 3.5.2 处理器模式
```java
@NoArgsConstructor
public class XxxHandler extends AbstractDataMoveService {
    
    @Override
    protected void execute(Map<String, Object> args) throws Exception {
        // 1. 查询数据库
        List<XxxEntity> dataList = new ArrayList<>();
        
        // 2. 数据处理
        setDefaultParams(dataList);
        
        // 3. 其他业务逻辑
    }
    
    private void setDefaultParams(List<XxxEntity> dataList) {
        for (XxxEntity entity : dataList) {
            entity.setField1(null); // 字段1注释
            entity.setField2(null); // 字段2注释
            // ... 其他字段
        }
    }
}
```

## 4. 数据模型设计

### 4.1 表分类

#### 4.1.1 电子账户相关表 (101-103)
| 编号 | 表名 | 中文名称 | 实体类 |
|------|------|----------|--------|
| 101 | DMDDNCAA | 电子账户绑定关系表 | DmddncaaEntity |
| 102 | DMDDNCRA | 电子账户登记簿 | DmddncraEntity |
| 103 | DMDDNCRP | 电子账户开户补充信息表 | DmddncrpEntity |

#### 4.1.2 账户相关表 (104-118)
| 编号 | 表名 | 中文名称 | 实体类 |
|------|------|----------|--------|
| 104 | DMDPABLA | 账户当前积数表 | DmdpablaEntity |
| 105 | DMDPABSA | 账户分段积数表 | DmdpabsaEntity |
| 106 | DMDPMSDA | 账户迁移表 | DmdpmsdaEntity |
| 107 | DMDPSDBA | 强制扣划登记簿中间文件 | DmdpsdbEntity |
| 108 | DMDPSLGA | 单位睡眠户客户账户登记簿 | DmdpslgaEntity |
| 109 | DMDPSRBA | 特种业务登记簿 | DmdpsrbaEntity |
| 110 | DMDPSRBP | 特种业务司法登记薄附表 | DmdpsrbpEntity |
| 111 | DMDPUPDA | 未登折明细记录表 | DmdpupdaEntity |
| 112 | DMPMAAHA | 代理人开户历史档 | DmpmaahaEntity |
| 113 | DMTDARTA | 代村镇银行支付定期利息明细文件 | DmtdartaEntity |
| 114 | DMTDPCDA | 个人周期计划文件-关联表 | DmtdpcdaEntity |
| 115 | DMTDRDDA | 定期账户明细文件 | DmtdrddaEntity |
| 116 | DMDDCDSA | 对公活期账户明细文件 | DmddcdsaEntity |
| 117 | DMDDRDSA | 对私活期账户明细文件 | DmddrdsaEntity |
| 118 | DMDPCTLA | 控制对象累计限额档 | DmdpctlaEntity |

#### 4.1.3 总账相关表 (201-203)
| 编号 | 表名 | 中文名称 | 实体类 |
|------|------|----------|--------|
| 201 | DMGLMSTB | 日总账 | DmglmstbEntity |
| 202 | DMIAIAIA | 内部账户 | DmiaiaiaEntity |
| 203 | DMGLMSTB | 老核心日总账表中间表 | DmglmstbEntity |

#### 4.1.4 贷款相关表 (301-318)
| 编号 | 表名 | 中文名称 | 实体类 |
|------|------|----------|--------|
| 301 | DMLNCOFA | 协议基本信息 | DmlncofaEntity |
| 302 | DMLNDDFA | 协议放款基本信息 | DmlnddfaEntity |
| 303 | DMLNETDA | 登记受托支付处理明细信息 | DmlnetdaEntity |
| 304 | DMLNFJNA | 表内交易明细 | DmlnfjnaEntity |
| 305 | DMLNIBFA | 借据主档 | DmlnibfaEntity |
| 306 | DMLNIBLA | 借据余额主档 | DmlniblaEntity |
| 307 | DMLNIRHA | 执行利率资料基本信息 | DmlnirhaEntity |
| 308 | DMLNIRPA | 贷款利率重定价资料 | DmlnirpaEntity |
| 309 | DMLNIRTA | 贷款正常利率资料基本信息 | DmlnirtaEntity |
| 310 | DMLNKJNA1 | 关键交易明细-正交易 | Dmlnkjna1Entity |
| 311 | DMLNKJNA2 | 关键交易明细-冲正 | Dmlnkjna2Entity |
| 312 | DMLNPIRA | 贷款罚息利率资料基本信息 | DmlnpiraEntity |
| 313 | DMLNRJNA | 贷款还款交易明细 | DmlnrjnaEntity |
| 314 | DMLNRLAA | 跨模块关联账户登记薄 | DmlnrlaaEntity |
| 315 | DMLNRPFA | 协议还款基本信息 | DmlnrpfaEntity |
| 316 | DMLNRPPA1 | 还款计划表（应还款明细） | Dmlnrppa1Entity |
| 317 | DMLNRPPA2 | 还款计划表（其他） | Dmlnrppa2Entity |
| 318 | DMLNWOFA | 核销及收回登记簿 | DmlnwofaEntity |

### 4.2 DMTableInfo枚举设计

#### 4.2.1 枚举结构
```java
public enum DMTableInfo {
    TABLE_NAME(编号, "中文名称", "表名", EntityClass.class, new Handler());
    
    private final Integer code;           // 编号
    private final String name;            // 中文名称
    private final String tableName;       // 表名
    private final Class<?> entityClass;   // 实体类
    private final AbstractDataMoveService handler; // 处理器
}
```

#### 4.2.2 查询方法
- `getByCode(Integer code)` - 根据编号查询
- `getByTableName(String tableName)` - 根据表名查询
- `getByEntityClass(Class<?> entityClass)` - 根据实体类查询

## 5. 技术规范

### 5.1 代码规范

#### 5.1.1 命名规范
- **实体类**: `{表名}Entity.java`
- **Dao接口**: `{表名}Dao.java`
- **Service接口**: `{表名}Service.java` (不使用I前缀)
- **Service实现**: `{表名}ServiceImpl.java`
- **DTO类**: `{表名}DTO.java`
- **Excel类**: `{表名}Excel.java`
- **Handler类**: `{表名}Handler.java`

#### 5.1.2 注解规范
- **实体类**: `@Data`, `@Builder`, `@ToString(callSuper = true)`, `@EqualsAndHashCode(callSuper = true)`
- **Dao接口**: `@Mapper`
- **Service实现**: `@Service`
- **DTO类**: `@Data`, `@Builder`, `@ToString(callSuper = true)`, `@EqualsAndHashCode(callSuper = true)`
- **Excel类**: `@Data`, `@ContentRowHeight(20)`, `@HeadRowHeight(25)`, `@ColumnWidth(20)`
- **Handler类**: `@NoArgsConstructor`

### 5.2 模板配置

#### 5.2.1 MyBatis-Plus 3.5.6+ 配置
```java
.strategyConfig(builder -> builder
    .entityBuilder()
    .javaTemplate("/templates/entity.java.ftl")
    .serviceBuilder()
    .serviceTemplate("/templates/service.java.ftl")
    .serviceImplTemplate("/templates/serviceImpl.java.ftl")
    .mapperBuilder()
    .mapperTemplate("/templates/mapper.java.ftl")
    .controllerBuilder()
    .disable())
```

### 5.3 数据库配置

#### 5.3.1 Oracle 12c 连接配置
```properties
spring.datasource.url=*****************************************
spring.datasource.username=DATAMOVE
spring.datasource.password=DATAMOVE
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
```

## 6. 部署和使用

### 6.1 环境要求
- JDK 17+
- Maven 3.6+
- Oracle 12c数据库
- Spring Boot 3.5.0

### 6.2 使用流程
1. 配置数据库连接信息
2. 运行 `EnhancedMybatisPlusGenerator.generateCode()` 生成所有表
3. 或运行 `generateSpecificTables()` 生成指定表
4. 运行 `generateDMTableInfoEnum()` 生成枚举类
5. 检查生成的代码并进行必要的调整

### 6.3 质量保证
- 所有生成的代码遵循统一的编码规范
- 自动处理中文注释转义符问题
- 确保文件名与类名一致
- 包路径结构清晰合理

## 7. 扩展和维护

### 7.1 模板扩展
- 可根据项目需求修改FreeMarker模板
- 支持添加新的自定义文件类型
- 模板文件位置：`src/main/resources/templates/`

### 7.2 配置扩展
- 支持添加新的表映射
- 可配置不同的包结构
- 支持自定义字段映射规则

### 7.3 版本兼容性
- 当前版本适配MyBatis-Plus 3.5.12
- 支持Oracle 12c及以上版本
- 兼容Spring Boot 3.x系列
